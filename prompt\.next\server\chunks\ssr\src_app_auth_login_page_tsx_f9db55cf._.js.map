{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport Link from 'next/link'\r\nimport { useAuth } from '~/lib/auth/context'\r\nimport toast from 'react-hot-toast'\r\n\r\nexport default function LoginPage() {\r\n  const [email, setEmail] = useState('')\r\n  const [password, setPassword] = useState('')\r\n  const [loading, setLoading] = useState(false)\r\n  const router = useRouter()\r\n  const { signIn } = useAuth()\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    setLoading(true)\r\n\r\n    try {\r\n      const { error } = await signIn(email, password)\r\n      if (error) {\r\n        toast.error(error.message || '登录失败')\r\n      } else {\r\n        toast.success('登录成功！')\r\n        router.push('/')\r\n      }\r\n    } catch (error) {\r\n      toast.error('登录失败，请稍后重试')\r\n    } finally {\r\n      setLoading(false)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n      <div className=\"card w-96 bg-base-100 shadow-xl\">\r\n        <div className=\"card-body\">\r\n          <h2 className=\"card-title text-center justify-center text-2xl font-bold mb-6\">\r\n            登录账户\r\n          </h2>\r\n          \r\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text\">邮箱地址</span>\r\n              </label>\r\n              <input\r\n                type=\"email\"\r\n                placeholder=\"请输入邮箱地址\"\r\n                className=\"input input-bordered w-full\"\r\n                value={email}\r\n                onChange={(e) => setEmail(e.target.value)}\r\n                required\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text\">密码</span>\r\n              </label>\r\n              <input\r\n                type=\"password\"\r\n                placeholder=\"请输入密码\"\r\n                className=\"input input-bordered w-full\"\r\n                value={password}\r\n                onChange={(e) => setPassword(e.target.value)}\r\n                required\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"form-control mt-6\">\r\n              <button\r\n                type=\"submit\"\r\n                className={`btn btn-primary w-full ${loading ? 'loading' : ''}`}\r\n                disabled={loading}\r\n              >\r\n                {loading ? '登录中...' : '登录'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n          \r\n          <div className=\"divider\">或</div>\r\n          \r\n          <div className=\"text-center\">\r\n            <span className=\"text-base-content/70\">还没有账户？</span>\r\n            <Link href=\"/auth/register\" className=\"link link-primary ml-1\">\r\n              立即注册\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAEzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;YACtC,IAAI,OAAO;gBACT,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgE;;;;;;kCAI9E,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;kDAE/B,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;kDAE/B,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAW,CAAC,uBAAuB,EAAE,UAAU,YAAY,IAAI;oCAC/D,UAAU;8CAET,UAAU,WAAW;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;wBAAI,WAAU;kCAAU;;;;;;kCAEzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;0CACvC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAiB,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E", "debugId": null}}]}