# 环境变量和域名设置指南

## 1. 环境变量配置

### 1.1 必需的环境变量

以下环境变量必须在 Vercel 项目中设置：

#### 数据库配置
```bash
# PostgreSQL 数据库连接字符串
DATABASE_URL="********************************************/database_name"
```

#### Supabase 配置
```bash
# Supabase 项目 URL
NEXT_PUBLIC_SUPABASE_URL="https://your-project-id.supabase.co"

# Supabase 公开 API 密钥
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# Supabase 服务角色密钥（仅服务器端）
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

#### 认证配置
```bash
# NextAuth.js 配置
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-nextauth-secret"
```

### 1.2 可选的环境变量

#### 应用配置
```bash
# 应用名称
NEXT_PUBLIC_APP_NAME="提示词管理工具"

# 应用版本
NEXT_PUBLIC_APP_VERSION="1.0.0"

# 应用 URL
NEXT_PUBLIC_APP_URL="https://your-domain.com"
```

#### 功能开关
```bash
# 启用分析
NEXT_PUBLIC_ENABLE_ANALYTICS="true"

# 启用错误监控
NEXT_PUBLIC_ENABLE_SENTRY="true"

# 启用热图分析
NEXT_PUBLIC_ENABLE_HOTJAR="false"
```

#### 第三方服务
```bash
# Sentry 错误监控
SENTRY_DSN="https://your-sentry-dsn"

# Google Analytics
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# Hotjar 热图分析
HOTJAR_ID="your-hotjar-id"
```

#### 安全配置
```bash
# CSRF 保护密钥
CSRF_SECRET="your-csrf-secret"

# 数据加密密钥
ENCRYPTION_KEY="your-encryption-key"

# 健康检查令牌
HEALTH_CHECK_TOKEN="your-health-check-token"
```

#### 缓存配置
```bash
# Redis 连接 URL
REDIS_URL="redis://localhost:6379"

# 缓存过期时间（秒）
CACHE_TTL="3600"
```

#### 文件上传配置
```bash
# 最大文件大小（字节）
UPLOAD_MAX_SIZE="5242880"

# 允许的文件类型
ALLOWED_FILE_TYPES="application/json,text/plain"
```

#### 邮件配置
```bash
# 发件人邮箱
EMAIL_FROM="<EMAIL>"

# 发件人姓名
EMAIL_FROM_NAME="提示词管理工具"

# SMTP 配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
```

#### 速率限制
```bash
# 速率限制时间窗口（毫秒）
RATE_LIMIT_WINDOW="900000"

# 速率限制最大请求数
RATE_LIMIT_MAX="100"
```

## 2. 环境变量设置方法

### 2.1 通过 Vercel Dashboard 设置

1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 选择您的项目
3. 进入 **Settings** 标签
4. 点击 **Environment Variables**
5. 添加环境变量：
   - **Name**: 变量名称
   - **Value**: 变量值
   - **Environment**: 选择环境（Development/Preview/Production）

### 2.2 通过 Vercel CLI 设置

```bash
# 安装 Vercel CLI
npm install -g vercel

# 登录 Vercel
vercel login

# 设置生产环境变量
vercel env add DATABASE_URL production

# 设置预览环境变量
vercel env add DATABASE_URL preview

# 设置开发环境变量
vercel env add DATABASE_URL development

# 查看所有环境变量
vercel env ls

# 拉取环境变量到本地
vercel env pull .env.local
```

### 2.3 批量设置环境变量

创建一个脚本来批量设置环境变量：

```bash
#!/bin/bash

# 设置生产环境变量
echo "设置生产环境变量..."
vercel env add DATABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add SUPABASE_SERVICE_ROLE_KEY production
vercel env add NEXTAUTH_URL production
vercel env add NEXTAUTH_SECRET production

# 设置预览环境变量
echo "设置预览环境变量..."
vercel env add DATABASE_URL preview
vercel env add NEXT_PUBLIC_SUPABASE_URL preview
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY preview
vercel env add SUPABASE_SERVICE_ROLE_KEY preview
vercel env add NEXTAUTH_URL preview
vercel env add NEXTAUTH_SECRET preview

echo "环境变量设置完成！"
```

## 3. 域名配置

### 3.1 添加自定义域名

1. **通过 Vercel Dashboard**：
   - 进入项目设置
   - 点击 **Domains** 标签
   - 点击 **Add Domain**
   - 输入域名（例如：`your-domain.com`）
   - 按照提示配置 DNS 记录

2. **通过 Vercel CLI**：
   ```bash
   # 添加域名
   vercel domains add your-domain.com
   
   # 列出所有域名
   vercel domains ls
   
   # 移除域名
   vercel domains rm your-domain.com
   ```

### 3.2 DNS 配置

根据您的域名提供商，配置以下 DNS 记录：

#### 顶级域名（example.com）
```
Type: A
Name: @
Value: ***********
```

#### 子域名（www.example.com）
```
Type: CNAME
Name: www
Value: cname.vercel-dns.com
```

#### 自定义子域名（app.example.com）
```
Type: CNAME
Name: app
Value: cname.vercel-dns.com
```

### 3.3 SSL 证书

Vercel 自动为所有域名提供 SSL 证书：
- 自动续期
- 支持通配符证书
- 无需手动配置

## 4. 环境隔离

### 4.1 开发环境

```bash
# 本地开发环境变量文件
# .env.local
DATABASE_URL="postgresql://localhost:5432/prompt_dev"
NEXT_PUBLIC_SUPABASE_URL="https://dev-project.supabase.co"
NEXTAUTH_URL="http://localhost:3000"
NODE_ENV="development"
```

### 4.2 预览环境

```bash
# 预览环境通常使用与生产环境相同的配置
# 但可能使用不同的数据库或服务实例
DATABASE_URL="postgresql://preview-db:5432/prompt_preview"
NEXT_PUBLIC_SUPABASE_URL="https://preview-project.supabase.co"
NEXTAUTH_URL="https://preview-your-domain.vercel.app"
NODE_ENV="production"
```

### 4.3 生产环境

```bash
# 生产环境使用最稳定的配置
DATABASE_URL="postgresql://prod-db:5432/prompt_prod"
NEXT_PUBLIC_SUPABASE_URL="https://prod-project.supabase.co"
NEXTAUTH_URL="https://your-domain.com"
NODE_ENV="production"
```

## 5. 安全最佳实践

### 5.1 敏感信息保护

- 🔒 不要在代码中硬编码敏感信息
- 🔒 使用强密码和随机生成的密钥
- 🔒 定期轮换 API 密钥和令牌
- 🔒 限制环境变量的访问权限

### 5.2 环境变量验证

使用 Zod 验证环境变量：

```typescript
// src/env.js
import { z } from "zod";

const envSchema = z.object({
  DATABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
  NEXTAUTH_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(1),
  NODE_ENV: z.enum(["development", "test", "production"]),
});

export const env = envSchema.parse(process.env);
```

### 5.3 访问控制

```bash
# 设置团队成员权限
vercel <NAME_EMAIL> --scope=project

# 设置环境变量访问权限
vercel env add SECRET_KEY production --scope=team
```

## 6. 监控和日志

### 6.1 环境变量监控

```bash
# 检查环境变量状态
vercel env ls

# 验证环境变量值
vercel env get DATABASE_URL production
```

### 6.2 配置变更日志

- 记录所有环境变量变更
- 使用版本控制跟踪配置文件
- 设置配置变更通知

## 7. 故障排除

### 7.1 常见问题

**环境变量未生效**
```bash
# 检查环境变量设置
vercel env ls

# 重新部署应用
vercel --prod
```

**域名配置问题**
```bash
# 检查域名状态
vercel domains ls

# 验证 DNS 配置
nslookup your-domain.com
```

**SSL 证书问题**
- 等待 24-48 小时自动生成
- 检查 DNS 记录是否正确
- 联系 Vercel 支持

### 7.2 调试技巧

1. 使用 `console.log` 检查环境变量
2. 在健康检查端点返回环境信息
3. 查看 Vercel 构建日志
4. 使用 `vercel logs` 查看运行时日志

## 8. 最佳实践总结

### 8.1 配置管理

✅ 使用环境变量存储配置  
✅ 为不同环境设置不同值  
✅ 验证环境变量有效性  
✅ 文档化所有配置选项  

### 8.2 安全性

✅ 保护敏感信息  
✅ 使用强密码和密钥  
✅ 定期轮换凭证  
✅ 限制访问权限  

### 8.3 运维

✅ 监控配置变更  
✅ 备份关键配置  
✅ 自动化部署流程  
✅ 建立应急响应机制  

---

有关更多详细信息，请参阅：
- [Vercel 环境变量文档](https://vercel.com/docs/concepts/projects/environment-variables)
- [Vercel 域名文档](https://vercel.com/docs/concepts/projects/domains)
- [Next.js 环境变量文档](https://nextjs.org/docs/basic-features/environment-variables)