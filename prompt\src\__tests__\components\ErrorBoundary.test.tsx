import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ErrorBoundary } from '~/components/error/ErrorBoundary'

// 创建一个会抛出错误的测试组件
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('测试错误')
  }
  return <div>正常组件</div>
}

// 创建一个会异步抛出错误的组件
const ThrowAsyncError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    setTimeout(() => {
      throw new Error('异步错误')
    }, 0)
  }
  return <div>异步组件</div>
}

// Mock console.error to prevent error logs in tests
const originalConsoleError = console.error
beforeEach(() => {
  console.error = vi.fn()
})

afterEach(() => {
  console.error = originalConsoleError
})

describe('ErrorBoundary', () => {
  it('应该渲染正常的子组件', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('正常组件')).toBeInTheDocument()
  })

  it('应该捕获并显示错误界面', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
    expect(screen.getByText('很抱歉，应用程序遇到了错误')).toBeInTheDocument()
    expect(screen.getByText('重新加载')).toBeInTheDocument()
  })

  it('应该显示错误详情', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('查看详情')).toBeInTheDocument()
    
    // 点击查看详情
    fireEvent.click(screen.getByText('查看详情'))
    
    expect(screen.getByText('错误详情')).toBeInTheDocument()
    expect(screen.getByText('测试错误')).toBeInTheDocument()
  })

  it('应该能够重新加载页面', () => {
    // Mock window.location.reload
    const reloadMock = vi.fn()
    Object.defineProperty(window, 'location', {
      value: { reload: reloadMock },
      writable: true,
    })
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    fireEvent.click(screen.getByText('重新加载'))
    
    expect(reloadMock).toHaveBeenCalled()
  })

  it('应该支持重置错误状态', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
    
    // 点击重置按钮
    fireEvent.click(screen.getByText('重试'))
    
    // 重新渲染正常组件
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('正常组件')).toBeInTheDocument()
  })

  it('应该支持自定义错误消息', () => {
    render(
      <ErrorBoundary
        fallback={({ error, resetError }) => (
          <div>
            <h2>自定义错误界面</h2>
            <p>错误信息: {error.message}</p>
            <button onClick={resetError}>重置</button>
          </div>
        )}
      >
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('自定义错误界面')).toBeInTheDocument()
    expect(screen.getByText('错误信息: 测试错误')).toBeInTheDocument()
    expect(screen.getByText('重置')).toBeInTheDocument()
  })

  it('应该支持错误回调', () => {
    const onError = vi.fn()
    
    render(
      <ErrorBoundary onError={onError}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        message: '测试错误',
      }),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    )
  })

  it('应该记录错误到控制台', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(consoleSpy).toHaveBeenCalledWith(
      'ErrorBoundary caught an error:',
      expect.objectContaining({
        message: '测试错误',
      }),
      expect.any(Object)
    )
    
    consoleSpy.mockRestore()
  })

  it('应该处理组件更新时的错误', () => {
    const { rerender } = render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('正常组件')).toBeInTheDocument()
    
    // 重新渲染会抛出错误的组件
    rerender(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
  })

  it('应该处理嵌套的错误边界', () => {
    render(
      <ErrorBoundary>
        <div>外层内容</div>
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      </ErrorBoundary>
    )
    
    // 内层错误边界应该捕获错误
    expect(screen.getByText('外层内容')).toBeInTheDocument()
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
  })

  it('应该在开发模式下显示更多错误信息', () => {
    // Mock development environment
    const originalNodeEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('查看详情')).toBeInTheDocument()
    
    // 恢复环境变量
    process.env.NODE_ENV = originalNodeEnv
  })

  it('应该处理网络错误', () => {
    const NetworkError = () => {
      const error = new Error('网络连接失败')
      error.name = 'NetworkError'
      throw error
    }
    
    render(
      <ErrorBoundary>
        <NetworkError />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
    expect(screen.getByText('网络连接失败，请检查网络后重试')).toBeInTheDocument()
  })

  it('应该处理块加载错误', () => {
    const ChunkError = () => {
      const error = new Error('Loading chunk 1 failed')
      error.name = 'ChunkLoadError'
      throw error
    }
    
    render(
      <ErrorBoundary>
        <ChunkError />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
    expect(screen.getByText('页面资源加载失败，请刷新页面重试')).toBeInTheDocument()
  })

  it('应该处理权限错误', () => {
    const PermissionError = () => {
      const error = new Error('权限不足')
      error.name = 'PermissionError'
      throw error
    }
    
    render(
      <ErrorBoundary>
        <PermissionError />
      </ErrorBoundary>
    )
    
    expect(screen.getByText('出现了错误')).toBeInTheDocument()
    expect(screen.getByText('权限不足，请联系管理员')).toBeInTheDocument()
  })

  describe('错误恢复', () => {
    it('应该在 props 变化时重置错误状态', () => {
      const { rerender } = render(
        <ErrorBoundary resetKeys={['key1']}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('出现了错误')).toBeInTheDocument()
      
      // 改变 resetKeys 应该重置错误状态
      rerender(
        <ErrorBoundary resetKeys={['key2']}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('正常组件')).toBeInTheDocument()
    })

    it('应该支持自定义重置条件', () => {
      const resetOnPropsChange = vi.fn((prevProps, nextProps) => {
        return prevProps.userId !== nextProps.userId
      })
      
      const { rerender } = render(
        <ErrorBoundary resetOnPropsChange={resetOnPropsChange}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('出现了错误')).toBeInTheDocument()
      
      // 改变 props 应该触发重置检查
      rerender(
        <ErrorBoundary resetOnPropsChange={resetOnPropsChange}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )
      
      expect(resetOnPropsChange).toHaveBeenCalled()
    })
  })

  describe('边界情况', () => {
    it('应该处理没有错误消息的错误', () => {
      const EmptyError = () => {
        const error = new Error()
        throw error
      }
      
      render(
        <ErrorBoundary>
          <EmptyError />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('出现了错误')).toBeInTheDocument()
      expect(screen.getByText('未知错误')).toBeInTheDocument()
    })

    it('应该处理 null 错误', () => {
      const NullError = () => {
        throw null
      }
      
      render(
        <ErrorBoundary>
          <NullError />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('出现了错误')).toBeInTheDocument()
    })

    it('应该处理字符串错误', () => {
      const StringError = () => {
        throw '字符串错误'
      }
      
      render(
        <ErrorBoundary>
          <StringError />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('出现了错误')).toBeInTheDocument()
    })

    it('应该处理循环引用的错误对象', () => {
      const CircularError = () => {
        const error = new Error('循环引用错误')
        error.circular = error
        throw error
      }
      
      render(
        <ErrorBoundary>
          <CircularError />
        </ErrorBoundary>
      )
      
      expect(screen.getByText('出现了错误')).toBeInTheDocument()
    })
  })

  describe('可访问性', () => {
    it('应该具有适当的 ARIA 属性', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )
      
      const errorContainer = screen.getByRole('alert')
      expect(errorContainer).toBeInTheDocument()
      expect(errorContainer).toHaveAttribute('aria-live', 'assertive')
    })

    it('应该支持键盘导航', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )
      
      const retryButton = screen.getByText('重试')
      const reloadButton = screen.getByText('重新加载')
      
      expect(retryButton).toBeInstanceOf(HTMLButtonElement)
      expect(reloadButton).toBeInstanceOf(HTMLButtonElement)
    })
  })
})