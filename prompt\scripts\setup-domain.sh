#!/bin/bash

# 域名设置脚本
# 使用方法: ./scripts/setup-domain.sh [domain]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Vercel CLI 是否已安装
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI 未安装"
    print_info "请运行: npm install -g vercel"
    exit 1
fi

# 检查是否已登录 Vercel
if ! vercel whoami &> /dev/null; then
    print_error "未登录 Vercel"
    print_info "请运行: vercel login"
    exit 1
fi

# 检查项目是否已链接
if [ ! -f ".vercel/project.json" ]; then
    print_warning "项目未链接到 Vercel"
    print_info "开始链接项目..."
    vercel link
fi

# 获取域名参数
DOMAIN=$1
if [[ -z "$DOMAIN" ]]; then
    print_info "请输入要添加的域名:"
    read -p "域名 (例如: example.com): " DOMAIN
fi

# 验证域名格式
if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$ ]]; then
    print_error "无效的域名格式: $DOMAIN"
    print_info "示例: example.com, app.example.com"
    exit 1
fi

print_info "开始设置域名: $DOMAIN"

# 检查域名是否已存在
if vercel domains ls | grep -q "$DOMAIN"; then
    print_warning "域名 $DOMAIN 已存在"
    read -p "是否继续配置？ (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "取消域名设置"
        exit 0
    fi
else
    # 添加域名
    print_info "添加域名到 Vercel..."
    if vercel domains add "$DOMAIN"; then
        print_success "域名 $DOMAIN 添加成功"
    else
        print_error "域名 $DOMAIN 添加失败"
        exit 1
    fi
fi

# 检查域名状态
print_info "检查域名状态..."
DOMAIN_STATUS=$(vercel domains ls | grep "$DOMAIN" | awk '{print $2}')

if [[ "$DOMAIN_STATUS" == "Valid" ]]; then
    print_success "域名 $DOMAIN 已配置且有效"
else
    print_warning "域名 $DOMAIN 状态: $DOMAIN_STATUS"
    print_info "需要配置 DNS 记录"
fi

# 显示 DNS 配置信息
print_info "========== DNS 配置信息 =========="

# 检查是否是顶级域名
if [[ "$DOMAIN" == *.* ]] && [[ "$DOMAIN" != www.* ]] && [[ ! "$DOMAIN" =~ \.[^.]+\.[^.]+$ ]]; then
    # 顶级域名
    print_info "顶级域名 DNS 配置:"
    echo "Type: A"
    echo "Name: @"
    echo "Value: 76.76.19.61"
    echo "TTL: 300"
    echo ""
    
    # 询问是否添加 www 子域名
    read -p "是否同时添加 www.$DOMAIN？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if vercel domains add "www.$DOMAIN"; then
            print_success "www.$DOMAIN 添加成功"
            print_info "www 子域名 DNS 配置:"
            echo "Type: CNAME"
            echo "Name: www"
            echo "Value: cname.vercel-dns.com"
            echo "TTL: 300"
        else
            print_error "www.$DOMAIN 添加失败"
        fi
    fi
else
    # 子域名
    print_info "子域名 DNS 配置:"
    echo "Type: CNAME"
    echo "Name: $(echo "$DOMAIN" | cut -d'.' -f1)"
    echo "Value: cname.vercel-dns.com"
    echo "TTL: 300"
fi

# 显示常见 DNS 提供商配置指南
print_info "========== 常见 DNS 提供商配置指南 =========="
print_info "Cloudflare:"
print_info "  1. 登录 Cloudflare Dashboard"
print_info "  2. 选择您的域名"
print_info "  3. 点击 DNS 标签"
print_info "  4. 添加上述 DNS 记录"
echo ""
print_info "阿里云 DNS:"
print_info "  1. 登录阿里云控制台"
print_info "  2. 进入云解析 DNS"
print_info "  3. 选择您的域名"
print_info "  4. 添加解析记录"
echo ""
print_info "腾讯云 DNS:"
print_info "  1. 登录腾讯云控制台"
print_info "  2. 进入 DNS 解析"
print_info "  3. 选择您的域名"
print_info "  4. 添加记录"

# 检查 DNS 配置
print_info "========== DNS 检查 =========="
check_dns() {
    local domain=$1
    local record_type=$2
    
    print_info "检查 $domain 的 $record_type 记录..."
    
    case $record_type in
        "A")
            if nslookup "$domain" | grep -q "76.76.19.61"; then
                print_success "$domain A 记录配置正确"
                return 0
            else
                print_warning "$domain A 记录未配置或未生效"
                return 1
            fi
            ;;
        "CNAME")
            if nslookup "$domain" | grep -q "cname.vercel-dns.com"; then
                print_success "$domain CNAME 记录配置正确"
                return 0
            else
                print_warning "$domain CNAME 记录未配置或未生效"
                return 1
            fi
            ;;
    esac
}

# 等待用户配置 DNS
echo ""
read -p "DNS 记录配置完成后，按 Enter 继续检查..." -r

# 检查 DNS 配置
if [[ "$DOMAIN" == *.* ]] && [[ "$DOMAIN" != www.* ]] && [[ ! "$DOMAIN" =~ \.[^.]+\.[^.]+$ ]]; then
    check_dns "$DOMAIN" "A"
else
    check_dns "$DOMAIN" "CNAME"
fi

# 等待 DNS 传播
print_info "等待 DNS 传播..."
print_info "DNS 传播可能需要几分钟到几小时时间"

# 循环检查域名状态
MAX_ATTEMPTS=30
ATTEMPT=0

while [[ $ATTEMPT -lt $MAX_ATTEMPTS ]]; do
    CURRENT_STATUS=$(vercel domains ls | grep "$DOMAIN" | awk '{print $2}')
    
    if [[ "$CURRENT_STATUS" == "Valid" ]]; then
        print_success "域名 $DOMAIN 验证成功！"
        break
    else
        print_info "当前状态: $CURRENT_STATUS (尝试 $((ATTEMPT + 1))/$MAX_ATTEMPTS)"
        sleep 30
        ((ATTEMPT++))
    fi
done

if [[ $ATTEMPT -eq $MAX_ATTEMPTS ]]; then
    print_warning "域名验证超时，请手动检查 DNS 配置"
fi

# 更新 NEXTAUTH_URL 环境变量
print_info "========== 更新环境变量 =========="
read -p "是否更新 NEXTAUTH_URL 环境变量？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "更新 NEXTAUTH_URL 为 https://$DOMAIN"
    
    # 更新生产环境
    if echo "https://$DOMAIN" | vercel env add NEXTAUTH_URL production; then
        print_success "生产环境 NEXTAUTH_URL 更新成功"
    else
        print_error "生产环境 NEXTAUTH_URL 更新失败"
    fi
    
    # 询问是否更新其他环境变量
    read -p "是否更新其他应用 URL 环境变量？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if echo "https://$DOMAIN" | vercel env add NEXT_PUBLIC_APP_URL production; then
            print_success "NEXT_PUBLIC_APP_URL 更新成功"
        fi
    fi
fi

# 触发重新部署
print_info "========== 重新部署 =========="
read -p "是否重新部署应用以应用新的域名配置？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "开始重新部署..."
    if vercel --prod; then
        print_success "部署成功！"
        print_info "应用现在可以通过 https://$DOMAIN 访问"
    else
        print_error "部署失败"
    fi
fi

# 显示 SSL 证书信息
print_info "========== SSL 证书 =========="
print_info "Vercel 自动为您的域名提供 SSL 证书"
print_info "证书通常在域名验证后几分钟内生效"
print_info "您可以访问 https://$DOMAIN 检查证书状态"

# 执行健康检查
print_info "========== 健康检查 =========="
if command -v curl &> /dev/null; then
    print_info "执行健康检查..."
    sleep 10
    
    if curl -f -s "https://$DOMAIN/api/health" > /dev/null; then
        print_success "应用健康检查通过"
    else
        print_warning "应用健康检查失败，请检查部署状态"
    fi
else
    print_info "curl 未安装，跳过健康检查"
fi

print_success "域名设置完成！"

# 显示摘要信息
print_info "========== 设置摘要 =========="
print_info "域名: $DOMAIN"
print_info "状态: $(vercel domains ls | grep "$DOMAIN" | awk '{print $2}')"
print_info "访问地址: https://$DOMAIN"
print_info "部署时间: $(date)"

# 显示有用的命令
print_info "========== 有用的命令 =========="
print_info "  查看所有域名: vercel domains ls"
print_info "  删除域名: vercel domains rm $DOMAIN"
print_info "  检查部署状态: vercel ls"
print_info "  查看日志: vercel logs"
print_info "  查看项目设置: vercel env ls"

print_info "========== 后续步骤 =========="
print_info "1. 等待 DNS 完全传播（可能需要几小时）"
print_info "2. 访问 https://$DOMAIN 测试应用"
print_info "3. 检查 SSL 证书状态"
print_info "4. 更新应用中的任何硬编码 URL"
print_info "5. 设置监控和报警"