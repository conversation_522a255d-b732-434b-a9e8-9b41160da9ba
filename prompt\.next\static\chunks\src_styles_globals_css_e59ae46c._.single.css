/* [project]/src/styles/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-500: #fb2c36;
    --color-yellow-200: #fff085;
    --color-yellow-800: #874b00;
    --color-yellow-900: #733e0a;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-yellow-200: color(display-p3 .988789 .943116 .579188);
      --color-yellow-800: color(display-p3 .503181 .30478 .075537);
      --color-yellow-900: color(display-p3 .422485 .252729 .095052);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-yellow-200: lab(94.3433% -5.00426 52.9663);
      --color-yellow-800: lab(38.7484% 23.5833 51.4916);
      --color-yellow-900: lab(32.3865% 21.1274 38.5958);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  @supports (color: color(display-p3 0 0 0)) {
    [data-theme="light"] {
      --color-base-100: color(display-p3 1 1 1);
      --color-base-200: color(display-p3 .973691 .973691 .973691);
      --color-base-300: color(display-p3 .93448 .93448 .93448);
      --color-base-content: color(display-p3 .0937957 .093793 .104806);
      --color-primary: color(display-p3 .244907 .16847 .802628);
      --color-primary-content: color(display-p3 .883035 .90499 .993138);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    [data-theme="light"] {
      --color-base-100: lab(100% 0 0);
      --color-base-200: lab(97.68% -.0000298023 .0000119209);
      --color-base-300: lab(94.2% 0 0);
      --color-base-content: lab(8.30603% .618212 -2.16573);
      --color-primary: lab(31.573% 49.867 -84.7065);
      --color-primary-content: lab(91.6577% 1.04591 -12.7199);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  :where(:root) {
    --lightningcss-light: initial;
    --lightningcss-dark: ;
    color-scheme: light;
    --color-base-100: #fff;
    --color-base-200: #f8f8f8;
    --color-base-300: #eee;
    --color-base-content: #18181b;
    --color-primary: #422ad5;
    --color-primary-content: #e0e7ff;
    --color-secondary: #f43098;
    --color-secondary-content: #f9e4f0;
    --color-accent: #00d1bb;
    --color-accent-content: #084d49;
    --color-neutral: #09090b;
    --color-neutral-content: #e4e4e7;
    --color-info: #00bafc;
    --color-info-content: #042e49;
    --color-success: #00d193;
    --color-success-content: #004c39;
    --color-warning: #f9b800;
    --color-warning-content: #793205;
    --color-error: #ff657f;
    --color-error-content: #4d0218;
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }

  @supports (color: color(display-p3 0 0 0)) {
    :where(:root) {
      --color-base-100: color(display-p3 1 1 1);
      --color-base-200: color(display-p3 .973691 .973691 .973691);
      --color-base-300: color(display-p3 .93448 .93448 .93448);
      --color-base-content: color(display-p3 .0937957 .093793 .104806);
      --color-primary: color(display-p3 .244907 .16847 .802628);
      --color-primary-content: color(display-p3 .883035 .90499 .993138);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :where(:root) {
      --color-base-100: lab(100% 0 0);
      --color-base-200: lab(97.68% -.0000298023 .0000119209);
      --color-base-300: lab(94.2% 0 0);
      --color-base-content: lab(8.30603% .618212 -2.16573);
      --color-primary: lab(31.573% 49.867 -84.7065);
      --color-primary-content: lab(91.6577% 1.04591 -12.7199);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  :root:has(input.theme-controller[value="light"]:checked) {
    --lightningcss-light: initial;
    --lightningcss-dark: ;
    color-scheme: light;
    --color-base-100: #fff;
    --color-base-200: #f8f8f8;
    --color-base-300: #eee;
    --color-base-content: #18181b;
    --color-primary: #422ad5;
    --color-primary-content: #e0e7ff;
    --color-secondary: #f43098;
    --color-secondary-content: #f9e4f0;
    --color-accent: #00d1bb;
    --color-accent-content: #084d49;
    --color-neutral: #09090b;
    --color-neutral-content: #e4e4e7;
    --color-info: #00bafc;
    --color-info-content: #042e49;
    --color-success: #00d193;
    --color-success-content: #004c39;
    --color-warning: #f9b800;
    --color-warning-content: #793205;
    --color-error: #ff657f;
    --color-error-content: #4d0218;
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root:has(input.theme-controller[value="light"]:checked) {
      --color-base-100: color(display-p3 1 1 1);
      --color-base-200: color(display-p3 .973691 .973691 .973691);
      --color-base-300: color(display-p3 .93448 .93448 .93448);
      --color-base-content: color(display-p3 .0937957 .093793 .104806);
      --color-primary: color(display-p3 .244907 .16847 .802628);
      --color-primary-content: color(display-p3 .883035 .90499 .993138);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root:has(input.theme-controller[value="light"]:checked) {
      --color-base-100: lab(100% 0 0);
      --color-base-200: lab(97.68% -.0000298023 .0000119209);
      --color-base-300: lab(94.2% 0 0);
      --color-base-content: lab(8.30603% .618212 -2.16573);
      --color-primary: lab(31.573% 49.867 -84.7065);
      --color-primary-content: lab(91.6577% 1.04591 -12.7199);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  @media (prefers-color-scheme: dark) {
    :root {
      --lightningcss-light: ;
      --lightningcss-dark: initial;
      color-scheme: dark;
      --color-base-100: #1d232a;
      --color-base-200: #191e24;
      --color-base-300: #15191e;
      --color-base-content: #f2f8ff;
      --color-primary: #605dff;
      --color-primary-content: #edf1fe;
      --color-secondary: #f43098;
      --color-secondary-content: #f9e4f0;
      --color-accent: #00d1bb;
      --color-accent-content: #084d49;
      --color-neutral: #09090b;
      --color-neutral-content: #e4e4e7;
      --color-info: #00bafc;
      --color-info-content: #042e49;
      --color-success: #00d193;
      --color-success-content: #004c39;
      --color-warning: #f9b800;
      --color-warning-content: #793205;
      --color-error: #ff657f;
      --color-error-content: #4d0218;
      --radius-selector: .5rem;
      --radius-field: .25rem;
      --radius-box: .5rem;
      --size-selector: .25rem;
      --size-field: .25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }

    @supports (color: color(display-p3 0 0 0)) {
      :root {
        --color-base-100: color(display-p3 .118154 .136578 .162345);
        --color-base-200: color(display-p3 .101571 .117056 .139288);
        --color-base-300: color(display-p3 .084995 .0975948 .116397);
        --color-base-content: color(display-p3 .933919 .975165 1.04359);
        --color-primary: color(display-p3 .375125 .365608 .964847);
        --color-primary-content: color(display-p3 .933611 .94601 .992985);
        --color-secondary: color(display-p3 .880667 .26782 .587861);
        --color-secondary-content: color(display-p3 .964052 .897855 .939789);
        --color-accent: color(display-p3 .312493 .813034 .735662);
        --color-accent-content: color(display-p3 .128725 .296875 .284306);
        --color-neutral: color(display-p3 .0346186 .034606 .0427327);
        --color-neutral-content: color(display-p3 .894477 .894477 .905114);
        --color-info: color(display-p3 .297526 .717371 .972161);
        --color-info-content: color(display-p3 .0695709 .177759 .278667);
        --color-success: color(display-p3 .32762 .813141 .585553);
        --color-success-content: color(display-p3 .108674 .294366 .226358);
        --color-warning: color(display-p3 .94863 .728271 .0946339);
        --color-warning-content: color(display-p3 .441203 .212026 .0773632);
        --color-error: color(display-p3 .940178 .428237 .500757);
        --color-error-content: color(display-p3 .274274 .0392692 .0981547);
      }
    }

    @supports (color: lab(0% 0 0)) {
      :root {
        --color-base-100: lab(13.3466% -1.27321 -5.67451);
        --color-base-200: lab(10.9483% -1.0722 -4.98787);
        --color-base-300: lab(8.50319% -.863373 -4.30144);
        --color-base-content: lab(97.3754% -1.86673 -10.6283);
        --color-primary: lab(47.6934% 38.5675 -81.9644);
        --color-primary-content: lab(95.2498% .411481 -6.78517);
        --color-secondary: lab(56.234% 76.7852 -8.06803);
        --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
        --color-accent: lab(75.1988% -53.3697 -2.27785);
        --color-accent-content: lab(28.81% -21.4781 -3.59725);
        --color-neutral: lab(2.45787% .239417 -.873864);
        --color-neutral-content: lab(90.6853% .399202 -1.45452);
        --color-info: lab(69.9876% -23.5256 -45.9352);
        --color-info-content: lab(17.4794% -5.25945 -21.1512);
        --color-success: lab(74.4967% -60.7579 19.4189);
        --color-success-content: lab(27.9355% -26.9592 5.46191);
        --color-warning: lab(79.2305% 16.6936 100.392);
        --color-warning-content: lab(30.7627% 30.2938 40.2828);
        --color-error: lab(64.1803% 63.0275 19.2122);
        --color-error-content: lab(14.1162% 34.0067 9.81536);
      }
    }
  }

  [data-theme="light"] {
    --lightningcss-light: initial;
    --lightningcss-dark: ;
    color-scheme: light;
    --color-base-100: #fff;
    --color-base-200: #f8f8f8;
    --color-base-300: #eee;
    --color-base-content: #18181b;
    --color-primary: #422ad5;
    --color-primary-content: #e0e7ff;
    --color-secondary: #f43098;
    --color-secondary-content: #f9e4f0;
    --color-accent: #00d1bb;
    --color-accent-content: #084d49;
    --color-neutral: #09090b;
    --color-neutral-content: #e4e4e7;
    --color-info: #00bafc;
    --color-info-content: #042e49;
    --color-success: #00d193;
    --color-success-content: #004c39;
    --color-warning: #f9b800;
    --color-warning-content: #793205;
    --color-error: #ff657f;
    --color-error-content: #4d0218;
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }

  @supports (color: color(display-p3 0 0 0)) {
    [data-theme="light"] {
      --color-base-100: color(display-p3 1 1 1);
      --color-base-200: color(display-p3 .973691 .973691 .973691);
      --color-base-300: color(display-p3 .93448 .93448 .93448);
      --color-base-content: color(display-p3 .0937957 .093793 .104806);
      --color-primary: color(display-p3 .244907 .16847 .802628);
      --color-primary-content: color(display-p3 .883035 .90499 .993138);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    [data-theme="light"] {
      --color-base-100: lab(100% 0 0);
      --color-base-200: lab(97.68% -.0000298023 .0000119209);
      --color-base-300: lab(94.2% 0 0);
      --color-base-content: lab(8.30603% .618212 -2.16573);
      --color-primary: lab(31.573% 49.867 -84.7065);
      --color-primary-content: lab(91.6577% 1.04591 -12.7199);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  :root:has(input.theme-controller[value="light"]:checked) {
    --lightningcss-light: initial;
    --lightningcss-dark: ;
    color-scheme: light;
    --color-base-100: #fff;
    --color-base-200: #f8f8f8;
    --color-base-300: #eee;
    --color-base-content: #18181b;
    --color-primary: #422ad5;
    --color-primary-content: #e0e7ff;
    --color-secondary: #f43098;
    --color-secondary-content: #f9e4f0;
    --color-accent: #00d1bb;
    --color-accent-content: #084d49;
    --color-neutral: #09090b;
    --color-neutral-content: #e4e4e7;
    --color-info: #00bafc;
    --color-info-content: #042e49;
    --color-success: #00d193;
    --color-success-content: #004c39;
    --color-warning: #f9b800;
    --color-warning-content: #793205;
    --color-error: #ff657f;
    --color-error-content: #4d0218;
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root:has(input.theme-controller[value="light"]:checked) {
      --color-base-100: color(display-p3 1 1 1);
      --color-base-200: color(display-p3 .973691 .973691 .973691);
      --color-base-300: color(display-p3 .93448 .93448 .93448);
      --color-base-content: color(display-p3 .0937957 .093793 .104806);
      --color-primary: color(display-p3 .244907 .16847 .802628);
      --color-primary-content: color(display-p3 .883035 .90499 .993138);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root:has(input.theme-controller[value="light"]:checked) {
      --color-base-100: lab(100% 0 0);
      --color-base-200: lab(97.68% -.0000298023 .0000119209);
      --color-base-300: lab(94.2% 0 0);
      --color-base-content: lab(8.30603% .618212 -2.16573);
      --color-primary: lab(31.573% 49.867 -84.7065);
      --color-primary-content: lab(91.6577% 1.04591 -12.7199);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  [data-theme="dark"] {
    --lightningcss-light: ;
    --lightningcss-dark: initial;
    color-scheme: dark;
    --color-base-100: #1d232a;
    --color-base-200: #191e24;
    --color-base-300: #15191e;
    --color-base-content: #f2f8ff;
    --color-primary: #605dff;
    --color-primary-content: #edf1fe;
    --color-secondary: #f43098;
    --color-secondary-content: #f9e4f0;
    --color-accent: #00d1bb;
    --color-accent-content: #084d49;
    --color-neutral: #09090b;
    --color-neutral-content: #e4e4e7;
    --color-info: #00bafc;
    --color-info-content: #042e49;
    --color-success: #00d193;
    --color-success-content: #004c39;
    --color-warning: #f9b800;
    --color-warning-content: #793205;
    --color-error: #ff657f;
    --color-error-content: #4d0218;
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }

  @supports (color: color(display-p3 0 0 0)) {
    [data-theme="dark"] {
      --color-base-100: color(display-p3 .118154 .136578 .162345);
      --color-base-200: color(display-p3 .101571 .117056 .139288);
      --color-base-300: color(display-p3 .084995 .0975948 .116397);
      --color-base-content: color(display-p3 .933919 .975165 1.04359);
      --color-primary: color(display-p3 .375125 .365608 .964847);
      --color-primary-content: color(display-p3 .933611 .94601 .992985);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    [data-theme="dark"] {
      --color-base-100: lab(13.3466% -1.27321 -5.67451);
      --color-base-200: lab(10.9483% -1.0722 -4.98787);
      --color-base-300: lab(8.50319% -.863373 -4.30144);
      --color-base-content: lab(97.3754% -1.86673 -10.6283);
      --color-primary: lab(47.6934% 38.5675 -81.9644);
      --color-primary-content: lab(95.2498% .411481 -6.78517);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  :root:has(input.theme-controller[value="dark"]:checked) {
    --lightningcss-light: ;
    --lightningcss-dark: initial;
    color-scheme: dark;
    --color-base-100: #1d232a;
    --color-base-200: #191e24;
    --color-base-300: #15191e;
    --color-base-content: #f2f8ff;
    --color-primary: #605dff;
    --color-primary-content: #edf1fe;
    --color-secondary: #f43098;
    --color-secondary-content: #f9e4f0;
    --color-accent: #00d1bb;
    --color-accent-content: #084d49;
    --color-neutral: #09090b;
    --color-neutral-content: #e4e4e7;
    --color-info: #00bafc;
    --color-info-content: #042e49;
    --color-success: #00d193;
    --color-success-content: #004c39;
    --color-warning: #f9b800;
    --color-warning-content: #793205;
    --color-error: #ff657f;
    --color-error-content: #4d0218;
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root:has(input.theme-controller[value="dark"]:checked) {
      --color-base-100: color(display-p3 .118154 .136578 .162345);
      --color-base-200: color(display-p3 .101571 .117056 .139288);
      --color-base-300: color(display-p3 .084995 .0975948 .116397);
      --color-base-content: color(display-p3 .933919 .975165 1.04359);
      --color-primary: color(display-p3 .375125 .365608 .964847);
      --color-primary-content: color(display-p3 .933611 .94601 .992985);
      --color-secondary: color(display-p3 .880667 .26782 .587861);
      --color-secondary-content: color(display-p3 .964052 .897855 .939789);
      --color-accent: color(display-p3 .312493 .813034 .735662);
      --color-accent-content: color(display-p3 .128725 .296875 .284306);
      --color-neutral: color(display-p3 .0346186 .034606 .0427327);
      --color-neutral-content: color(display-p3 .894477 .894477 .905114);
      --color-info: color(display-p3 .297526 .717371 .972161);
      --color-info-content: color(display-p3 .0695709 .177759 .278667);
      --color-success: color(display-p3 .32762 .813141 .585553);
      --color-success-content: color(display-p3 .108674 .294366 .226358);
      --color-warning: color(display-p3 .94863 .728271 .0946339);
      --color-warning-content: color(display-p3 .441203 .212026 .0773632);
      --color-error: color(display-p3 .940178 .428237 .500757);
      --color-error-content: color(display-p3 .274274 .0392692 .0981547);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root:has(input.theme-controller[value="dark"]:checked) {
      --color-base-100: lab(13.3466% -1.27321 -5.67451);
      --color-base-200: lab(10.9483% -1.0722 -4.98787);
      --color-base-300: lab(8.50319% -.863373 -4.30144);
      --color-base-content: lab(97.3754% -1.86673 -10.6283);
      --color-primary: lab(47.6934% 38.5675 -81.9644);
      --color-primary-content: lab(95.2498% .411481 -6.78517);
      --color-secondary: lab(56.234% 76.7852 -8.06803);
      --color-secondary-content: lab(92.6584% 9.01154 -3.15071);
      --color-accent: lab(75.1988% -53.3697 -2.27785);
      --color-accent-content: lab(28.81% -21.4781 -3.59725);
      --color-neutral: lab(2.45787% .239417 -.873864);
      --color-neutral-content: lab(90.6853% .399202 -1.45452);
      --color-info: lab(69.9876% -23.5256 -45.9352);
      --color-info-content: lab(17.4794% -5.25945 -21.1512);
      --color-success: lab(74.4967% -60.7579 19.4189);
      --color-success-content: lab(27.9355% -26.9592 5.46191);
      --color-warning: lab(79.2305% 16.6936 100.392);
      --color-warning-content: lab(30.7627% 30.2938 40.2828);
      --color-error: lab(64.1803% 63.0275 19.2122);
      --color-error-content: lab(14.1162% 34.0067 9.81536);
    }
  }

  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }

  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }

  :root:has(.modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked) {
    overflow: hidden;
  }

  @property --radialprogress {
    syntax: "<percentage>"; inherits: true; initial-value: 0%;
  }

  :where(:root:has(.modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked)) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: var(--color-base-100);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(:root:has(.modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked)) {
      --root-bg: color-mix(in srgb, var(--color-base-100), lab(0% 0 0) 40%);
    }
  }

  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(:-webkit-any(.modal-start, .modal-end)) {
    scrollbar-gutter: stable;
  }

  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(:-moz-any(.modal-start, .modal-end)) {
    scrollbar-gutter: stable;
  }

  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(:is(.modal-start, .modal-end)) {
    scrollbar-gutter: stable;
  }

  :root {
    scrollbar-color: currentColor rgba(0, 0, 0, 0);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root {
      scrollbar-color: color-mix(in oklch, currentColor 35%, rgba(0, 0, 0, 0)) rgba(0, 0, 0, 0);
    }
  }
}

@layer components;

@layer utilities {
  .diff {
    webkit-user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    direction: ltr;
    grid-template-columns: auto 1fr;
    width: 100%;
    display: grid;
    position: relative;
    overflow: hidden;
    container-type: inline-size;
  }

  .diff:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-offset: 1px;
    outline-width: 2px;
    outline-color: var(--color-base-content);
  }

  .diff:has(.diff-item-1:focus-visible) {
    outline-style: var(--tw-outline-style);
    outline-offset: 1px;
    outline-width: 2px;
    outline-color: var(--color-base-content);
  }

  .diff:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-offset: 1px;
    outline-width: 2px;
    outline-color: var(--color-base-content);
  }

  .diff:focus-visible .diff-resizer {
    min-width: 90cqi;
    max-width: 90cqi;
  }

  .diff:has(.diff-item-2:focus-visible) {
    outline-style: var(--tw-outline-style);
    outline-offset: 1px;
    outline-width: 2px;
  }

  .diff:has(.diff-item-2:focus-visible) .diff-resizer {
    min-width: 10cqi;
    max-width: 10cqi;
  }

  @supports (-webkit-overflow-scrolling: touch) and (overflow: -webkit-paged-x) {
    .diff:focus .diff-resizer {
      min-width: 10cqi;
      max-width: 10cqi;
    }

    .diff:has(.diff-item-1:focus) .diff-resizer {
      min-width: 90cqi;
      max-width: 90cqi;
    }
  }

  .modal {
    pointer-events: none;
    visibility: hidden;
    width: 100%;
    max-width: none;
    height: 100%;
    max-height: none;
    color: inherit;
    transition: translate .3s ease-out, visibility .3s allow-discrete, background-color .3s ease-out, opacity .1s ease-out;
    overscroll-behavior: contain;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0);
    place-items: center;
    margin: 0;
    padding: 0;
    display: grid;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
  }

  .modal::backdrop {
    display: none;
  }

  .modal.modal-open {
    pointer-events: auto;
    visibility: visible;
    opacity: 1;
    background-color: rgba(0, 0, 0, .4);
    background-color: color(display-p3 0 0 0 / .4);
    background-color: lab(0% 0 0 / .4);
  }

  .modal.modal-open .modal-box {
    opacity: 1;
    translate: 0;
    scale: 1;
  }

  .modal[open] {
    pointer-events: auto;
    visibility: visible;
    opacity: 1;
    background-color: rgba(0, 0, 0, .4);
    background-color: color(display-p3 0 0 0 / .4);
    background-color: lab(0% 0 0 / .4);
  }

  .modal[open] .modal-box {
    opacity: 1;
    translate: 0;
    scale: 1;
  }

  .modal:target {
    pointer-events: auto;
    visibility: visible;
    opacity: 1;
    background-color: rgba(0, 0, 0, .4);
    background-color: color(display-p3 0 0 0 / .4);
    background-color: lab(0% 0 0 / .4);
  }

  .modal:target .modal-box {
    opacity: 1;
    translate: 0;
    scale: 1;
  }

  @starting-style {
    .modal.modal-open {
      visibility: hidden;
      opacity: 0;
    }

    .modal[open] {
      visibility: hidden;
      opacity: 0;
    }

    .modal:target {
      visibility: hidden;
      opacity: 0;
    }
  }

  .tooltip {
    --tt-bg: var(--color-neutral);
    --tt-off: calc(100% + .5rem);
    --tt-tail: calc(100% + 1px + .25rem);
    display: inline-block;
    position: relative;
  }

  .tooltip > :where(.tooltip-content) {
    border-radius: var(--radius-field);
    text-align: center;
    white-space: normal;
    max-width: 20rem;
    color: var(--color-neutral-content);
    opacity: 0;
    background-color: var(--tt-bg);
    pointer-events: none;
    z-index: 2;
    --tw-content: attr(data-tip);
    content: var(--tw-content);
    width: max-content;
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    padding-right: .5rem;
    font-size: .875rem;
    line-height: 1.25;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1) 75ms, transform .2s cubic-bezier(.4, 0, .2, 1) 75ms;
    position: absolute;
  }

  .tooltip:where([data-tip]):before {
    border-radius: var(--radius-field);
    text-align: center;
    white-space: normal;
    max-width: 20rem;
    color: var(--color-neutral-content);
    opacity: 0;
    background-color: var(--tt-bg);
    pointer-events: none;
    z-index: 2;
    --tw-content: attr(data-tip);
    content: var(--tw-content);
    width: max-content;
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    padding-right: .5rem;
    font-size: .875rem;
    line-height: 1.25;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1) 75ms, transform .2s cubic-bezier(.4, 0, .2, 1) 75ms;
    position: absolute;
  }

  .tooltip:after {
    opacity: 0;
    background-color: var(--tt-bg);
    content: "";
    pointer-events: none;
    --mask-tooltip: url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");
    width: .625rem;
    height: .25rem;
    -webkit-mask-position: -1px 0;
    mask-position: -1px 0;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-image: var(--mask-tooltip);
    mask-image: var(--mask-tooltip);
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1) 75ms, transform .2s cubic-bezier(.4, 0, .2, 1) 75ms;
    display: block;
    position: absolute;
  }

  .tooltip.tooltip-open > .tooltip-content {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip.tooltip-open[data-tip]:before {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip.tooltip-open:after {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip[data-tip]:not([data-tip=""]):hover > .tooltip-content {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip[data-tip]:not([data-tip=""]):hover[data-tip]:before {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip[data-tip]:not([data-tip=""]):hover:after {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover > .tooltip-content {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover[data-tip]:before {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover:after {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip:has(:focus-visible) > .tooltip-content {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip:has(:focus-visible)[data-tip]:before {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip:has(:focus-visible):after {
    opacity: 1;
    --tt-pos: 0rem;
    transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
  }

  .tooltip > .tooltip-content {
    transform: translateX(-50%) translateY(var(--tt-pos, .25rem));
    inset: auto auto var(--tt-off) 50%;
  }

  .tooltip[data-tip]:before {
    transform: translateX(-50%) translateY(var(--tt-pos, .25rem));
    inset: auto auto var(--tt-off) 50%;
  }

  .tooltip:after {
    transform: translateX(-50%) translateY(var(--tt-pos, .25rem));
    inset: auto auto var(--tt-tail) 50%;
  }

  .tab {
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    text-align: center;
    webkit-user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    display: inline-flex;
    position: relative;
  }

  @media (hover: hover) {
    .tab:hover {
      color: var(--color-base-content);
    }
  }

  .tab {
    --tab-p: 1rem;
    --tab-bg: var(--color-base-100);
    --tab-border-color: var(--color-base-300);
    --tab-radius-ss: 0;
    --tab-radius-se: 0;
    --tab-radius-es: 0;
    --tab-radius-ee: 0;
    --tab-order: 0;
    --tab-radius-min: calc(.75rem - var(--border));
    order: var(--tab-order);
    height: var(--tab-height);
    border-color: rgba(0, 0, 0, 0);
    font-size: .875rem;
  }

  .tab:-webkit-any(input[type="radio"]) {
    min-width: -moz-fit-content;
    min-width: fit-content;
  }

  .tab:-moz-any(input[type="radio"]) {
    min-width: -moz-fit-content;
    min-width: fit-content;
  }

  .tab:is(input[type="radio"]) {
    min-width: -moz-fit-content;
    min-width: fit-content;
  }

  .tab:-webkit-any(input[type="radio"]):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: var(--tab-p);
    padding-right: var(--tab-p);
  }

  .tab:-moz-any(input[type="radio"]):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: var(--tab-p);
    padding-right: var(--tab-p);
  }

  .tab:is(input[type="radio"]):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: var(--tab-p);
    padding-right: var(--tab-p);
  }

  .tab:-webkit-any(input[type="radio"]):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: var(--tab-p);
    padding-left: var(--tab-p);
  }

  .tab:-moz-any(input[type="radio"]):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: var(--tab-p);
    padding-left: var(--tab-p);
  }

  .tab:is(input[type="radio"]):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: var(--tab-p);
    padding-left: var(--tab-p);
  }

  .tab:-webkit-any(input[type="radio"]):after {
    content: attr(aria-label);
  }

  .tab:-moz-any(input[type="radio"]):after {
    content: attr(aria-label);
  }

  .tab:is(input[type="radio"]):after {
    content: attr(aria-label);
  }

  .tab:-webkit-any(label) {
    position: relative;
  }

  .tab:-webkit-any(label) input {
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    opacity: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .tab:-moz-any(label) {
    position: relative;
  }

  .tab:-moz-any(label) input {
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    opacity: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .tab:is(label) {
    position: relative;
  }

  .tab:is(label) input {
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    opacity: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .tab:checked + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:-webkit-any(label:has(:checked)) + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:-moz-any(label:has(:checked)) + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:is(label:has(:checked)) + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:-webkit-any(.tab-active, [aria-selected="true"]) + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:-moz-any(.tab-active, [aria-selected="true"]) + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:is(.tab-active, [aria-selected="true"]) + .tab-content {
    height: calc(100% - var(--tab-height)  + var(--border));
    display: block;
  }

  .tab:not(:-webkit-any(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"])) {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .tab:not(:-webkit-any(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"])) {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
  }

  .tab:not(:-moz-any(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"])) {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .tab:not(:-moz-any(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"])) {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
  }

  .tab:not(:is(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"])) {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .tab:not(:is(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"])) {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
  }

  .tab:not(input):empty {
    cursor: default;
    flex-grow: 1;
  }

  .tab:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .tab:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .tab:focus-visible {
    outline-offset: -5px;
    outline: 2px solid;
  }

  .tab:-webkit-any(label:has(:checked:focus-visible)) {
    outline-offset: -5px;
    outline: 2px solid;
  }

  .tab:-moz-any(label:has(:checked:focus-visible)) {
    outline-offset: -5px;
    outline: 2px solid;
  }

  .tab:is(label:has(:checked:focus-visible)) {
    outline-offset: -5px;
    outline: 2px solid;
  }

  .tab[disabled] {
    pointer-events: none;
    opacity: .4;
  }

  .menu {
    --menu-active-fg: var(--color-neutral-content);
    --menu-active-bg: var(--color-neutral);
    flex-flow: column wrap;
    width: -moz-fit-content;
    width: fit-content;
    padding: .5rem;
    font-size: .875rem;
    display: flex;
  }

  .menu :where(li ul) {
    white-space: nowrap;
    position: relative;
  }

  .menu :where(li ul):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: 1rem;
    padding-left: .5rem;
  }

  .menu :where(li ul):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: 1rem;
    padding-left: .5rem;
  }

  .menu :where(li ul):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: 1rem;
    padding-left: .5rem;
  }

  .menu :where(li ul):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: 1rem;
    padding-right: .5rem;
  }

  .menu :where(li ul):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: 1rem;
    padding-right: .5rem;
  }

  .menu :where(li ul):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: 1rem;
    padding-right: .5rem;
  }

  .menu :where(li ul):before {
    background-color: var(--color-base-content);
    opacity: .1;
    width: var(--border);
    content: "";
    position: absolute;
    top: .75rem;
    bottom: .75rem;
  }

  .menu :where(li ul):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: 0;
  }

  .menu :where(li ul):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: 0;
  }

  .menu :where(li ul):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    left: 0;
  }

  .menu :where(li ul):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 0;
  }

  .menu :where(li ul):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 0;
  }

  .menu :where(li ul):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    right: 0;
  }

  .menu :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
    display: none;
  }

  .menu :where(li:not(.menu-title) > :not(:-webkit-any(ul, details, .menu-title, .btn))) {
    border-radius: var(--radius-field);
    text-align: start;
    text-wrap: balance;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    grid-auto-columns: minmax(auto, max-content) auto max-content;
    grid-auto-flow: column;
    align-content: flex-start;
    align-items: center;
    gap: .5rem;
    padding-top: .375rem;
    padding-bottom: .375rem;
    padding-left: .75rem;
    padding-right: .75rem;
    transition-property: color, background-color, box-shadow;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    display: grid;
  }

  .menu :where(li:not(.menu-title) > :not(:-moz-any(ul, details, .menu-title, .btn))) {
    border-radius: var(--radius-field);
    text-align: start;
    text-wrap: balance;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    grid-auto-columns: minmax(auto, max-content) auto max-content;
    grid-auto-flow: column;
    align-content: flex-start;
    align-items: center;
    gap: .5rem;
    padding-top: .375rem;
    padding-bottom: .375rem;
    padding-left: .75rem;
    padding-right: .75rem;
    transition-property: color, background-color, box-shadow;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    display: grid;
  }

  .menu :where(li:not(.menu-title) > :not(:is(ul, details, .menu-title, .btn))) {
    border-radius: var(--radius-field);
    text-align: start;
    text-wrap: balance;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    grid-auto-columns: minmax(auto, max-content) auto max-content;
    grid-auto-flow: column;
    align-content: flex-start;
    align-items: center;
    gap: .5rem;
    padding-top: .375rem;
    padding-bottom: .375rem;
    padding-left: .75rem;
    padding-right: .75rem;
    transition-property: color, background-color, box-shadow;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    display: grid;
  }

  .menu :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
    border-radius: var(--radius-field);
    text-align: start;
    text-wrap: balance;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    grid-auto-columns: minmax(auto, max-content) auto max-content;
    grid-auto-flow: column;
    align-content: flex-start;
    align-items: center;
    gap: .5rem;
    padding-top: .375rem;
    padding-bottom: .375rem;
    padding-left: .75rem;
    padding-right: .75rem;
    transition-property: color, background-color, box-shadow;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    display: grid;
  }

  .menu :where(li > details > summary) {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li > details > summary) {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li > details > summary)::-webkit-details-marker {
    display: none;
  }

  .menu :where(li > details > summary):after {
    content: "";
    transform-origin: 50%;
    pointer-events: none;
    justify-self: flex-end;
    width: .375rem;
    height: .375rem;
    transition-property: rotate, translate;
    transition-duration: .2s;
    display: block;
    translate: 0 -1px;
    rotate: -135deg;
    box-shadow: inset 2px 2px;
  }

  .menu :where(li > .menu-dropdown-toggle):after {
    content: "";
    transform-origin: 50%;
    pointer-events: none;
    justify-self: flex-end;
    width: .375rem;
    height: .375rem;
    transition-property: rotate, translate;
    transition-duration: .2s;
    display: block;
    translate: 0 -1px;
    rotate: -135deg;
    box-shadow: inset 2px 2px;
  }

  .menu :where(li > details[open] > summary):after {
    translate: 0 1px;
    rotate: 45deg;
  }

  .menu :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
    translate: 0 1px;
    rotate: 45deg;
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)).menu-focus {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)).menu-focus {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)).menu-focus {
    color: var(--color-base-content);
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)).menu-focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):focus-visible {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):focus-visible {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):focus-visible {
    color: var(--color-base-content);
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)), li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):focus-visible {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)).menu-focus {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)).menu-focus {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)).menu-focus {
    color: var(--color-base-content);
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)).menu-focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)):focus-visible {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)):focus-visible {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)):focus-visible {
    color: var(--color-base-content);
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)), li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:-moz-any(.menu-active, :active, .btn)):focus-visible {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)).menu-focus {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)).menu-focus {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)).menu-focus {
    color: var(--color-base-content);
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)).menu-focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)):focus-visible {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)):focus-visible {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)):focus-visible {
    color: var(--color-base-content);
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)), li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title)):not(:is(.menu-active, :active, .btn)):focus-visible {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):hover, li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-webkit-any(.menu-active, :active, .btn)):hover) {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):hover, li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-webkit-any(.menu-active, :active, .btn)):hover) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):hover, li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-webkit-any(.menu-active, :active, .btn)):hover) {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):hover, li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-webkit-any(.menu-active, :active, .btn)):hover) {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:-webkit-any(.menu-title, .disabled)) > :not(:-webkit-any(ul, details, .menu-title)):not(:-webkit-any(.menu-active, :active, .btn)):hover, li:not(:-webkit-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-webkit-any(.menu-active, :active, .btn)):hover) {
    box-shadow: inset 0 1px rgba(0, 0, 0, .01), inset 0 -1px rgba(255, 255, 255, .01);
    box-shadow: inset 0 1px color(display-p3 0 0 0 / .01), inset 0 -1px color(display-p3 1 1 1 / .01);
    box-shadow: inset 0 1px lab(0% 0 0 / .01), inset 0 -1px lab(100% 0 0 / .01);
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)):not(:-moz-any(.menu-active, :active, .btn)):hover, li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-moz-any(.menu-active, :active, .btn)):hover) {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)):not(:-moz-any(.menu-active, :active, .btn)):hover, li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-moz-any(.menu-active, :active, .btn)):hover) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)):not(:-moz-any(.menu-active, :active, .btn)):hover, li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-moz-any(.menu-active, :active, .btn)):hover) {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)):not(:-moz-any(.menu-active, :active, .btn)):hover, li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-moz-any(.menu-active, :active, .btn)):hover) {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:-moz-any(.menu-title, .disabled)) > :not(:-moz-any(ul, details, .menu-title)):not(:-moz-any(.menu-active, :active, .btn)):hover, li:not(:-moz-any(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:-moz-any(.menu-active, :active, .btn)):hover) {
    box-shadow: inset 0 1px rgba(0, 0, 0, .01), inset 0 -1px rgba(255, 255, 255, .01);
    box-shadow: inset 0 1px color(display-p3 0 0 0 / .01), inset 0 -1px color(display-p3 1 1 1 / .01);
    box-shadow: inset 0 1px lab(0% 0 0 / .01), inset 0 -1px lab(100% 0 0 / .01);
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)):not(:is(.menu-active, :active, .btn)):hover, li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:is(.menu-active, :active, .btn)):hover) {
    cursor: pointer;
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)):not(:is(.menu-active, :active, .btn)):hover, li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:is(.menu-active, :active, .btn)):hover) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)):not(:is(.menu-active, :active, .btn)):hover, li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:is(.menu-active, :active, .btn)):hover) {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)):not(:is(.menu-active, :active, .btn)):hover, li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:is(.menu-active, :active, .btn)):hover) {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li:not(:is(.menu-title, .disabled)) > :not(:is(ul, details, .menu-title)):not(:is(.menu-active, :active, .btn)):hover, li:not(:is(.menu-title, .disabled)) > details > summary:not(.menu-title):not(:is(.menu-active, :active, .btn)):hover) {
    box-shadow: inset 0 1px rgba(0, 0, 0, .01), inset 0 -1px rgba(255, 255, 255, .01);
    box-shadow: inset 0 1px color(display-p3 0 0 0 / .01), inset 0 -1px color(display-p3 1 1 1 / .01);
    box-shadow: inset 0 1px lab(0% 0 0 / .01), inset 0 -1px lab(100% 0 0 / .01);
  }

  .menu :where(li:empty) {
    background-color: var(--color-base-content);
    opacity: .1;
    height: 1px;
    margin: .5rem 1rem;
  }

  .menu :where(li) {
    flex-flow: column wrap;
    flex-shrink: 0;
    align-items: stretch;
    display: flex;
    position: relative;
  }

  .menu :where(li) .badge {
    justify-self: flex-end;
  }

  .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)):active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)):active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)):active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)):active:not(.menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)):active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)):active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)):active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)):active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)):active:not(.menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)):active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)):active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)):active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)):active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)):active:not(.menu :where(li) > :not(:is(ul, .menu-title, details, .btn)):active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)).menu-active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)).menu-active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)).menu-active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)).menu-active:not(.menu :where(li) > :not(:-webkit-any(ul, .menu-title, details, .btn)).menu-active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)).menu-active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)).menu-active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)).menu-active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)).menu-active:not(.menu :where(li) > :not(:-moz-any(ul, .menu-title, details, .btn)).menu-active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)).menu-active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)).menu-active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)).menu-active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > :not(:is(ul, .menu-title, details, .btn)).menu-active:not(.menu :where(li) > :not(:is(ul, .menu-title, details, .btn)).menu-active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li) > details > summary:active {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .menu :where(li) > details > summary:active {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .menu :where(li) > details > summary:active {
    color: var(--menu-active-fg);
    background-color: var(--menu-active-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  .menu :where(li) > details > summary:active:not(.menu :where(li) > details > summary:active:active) {
    box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
  }

  .menu :where(li).menu-disabled {
    pointer-events: none;
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .menu :where(li).menu-disabled {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .menu .dropdown:focus-within .menu-dropdown-toggle:after {
    translate: 0 1px;
    rotate: 45deg;
  }

  .menu .dropdown-content {
    margin-top: .5rem;
    padding: .5rem;
  }

  .menu .dropdown-content:before {
    display: none;
  }

  .dropdown {
    position-area: var(--anchor-v, bottom) var(--anchor-h, span-right);
    display: inline-block;
    position: relative;
  }

  .dropdown > :not(summary):focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .dropdown > :not(summary):focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .dropdown .dropdown-content {
    position: absolute;
  }

  .dropdown:not(:-webkit-any(details, .dropdown-open, .dropdown-hover:hover, :focus-within)) .dropdown-content {
    transform-origin: top;
    opacity: 0;
    display: none;
    scale: 95%;
  }

  .dropdown:not(:-moz-any(details, .dropdown-open, .dropdown-hover:hover, :focus-within)) .dropdown-content {
    transform-origin: top;
    opacity: 0;
    display: none;
    scale: 95%;
  }

  .dropdown:not(:is(details, .dropdown-open, .dropdown-hover:hover, :focus-within)) .dropdown-content {
    transform-origin: top;
    opacity: 0;
    display: none;
    scale: 95%;
  }

  .dropdown[popover] {
    z-index: 999;
    transition-behavior: allow-discrete;
    transition-property: opacity, scale, display;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    animation: .2s dropdown;
  }

  .dropdown .dropdown-content {
    z-index: 999;
    transition-behavior: allow-discrete;
    transition-property: opacity, scale, display;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    animation: .2s dropdown;
  }

  @starting-style {
    .dropdown[popover] {
      opacity: 0;
      scale: 95%;
    }

    .dropdown .dropdown-content {
      opacity: 0;
      scale: 95%;
    }
  }

  .dropdown.dropdown-open > [tabindex]:first-child {
    pointer-events: none;
  }

  .dropdown.dropdown-open .dropdown-content {
    opacity: 1;
  }

  .dropdown:not(.dropdown-hover):focus > [tabindex]:first-child {
    pointer-events: none;
  }

  .dropdown:not(.dropdown-hover):focus .dropdown-content {
    opacity: 1;
  }

  .dropdown:focus-within > [tabindex]:first-child {
    pointer-events: none;
  }

  .dropdown:focus-within .dropdown-content {
    opacity: 1;
  }

  .dropdown.dropdown-hover:hover .dropdown-content {
    opacity: 1;
    scale: 100%;
  }

  .dropdown:-webkit-any(details) summary::-webkit-details-marker {
    display: none;
  }

  .dropdown:-moz-any(details) summary::-webkit-details-marker {
    display: none;
  }

  .dropdown:is(details) summary::-webkit-details-marker {
    display: none;
  }

  .dropdown.dropdown-open .dropdown-content {
    scale: 100%;
  }

  .dropdown:focus .dropdown-content {
    scale: 100%;
  }

  .dropdown:focus-within .dropdown-content {
    scale: 100%;
  }

  .dropdown:where([popover]) {
    background: none;
  }

  .dropdown[popover] {
    color: inherit;
    position: fixed;
  }

  @supports not (position-area: bottom) {
    .dropdown[popover] {
      margin: auto;
    }

    .dropdown[popover].dropdown-open:not(:popover-open) {
      transform-origin: top;
      opacity: 0;
      display: none;
      scale: 95%;
    }

    .dropdown[popover]::backdrop {
      background-color: rgba(0, 0, 0, .3);
      background-color: color(display-p3 0 0 0 / .3);
      background-color: lab(0% 0 0 / .3);
    }
  }

  .dropdown[popover]:not(:-webkit-any(.dropdown-open, :popover-open)) {
    transform-origin: top;
    opacity: 0;
    display: none;
    scale: 95%;
  }

  .dropdown[popover]:not(:-moz-any(.dropdown-open, :popover-open)) {
    transform-origin: top;
    opacity: 0;
    display: none;
    scale: 95%;
  }

  .dropdown[popover]:not(:is(.dropdown-open, :popover-open)) {
    transform-origin: top;
    opacity: 0;
    display: none;
    scale: 95%;
  }

  :where(.btn) {
    width: unset;
  }

  .btn {
    cursor: pointer;
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, .875rem);
    outline-color: var(--btn-color, var(--color-base-content));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 .5px oklch(100% 0 0 / calc(var(--depth) * .15));
    touch-action: manipulation;
    box-shadow: 0 .5px 0 .5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, .25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: var(--btn-bg);
    flex-wrap: nowrap;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    gap: .375rem;
    font-weight: 600;
    transition-property: color, background-color, border-color, box-shadow;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(0, 0, .2, 1);
    display: inline-flex;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn {
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    }
  }

  .btn {
    --btn-shadow: 0 3px 2px -2px var(--btn-bg), 0 4px 3px -2px var(--btn-bg);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn {
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), rgba(0, 0, 0, 0)), 0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), rgba(0, 0, 0, 0));
    }
  }

  .btn {
    --btn-noise: var(--fx-noise);
  }

  .prose .btn {
    -webkit-text-decoration-line: none;
    text-decoration-line: none;
  }

  .prose .btn:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
  }

  .prose .btn:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
  }

  .prose .btn:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
  }

  .prose .btn:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
  }

  .prose .btn:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
  }

  .prose .btn:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
  }

  @media (hover: hover) {
    .btn:hover {
      --btn-bg: var(--btn-color, var(--color-base-200));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:hover {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
    }
  }

  .btn:focus-visible {
    isolation: isolate;
    outline-width: 2px;
    outline-style: solid;
  }

  .btn:active:not(.btn-active) {
    --btn-bg: var(--btn-color, var(--color-base-200));
    translate: 0 .5px;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:active:not(.btn-active) {
      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
    }
  }

  .btn:active:not(.btn-active) {
    --btn-border: var(--btn-color, var(--color-base-200));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:active:not(.btn-active) {
      --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
    }
  }

  .btn:active:not(.btn-active) {
    --btn-shadow: 0 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0 rgba(0, 0, 0, 0);
  }

  .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:is(:disabled, [disabled], .btn-disabled) {
    pointer-events: none;
    --btn-border: rgba(0, 0, 0, 0);
    --btn-noise: none;
    --btn-fg: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:is(:disabled, [disabled], .btn-disabled) {
      --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  @media (hover: hover) {
    .btn:is(:disabled, [disabled], .btn-disabled):hover {
      pointer-events: none;
      background-color: var(--color-neutral);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:is(:disabled, [disabled], .btn-disabled):hover {
        background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
      }
    }

    .btn:is(:disabled, [disabled], .btn-disabled):hover {
      --btn-border: rgba(0, 0, 0, 0);
      --btn-fg: var(--color-base-content);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:is(:disabled, [disabled], .btn-disabled):hover {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
      }
    }
  }

  .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:is(:disabled, [disabled], .btn-disabled) {
    pointer-events: none;
    --btn-border: rgba(0, 0, 0, 0);
    --btn-noise: none;
    --btn-fg: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:is(:disabled, [disabled], .btn-disabled) {
      --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  @media (hover: hover) {
    .btn:is(:disabled, [disabled], .btn-disabled):hover {
      pointer-events: none;
      background-color: var(--color-neutral);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:is(:disabled, [disabled], .btn-disabled):hover {
        background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
      }
    }

    .btn:is(:disabled, [disabled], .btn-disabled):hover {
      --btn-border: rgba(0, 0, 0, 0);
      --btn-fg: var(--color-base-content);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:is(:disabled, [disabled], .btn-disabled):hover {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
      }
    }
  }

  .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:-webkit-any(:disabled, [disabled], .btn-disabled):not(:-webkit-any(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:-moz-any(:disabled, [disabled], .btn-disabled):not(:-moz-any(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
      background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .btn:is(:disabled, [disabled], .btn-disabled):not(:is(.btn-link, .btn-ghost)) {
    box-shadow: none;
  }

  .btn:is(:disabled, [disabled], .btn-disabled) {
    pointer-events: none;
    --btn-border: rgba(0, 0, 0, 0);
    --btn-noise: none;
    --btn-fg: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn:is(:disabled, [disabled], .btn-disabled) {
      --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  @media (hover: hover) {
    .btn:is(:disabled, [disabled], .btn-disabled):hover {
      pointer-events: none;
      background-color: var(--color-neutral);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:is(:disabled, [disabled], .btn-disabled):hover {
        background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
      }
    }

    .btn:is(:disabled, [disabled], .btn-disabled):hover {
      --btn-border: rgba(0, 0, 0, 0);
      --btn-fg: var(--color-base-content);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .btn:is(:disabled, [disabled], .btn-disabled):hover {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
      }
    }
  }

  .btn:-webkit-any(input[type="checkbox"], input[type="radio"]) {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .btn:-webkit-any(input[type="checkbox"], input[type="radio"]):after {
    content: attr(aria-label);
  }

  .btn:-moz-any(input[type="checkbox"], input[type="radio"]) {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .btn:-moz-any(input[type="checkbox"], input[type="radio"]):after {
    content: attr(aria-label);
  }

  .btn:is(input[type="checkbox"], input[type="radio"]) {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .btn:is(input[type="checkbox"], input[type="radio"]):after {
    content: attr(aria-label);
  }

  .btn:where(input:checked:not(.filter .btn)) {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
    isolation: isolate;
  }

  .\!loading {
    pointer-events: none !important;
    aspect-ratio: 1 !important;
    vertical-align: middle !important;
    width: calc(var(--size-selector, .25rem) * 6) !important;
    background-color: currentColor !important;
    display: inline-block !important;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E") !important;
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E") !important;
    -webkit-mask-position: center !important;
    mask-position: center !important;
    -webkit-mask-size: 100% !important;
    mask-size: 100% !important;
    -webkit-mask-repeat: no-repeat !important;
    mask-repeat: no-repeat !important;
  }

  .loading {
    pointer-events: none;
    aspect-ratio: 1;
    vertical-align: middle;
    width: calc(var(--size-selector, .25rem) * 6);
    background-color: currentColor;
    display: inline-block;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    -webkit-mask-position: center;
    mask-position: center;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .visible {
    visibility: visible;
  }

  .list {
    flex-direction: column;
    font-size: .875rem;
    display: flex;
  }

  .list :where(.list-row) {
    --list-grid-cols: minmax(0, auto) 1fr;
    border-radius: var(--radius-box);
    word-break: break-word;
    grid-auto-flow: column;
    grid-template-columns: var(--list-grid-cols);
    gap: 1rem;
    padding: 1rem;
    display: grid;
    position: relative;
  }

  .list :where(.list-row):has(.list-col-grow:first-child) {
    --list-grid-cols: 1fr;
  }

  .list :where(.list-row):has(.list-col-grow:nth-child(2)) {
    --list-grid-cols: minmax(0, auto) 1fr;
  }

  .list :where(.list-row):has(.list-col-grow:nth-child(3)) {
    --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
  }

  .list :where(.list-row):has(.list-col-grow:nth-child(4)) {
    --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
  }

  .list :where(.list-row):has(.list-col-grow:nth-child(5)) {
    --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
  }

  .list :where(.list-row):has(.list-col-grow:nth-child(6)) {
    --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
  }

  .list :where(.list-row) :not(.list-col-wrap) {
    grid-row-start: 1;
  }

  .list > :not(:last-child).list-row:after {
    content: "";
    border-bottom: var(--border) solid;
    inset-inline: var(--radius-box);
    border-color: var(--color-base-content);
    position: absolute;
    bottom: 0;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .list > :not(:last-child).list-row:after {
      border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
    }
  }

  .list > :not(:last-child) .list-row:after {
    content: "";
    border-bottom: var(--border) solid;
    inset-inline: var(--radius-box);
    border-color: var(--color-base-content);
    position: absolute;
    bottom: 0;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .list > :not(:last-child) .list-row:after {
      border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
    }
  }

  .toast {
    translate: var(--toast-x, 0) var(--toast-y, 0);
    background-color: rgba(0, 0, 0, 0);
    flex-direction: column;
    gap: .5rem;
    width: max-content;
    max-width: calc(100vw - 2rem);
    display: flex;
    position: fixed;
    top: auto;
    bottom: 1rem;
  }

  .toast:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: auto;
    right: 1rem;
  }

  .toast:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: auto;
    right: 1rem;
  }

  .toast:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: auto;
    right: 1rem;
  }

  .toast:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
    right: auto;
  }

  .toast:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
    right: auto;
  }

  .toast:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
    right: auto;
  }

  .toast > * {
    animation: .25s ease-out toast;
  }

  .toast:where(.toast-start) {
    --toast-x: 0;
  }

  .toast:where(.toast-start):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: 1rem;
    right: auto;
  }

  .toast:where(.toast-start):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: 1rem;
    right: auto;
  }

  .toast:where(.toast-start):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: 1rem;
    right: auto;
  }

  .toast:where(.toast-start):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: auto;
    right: 1rem;
  }

  .toast:where(.toast-start):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: auto;
    right: 1rem;
  }

  .toast:where(.toast-start):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: auto;
    right: 1rem;
  }

  .toast:where(.toast-center) {
    --toast-x: -50%;
    left: 50%;
    right: 50%;
  }

  .toast:where(.toast-end) {
    --toast-x: 0;
  }

  .toast:where(.toast-end):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: auto;
    right: 1rem;
  }

  .toast:where(.toast-end):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: auto;
    right: 1rem;
  }

  .toast:where(.toast-end):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    left: auto;
    right: 1rem;
  }

  .toast:where(.toast-end):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
    right: auto;
  }

  .toast:where(.toast-end):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
    right: auto;
  }

  .toast:where(.toast-end):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
    right: auto;
  }

  .toast:where(.toast-bottom) {
    --toast-y: 0;
    top: auto;
    bottom: 1rem;
  }

  .toast:where(.toast-middle) {
    --toast-y: -50%;
    top: 50%;
    bottom: auto;
  }

  .toast:where(.toast-top) {
    --toast-y: 0;
    top: 1rem;
    bottom: auto;
  }

  .toggle {
    border: var(--border) solid currentColor;
    color: var(--input-color);
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    vertical-align: middle;
    webkit-user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    --radius-selector-max: calc(var(--radius-selector)  + var(--radius-selector)  + var(--radius-selector));
    border-radius: calc(var(--radius-selector)  + min(var(--toggle-p), var(--radius-selector-max))  + min(var(--border), var(--radius-selector-max)));
    padding: var(--toggle-p);
    flex-shrink: 0;
    grid-template-columns: 0fr 1fr 1fr;
    place-content: center;
    display: inline-grid;
    position: relative;
    box-shadow: inset 0 1px;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .toggle {
      box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), rgba(0, 0, 0, 0)) inset;
    }
  }

  .toggle {
    --input-color: var(--color-base-content);
    transition: color .3s, grid-template-columns .2s;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .toggle {
      --input-color: color-mix(in oklab, var(--color-base-content) 50%, rgba(0, 0, 0, 0));
    }
  }

  .toggle {
    --toggle-p: calc(var(--size) * .125);
    --size: calc(var(--size-selector, .25rem) * 6);
    width: calc((var(--size) * 2)  - (var(--border)  + var(--toggle-p)) * 2);
    height: var(--size);
  }

  .toggle > * {
    z-index: 1;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: rgba(0, 0, 0, 0);
    border: none;
    grid-column: 2 / span 1;
    grid-row-start: 1;
    height: 100%;
    padding: .125rem;
    transition: opacity .2s, rotate .4s;
  }

  .toggle > *:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .toggle > *:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .toggle > *:nth-child(2) {
    color: var(--color-base-100);
    rotate: none;
  }

  .toggle > *:nth-child(3) {
    color: var(--color-base-100);
    opacity: 0;
    rotate: -15deg;
  }

  .toggle:has(:checked) > :nth-child(2) {
    opacity: 0;
    rotate: 15deg;
  }

  .toggle:has(:checked) > :nth-child(3) {
    opacity: 1;
    rotate: none;
  }

  .toggle:before {
    aspect-ratio: 1;
    border-radius: var(--radius-selector);
    --tw-content: "";
    content: var(--tw-content);
    height: 100%;
    box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * .1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px currentColor;
    background-color: currentColor;
    grid-row-start: 1;
    grid-column-start: 2;
    position: relative;
    translate: 0;
  }

  .toggle:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    transition: background-color .1s, translate .2s, left .2s;
    left: 0;
  }

  .toggle:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    transition: background-color .1s, translate .2s, left .2s;
    left: 0;
  }

  .toggle:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    transition: background-color .1s, translate .2s, left .2s;
    left: 0;
  }

  .toggle:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    transition: background-color .1s, translate .2s, right .2s;
    right: 0;
  }

  .toggle:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    transition: background-color .1s, translate .2s, right .2s;
    right: 0;
  }

  .toggle:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    transition: background-color .1s, translate .2s, right .2s;
    right: 0;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .toggle:before {
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * .1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .toggle:before {
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
  }

  @media (forced-colors: active) {
    .toggle:before {
      outline-style: var(--tw-outline-style);
      outline-offset: calc(1px * -1);
      outline-width: 1px;
    }
  }

  @media print {
    .toggle:before {
      outline-offset: -1rem;
      outline: .25rem solid;
    }
  }

  .toggle:focus-visible {
    outline-offset: 2px;
    outline: 2px solid;
  }

  .toggle:has(:focus-visible) {
    outline-offset: 2px;
    outline: 2px solid;
  }

  .toggle:checked {
    background-color: var(--color-base-100);
    --input-color: var(--color-base-content);
    grid-template-columns: 1fr 1fr 0fr;
  }

  .toggle:checked:before {
    background-color: currentColor;
  }

  @starting-style {
    .toggle:checked:before {
      opacity: 0;
    }
  }

  .toggle[aria-checked="true"] {
    background-color: var(--color-base-100);
    --input-color: var(--color-base-content);
    grid-template-columns: 1fr 1fr 0fr;
  }

  .toggle[aria-checked="true"]:before {
    background-color: currentColor;
  }

  @starting-style {
    .toggle[aria-checked="true"]:before {
      opacity: 0;
    }
  }

  .toggle:has( > input:checked) {
    background-color: var(--color-base-100);
    --input-color: var(--color-base-content);
    grid-template-columns: 1fr 1fr 0fr;
  }

  .toggle:has( > input:checked):before {
    background-color: currentColor;
  }

  @starting-style {
    .toggle:has( > input:checked):before {
      opacity: 0;
    }
  }

  .toggle:indeterminate {
    grid-template-columns: .5fr 1fr .5fr;
  }

  .toggle:disabled {
    cursor: not-allowed;
    opacity: .3;
  }

  .toggle:disabled:before {
    border: var(--border) solid currentColor;
    background-color: rgba(0, 0, 0, 0);
  }

  .input {
    cursor: text;
    border: var(--border) solid rgba(0, 0, 0, 0);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--color-base-100);
    vertical-align: middle;
    white-space: nowrap;
    width: max(3rem, min(20rem, 100%));
    height: var(--size);
    touch-action: manipulation;
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    flex-shrink: 1;
    align-items: center;
    gap: .5rem;
    padding-left: .75rem;
    padding-right: .75rem;
    font-size: .875rem;
    display: inline-flex;
    position: relative;
  }

  .input:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
  }

  .input:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
  }

  .input:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
  }

  .input:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
  }

  .input:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
  }

  .input:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0)) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    }
  }

  .input {
    --size: calc(var(--size-field, .25rem) * 10);
    --input-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  .input:where(input) {
    display: inline-flex;
  }

  .input :where(input) {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: rgba(0, 0, 0, 0);
    border: none;
    width: 100%;
    height: 100%;
    display: inline-flex;
  }

  .input :where(input):focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .input :where(input):focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .input :where(input):focus-within {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .input :where(input):focus-within {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .input :where(input[type="url"]) {
    direction: ltr;
  }

  .input :where(input[type="email"]) {
    direction: ltr;
  }

  .input :where(input[type="date"]) {
    display: inline-block;
  }

  .input:focus {
    --input-color: var(--color-base-content);
    box-shadow: 0 1px var(--input-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:focus {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .input:focus {
    outline: 2px solid var(--input-color);
    outline-offset: 2px;
    isolation: isolate;
    z-index: 1;
  }

  .input:focus-within {
    --input-color: var(--color-base-content);
    box-shadow: 0 1px var(--input-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:focus-within {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .input:focus-within {
    outline: 2px solid var(--input-color);
    outline-offset: 2px;
    isolation: isolate;
    z-index: 1;
  }

  .input:has( > input[disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:has( > input[disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .input:has( > input[disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:has( > input[disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .input:has( > input[disabled]) {
    box-shadow: none;
  }

  .input:-webkit-any(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:-webkit-any(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .input:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .input:is(:disabled, [disabled]) {
    box-shadow: none;
  }

  .input:-moz-any(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:-moz-any(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .input:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .input:is(:disabled, [disabled]) {
    box-shadow: none;
  }

  .input:is(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:is(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .input:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .input:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .input:is(:disabled, [disabled]) {
    box-shadow: none;
  }

  .input:has( > input[disabled]) > input[disabled] {
    cursor: not-allowed;
  }

  .input::-webkit-date-and-time-value {
    text-align: inherit;
  }

  .input[type="number"]::-webkit-inner-spin-button {
    margin-top: -.75rem;
    margin-bottom: -.75rem;
  }

  .input[type="number"]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input[type="number"]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input[type="number"]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input[type="number"]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input[type="number"]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input[type="number"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input::-webkit-calendar-picker-indicator {
    position: absolute;
  }

  .input:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-calendar-picker-indicator {
    right: .75em;
  }

  .input:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-calendar-picker-indicator {
    right: .75em;
  }

  .input:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-calendar-picker-indicator {
    right: .75em;
  }

  .input:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-calendar-picker-indicator {
    left: .75em;
  }

  .input:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-calendar-picker-indicator {
    left: .75em;
  }

  .input:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-calendar-picker-indicator {
    left: .75em;
  }

  .indicator {
    width: max-content;
    display: inline-flex;
    position: relative;
  }

  .indicator :where(.indicator-item) {
    z-index: 1;
    white-space: nowrap;
    top: var(--indicator-t, 0);
    bottom: var(--indicator-b, auto);
    left: var(--indicator-s, auto);
    right: var(--indicator-e, 0);
    translate: var(--indicator-x, 50%) var(--indicator-y, -50%);
    position: absolute;
  }

  .table {
    border-radius: var(--radius-box);
    text-align: left;
    width: 100%;
    font-size: .875rem;
    position: relative;
  }

  .table:where(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    text-align: right;
  }

  .table:where(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    text-align: right;
  }

  .table:where(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)), [dir="rtl"], [dir="rtl"] *) {
    text-align: right;
  }

  @media (hover: hover) {
    .table tr.row-hover:hover {
      background-color: var(--color-base-200);
    }
  }

  @media (hover: hover) {
    .table tr.row-hover:nth-child(2n):hover {
      background-color: var(--color-base-200);
    }
  }

  .table :where(th, td) {
    vertical-align: middle;
    padding-top: .75rem;
    padding-bottom: .75rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .table :where(thead, tfoot) {
    white-space: nowrap;
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .table :where(thead, tfoot) {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
  }

  .table :where(thead, tfoot) {
    font-size: .875rem;
    font-weight: 600;
  }

  .table :where(tfoot) {
    border-top: var(--border) solid var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .table :where(tfoot) {
      border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, rgba(0, 0, 0, 0));
    }
  }

  .table :where(.table-pin-rows thead tr) {
    z-index: 1;
    background-color: var(--color-base-100);
    position: -webkit-sticky;
    position: sticky;
    top: 0;
  }

  .table :where(.table-pin-rows tfoot tr) {
    z-index: 1;
    background-color: var(--color-base-100);
    position: -webkit-sticky;
    position: sticky;
    bottom: 0;
  }

  .table :where(.table-pin-cols tr th) {
    background-color: var(--color-base-100);
    position: -webkit-sticky;
    position: sticky;
    left: 0;
    right: 0;
  }

  .table :where(thead tr, tbody tr:not(:last-child)) {
    border-bottom: var(--border) solid var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .table :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, rgba(0, 0, 0, 0));
    }
  }

  .steps {
    counter-reset: step;
    grid-auto-columns: 1fr;
    grid-auto-flow: column;
    display: inline-grid;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .steps .step {
    text-align: center;
    --step-bg: var(--color-base-300);
    --step-fg: var(--color-base-content);
    grid-template-rows: 40px 1fr;
    grid-template-columns: auto;
    place-items: center;
    min-width: 4rem;
    display: grid;
  }

  .steps .step:before {
    width: 100%;
    height: .5rem;
    color: var(--step-bg);
    background-color: var(--step-bg);
    --tw-content: "";
    content: var(--tw-content);
    border: 1px solid;
    grid-row-start: 1;
    grid-column-start: 1;
    top: 0;
  }

  .steps .step:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    margin-left: -100%;
  }

  .steps .step:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    margin-left: -100%;
  }

  .steps .step:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    margin-left: -100%;
  }

  .steps .step:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    margin-right: -100%;
  }

  .steps .step:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    margin-right: -100%;
  }

  .steps .step:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    margin-right: -100%;
  }

  .steps .step > .step-icon {
    content: counter(step);
    counter-increment: step;
    z-index: 1;
    color: var(--step-fg);
    background-color: var(--step-bg);
    border: 1px solid var(--step-bg);
    border-radius: 3.40282e38px;
    grid-row-start: 1;
    grid-column-start: 1;
    place-self: center;
    place-items: center;
    width: 2rem;
    height: 2rem;
    display: grid;
    position: relative;
  }

  .steps .step:not(:has(.step-icon)):after {
    content: counter(step);
    counter-increment: step;
    z-index: 1;
    color: var(--step-fg);
    background-color: var(--step-bg);
    border: 1px solid var(--step-bg);
    border-radius: 3.40282e38px;
    grid-row-start: 1;
    grid-column-start: 1;
    place-self: center;
    place-items: center;
    width: 2rem;
    height: 2rem;
    display: grid;
    position: relative;
  }

  .steps .step:first-child:before {
    content: none;
  }

  .steps .step[data-content]:after {
    content: attr(data-content);
  }

  .steps .step-neutral + .step-neutral:before {
    --step-bg: var(--color-neutral);
    --step-fg: var(--color-neutral-content);
  }

  .steps .step-neutral:after {
    --step-bg: var(--color-neutral);
    --step-fg: var(--color-neutral-content);
  }

  .steps .step-neutral > .step-icon {
    --step-bg: var(--color-neutral);
    --step-fg: var(--color-neutral-content);
  }

  .steps .step-primary + .step-primary:before {
    --step-bg: var(--color-primary);
    --step-fg: var(--color-primary-content);
  }

  .steps .step-primary:after {
    --step-bg: var(--color-primary);
    --step-fg: var(--color-primary-content);
  }

  .steps .step-primary > .step-icon {
    --step-bg: var(--color-primary);
    --step-fg: var(--color-primary-content);
  }

  .steps .step-secondary + .step-secondary:before {
    --step-bg: var(--color-secondary);
    --step-fg: var(--color-secondary-content);
  }

  .steps .step-secondary:after {
    --step-bg: var(--color-secondary);
    --step-fg: var(--color-secondary-content);
  }

  .steps .step-secondary > .step-icon {
    --step-bg: var(--color-secondary);
    --step-fg: var(--color-secondary-content);
  }

  .steps .step-accent + .step-accent:before {
    --step-bg: var(--color-accent);
    --step-fg: var(--color-accent-content);
  }

  .steps .step-accent:after {
    --step-bg: var(--color-accent);
    --step-fg: var(--color-accent-content);
  }

  .steps .step-accent > .step-icon {
    --step-bg: var(--color-accent);
    --step-fg: var(--color-accent-content);
  }

  .steps .step-info + .step-info:before {
    --step-bg: var(--color-info);
    --step-fg: var(--color-info-content);
  }

  .steps .step-info:after {
    --step-bg: var(--color-info);
    --step-fg: var(--color-info-content);
  }

  .steps .step-info > .step-icon {
    --step-bg: var(--color-info);
    --step-fg: var(--color-info-content);
  }

  .steps .step-success + .step-success:before {
    --step-bg: var(--color-success);
    --step-fg: var(--color-success-content);
  }

  .steps .step-success:after {
    --step-bg: var(--color-success);
    --step-fg: var(--color-success-content);
  }

  .steps .step-success > .step-icon {
    --step-bg: var(--color-success);
    --step-fg: var(--color-success-content);
  }

  .steps .step-warning + .step-warning:before {
    --step-bg: var(--color-warning);
    --step-fg: var(--color-warning-content);
  }

  .steps .step-warning:after {
    --step-bg: var(--color-warning);
    --step-fg: var(--color-warning-content);
  }

  .steps .step-warning > .step-icon {
    --step-bg: var(--color-warning);
    --step-fg: var(--color-warning-content);
  }

  .steps .step-error + .step-error:before {
    --step-bg: var(--color-error);
    --step-fg: var(--color-error-content);
  }

  .steps .step-error:after {
    --step-bg: var(--color-error);
    --step-fg: var(--color-error-content);
  }

  .steps .step-error > .step-icon {
    --step-bg: var(--color-error);
    --step-fg: var(--color-error-content);
  }

  .select {
    border: var(--border) solid rgba(0, 0, 0, 0);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--color-base-100);
    vertical-align: middle;
    width: max(3rem, min(20rem, 100%));
    height: var(--size);
    touch-action: manipulation;
    text-overflow: ellipsis;
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    background-image: linear-gradient(45deg, rgba(0, 0, 0, 0) 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, rgba(0, 0, 0, 0) 50%);
    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);
    background-repeat: no-repeat;
    background-size: 4px 4px, 4px 4px;
    flex-shrink: 1;
    align-items: center;
    gap: .375rem;
    font-size: .875rem;
    display: inline-flex;
    position: relative;
  }

  .select:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
    padding-left: 1rem;
    padding-right: 1.75rem;
  }

  .select:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
    padding-left: 1rem;
    padding-right: 1.75rem;
  }

  .select:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, var(--radius-field));
    border-top-right-radius: var(--join-se, var(--radius-field));
    border-bottom-right-radius: var(--join-ee, var(--radius-field));
    border-bottom-left-radius: var(--join-es, var(--radius-field));
    padding-left: 1rem;
    padding-right: 1.75rem;
  }

  .select:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
    padding-left: 1.75rem;
    padding-right: 1rem;
  }

  .select:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
    padding-left: 1.75rem;
    padding-right: 1rem;
  }

  .select:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, var(--radius-field));
    border-top-left-radius: var(--join-se, var(--radius-field));
    border-bottom-left-radius: var(--join-ee, var(--radius-field));
    border-bottom-right-radius: var(--join-es, var(--radius-field));
    padding-left: 1.75rem;
    padding-right: 1rem;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0)) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    }
  }

  .select {
    border-color: var(--input-color);
    --input-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  .select {
    --size: calc(var(--size-field, .25rem) * 10);
  }

  [dir="rtl"] .select {
    background-position: 12px calc(1px + 50%), 16px calc(1px + 50%);
  }

  .select select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: inherit;
    border-radius: inherit;
    border-style: none;
    width: calc(100% + 2.75rem);
    height: calc(100% - 2px);
  }

  .select select:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: -1rem;
    margin-right: -1.75rem;
    padding-left: 1rem;
    padding-right: 1.75rem;
  }

  .select select:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: -1rem;
    margin-right: -1.75rem;
    padding-left: 1rem;
    padding-right: 1.75rem;
  }

  .select select:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: -1rem;
    margin-right: -1.75rem;
    padding-left: 1rem;
    padding-right: 1.75rem;
  }

  .select select:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: -1.75rem;
    margin-right: -1rem;
    padding-left: 1.75rem;
    padding-right: 1rem;
  }

  .select select:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: -1.75rem;
    margin-right: -1rem;
    padding-left: 1.75rem;
    padding-right: 1rem;
  }

  .select select:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: -1.75rem;
    margin-right: -1rem;
    padding-left: 1.75rem;
    padding-right: 1rem;
  }

  .select select:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .select select:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .select select:focus-within {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .select select:focus-within {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .select select:not(:last-child) {
    background-image: none;
  }

  .select select:not(:last-child):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: -1.375rem;
  }

  .select select:not(:last-child):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: -1.375rem;
  }

  .select select:not(:last-child):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: -1.375rem;
  }

  .select select:not(:last-child):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: -1.375rem;
  }

  .select select:not(:last-child):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: -1.375rem;
  }

  .select select:not(:last-child):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: -1.375rem;
  }

  .select:focus {
    --input-color: var(--color-base-content);
    box-shadow: 0 1px var(--input-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:focus {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .select:focus {
    outline: 2px solid var(--input-color);
    outline-offset: 2px;
    isolation: isolate;
    z-index: 1;
  }

  .select:focus-within {
    --input-color: var(--color-base-content);
    box-shadow: 0 1px var(--input-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:focus-within {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .select:focus-within {
    outline: 2px solid var(--input-color);
    outline-offset: 2px;
    isolation: isolate;
    z-index: 1;
  }

  .select:has( > select[disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:has( > select[disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .select:has( > select[disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:has( > select[disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .select:-webkit-any(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:-webkit-any(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .select:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .select:-moz-any(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:-moz-any(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .select:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .select:is(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:is(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .select:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .select:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .select:has( > select[disabled]) > select[disabled] {
    cursor: not-allowed;
  }

  .card {
    border-radius: var(--radius-box);
    outline-offset: 2px;
    outline: 0 solid rgba(0, 0, 0, 0);
    flex-direction: column;
    transition: outline .2s ease-in-out;
    display: flex;
    position: relative;
  }

  .card:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .card:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .card:focus-visible {
    outline-color: currentColor;
  }

  .card :where(figure:first-child) {
    overflow: hidden;
  }

  .card :where(figure:first-child):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    border-bottom-right-radius: unset;
    border-bottom-left-radius: unset;
  }

  .card :where(figure:first-child):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    border-bottom-right-radius: unset;
    border-bottom-left-radius: unset;
  }

  .card :where(figure:first-child):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    border-bottom-right-radius: unset;
    border-bottom-left-radius: unset;
  }

  .card :where(figure:first-child):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: inherit;
    border-top-left-radius: inherit;
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
  }

  .card :where(figure:first-child):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: inherit;
    border-top-left-radius: inherit;
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
  }

  .card :where(figure:first-child):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: inherit;
    border-top-left-radius: inherit;
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
  }

  .card :where(figure:last-child) {
    overflow: hidden;
  }

  .card :where(figure:last-child):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
  }

  .card :where(figure:last-child):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
  }

  .card :where(figure:last-child):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
  }

  .card :where(figure:last-child):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: unset;
    border-top-left-radius: unset;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
  }

  .card :where(figure:last-child):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: unset;
    border-top-left-radius: unset;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
  }

  .card :where(figure:last-child):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: unset;
    border-top-left-radius: unset;
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
  }

  .card:where(.card-border) {
    border: var(--border) solid var(--color-base-200);
  }

  .card:where(.card-dash) {
    border: var(--border) dashed var(--color-base-200);
  }

  .card.image-full {
    display: grid;
  }

  .card.image-full > * {
    grid-row-start: 1;
    grid-column-start: 1;
  }

  .card.image-full > .card-body {
    color: var(--color-neutral-content);
    position: relative;
  }

  .card.image-full :where(figure) {
    border-radius: inherit;
    overflow: hidden;
  }

  .card.image-full > figure img {
    object-fit: cover;
    filter: brightness(28%);
    height: 100%;
  }

  .card figure {
    justify-content: center;
    align-items: center;
    display: flex;
  }

  .card:has( > input:-webkit-any(input[type="checkbox"], input[type="radio"])) {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

  .card:has( > input:-moz-any(input[type="checkbox"], input[type="radio"])) {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

  .card:has( > input:is(input[type="checkbox"], input[type="radio"])) {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

  .card:has( > :checked) {
    outline: 2px solid;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .avatar {
    vertical-align: middle;
    display: inline-flex;
    position: relative;
  }

  .avatar > div {
    aspect-ratio: 1;
    display: block;
    overflow: hidden;
  }

  .avatar img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .checkbox {
    border: var(--border) solid var(--input-color, var(--color-base-content));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .checkbox {
      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0)));
    }
  }

  .checkbox {
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * .1)) inset, 0 0 rgba(0, 0, 0, 0) inset, 0 0 rgba(0, 0, 0, 0);
    --size: calc(var(--size-selector, .25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    flex-shrink: 0;
    padding: .25rem;
    transition: background-color .2s, box-shadow .2s;
    display: inline-block;
    position: relative;
  }

  .checkbox:before {
    --tw-content: "";
    content: var(--tw-content);
    opacity: 0;
    clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
    width: 100%;
    height: 100%;
    box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    background-color: currentColor;
    font-size: 1rem;
    line-height: .75;
    transition: clip-path .3s .1s, opacity .1s .1s, rotate .3s .1s, translate .3s .1s;
    display: block;
    rotate: 45deg;
  }

  .checkbox:focus-visible {
    outline: 2px solid var(--input-color, currentColor);
    outline-offset: 2px;
  }

  .checkbox:checked {
    background-color: var(--input-color, rgba(0, 0, 0, 0));
    box-shadow: 0 0 rgba(0, 0, 0, 0) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * .1));
  }

  .checkbox:checked:before {
    clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
    opacity: 1;
  }

  @media (forced-colors: active) {
    .checkbox:checked:before {
      --tw-content: "✔︎";
      clip-path: none;
      background-color: rgba(0, 0, 0, 0);
      rotate: none;
    }
  }

  @media print {
    .checkbox:checked:before {
      --tw-content: "✔︎";
      clip-path: none;
      background-color: rgba(0, 0, 0, 0);
      rotate: none;
    }
  }

  .checkbox[aria-checked="true"] {
    background-color: var(--input-color, rgba(0, 0, 0, 0));
    box-shadow: 0 0 rgba(0, 0, 0, 0) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * .1));
  }

  .checkbox[aria-checked="true"]:before {
    clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
    opacity: 1;
  }

  @media (forced-colors: active) {
    .checkbox[aria-checked="true"]:before {
      --tw-content: "✔︎";
      clip-path: none;
      background-color: rgba(0, 0, 0, 0);
      rotate: none;
    }
  }

  @media print {
    .checkbox[aria-checked="true"]:before {
      --tw-content: "✔︎";
      clip-path: none;
      background-color: rgba(0, 0, 0, 0);
      rotate: none;
    }
  }

  .checkbox:indeterminate:before {
    opacity: 1;
    clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
    translate: 0 -35%;
    rotate: none;
  }

  .checkbox:disabled {
    cursor: not-allowed;
    opacity: .2;
  }

  .radio {
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    vertical-align: middle;
    border: var(--border) solid var(--input-color, currentColor);
    border-radius: 3.40282e38px;
    flex-shrink: 0;
    padding: .25rem;
    display: inline-block;
    position: relative;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .radio {
      border: var(--border) solid var(--input-color, color-mix(in srgb, currentColor 20%, rgba(0, 0, 0, 0)));
    }
  }

  .radio {
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * .1)) inset;
    --size: calc(var(--size-selector, .25rem) * 6);
    width: var(--size);
    height: var(--size);
    color: var(--input-color, currentColor);
  }

  .radio:before {
    --tw-content: "";
    content: var(--tw-content);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    border-radius: 3.40282e38px;
    width: 100%;
    height: 100%;
    display: block;
  }

  .radio:focus-visible {
    outline: 2px solid;
  }

  .radio:checked {
    background-color: var(--color-base-100);
    border-color: currentColor;
    animation: .2s ease-out radio;
  }

  .radio:checked:before {
    box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * .1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * .1));
    background-color: currentColor;
  }

  @media (forced-colors: active) {
    .radio:checked:before {
      outline-style: var(--tw-outline-style);
      outline-offset: calc(1px * -1);
      outline-width: 1px;
    }
  }

  @media print {
    .radio:checked:before {
      outline-offset: -1rem;
      outline: .25rem solid;
    }
  }

  .radio[aria-checked="true"] {
    background-color: var(--color-base-100);
    border-color: currentColor;
    animation: .2s ease-out radio;
  }

  .radio[aria-checked="true"]:before {
    box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * .1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * .1));
    background-color: currentColor;
  }

  @media (forced-colors: active) {
    .radio[aria-checked="true"]:before {
      outline-style: var(--tw-outline-style);
      outline-offset: calc(1px * -1);
      outline-width: 1px;
    }
  }

  @media print {
    .radio[aria-checked="true"]:before {
      outline-offset: -1rem;
      outline: .25rem solid;
    }
  }

  .radio:disabled {
    cursor: not-allowed;
    opacity: .2;
  }

  .stats {
    border-radius: var(--radius-box);
    grid-auto-flow: column;
    display: inline-grid;
    position: relative;
    overflow-x: auto;
  }

  .progress {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: var(--radius-box);
    background-color: currentColor;
    width: 100%;
    height: .5rem;
    position: relative;
    overflow: hidden;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .progress {
      background-color: color-mix(in oklab, currentColor 20%, transparent);
    }
  }

  .progress {
    color: var(--color-base-content);
  }

  .progress:indeterminate {
    background-image: repeating-linear-gradient(90deg, currentColor -1%, currentColor 10%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0) 90%);
    background-position-x: 15%;
    background-size: 200%;
    animation: 5s ease-in-out infinite progress;
  }

  @supports ((-moz-appearance: none)) {
    .progress:indeterminate::-moz-progress-bar {
      background-color: rgba(0, 0, 0, 0);
      background-image: repeating-linear-gradient(90deg, currentColor -1%, currentColor 10%, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0) 90%);
      background-position-x: 15%;
      background-size: 200%;
      animation: 5s ease-in-out infinite progress;
    }
  }

  @supports ((-moz-appearance: none)) {
    .progress::-moz-progress-bar {
      border-radius: var(--radius-box);
      background-color: currentColor;
    }
  }

  @supports ((-webkit-appearance: none)) {
    .progress::-webkit-progress-bar {
      border-radius: var(--radius-box);
      background-color: rgba(0, 0, 0, 0);
    }

    .progress::-webkit-progress-value {
      border-radius: var(--radius-box);
      background-color: currentColor;
    }
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .tooltip-left > .tooltip-content {
    transform: translateX(calc(var(--tt-pos, .25rem)  - .25rem)) translateY(-50%);
    inset: 50% var(--tt-off) auto auto;
  }

  .tooltip-left[data-tip]:before {
    transform: translateX(calc(var(--tt-pos, .25rem)  - .25rem)) translateY(-50%);
    inset: 50% var(--tt-off) auto auto;
  }

  .tooltip-left:after {
    transform: translateX(var(--tt-pos, .25rem)) translateY(-50%) rotate(-90deg);
    inset: 50% calc(var(--tt-tail)  + 1px) auto auto;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-4 {
    inset: calc(var(--spacing) * 4);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .dropdown-end {
    --anchor-h: span-left;
  }

  .dropdown-end :where(.dropdown-content) {
    translate: 0;
  }

  .dropdown-end :where(.dropdown-content):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: 0;
  }

  .dropdown-end :where(.dropdown-content):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: 0;
  }

  .dropdown-end :where(.dropdown-content):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    right: 0;
  }

  .dropdown-end :where(.dropdown-content):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .dropdown-end :where(.dropdown-content):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  .dropdown-end :where(.dropdown-content):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }

  [dir="rtl"] :is(.dropdown-end :where(.dropdown-content)) {
    translate: 0;
  }

  .dropdown-end.dropdown-left {
    --anchor-h: left;
    --anchor-v: span-top;
  }

  .dropdown-end.dropdown-left .dropdown-content {
    top: auto;
    bottom: 0;
  }

  .dropdown-end.dropdown-right {
    --anchor-h: right;
    --anchor-v: span-top;
  }

  .dropdown-end.dropdown-right .dropdown-content {
    top: auto;
    bottom: 0;
  }

  .dropdown-top {
    --anchor-v: top;
  }

  .dropdown-top .dropdown-content {
    transform-origin: bottom;
    top: auto;
    bottom: 100%;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-full {
    top: 100%;
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-full {
    bottom: 100%;
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-2\.5 {
    left: calc(var(--spacing) * 2.5);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .textarea {
    border: var(--border) solid rgba(0, 0, 0, 0);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: var(--radius-field);
    background-color: var(--color-base-100);
    vertical-align: middle;
    touch-action: manipulation;
    border-color: var(--input-color);
    width: max(3rem, min(20rem, 100%));
    min-height: 5rem;
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    flex-shrink: 1;
    padding-top: .5rem;
    padding-bottom: .5rem;
    padding-left: .75rem;
    padding-right: .75rem;
    font-size: .875rem;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0)) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
    }
  }

  .textarea {
    --input-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  .textarea textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: rgba(0, 0, 0, 0);
    border: none;
  }

  .textarea textarea:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .textarea textarea:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .textarea textarea:focus-within {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .textarea textarea:focus-within {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .textarea:focus {
    --input-color: var(--color-base-content);
    box-shadow: 0 1px var(--input-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:focus {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .textarea:focus {
    outline: 2px solid var(--input-color);
    outline-offset: 2px;
    isolation: isolate;
  }

  .textarea:focus-within {
    --input-color: var(--color-base-content);
    box-shadow: 0 1px var(--input-color);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:focus-within {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), rgba(0, 0, 0, 0));
    }
  }

  .textarea:focus-within {
    outline: 2px solid var(--input-color);
    outline-offset: 2px;
    isolation: isolate;
  }

  .textarea:has( > textarea[disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:has( > textarea[disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .textarea:has( > textarea[disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:has( > textarea[disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .textarea:has( > textarea[disabled]) {
    box-shadow: none;
  }

  .textarea:-webkit-any(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:-webkit-any(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .textarea:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .textarea:is(:disabled, [disabled]) {
    box-shadow: none;
  }

  .textarea:-moz-any(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:-moz-any(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .textarea:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .textarea:is(:disabled, [disabled]) {
    box-shadow: none;
  }

  .textarea:is(:disabled, [disabled]) {
    cursor: not-allowed;
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:is(:disabled, [disabled]) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .textarea:is(:disabled, [disabled])::placeholder {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .textarea:is(:disabled, [disabled])::placeholder {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .textarea:is(:disabled, [disabled]) {
    box-shadow: none;
  }

  .textarea:has( > textarea[disabled]) > textarea[disabled] {
    cursor: not-allowed;
  }

  .btn-active {
    --btn-bg: var(--btn-color, var(--color-base-200));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .btn-active {
      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
    }
  }

  .btn-active {
    --btn-shadow: 0 0 0 0 rgba(0, 0, 0, 0), 0 0 0 0 rgba(0, 0, 0, 0);
    isolation: isolate;
  }

  .stack {
    grid-template-rows: 3px 4px 1fr 4px 3px;
    grid-template-columns: 3px 4px 1fr 4px 3px;
    display: inline-grid;
  }

  .stack > * {
    width: 100%;
    height: 100%;
  }

  .stack > *:nth-child(n+2) {
    opacity: .7;
    width: 100%;
  }

  .stack > *:nth-child(2) {
    z-index: 2;
    opacity: .9;
  }

  .stack > *:first-child {
    z-index: 3;
    width: 100%;
  }

  .stack > * {
    grid-area: 3 / 3 / 6 / 4;
  }

  .stack > *:nth-child(2) {
    grid-area: 2 / 2 / 5 / 5;
  }

  .stack > *:first-child {
    grid-area: 1 / 1 / 4 / 6;
  }

  .stack.stack-bottom > * {
    grid-area: 3 / 3 / 6 / 4;
  }

  .stack.stack-bottom > *:nth-child(2) {
    grid-area: 2 / 2 / 5 / 5;
  }

  .stack.stack-bottom > *:first-child {
    grid-area: 1 / 1 / 4 / 6;
  }

  .stack.stack-top > * {
    grid-area: 1 / 3 / 4 / 4;
  }

  .stack.stack-top > *:nth-child(2) {
    grid-area: 2 / 2 / 5 / 5;
  }

  .stack.stack-top > *:first-child {
    grid-area: 3 / 1 / 6 / 6;
  }

  .stack.stack-start > * {
    grid-area: 3 / 1 / 4 / 4;
  }

  .stack.stack-start > *:nth-child(2) {
    grid-area: 2 / 2 / 5 / 5;
  }

  .stack.stack-start > *:first-child {
    grid-area: 1 / 3 / 6 / 6;
  }

  .stack.stack-end > * {
    grid-area: 3 / 3 / 4 / 6;
  }

  .stack.stack-end > *:nth-child(2) {
    grid-area: 2 / 2 / 5 / 5;
  }

  .stack.stack-end > *:first-child {
    grid-area: 1 / 1 / 6 / 4;
  }

  .z-5 {
    z-index: 5;
  }

  .z-10 {
    z-index: 10;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .tab-content {
    order: var(--tabcontent-order);
    --tabcontent-radius-ss: 0;
    --tabcontent-radius-se: 0;
    --tabcontent-radius-es: 0;
    --tabcontent-radius-ee: 0;
    --tabcontent-order: 1;
    width: 100%;
    margin: var(--tabcontent-margin);
    border-color: rgba(0, 0, 0, 0);
    border-width: var(--border);
    display: none;
  }

  .tab-content:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--tabcontent-radius-ss);
    border-top-right-radius: var(--tabcontent-radius-se);
    border-bottom-right-radius: var(--tabcontent-radius-ee);
    border-bottom-left-radius: var(--tabcontent-radius-es);
  }

  .tab-content:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--tabcontent-radius-ss);
    border-top-right-radius: var(--tabcontent-radius-se);
    border-bottom-right-radius: var(--tabcontent-radius-ee);
    border-bottom-left-radius: var(--tabcontent-radius-es);
  }

  .tab-content:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--tabcontent-radius-ss);
    border-top-right-radius: var(--tabcontent-radius-se);
    border-bottom-right-radius: var(--tabcontent-radius-ee);
    border-bottom-left-radius: var(--tabcontent-radius-es);
  }

  .tab-content:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--tabcontent-radius-ss);
    border-top-left-radius: var(--tabcontent-radius-se);
    border-bottom-left-radius: var(--tabcontent-radius-ee);
    border-bottom-right-radius: var(--tabcontent-radius-es);
  }

  .tab-content:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--tabcontent-radius-ss);
    border-top-left-radius: var(--tabcontent-radius-se);
    border-bottom-left-radius: var(--tabcontent-radius-ee);
    border-bottom-right-radius: var(--tabcontent-radius-es);
  }

  .tab-content:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--tabcontent-radius-ss);
    border-top-left-radius: var(--tabcontent-radius-se);
    border-bottom-left-radius: var(--tabcontent-radius-ee);
    border-bottom-right-radius: var(--tabcontent-radius-es);
  }

  .stat-figure {
    grid-row: 1 / span 3;
    grid-column-start: 2;
    place-self: center flex-end;
  }

  .modal-box {
    background-color: var(--color-base-100);
    border-top-left-radius: var(--modal-tl, var(--radius-box));
    border-top-right-radius: var(--modal-tr, var(--radius-box));
    border-bottom-left-radius: var(--modal-bl, var(--radius-box));
    border-bottom-right-radius: var(--modal-br, var(--radius-box));
    opacity: 0;
    overscroll-behavior: contain;
    grid-row-start: 1;
    grid-column-start: 1;
    width: 91.6667%;
    max-width: 32rem;
    max-height: 100vh;
    padding: 1.5rem;
    transition: translate .3s ease-out, scale .3s ease-out, opacity .2s ease-out 50ms, box-shadow .3s ease-out;
    overflow-y: auto;
    scale: 95%;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    box-shadow: 0 25px 50px -12px color(display-p3 0 0 0 / .25);
    box-shadow: 0 25px 50px -12px lab(0% 0 0 / .25);
  }

  .stat-value {
    white-space: nowrap;
    grid-column-start: 1;
    font-size: 2rem;
    font-weight: 800;
  }

  .stat-desc {
    white-space: nowrap;
    color: var(--color-base-content);
    grid-column-start: 1;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .stat-desc {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
  }

  .stat-desc {
    font-size: .75rem;
  }

  .stat-title {
    white-space: nowrap;
    color: var(--color-base-content);
    grid-column-start: 1;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .stat-title {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
  }

  .stat-title {
    font-size: .75rem;
  }

  .float-left {
    float: left;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .divider {
    white-space: nowrap;
    height: 1rem;
    margin: var(--divider-m, 1rem 0);
    --divider-color: var(--color-base-content);
    flex-direction: row;
    align-self: stretch;
    align-items: center;
    display: flex;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .divider {
      --divider-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
  }

  .divider:before {
    content: "";
    background-color: var(--divider-color);
    flex-grow: 1;
    width: 100%;
    height: .125rem;
  }

  .divider:after {
    content: "";
    background-color: var(--divider-color);
    flex-grow: 1;
    width: 100%;
    height: .125rem;
  }

  @media print {
    .divider:before {
      border: .5px solid;
    }

    .divider:after {
      border: .5px solid;
    }
  }

  .divider:not(:empty) {
    gap: 1rem;
  }

  .m-1 {
    margin: calc(var(--spacing) * 1);
  }

  .m-2 {
    margin: calc(var(--spacing) * 2);
  }

  .m-3 {
    margin: calc(var(--spacing) * 3);
  }

  .m-4 {
    margin: calc(var(--spacing) * 4);
  }

  .m-5 {
    margin: calc(var(--spacing) * 5);
  }

  .filter {
    flex-wrap: wrap;
    display: flex;
  }

  .filter input[type="radio"] {
    width: auto;
  }

  .filter input {
    opacity: 1;
    transition: margin .1s, opacity .3s, padding .3s, border-width .1s;
    overflow: hidden;
    scale: 1;
  }

  .filter input:not(:last-child):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: .25rem;
  }

  .filter input:not(:last-child):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: .25rem;
  }

  .filter input:not(:last-child):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-right: .25rem;
  }

  .filter input:not(:last-child):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: .25rem;
  }

  .filter input:not(:last-child):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: .25rem;
  }

  .filter input:not(:last-child):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-left: .25rem;
  }

  .filter input.filter-reset {
    aspect-ratio: 1;
  }

  .filter input.filter-reset:after {
    content: "×";
  }

  .filter:not(:has(input:checked:not(.filter-reset))) .filter-reset {
    opacity: 0;
    border-width: 0;
    width: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    scale: 0;
  }

  .filter:not(:has(input:checked:not(.filter-reset))) input[type="reset"] {
    opacity: 0;
    border-width: 0;
    width: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    scale: 0;
  }

  .filter:has(input:checked:not(.filter-reset)) input:not(:-webkit-any(:checked, .filter-reset, input[type="reset"])) {
    opacity: 0;
    border-width: 0;
    width: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    scale: 0;
  }

  .filter:has(input:checked:not(.filter-reset)) input:not(:-moz-any(:checked, .filter-reset, input[type="reset"])) {
    opacity: 0;
    border-width: 0;
    width: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    scale: 0;
  }

  .filter:has(input:checked:not(.filter-reset)) input:not(:is(:checked, .filter-reset, input[type="reset"])) {
    opacity: 0;
    border-width: 0;
    width: 0;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
    scale: 0;
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .input-lg {
    --size: calc(var(--size-field, .25rem) * 12);
    font-size: 1.125rem;
  }

  .input-lg[type="number"]::-webkit-inner-spin-button {
    margin-top: -.75rem;
    margin-bottom: -.75rem;
  }

  .input-lg[type="number"]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-lg[type="number"]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-lg[type="number"]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-lg[type="number"]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-lg[type="number"]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-lg[type="number"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-md {
    --size: calc(var(--size-field, .25rem) * 10);
    font-size: .875rem;
  }

  .input-md[type="number"]::-webkit-inner-spin-button {
    margin-top: -.75rem;
    margin-bottom: -.75rem;
  }

  .input-md[type="number"]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-md[type="number"]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-md[type="number"]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-md[type="number"]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-md[type="number"]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-md[type="number"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-sm {
    --size: calc(var(--size-field, .25rem) * 8);
    font-size: .75rem;
  }

  .input-sm[type="number"]::-webkit-inner-spin-button {
    margin-top: -.5rem;
    margin-bottom: -.5rem;
  }

  .input-sm[type="number"]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-sm[type="number"]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-sm[type="number"]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-sm[type="number"]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-sm[type="number"]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-sm[type="number"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-xs {
    --size: calc(var(--size-field, .25rem) * 6);
    font-size: .6875rem;
  }

  .input-xs[type="number"]::-webkit-inner-spin-button {
    margin-top: -.25rem;
    margin-bottom: -.25rem;
  }

  .input-xs[type="number"]:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-xs[type="number"]:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-xs[type="number"]:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-inner-spin-button {
    margin-right: -.75rem;
  }

  .input-xs[type="number"]:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-xs[type="number"]:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .input-xs[type="number"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-inner-spin-button {
    margin-left: -.75rem;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }

  .label {
    white-space: nowrap;
    color: currentColor;
    align-items: center;
    gap: .375rem;
    display: inline-flex;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .label {
      color: color-mix(in oklab, currentColor 60%, transparent);
    }
  }

  .label:has(input) {
    cursor: pointer;
  }

  .label:is(.input > *, .select > *) {
    white-space: nowrap;
    height: calc(100% - .5rem);
    font-size: inherit;
    align-items: center;
    padding-left: .75rem;
    padding-right: .75rem;
    display: flex;
  }

  .label:-webkit-any(.input > *, .select > *):first-child:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right: var(--border) solid currentColor;
    margin-left: -.75rem;
    margin-right: .75rem;
  }

  .label:-moz-any(.input > *, .select > *):first-child:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right: var(--border) solid currentColor;
    margin-left: -.75rem;
    margin-right: .75rem;
  }

  .label:is(.input > *, .select > *):first-child:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right: var(--border) solid currentColor;
    margin-left: -.75rem;
    margin-right: .75rem;
  }

  .label:-webkit-any(.input > *, .select > *):first-child:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left: var(--border) solid currentColor;
    margin-left: .75rem;
    margin-right: -.75rem;
  }

  .label:-moz-any(.input > *, .select > *):first-child:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left: var(--border) solid currentColor;
    margin-left: .75rem;
    margin-right: -.75rem;
  }

  .label:is(.input > *, .select > *):first-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left: var(--border) solid currentColor;
    margin-left: .75rem;
    margin-right: -.75rem;
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .label:-webkit-any(.input > *, .select > *):last-child:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: var(--border) solid currentColor;
    margin-left: .75rem;
    margin-right: -.75rem;
  }

  .label:-moz-any(.input > *, .select > *):last-child:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: var(--border) solid currentColor;
    margin-left: .75rem;
    margin-right: -.75rem;
  }

  .label:is(.input > *, .select > *):last-child:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left: var(--border) solid currentColor;
    margin-left: .75rem;
    margin-right: -.75rem;
  }

  .label:-webkit-any(.input > *, .select > *):last-child:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: var(--border) solid currentColor;
    margin-left: -.75rem;
    margin-right: .75rem;
  }

  .label:-moz-any(.input > *, .select > *):last-child:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: var(--border) solid currentColor;
    margin-left: -.75rem;
    margin-right: .75rem;
  }

  .label:is(.input > *, .select > *):last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right: var(--border) solid currentColor;
    margin-left: -.75rem;
    margin-right: .75rem;
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .steps-horizontal {
    grid-auto-columns: 1fr;
    grid-auto-flow: column;
    display: inline-grid;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .steps-horizontal .step {
    text-align: center;
    grid-template-rows: 40px 1fr;
    grid-template-columns: auto;
    place-items: center;
    min-width: 4rem;
    display: grid;
  }

  .steps-horizontal .step:before {
    content: "";
    width: 100%;
    height: .5rem;
    translate: 0;
  }

  .steps-horizontal .step:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    margin-left: -100%;
  }

  .steps-horizontal .step:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    margin-left: -100%;
  }

  .steps-horizontal .step:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))):before {
    margin-left: -100%;
  }

  .steps-horizontal .step:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    margin-right: -100%;
  }

  .steps-horizontal .step:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    margin-right: -100%;
  }

  .steps-horizontal .step:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
    margin-right: -100%;
  }

  [dir="rtl"] :is(.steps-horizontal .step):before {
    translate: 0;
  }

  .modal-action {
    justify-content: flex-end;
    gap: .5rem;
    margin-top: 1.5rem;
    display: flex;
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mr-5 {
    margin-right: calc(var(--spacing) * 5);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-5 {
    margin-left: calc(var(--spacing) * 5);
  }

  .ml-auto {
    margin-left: auto;
  }

  .status {
    aspect-ratio: 1;
    border-radius: var(--radius-selector);
    background-color: var(--color-base-content);
    width: .5rem;
    height: .5rem;
    display: inline-block;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .status {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .status {
    vertical-align: middle;
    color: rgba(0, 0, 0, .3);
    background-position: center;
    background-repeat: no-repeat;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .status {
      color: rgba(0, 0, 0, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .status {
        color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
  }

  .status {
    background-image: radial-gradient(circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * .5)), rgba(0, 0, 0, 0));
    box-shadow: 0 2px 3px -1px;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .status {
      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), rgba(0, 0, 0, 0));
    }
  }

  .status\! {
    aspect-ratio: 1 !important;
    border-radius: var(--radius-selector) !important;
    background-color: var(--color-base-content) !important;
    width: .5rem !important;
    height: .5rem !important;
    display: inline-block !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .status\! {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
    }
  }

  .status\! {
    vertical-align: middle !important;
    color: rgba(0, 0, 0, .3) !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .status\! {
      color: rgba(0, 0, 0, .3) !important;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .status\! {
        color: color-mix(in oklab, var(--color-black) 30%, transparent) !important;
      }
    }
  }

  .status\! {
    background-image: radial-gradient(circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * .5)), rgba(0, 0, 0, 0)) !important;
    box-shadow: 0 2px 3px -1px !important;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .status\! {
      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), rgba(0, 0, 0, 0)) !important;
    }
  }

  .badge {
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--badge-fg);
    border: var(--border) solid var(--badge-color, var(--color-base-200));
    width: -moz-fit-content;
    width: fit-content;
    padding-inline: calc(.25rem * 3 - var(--border));
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    background-color: var(--badge-bg);
    --badge-bg: var(--badge-color, var(--color-base-100));
    --badge-fg: var(--color-base-content);
    --size: calc(var(--size-selector, .25rem) * 6);
    height: var(--size);
    justify-content: center;
    align-items: center;
    gap: .5rem;
    font-size: .875rem;
    display: inline-flex;
  }

  .kbd {
    border-radius: var(--radius-field);
    background-color: var(--color-base-200);
    vertical-align: middle;
    border: var(--border) solid var(--color-base-content);
    justify-content: center;
    align-items: center;
    padding-left: .5em;
    padding-right: .5em;
    display: inline-flex;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .kbd {
      border: var(--border) solid color-mix(in srgb, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  .kbd {
    border-bottom: calc(var(--border)  + 1px) solid var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .kbd {
      border-bottom: calc(var(--border)  + 1px) solid color-mix(in srgb, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  .kbd {
    --size: calc(var(--size-selector, .25rem) * 6);
    height: var(--size);
    min-width: var(--size);
    font-size: .875rem;
  }

  .tabs {
    --tabs-height: auto;
    --tabs-direction: row;
    --tab-height: calc(var(--size-field, .25rem) * 10);
    height: var(--tabs-height);
    flex-wrap: wrap;
    flex-direction: var(--tabs-direction);
    display: flex;
  }

  .navbar {
    align-items: center;
    width: 100%;
    min-height: 4rem;
    padding: .5rem;
    display: flex;
  }

  .stat {
    grid-template-columns: repeat(1, 1fr);
    column-gap: 1rem;
    width: 100%;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    display: inline-grid;
  }

  .stat:not(:last-child):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right: var(--border) dashed currentColor;
  }

  .stat:not(:last-child):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right: var(--border) dashed currentColor;
  }

  .stat:not(:last-child):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-right: var(--border) dashed currentColor;
  }

  .stat:not(:last-child):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left: var(--border) dashed currentColor;
  }

  .stat:not(:last-child):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left: var(--border) dashed currentColor;
  }

  .stat:not(:last-child):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-left: var(--border) dashed currentColor;
  }

  @supports (color: color-mix(in lab, red, red)) {
    
  }

  .stat:not(:last-child) {
    border-bottom: none;
  }

  .navbar-end {
    justify-content: flex-end;
    align-items: center;
    width: 50%;
    display: inline-flex;
  }

  .navbar-start {
    justify-content: flex-start;
    align-items: center;
    width: 50%;
    display: inline-flex;
  }

  .card-body {
    padding: var(--card-p, 1.5rem);
    font-size: var(--card-fs, .875rem);
    flex-direction: column;
    flex: auto;
    gap: .5rem;
    display: flex;
  }

  .card-body :where(p) {
    flex-grow: 1;
  }

  .navbar-center {
    flex-shrink: 0;
    align-items: center;
    display: inline-flex;
  }

  .alert {
    border-radius: var(--radius-box);
    color: var(--color-base-content);
    background-color: var(--alert-color, var(--color-base-200));
    text-align: start;
    border: var(--border) solid var(--color-base-200);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * .08)) inset, 0 1px #000, 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * .08));
    grid-template-columns: auto;
    grid-auto-flow: column;
    justify-content: start;
    place-items: center start;
    gap: 1rem;
    padding-top: .75rem;
    padding-bottom: .75rem;
    padding-left: 1rem;
    padding-right: 1rem;
    font-size: .875rem;
    line-height: 1.25rem;
    display: grid;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .alert {
      box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * .08)) inset, 0 1px color-mix(in oklab, color-mix(in oklab, #000 20%, var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%), rgba(0, 0, 0, 0)), 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * .08));
    }
  }

  .alert:has(:nth-child(2)) {
    grid-template-columns: auto minmax(auto, 1fr);
  }

  .alert.alert-outline {
    color: var(--alert-color);
    box-shadow: none;
    background-color: rgba(0, 0, 0, 0);
    background-image: none;
  }

  .alert.alert-dash {
    color: var(--alert-color);
    box-shadow: none;
    background-color: rgba(0, 0, 0, 0);
    background-image: none;
    border-style: dashed;
  }

  .alert.alert-soft {
    color: var(--alert-color, var(--color-base-content));
    background: var(--alert-color, var(--color-base-content));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .alert.alert-soft {
      background: color-mix(in oklab, var(--alert-color, var(--color-base-content)) 8%, var(--color-base-100));
    }
  }

  .alert.alert-soft {
    border-color: var(--alert-color, var(--color-base-content));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .alert.alert-soft {
      border-color: color-mix(in oklab, var(--alert-color, var(--color-base-content)) 10%, var(--color-base-100));
    }
  }

  .alert.alert-soft {
    box-shadow: none;
    background-image: none;
  }

  .card-actions {
    flex-wrap: wrap;
    align-items: flex-start;
    gap: .5rem;
    display: flex;
  }

  .card-title {
    font-size: var(--cardtitle-fs, 1.125rem);
    align-items: center;
    gap: .5rem;
    font-weight: 600;
    display: flex;
  }

  .join {
    --join-ss: 0;
    --join-se: 0;
    --join-es: 0;
    --join-ee: 0;
    align-items: stretch;
    display: inline-flex;
  }

  .join :where(.join-item):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, 0);
    border-top-right-radius: var(--join-se, 0);
    border-bottom-right-radius: var(--join-ee, 0);
    border-bottom-left-radius: var(--join-es, 0);
  }

  .join :where(.join-item):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, 0);
    border-top-right-radius: var(--join-se, 0);
    border-bottom-right-radius: var(--join-ee, 0);
    border-bottom-left-radius: var(--join-es, 0);
  }

  .join :where(.join-item):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-top-left-radius: var(--join-ss, 0);
    border-top-right-radius: var(--join-se, 0);
    border-bottom-right-radius: var(--join-ee, 0);
    border-bottom-left-radius: var(--join-es, 0);
  }

  .join :where(.join-item):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, 0);
    border-top-left-radius: var(--join-se, 0);
    border-bottom-left-radius: var(--join-ee, 0);
    border-bottom-right-radius: var(--join-es, 0);
  }

  .join :where(.join-item):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, 0);
    border-top-left-radius: var(--join-se, 0);
    border-bottom-left-radius: var(--join-ee, 0);
    border-bottom-right-radius: var(--join-es, 0);
  }

  .join :where(.join-item):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-top-right-radius: var(--join-ss, 0);
    border-top-left-radius: var(--join-se, 0);
    border-bottom-left-radius: var(--join-ee, 0);
    border-bottom-right-radius: var(--join-es, 0);
  }

  .join :where(.join-item) * {
    --join-ss: var(--radius-field);
    --join-se: var(--radius-field);
    --join-es: var(--radius-field);
    --join-ee: var(--radius-field);
  }

  .join > .join-item:where(:first-child) {
    --join-ss: var(--radius-field);
    --join-se: 0;
    --join-es: var(--radius-field);
    --join-ee: 0;
  }

  .join :first-child:not(:last-child) :where(.join-item) {
    --join-ss: var(--radius-field);
    --join-se: 0;
    --join-es: var(--radius-field);
    --join-ee: 0;
  }

  .join > .join-item:where(:last-child) {
    --join-ss: 0;
    --join-se: var(--radius-field);
    --join-es: 0;
    --join-ee: var(--radius-field);
  }

  .join :last-child:not(:first-child) :where(.join-item) {
    --join-ss: 0;
    --join-se: var(--radius-field);
    --join-es: 0;
    --join-ee: var(--radius-field);
  }

  .join > .join-item:where(:only-child) {
    --join-ss: var(--radius-field);
    --join-se: var(--radius-field);
    --join-es: var(--radius-field);
    --join-ee: var(--radius-field);
  }

  .join :only-child :where(.join-item) {
    --join-ss: var(--radius-field);
    --join-se: var(--radius-field);
    --join-es: var(--radius-field);
    --join-ee: var(--radius-field);
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :root .prose {
    --tw-prose-body: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-body: color-mix(in oklab, var(--color-base-content) 80%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-headings: var(--color-base-content);
    --tw-prose-lead: var(--color-base-content);
    --tw-prose-links: var(--color-base-content);
    --tw-prose-bold: var(--color-base-content);
    --tw-prose-counters: var(--color-base-content);
    --tw-prose-bullets: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-bullets: color-mix(in oklab, var(--color-base-content) 50%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-hr: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-hr: color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-quotes: var(--color-base-content);
    --tw-prose-quote-borders: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-quote-borders: color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-captions: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-captions: color-mix(in oklab, var(--color-base-content) 50%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-code: var(--color-base-content);
    --tw-prose-pre-code: var(--color-neutral-content);
    --tw-prose-pre-bg: var(--color-neutral);
    --tw-prose-th-borders: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-th-borders: color-mix(in oklab, var(--color-base-content) 50%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-td-borders: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-td-borders: color-mix(in oklab, var(--color-base-content) 20%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose {
    --tw-prose-kbd: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :root .prose {
      --tw-prose-kbd: color-mix(in oklab, var(--color-base-content) 80%, rgba(0, 0, 0, 0));
    }
  }

  :root .prose :where(code):not(pre > code) {
    background-color: var(--color-base-200);
    border-radius: var(--radius-selector);
    border: var(--border) solid var(--color-base-300);
    font-weight: inherit;
    padding-left: .5em;
    padding-right: .5em;
  }

  :root .prose :where(code):not(pre > code):before {
    display: none;
  }

  :root .prose :where(code):not(pre > code):after {
    display: none;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .table {
    display: table;
  }

  .btn-circle {
    width: var(--size);
    height: var(--size);
    border-radius: 3.40282e38px;
    padding-left: 0;
    padding-right: 0;
  }

  .btn-square {
    width: var(--size);
    height: var(--size);
    padding-left: 0;
    padding-right: 0;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-full {
    height: 100%;
  }

  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }

  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }

  .max-h-80 {
    max-height: calc(var(--spacing) * 80);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .min-h-\[400px\] {
    min-height: 400px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .loading-lg {
    width: calc(var(--size-selector, .25rem) * 7);
  }

  .loading-md {
    width: calc(var(--size-selector, .25rem) * 6);
  }

  .loading-sm {
    width: calc(var(--size-selector, .25rem) * 5);
  }

  .loading-xs {
    width: calc(var(--size-selector, .25rem) * 4);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-52 {
    width: calc(var(--spacing) * 52);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-48 {
    min-width: calc(var(--spacing) * 48);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .origin-left {
    transform-origin: 0;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .link {
    cursor: pointer;
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .link:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .link:focus {
      outline-offset: 2px;
      outline: 2px solid rgba(0, 0, 0, 0);
    }
  }

  .link:focus-visible {
    outline-offset: 2px;
    outline: 2px solid;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-1 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-3 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-4 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .menu-sm :where(li:not(.menu-title) > :not(:-webkit-any(ul, details, .menu-title))) {
    border-radius: var(--radius-field);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .625rem;
    padding-right: .625rem;
    font-size: .75rem;
  }

  .menu-sm :where(li:not(.menu-title) > :not(:-moz-any(ul, details, .menu-title))) {
    border-radius: var(--radius-field);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .625rem;
    padding-right: .625rem;
    font-size: .75rem;
  }

  .menu-sm :where(li:not(.menu-title) > :not(:is(ul, details, .menu-title))) {
    border-radius: var(--radius-field);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .625rem;
    padding-right: .625rem;
    font-size: .75rem;
  }

  .menu-sm :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
    border-radius: var(--radius-field);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .625rem;
    padding-right: .625rem;
    font-size: .75rem;
  }

  .menu-sm .menu-title {
    padding-top: .5rem;
    padding-bottom: .5rem;
    padding-left: .75rem;
    padding-right: .75rem;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-box {
    border-radius: var(--radius-box);
    border-radius: var(--radius-box);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .badge-ghost {
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
    background-image: none;
  }

  .badge-outline {
    color: var(--badge-color);
    --badge-bg: rgba(0, 0, 0, 0);
    background-image: none;
    border-color: currentColor;
  }

  .alert-error {
    border-color: var(--color-error);
    color: var(--color-error-content);
    --alert-color: var(--color-error);
  }

  .alert-info {
    border-color: var(--color-info);
    color: var(--color-info-content);
    --alert-color: var(--color-info);
  }

  .alert-success {
    border-color: var(--color-success);
    color: var(--color-success-content);
    --alert-color: var(--color-success);
  }

  .border-base-200 {
    border-color: var(--color-base-200);
  }

  .border-base-300 {
    border-color: var(--color-base-300);
  }

  .border-error\/20 {
    border-color: var(--color-error);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-error\/20 {
      border-color: color-mix(in oklab, var(--color-error) 20%, transparent);
    }
  }

  .border-info\/20 {
    border-color: var(--color-info);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-info\/20 {
      border-color: color-mix(in oklab, var(--color-info) 20%, transparent);
    }
  }

  .border-primary {
    border-color: var(--color-primary);
  }

  .border-primary\/20 {
    border-color: var(--color-primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/20 {
      border-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }

  .border-warning {
    border-color: var(--color-warning);
  }

  .border-t-transparent {
    border-top-color: rgba(0, 0, 0, 0);
  }

  .bg-base-100 {
    background-color: var(--color-base-100);
  }

  .bg-base-200 {
    background-color: var(--color-base-200);
  }

  .bg-base-300 {
    background-color: var(--color-base-300);
  }

  .bg-base-content\/20 {
    background-color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-base-content\/20 {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: rgba(0, 0, 0, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-black\/60 {
    background-color: rgba(0, 0, 0, .6);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/60 {
      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }

  .bg-current {
    background-color: currentColor;
  }

  .bg-error {
    background-color: var(--color-error);
  }

  .bg-error\/10 {
    background-color: var(--color-error);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-error\/10 {
      background-color: color-mix(in oklab, var(--color-error) 10%, transparent);
    }
  }

  .bg-error\/20 {
    background-color: var(--color-error);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-error\/20 {
      background-color: color-mix(in oklab, var(--color-error) 20%, transparent);
    }
  }

  .bg-info\/10 {
    background-color: var(--color-info);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-info\/10 {
      background-color: color-mix(in oklab, var(--color-info) 10%, transparent);
    }
  }

  .bg-primary {
    background-color: var(--color-primary);
  }

  .bg-primary-content\/20 {
    background-color: var(--color-primary-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary-content\/20 {
      background-color: color-mix(in oklab, var(--color-primary-content) 20%, transparent);
    }
  }

  .bg-primary\/5 {
    background-color: var(--color-primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/5 {
      background-color: color-mix(in oklab, var(--color-primary) 5%, transparent);
    }
  }

  .bg-primary\/10 {
    background-color: var(--color-primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/10 {
      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
  }

  .bg-success\/10 {
    background-color: var(--color-success);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-success\/10 {
      background-color: color-mix(in oklab, var(--color-success) 10%, transparent);
    }
  }

  .bg-warning {
    background-color: var(--color-warning);
  }

  .bg-warning\/20 {
    background-color: var(--color-warning);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-warning\/20 {
      background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
    }
  }

  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-primary {
    --tw-gradient-from: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-secondary {
    --tw-gradient-to: var(--color-secondary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .loading-spinner {
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }

  .stroke-current {
    stroke: currentColor;
  }

  .object-cover {
    object-fit: cover;
  }

  .checkbox-sm {
    --size: calc(var(--size-selector, .25rem) * 5);
    padding: .1875rem;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .badge-lg {
    --size: calc(var(--size-selector, .25rem) * 7);
    padding-inline: calc(.25rem * 3.5 - var(--border));
    font-size: 1rem;
  }

  .badge-sm {
    --size: calc(var(--size-selector, .25rem) * 5);
    padding-inline: calc(.25rem * 2.5 - var(--border));
    font-size: .75rem;
  }

  .badge-xs {
    --size: calc(var(--size-selector, .25rem) * 4);
    padding-inline: calc(.25rem * 2 - var(--border));
    font-size: .625rem;
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pr-20 {
    padding-right: calc(var(--spacing) * 20);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }

  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .kbd-xs {
    --size: calc(var(--size-selector, .25rem) * 4);
    font-size: .625rem;
  }

  .select-sm {
    --size: calc(var(--size-field, .25rem) * 8);
    font-size: .75rem;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .checkbox-error {
    color: var(--color-error-content);
    --input-color: var(--color-error);
  }

  .checkbox-primary {
    color: var(--color-primary-content);
    --input-color: var(--color-primary);
  }

  .link-primary {
    color: var(--color-primary);
  }

  @media (hover: hover) {
    .link-primary:hover {
      color: var(--color-primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .link-primary:hover {
        color: color-mix(in oklab, var(--color-primary) 80%, #000);
      }
    }
  }

  .text-accent {
    color: var(--color-accent);
  }

  .text-base-content {
    color: var(--color-base-content);
  }

  .text-base-content\/30 {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-base-content\/30 {
      color: color-mix(in oklab, var(--color-base-content) 30%, transparent);
    }
  }

  .text-base-content\/40 {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-base-content\/40 {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }

  .text-base-content\/50 {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-base-content\/50 {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
  }

  .text-base-content\/60 {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-base-content\/60 {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
  }

  .text-base-content\/70 {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-base-content\/70 {
      color: color-mix(in oklab, var(--color-base-content) 70%, transparent);
    }
  }

  .text-base-content\/80 {
    color: var(--color-base-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-base-content\/80 {
      color: color-mix(in oklab, var(--color-base-content) 80%, transparent);
    }
  }

  .text-error {
    color: var(--color-error);
  }

  .text-error-content {
    color: var(--color-error-content);
  }

  .text-error\/80 {
    color: var(--color-error);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-error\/80 {
      color: color-mix(in oklab, var(--color-error) 80%, transparent);
    }
  }

  .text-info {
    color: var(--color-info);
  }

  .text-info-content {
    color: var(--color-info-content);
  }

  .text-primary {
    color: var(--color-primary);
  }

  .text-primary-content {
    color: var(--color-primary-content);
  }

  .text-primary-content\/70 {
    color: var(--color-primary-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary-content\/70 {
      color: color-mix(in oklab, var(--color-primary-content) 70%, transparent);
    }
  }

  .text-primary-content\/90 {
    color: var(--color-primary-content);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary-content\/90 {
      color: color-mix(in oklab, var(--color-primary-content) 90%, transparent);
    }
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-secondary {
    color: var(--color-secondary);
  }

  .text-success {
    color: var(--color-success);
  }

  .text-success-content {
    color: var(--color-success-content);
  }

  .text-warning {
    color: var(--color-warning);
  }

  .text-warning-content {
    color: var(--color-warning-content);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .text-yellow-900 {
    color: var(--color-yellow-900);
  }

  .italic {
    font-style: italic;
  }

  .line-through {
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
  }

  .overline {
    -webkit-text-decoration-line: overline;
    text-decoration-line: overline;
  }

  .underline {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-70 {
    opacity: .7;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-primary {
    --tw-ring-color: var(--color-primary);
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .btn-ghost:not(:-webkit-any(.btn-active, :hover, :active:focus, :focus-visible)) {
    --btn-shadow: "";
    --btn-bg: rgba(0, 0, 0, 0);
    --btn-border: rgba(0, 0, 0, 0);
    --btn-noise: none;
  }

  .btn-ghost:not(:-webkit-any(.btn-active, :hover, :active:focus, :focus-visible)):not(:-webkit-any(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:-moz-any(.btn-active, :hover, :active:focus, :focus-visible)):not(:-moz-any(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:is(.btn-active, :hover, :active:focus, :focus-visible)):not(:is(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:-moz-any(.btn-active, :hover, :active:focus, :focus-visible)) {
    --btn-shadow: "";
    --btn-bg: rgba(0, 0, 0, 0);
    --btn-border: rgba(0, 0, 0, 0);
    --btn-noise: none;
  }

  .btn-ghost:not(:-webkit-any(.btn-active, :hover, :active:focus, :focus-visible)):not(:-webkit-any(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:-moz-any(.btn-active, :hover, :active:focus, :focus-visible)):not(:-moz-any(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:is(.btn-active, :hover, :active:focus, :focus-visible)):not(:is(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:is(.btn-active, :hover, :active:focus, :focus-visible)) {
    --btn-shadow: "";
    --btn-bg: rgba(0, 0, 0, 0);
    --btn-border: rgba(0, 0, 0, 0);
    --btn-noise: none;
  }

  .btn-ghost:not(:-webkit-any(.btn-active, :hover, :active:focus, :focus-visible)):not(:-webkit-any(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:-moz-any(.btn-active, :hover, :active:focus, :focus-visible)):not(:-moz-any(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  .btn-ghost:not(:is(.btn-active, :hover, :active:focus, :focus-visible)):not(:is(:disabled, [disabled], .btn-disabled)) {
    --btn-fg: currentColor;
    outline-color: currentColor;
  }

  @media (hover: none) {
    .btn-ghost:hover:not(:-webkit-any(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled)) {
      --btn-shadow: "";
      --btn-bg: rgba(0, 0, 0, 0);
      --btn-border: rgba(0, 0, 0, 0);
      --btn-noise: none;
      --btn-fg: currentColor;
    }

    .btn-ghost:hover:not(:-moz-any(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled)) {
      --btn-shadow: "";
      --btn-bg: rgba(0, 0, 0, 0);
      --btn-border: rgba(0, 0, 0, 0);
      --btn-noise: none;
      --btn-fg: currentColor;
    }

    .btn-ghost:hover:not(:is(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled)) {
      --btn-shadow: "";
      --btn-bg: rgba(0, 0, 0, 0);
      --btn-border: rgba(0, 0, 0, 0);
      --btn-noise: none;
      --btn-fg: currentColor;
    }
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition\! {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events !important;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;
    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .btn-outline:not(:-webkit-any(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked)) {
    --btn-shadow: "";
    --btn-bg: rgba(0, 0, 0, 0);
    --btn-fg: var(--btn-color);
    --btn-border: var(--btn-color);
    --btn-noise: none;
  }

  .btn-outline:not(:-moz-any(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked)) {
    --btn-shadow: "";
    --btn-bg: rgba(0, 0, 0, 0);
    --btn-fg: var(--btn-color);
    --btn-border: var(--btn-color);
    --btn-noise: none;
  }

  .btn-outline:not(:is(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked)) {
    --btn-shadow: "";
    --btn-bg: rgba(0, 0, 0, 0);
    --btn-fg: var(--btn-color);
    --btn-border: var(--btn-color);
    --btn-noise: none;
  }

  @media (hover: none) {
    .btn-outline:hover:not(:-webkit-any(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked)) {
      --btn-shadow: "";
      --btn-bg: rgba(0, 0, 0, 0);
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }

    .btn-outline:hover:not(:-moz-any(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked)) {
      --btn-shadow: "";
      --btn-bg: rgba(0, 0, 0, 0);
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }

    .btn-outline:hover:not(:is(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked)) {
      --btn-shadow: "";
      --btn-bg: rgba(0, 0, 0, 0);
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
  }

  .btn-sm {
    --fontsize: .75rem;
    --btn-p: .75rem;
    --size: calc(var(--size-field, .25rem) * 8);
  }

  .btn-xs {
    --fontsize: .6875rem;
    --btn-p: .5rem;
    --size: calc(var(--size-field, .25rem) * 6);
  }

  .badge-primary {
    --badge-color: var(--color-primary);
    --badge-fg: var(--color-primary-content);
  }

  .badge-secondary {
    --badge-color: var(--color-secondary);
    --badge-fg: var(--color-secondary-content);
  }

  .badge-warning {
    --badge-color: var(--color-warning);
    --badge-fg: var(--color-warning-content);
  }

  .btn-accent {
    --btn-color: var(--color-accent);
    --btn-fg: var(--color-accent-content);
  }

  .btn-error {
    --btn-color: var(--color-error);
    --btn-fg: var(--color-error-content);
  }

  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }

  .btn-secondary {
    --btn-color: var(--color-secondary);
    --btn-fg: var(--color-secondary-content);
  }

  .btn-success {
    --btn-color: var(--color-success);
    --btn-fg: var(--color-success-content);
  }

  .btn-warning {
    --btn-color: var(--color-warning);
    --btn-fg: var(--color-warning-content);
  }

  .input-error {
    --input-color: var(--color-error);
  }

  .input-error:focus {
    --input-color: var(--color-error);
  }

  .input-error:focus-within {
    --input-color: var(--color-error);
  }

  .radio-error {
    --input-color: var(--color-error);
  }

  .select-error {
    --input-color: var(--color-error);
  }

  .select-error:focus {
    --input-color: var(--color-error);
  }

  .select-error:focus-within {
    --input-color: var(--color-error);
  }

  .textarea-error {
    --input-color: var(--color-error);
  }

  .textarea-error:focus {
    --input-color: var(--color-error);
  }

  .textarea-error:focus-within {
    --input-color: var(--color-error);
  }

  @media (hover: hover) {
    .group-hover\:bg-base-300:is(:where(.group):hover *) {
      background-color: var(--color-base-300);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:bg-base-200:hover {
      background-color: var(--color-base-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-base-300:hover {
      background-color: var(--color-base-300);
    }
  }

  @media (hover: hover) {
    .hover\:bg-base-content\/30:hover {
      background-color: var(--color-base-content);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-base-content\/30:hover {
        background-color: color-mix(in oklab, var(--color-base-content) 30%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/10:hover {
      background-color: var(--color-primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/10:hover {
        background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-base-content:hover {
      color: var(--color-base-content);
    }
  }

  @media (hover: hover) {
    .hover\:text-base-content\/70:hover {
      color: var(--color-base-content);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-base-content\/70:hover {
        color: color-mix(in oklab, var(--color-base-content) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:badge-primary:hover {
      --badge-color: var(--color-primary);
      --badge-fg: var(--color-primary-content);
    }
  }

  @media (hover: hover) {
    .hover\:btn-error:hover {
      --btn-color: var(--color-error);
      --btn-fg: var(--color-error-content);
    }
  }

  @media (hover: hover) {
    .hover\:btn-info:hover {
      --btn-color: var(--color-info);
      --btn-fg: var(--color-info-content);
    }
  }

  @media (hover: hover) {
    .hover\:btn-primary:hover {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
    }
  }

  @media (hover: hover) {
    .hover\:btn-secondary:hover {
      --btn-color: var(--color-secondary);
      --btn-fg: var(--color-secondary-content);
    }
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-primary:focus {
    --tw-ring-color: var(--color-primary);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (min-width: 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:static {
      position: static;
    }
  }

  @media (min-width: 64rem) {
    .lg\:inset-0 {
      inset: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (min-width: 64rem) {
    .lg\:ml-0 {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:translate-x-0 {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 96rem) {
    .\32 xl\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
}

.card-hover {
  transition: all .3s;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px -4px rgba(0, 0, 0, .1);
}

.search-highlight {
  color: #92400e;
  background-color: #fef3c7;
  border-radius: 2px;
  padding: 1px 3px;
}

.prose-code {
  color: #334155;
  background-color: #f1f5f9;
  border-radius: 3px;
  padding: 2px 4px;
  font-size: .875em;
}

.animate-fade-in {
  animation: .3s ease-in-out fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: .3s ease-out slideIn;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}

@keyframes rating {
  0%, 40% {
    filter: brightness(1.05) contrast(1.05);
    scale: 1.1;
  }
}

@keyframes skeleton {
  0% {
    background-position: 150%;
  }

  100% {
    background-position: -50%;
  }
}

@keyframes dropdown {
  0% {
    opacity: 0;
  }
}

@keyframes radio {
  0% {
    padding: 5px;
  }

  50% {
    padding: 3px;
  }
}

@keyframes toast {
  0% {
    opacity: 0;
    scale: .9;
  }

  100% {
    opacity: 1;
    scale: 1;
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=src_styles_globals_css_e59ae46c._.single.css.map*/