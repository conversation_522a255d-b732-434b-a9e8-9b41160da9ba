{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useAutoSave.ts"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface AutoSaveOptions {\r\n  delay?: number\r\n  enabled?: boolean\r\n  onSave?: (data: any) => Promise<void>\r\n  onError?: (error: Error) => void\r\n  storageKey?: string\r\n}\r\n\r\ninterface AutoSaveResult {\r\n  isSaving: boolean\r\n  lastSaved: Date | null\r\n  hasUnsavedChanges: boolean\r\n  save: () => Promise<void>\r\n  clearDraft: () => void\r\n  restoreDraft: () => any\r\n}\r\n\r\nexport function useAutoSave<T>(\r\n  data: T,\r\n  options: AutoSaveOptions = {}\r\n): AutoSaveResult {\r\n  const {\r\n    delay = 2000,\r\n    enabled = true,\r\n    onSave,\r\n    onError,\r\n    storageKey = 'draft',\r\n  } = options\r\n\r\n  const [isSaving, setIsSaving] = useState(false)\r\n  const [lastSaved, setLastSaved] = useState<Date | null>(null)\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)\r\n  const [initialData, setInitialData] = useState<T>(data)\r\n  \r\n  const timeoutRef = useRef<NodeJS.Timeout>()\r\n  const lastDataRef = useRef<T>(data)\r\n\r\n  // 保存到本地存储\r\n  const saveDraftToLocal = useCallback((draftData: T) => {\r\n    try {\r\n      localStorage.setItem(storageKey, JSON.stringify({\r\n        data: draftData,\r\n        timestamp: Date.now(),\r\n      }))\r\n    } catch (error) {\r\n      console.error('保存草稿到本地存储失败:', error)\r\n    }\r\n  }, [storageKey])\r\n\r\n  // 从本地存储恢复\r\n  const restoreDraft = useCallback(() => {\r\n    try {\r\n      const saved = localStorage.getItem(storageKey)\r\n      if (saved) {\r\n        const parsed = JSON.parse(saved)\r\n        return parsed.data\r\n      }\r\n    } catch (error) {\r\n      console.error('恢复草稿失败:', error)\r\n    }\r\n    return null\r\n  }, [storageKey])\r\n\r\n  // 清除草稿\r\n  const clearDraft = useCallback(() => {\r\n    try {\r\n      localStorage.removeItem(storageKey)\r\n      setHasUnsavedChanges(false)\r\n    } catch (error) {\r\n      console.error('清除草稿失败:', error)\r\n    }\r\n  }, [storageKey])\r\n\r\n  // 手动保存\r\n  const save = useCallback(async () => {\r\n    if (!onSave || isSaving) return\r\n\r\n    try {\r\n      setIsSaving(true)\r\n      await onSave(data)\r\n      setLastSaved(new Date())\r\n      setHasUnsavedChanges(false)\r\n      setInitialData(data)\r\n      clearDraft()\r\n      toast.success('保存成功')\r\n    } catch (error) {\r\n      const err = error instanceof Error ? error : new Error('保存失败')\r\n      if (onError) {\r\n        onError(err)\r\n      } else {\r\n        toast.error(err.message)\r\n      }\r\n    } finally {\r\n      setIsSaving(false)\r\n    }\r\n  }, [data, onSave, isSaving, onError, clearDraft])\r\n\r\n  // 自动保存逻辑\r\n  useEffect(() => {\r\n    if (!enabled || !onSave) return\r\n\r\n    // 检查数据是否发生变化\r\n    const hasChanged = JSON.stringify(data) !== JSON.stringify(lastDataRef.current)\r\n    \r\n    if (hasChanged) {\r\n      lastDataRef.current = data\r\n      setHasUnsavedChanges(true)\r\n      \r\n      // 保存到本地存储\r\n      saveDraftToLocal(data)\r\n      \r\n      // 清除之前的定时器\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current)\r\n      }\r\n      \r\n      // 设置新的定时器\r\n      timeoutRef.current = setTimeout(async () => {\r\n        if (isSaving) return\r\n        \r\n        try {\r\n          setIsSaving(true)\r\n          await onSave(data)\r\n          setLastSaved(new Date())\r\n          setHasUnsavedChanges(false)\r\n          setInitialData(data)\r\n          clearDraft()\r\n          toast.success('自动保存成功', { duration: 2000 })\r\n        } catch (error) {\r\n          const err = error instanceof Error ? error : new Error('自动保存失败')\r\n          if (onError) {\r\n            onError(err)\r\n          } else {\r\n            toast.error(err.message)\r\n          }\r\n        } finally {\r\n          setIsSaving(false)\r\n        }\r\n      }, delay)\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current)\r\n      }\r\n    }\r\n  }, [data, enabled, delay, onSave, onError, isSaving, saveDraftToLocal, clearDraft])\r\n\r\n  // 检查是否有未保存的更改\r\n  useEffect(() => {\r\n    const hasChanges = JSON.stringify(data) !== JSON.stringify(initialData)\r\n    setHasUnsavedChanges(hasChanges)\r\n  }, [data, initialData])\r\n\r\n  // 页面卸载时保存草稿\r\n  useEffect(() => {\r\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\r\n      if (hasUnsavedChanges) {\r\n        e.preventDefault()\r\n        e.returnValue = '有未保存的更改，确定要离开吗？'\r\n      }\r\n    }\r\n\r\n    window.addEventListener('beforeunload', handleBeforeUnload)\r\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload)\r\n  }, [hasUnsavedChanges])\r\n\r\n  return {\r\n    isSaving,\r\n    lastSaved,\r\n    hasUnsavedChanges,\r\n    save,\r\n    clearDraft,\r\n    restoreDraft,\r\n  }\r\n}\r\n\r\n// 专门用于提示词的自动保存 hook\r\nexport function usePromptAutoSave(\r\n  promptData: any,\r\n  options: {\r\n    enabled?: boolean\r\n    onSave?: (data: any) => Promise<void>\r\n    promptId?: string\r\n  } = {}\r\n) {\r\n  const { enabled = true, onSave, promptId } = options\r\n  \r\n  const storageKey = promptId ? `prompt-draft-${promptId}` : 'prompt-draft-new'\r\n  \r\n  return useAutoSave(promptData, {\r\n    enabled,\r\n    onSave,\r\n    storageKey,\r\n    delay: 3000, // 提示词自动保存延迟稍长\r\n  })\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAmBO,SAAS,YACd,IAAO;QACP,UAAA,iEAA2B,CAAC;;IAE5B,MAAM,EACJ,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,MAAM,EACN,OAAO,EACP,aAAa,OAAO,EACrB,GAAG;IAEJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAElD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAK;IAE9B,UAAU;IACV,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACpC,IAAI;gBACF,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;oBAC9C,MAAM;oBACN,WAAW,KAAK,GAAG;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;YAChC;QACF;oDAAG;QAAC;KAAW;IAEf,UAAU;IACV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC/B,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,OAAO,OAAO,IAAI;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B;YACA,OAAO;QACT;gDAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC7B,IAAI;gBACF,aAAa,UAAU,CAAC;gBACxB,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B;QACF;8CAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE;YACvB,IAAI,CAAC,UAAU,UAAU;YAEzB,IAAI;gBACF,YAAY;gBACZ,MAAM,OAAO;gBACb,aAAa,IAAI;gBACjB,qBAAqB;gBACrB,eAAe;gBACf;gBACA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,MAAM,MAAM,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;gBACvD,IAAI,SAAS;oBACX,QAAQ;gBACV,OAAO;oBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;gBACzB;YACF,SAAU;gBACR,YAAY;YACd;QACF;wCAAG;QAAC;QAAM;QAAQ;QAAU;QAAS;KAAW;IAEhD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,WAAW,CAAC,QAAQ;YAEzB,aAAa;YACb,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,OAAO;YAE9E,IAAI,YAAY;gBACd,YAAY,OAAO,GAAG;gBACtB,qBAAqB;gBAErB,UAAU;gBACV,iBAAiB;gBAEjB,WAAW;gBACX,IAAI,WAAW,OAAO,EAAE;oBACtB,aAAa,WAAW,OAAO;gBACjC;gBAEA,UAAU;gBACV,WAAW,OAAO,GAAG;6CAAW;wBAC9B,IAAI,UAAU;wBAEd,IAAI;4BACF,YAAY;4BACZ,MAAM,OAAO;4BACb,aAAa,IAAI;4BACjB,qBAAqB;4BACrB,eAAe;4BACf;4BACA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU;gCAAE,UAAU;4BAAK;wBAC3C,EAAE,OAAO,OAAO;4BACd,MAAM,MAAM,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;4BACvD,IAAI,SAAS;gCACX,QAAQ;4BACV,OAAO;gCACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;4BACzB;wBACF,SAAU;4BACR,YAAY;wBACd;oBACF;4CAAG;YACL;YAEA;yCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,aAAa,WAAW,OAAO;oBACjC;gBACF;;QACF;gCAAG;QAAC;QAAM;QAAS;QAAO;QAAQ;QAAS;QAAU;QAAkB;KAAW;IAElF,cAAc;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC;YAC3D,qBAAqB;QACvB;gCAAG;QAAC;QAAM;KAAY;IAEtB,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,mBAAmB;wBACrB,EAAE,cAAc;wBAChB,EAAE,WAAW,GAAG;oBAClB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YACxC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,gBAAgB;;QAC1D;gCAAG;QAAC;KAAkB;IAEtB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA9JgB;AAiKT,SAAS,kBACd,UAAe;QACf,UAAA,iEAII,CAAC;;IAEL,MAAM,EAAE,UAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAE7C,MAAM,aAAa,WAAW,AAAC,gBAAwB,OAAT,YAAa;IAE3D,OAAO,YAAY,YAAY;QAC7B;QACA;QACA;QACA,OAAO;IACT;AACF;IAlBgB;;QAYP", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useCopyToClipboard.ts"], "sourcesContent": ["import { useState, useCallback } from 'react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CopyOptions {\r\n  successMessage?: string\r\n  errorMessage?: string\r\n  showToast?: boolean\r\n  timeout?: number\r\n}\r\n\r\ninterface CopyResult {\r\n  isCopied: boolean\r\n  isLoading: boolean\r\n  copy: (text: string, options?: CopyOptions) => Promise<boolean>\r\n  reset: () => void\r\n}\r\n\r\nexport function useCopyToClipboard(): CopyResult {\r\n  const [isCopied, setIsCopied] = useState(false)\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  const copy = useCallback(async (text: string, options: CopyOptions = {}): Promise<boolean> => {\r\n    const {\r\n      successMessage = '已复制到剪贴板',\r\n      errorMessage = '复制失败',\r\n      showToast = true,\r\n      timeout = 2000,\r\n    } = options\r\n\r\n    if (!text || typeof text !== 'string') {\r\n      if (showToast) {\r\n        toast.error('没有内容可复制')\r\n      }\r\n      return false\r\n    }\r\n\r\n    setIsLoading(true)\r\n\r\n    try {\r\n      // 尝试使用现代 API\r\n      if (navigator.clipboard && window.isSecureContext) {\r\n        await navigator.clipboard.writeText(text)\r\n      } else {\r\n        // 降级到旧方法\r\n        const textArea = document.createElement('textarea')\r\n        textArea.value = text\r\n        textArea.style.position = 'fixed'\r\n        textArea.style.left = '-999999px'\r\n        textArea.style.top = '-999999px'\r\n        document.body.appendChild(textArea)\r\n        textArea.focus()\r\n        textArea.select()\r\n        \r\n        const successful = document.execCommand('copy')\r\n        document.body.removeChild(textArea)\r\n        \r\n        if (!successful) {\r\n          throw new Error('复制命令失败')\r\n        }\r\n      }\r\n\r\n      setIsCopied(true)\r\n      \r\n      if (showToast) {\r\n        toast.success(successMessage)\r\n      }\r\n\r\n      // 重置状态\r\n      setTimeout(() => {\r\n        setIsCopied(false)\r\n      }, timeout)\r\n\r\n      return true\r\n    } catch (error) {\r\n      console.error('复制失败:', error)\r\n      \r\n      if (showToast) {\r\n        toast.error(errorMessage)\r\n      }\r\n      \r\n      return false\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }, [])\r\n\r\n  const reset = useCallback(() => {\r\n    setIsCopied(false)\r\n    setIsLoading(false)\r\n  }, [])\r\n\r\n  return {\r\n    isCopied,\r\n    isLoading,\r\n    copy,\r\n    reset,\r\n  }\r\n}\r\n\r\n// 专门用于复制提示词的 hook\r\nexport function usePromptCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyPrompt = useCallback(async (prompt: { content: string; title: string }) => {\r\n    const success = await copy(prompt.content, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyPromptWithTitle = useCallback(async (prompt: { content: string; title: string }) => {\r\n    const textToCopy = `# ${prompt.title}\\n\\n${prompt.content}`\r\n    const success = await copy(textToCopy, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"（含标题）`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyPromptAsMarkdown = useCallback(async (prompt: { \r\n    content: string; \r\n    title: string; \r\n    description?: string;\r\n    tags?: { name: string }[];\r\n    category?: { name: string };\r\n  }) => {\r\n    let markdown = `# ${prompt.title}\\n\\n`\r\n    \r\n    if (prompt.description) {\r\n      markdown += `${prompt.description}\\n\\n`\r\n    }\r\n    \r\n    if (prompt.category) {\r\n      markdown += `**分类**: ${prompt.category.name}\\n\\n`\r\n    }\r\n    \r\n    if (prompt.tags && prompt.tags.length > 0) {\r\n      markdown += `**标签**: ${prompt.tags.map(t => t.name).join(', ')}\\n\\n`\r\n    }\r\n    \r\n    markdown += `## 内容\\n\\n\\`\\`\\`\\n${prompt.content}\\n\\`\\`\\``\r\n    \r\n    const success = await copy(markdown, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"（Markdown 格式）`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyPrompt,\r\n    copyPromptWithTitle,\r\n    copyPromptAsMarkdown,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}\r\n\r\n// 用于复制代码块的 hook\r\nexport function useCodeCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyCode = useCallback(async (code: string, language?: string) => {\r\n    const success = await copy(code, {\r\n      successMessage: `已复制${language ? ` ${language}` : ''} 代码`,\r\n      errorMessage: '复制代码失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyCode,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}\r\n\r\n// 用于复制链接的 hook\r\nexport function useLinkCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyLink = useCallback(async (url: string, title?: string) => {\r\n    const success = await copy(url, {\r\n      successMessage: title ? `已复制 \"${title}\" 链接` : '已复制链接',\r\n      errorMessage: '复制链接失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyCurrentUrl = useCallback(async () => {\r\n    const success = await copy(window.location.href, {\r\n      successMessage: '已复制当前页面链接',\r\n      errorMessage: '复制链接失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyLink,\r\n    copyCurrentUrl,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAgBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,eAAO;gBAAc,2EAAuB,CAAC;YACpE,MAAM,EACJ,iBAAiB,SAAS,EAC1B,eAAe,MAAM,EACrB,YAAY,IAAI,EAChB,UAAU,IAAI,EACf,GAAG;YAEJ,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,IAAI,WAAW;oBACb,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBACA,OAAO;YACT;YAEA,aAAa;YAEb,IAAI;gBACF,aAAa;gBACb,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;oBACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACtC,OAAO;oBACL,SAAS;oBACT,MAAM,WAAW,SAAS,aAAa,CAAC;oBACxC,SAAS,KAAK,GAAG;oBACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;oBAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;oBACtB,SAAS,KAAK,CAAC,GAAG,GAAG;oBACrB,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,SAAS,KAAK;oBACd,SAAS,MAAM;oBAEf,MAAM,aAAa,SAAS,WAAW,CAAC;oBACxC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAE1B,IAAI,CAAC,YAAY;wBACf,MAAM,IAAI,MAAM;oBAClB;gBACF;gBAEA,YAAY;gBAEZ,IAAI,WAAW;oBACb,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;gBAEA,OAAO;gBACP;4DAAW;wBACT,YAAY;oBACd;2DAAG;gBAEH,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBAEvB,IAAI,WAAW;oBACb,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBAEA,OAAO;YACT,SAAU;gBACR,aAAa;YACf;QACF;+CAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,YAAY;YACZ,aAAa;QACf;gDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GAhFgB;AAmFT,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACpC,MAAM,UAAU,MAAM,KAAK,OAAO,OAAO,EAAE;gBACzC,gBAAgB,AAAC,WAAuB,OAAb,OAAO,KAAK,EAAC;gBACxC,cAAc;YAChB;YAEA,OAAO;QACT;gDAAG;QAAC;KAAK;IAET,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO;YAC7C,MAAM,aAAa,AAAC,KAAuB,OAAnB,OAAO,KAAK,EAAC,QAAqB,OAAf,OAAO,OAAO;YACzD,MAAM,UAAU,MAAM,KAAK,YAAY;gBACrC,gBAAgB,AAAC,WAAuB,OAAb,OAAO,KAAK,EAAC;gBACxC,cAAc;YAChB;YAEA,OAAO;QACT;yDAAG;QAAC;KAAK;IAET,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAO9C,IAAI,WAAW,AAAC,KAAiB,OAAb,OAAO,KAAK,EAAC;YAEjC,IAAI,OAAO,WAAW,EAAE;gBACtB,YAAY,AAAC,GAAqB,OAAnB,OAAO,WAAW,EAAC;YACpC;YAEA,IAAI,OAAO,QAAQ,EAAE;gBACnB,YAAY,AAAC,WAA+B,OAArB,OAAO,QAAQ,CAAC,IAAI,EAAC;YAC9C;YAEA,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBACzC,YAAY,AAAC,WAAkD,OAAxC,OAAO,IAAI,CAAC,GAAG;uEAAC,CAAA,IAAK,EAAE,IAAI;sEAAE,IAAI,CAAC,OAAM;YACjE;YAEA,YAAY,AAAC,iBAAkC,OAAf,OAAO,OAAO,EAAC;YAE/C,MAAM,UAAU,MAAM,KAAK,UAAU;gBACnC,gBAAgB,AAAC,WAAuB,OAAb,OAAO,KAAK,EAAC;gBACxC,cAAc;YAChB;YAEA,OAAO;QACT;0DAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IA5DgB;;QACwB;;;AA8DjC,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAAO,MAAc;YAChD,MAAM,UAAU,MAAM,KAAK,MAAM;gBAC/B,gBAAgB,AAAC,MAAoC,OAA/B,WAAW,AAAC,IAAY,OAAT,YAAa,IAAG;gBACrD,cAAc;YAChB;YAEA,OAAO;QACT;4CAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;IACF;AACF;IAjBgB;;QACwB;;;AAmBjC,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAAO,KAAa;YAC/C,MAAM,UAAU,MAAM,KAAK,KAAK;gBAC9B,gBAAgB,QAAQ,AAAC,QAAa,OAAN,OAAM,UAAQ;gBAC9C,cAAc;YAChB;YAEA,OAAO;QACT;4CAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACjC,MAAM,UAAU,MAAM,KAAK,OAAO,QAAQ,CAAC,IAAI,EAAE;gBAC/C,gBAAgB;gBAChB,cAAc;YAChB;YAEA,OAAO;QACT;kDAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IA3BgB;;QACwB", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/index.ts"], "sourcesContent": ["export { useAutoSave, usePromptAutoSave } from './useAutoSave'\nexport { \n  useCopyToClipboard, \n  usePromptCopy, \n  useCodeCopy, \n  useLinkCopy \n} from './useCopyToClipboard'"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptCard.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { Prompt } from '~/types'\r\nimport { api } from '~/trpc/react'\r\nimport { usePromptCopy } from '~/hooks'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface PromptCardProps {\r\n  prompt: Prompt\r\n  onEdit?: (prompt: Prompt) => void\r\n  onDelete?: (promptId: string) => void\r\n  onUse?: (prompt: Prompt) => void\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n}\r\n\r\nexport const PromptCard = ({\r\n  prompt,\r\n  onEdit,\r\n  onDelete,\r\n  onUse,\r\n  showCategory = true,\r\n  showActions = true,\r\n}: PromptCardProps) => {\r\n  const [showFullContent, setShowFullContent] = useState(false)\r\n  \r\n  // 复制功能\r\n  const { copyPrompt, isCopied, isLoading: isCopying } = usePromptCopy()\r\n\r\n  // 更新使用次数\r\n  const updateUsageMutation = api.prompts.updateUsage.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('使用次数已更新')\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 复制内容到剪贴板\r\n  const copyToClipboard = async () => {\r\n    const success = await copyPrompt(prompt)\r\n    \r\n    if (success) {\r\n      // 更新使用次数\r\n      updateUsageMutation.mutate({ id: prompt.id })\r\n      \r\n      // 触发使用回调\r\n      if (onUse) {\r\n        onUse(prompt)\r\n      }\r\n    }\r\n  }\r\n\r\n  // 处理编辑\r\n  const handleEdit = () => {\r\n    if (onEdit) {\r\n      onEdit(prompt)\r\n    }\r\n  }\r\n\r\n  // 处理删除\r\n  const handleDelete = () => {\r\n    if (onDelete) {\r\n      onDelete(prompt.id)\r\n    }\r\n  }\r\n\r\n  // 格式化时间\r\n  const formatDate = (date: Date) => {\r\n    return new Intl.DateTimeFormat('zh-CN', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    }).format(date)\r\n  }\r\n\r\n  // 截断内容\r\n  const truncateContent = (content: string, maxLength: number = 150) => {\r\n    if (content.length <= maxLength) return content\r\n    return content.substring(0, maxLength) + '...'\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      exit={{ opacity: 0, y: -20 }}\r\n      whileHover={{ y: -4 }}\r\n      transition={{ duration: 0.2 }}\r\n      className=\"card bg-base-100 shadow-md hover:shadow-xl transition-shadow duration-200 group\"\r\n    >\r\n      <div className=\"card-body p-4\">\r\n        {/* 头部信息 */}\r\n        <div className=\"flex items-start justify-between mb-3\">\r\n          <div className=\"flex-1 min-w-0\">\r\n            <h3 className=\"card-title text-lg font-semibold text-base-content truncate\">\r\n              {prompt.title}\r\n            </h3>\r\n            \r\n            {/* 分类和标签 */}\r\n            <div className=\"flex items-center gap-2 mt-1\">\r\n              {showCategory && prompt.category && (\r\n                <Link\r\n                  href={`/categories/${prompt.category.id}`}\r\n                  className=\"badge badge-sm hover:badge-primary transition-colors\"\r\n                  style={{ backgroundColor: prompt.category.color + '20', color: prompt.category.color }}\r\n                >\r\n                  {prompt.category.name}\r\n                </Link>\r\n              )}\r\n              \r\n              {prompt.tags?.slice(0, 3).map((tag) => (\r\n                <Link\r\n                  key={tag.id}\r\n                  href={`/tags/${encodeURIComponent(tag.name)}`}\r\n                  className=\"badge badge-sm badge-ghost hover:badge-primary transition-colors\"\r\n                >\r\n                  {tag.name}\r\n                </Link>\r\n              ))}\r\n              \r\n              {prompt.tags && prompt.tags.length > 3 && (\r\n                <span className=\"badge badge-sm badge-ghost\">\r\n                  +{prompt.tags.length - 3}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 收藏状态 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            {prompt.isFavorite && (\r\n              <div className=\"tooltip tooltip-left\" data-tip=\"已收藏\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  className=\"w-4 h-4 text-warning\"\r\n                >\r\n                  <path d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\" />\r\n                </svg>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 内容预览 */}\r\n        <div className=\"mb-3\">\r\n          <p className=\"text-sm text-base-content/80 leading-relaxed\">\r\n            {showFullContent ? prompt.content : truncateContent(prompt.content)}\r\n          </p>\r\n          \r\n          {prompt.content.length > 150 && (\r\n            <button\r\n              onClick={() => setShowFullContent(!showFullContent)}\r\n              className=\"text-xs text-primary hover:text-primary-focus mt-1\"\r\n            >\r\n              {showFullContent ? '收起' : '展开'}\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* 底部信息 */}\r\n        <div className=\"flex items-center justify-between text-xs text-base-content/60\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"flex items-center space-x-1\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                />\r\n              </svg>\r\n              <span>{prompt.usageCount || 0} 次使用</span>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-1\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                />\r\n              </svg>\r\n              <span>{formatDate(prompt.updatedAt)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <AnimatePresence>\r\n          {showActions && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: 10 }}\r\n              transition={{ duration: 0.2 }}\r\n              className=\"card-actions justify-end mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n            >\r\n              <div className=\"flex items-center space-x-2\">\r\n              {/* 复制按钮 */}\r\n              <motion.button\r\n                onClick={copyToClipboard}\r\n                disabled={isCopying}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className={`btn btn-sm btn-ghost ${isCopied ? 'btn-success' : 'hover:btn-primary'}`}\r\n                title={isCopied ? '已复制' : '复制内容'}\r\n              >\r\n                {isCopying ? (\r\n                  <span className=\"loading loading-spinner loading-xs\"></span>\r\n                ) : isCopied ? (\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                ) : (\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                    />\r\n                  </svg>\r\n                )}\r\n              </motion.button>\r\n\r\n              {/* 编辑按钮 */}\r\n              <motion.button\r\n                onClick={handleEdit}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost hover:btn-secondary\"\r\n                title=\"编辑\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              {/* 删除按钮 */}\r\n              <motion.button\r\n                onClick={handleDelete}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost hover:btn-error\"\r\n                title=\"删除\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              {/* 详情链接 */}\r\n              <motion.div\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                <Link\r\n                  href={`/prompts/${prompt.id}`}\r\n                  className=\"btn btn-sm btn-ghost hover:btn-info\"\r\n                  title=\"查看详情\"\r\n                >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z\"\r\n                  />\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                  />\r\n                </svg>\r\n              </Link>\r\n              </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AACA;;;AARA;;;;;;;AAmBO,MAAM,aAAa;QAAC,EACzB,MAAM,EACN,MAAM,EACN,QAAQ,EACR,KAAK,EACL,eAAe,IAAI,EACnB,cAAc,IAAI,EACF;QA2FH;;IA1Fb,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,OAAO;IACP,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAEnE,SAAS;IACT,MAAM,sBAAsB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;QAC9D,SAAS;2DAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;2DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,WAAW;IACX,MAAM,kBAAkB;QACtB,MAAM,UAAU,MAAM,WAAW;QAEjC,IAAI,SAAS;YACX,SAAS;YACT,oBAAoB,MAAM,CAAC;gBAAE,IAAI,OAAO,EAAE;YAAC;YAE3C,SAAS;YACT,IAAI,OAAO;gBACT,MAAM;YACR;QACF;IACF;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,OAAO;IACP,MAAM,kBAAkB,SAAC;YAAiB,6EAAoB;QAC5D,IAAI,QAAQ,MAAM,IAAI,WAAW,OAAO;QACxC,OAAO,QAAQ,SAAS,CAAC,GAAG,aAAa;IAC3C;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,OAAO,KAAK;;;;;;8CAIf,6LAAC;oCAAI,WAAU;;wCACZ,gBAAgB,OAAO,QAAQ,kBAC9B,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,AAAC,eAAiC,OAAnB,OAAO,QAAQ,CAAC,EAAE;4CACvC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK,GAAG;gDAAM,OAAO,OAAO,QAAQ,CAAC,KAAK;4CAAC;sDAEpF,OAAO,QAAQ,CAAC,IAAI;;;;;;yCAIxB,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,AAAC,SAAqC,OAA7B,mBAAmB,IAAI,IAAI;gDAC1C,WAAU;0DAET,IAAI,IAAI;+CAJJ,IAAI,EAAE;;;;;wCAQd,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,6LAAC;4CAAK,WAAU;;gDAA6B;gDACzC,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;sCAO/B,6LAAC;4BAAI,WAAU;sCACZ,OAAO,UAAU,kBAChB,6LAAC;gCAAI,WAAU;gCAAuB,YAAS;0CAC7C,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCACV,kBAAkB,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO;;;;;;wBAGnE,OAAO,OAAO,CAAC,MAAM,GAAG,qBACvB,6LAAC;4BACC,SAAS,IAAM,mBAAmB,CAAC;4BACnC,WAAU;sCAET,kBAAkB,OAAO;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;;4CAAM,OAAO,UAAU,IAAI;4CAAE;;;;;;;;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDAAM,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC,4LAAA,CAAA,kBAAe;8BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,UAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAW,AAAC,wBAAsE,OAA/C,WAAW,gBAAgB;oCAC9D,OAAO,WAAW,QAAQ;8CAEzB,0BACC,6LAAC;wCAAK,WAAU;;;;;mFACd,yBACF,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;iGAIN,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAOV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,AAAC,YAAqB,OAAV,OAAO,EAAE;wCAC3B,WAAU;wCACV,OAAM;kDAER,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;;8DAEV,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;8DAEJ,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYtB;GA3Ua;;QAW4C,qIAAA,CAAA,gBAAa;;;KAXzD", "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptGrid.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { Prompt } from '~/types'\r\nimport { PromptCard } from './PromptCard'\r\n\r\ninterface PromptGridProps {\r\n  prompts: Prompt[]\r\n  isLoading?: boolean\r\n  onEdit?: (prompt: Prompt) => void\r\n  onDelete?: (promptId: string) => void\r\n  onUse?: (prompt: Prompt) => void\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n  emptyMessage?: string\r\n  columns?: 1 | 2 | 3 | 4 | 6\r\n}\r\n\r\nexport const PromptGrid = ({\r\n  prompts,\r\n  isLoading = false,\r\n  onEdit,\r\n  onDelete,\r\n  onUse,\r\n  showCategory = true,\r\n  showActions = true,\r\n  emptyMessage = '暂无提示词',\r\n  columns = 3,\r\n}: PromptGridProps) => {\r\n  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([])\r\n\r\n  // 响应式列数配置\r\n  const getGridCols = () => {\r\n    const colsMap = {\r\n      1: 'grid-cols-1',\r\n      2: 'grid-cols-1 md:grid-cols-2',\r\n      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\r\n      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',\r\n      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',\r\n    }\r\n    return colsMap[columns] || colsMap[3]\r\n  }\r\n\r\n  // 渲染加载骨架屏\r\n  const renderLoadingSkeleton = () => {\r\n    return (\r\n      <div className={`grid ${getGridCols()} gap-4`}>\r\n        {Array.from({ length: 6 }, (_, i) => (\r\n          <div key={i} className=\"card bg-base-100 shadow-md\">\r\n            <div className=\"card-body p-4\">\r\n              {/* 标题骨架 */}\r\n              <div className=\"h-6 bg-base-300 rounded animate-pulse mb-3\"></div>\r\n              \r\n              {/* 分类和标签骨架 */}\r\n              <div className=\"flex items-center gap-2 mb-3\">\r\n                <div className=\"h-4 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 w-12 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 w-14 bg-base-300 rounded animate-pulse\"></div>\r\n              </div>\r\n              \r\n              {/* 内容骨架 */}\r\n              <div className=\"space-y-2 mb-3\">\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse w-3/4\"></div>\r\n              </div>\r\n              \r\n              {/* 底部信息骨架 */}\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"h-3 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n                  <div className=\"h-3 w-20 bg-base-300 rounded animate-pulse\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染空状态\r\n  const renderEmptyState = () => {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex flex-col items-center justify-center py-16 text-center\"\r\n      >\r\n        <motion.div\r\n          initial={{ scale: 0 }}\r\n          animate={{ scale: 1 }}\r\n          transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\r\n          className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mb-4\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-12 h-12 text-base-content/40\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H9a2.25 2.25 0 01-2.25-2.25V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 002.25-2.25V9.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08M15.75 18v-2.625c0-.621.504-1.125 1.125-1.125h.375a1.125 1.125 0 011.125 1.125V18\"\r\n            />\r\n          </svg>\r\n        </motion.div>\r\n        <motion.h3\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"text-lg font-medium text-base-content mb-2\"\r\n        >\r\n          {emptyMessage}\r\n        </motion.h3>\r\n        <motion.p\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"text-base-content/70 mb-6\"\r\n        >\r\n          创建你的第一个提示词开始使用吧\r\n        </motion.p>\r\n        <motion.a\r\n          href=\"/prompts/new\"\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"btn btn-primary\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-5 h-5\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 4.5v15m7.5-7.5h-15\"\r\n            />\r\n          </svg>\r\n          新建提示词\r\n        </motion.a>\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  // 处理批量选择\r\n  const handleSelectPrompt = (promptId: string) => {\r\n    setSelectedPrompts(prev => \r\n      prev.includes(promptId)\r\n        ? prev.filter(id => id !== promptId)\r\n        : [...prev, promptId]\r\n    )\r\n  }\r\n\r\n  // 清空选择\r\n  const clearSelection = () => {\r\n    setSelectedPrompts([])\r\n  }\r\n\r\n  // 全选/取消全选\r\n  const toggleSelectAll = () => {\r\n    if (selectedPrompts.length === prompts.length) {\r\n      clearSelection()\r\n    } else {\r\n      setSelectedPrompts(prompts.map(p => p.id))\r\n    }\r\n  }\r\n\r\n  if (isLoading) {\r\n    return renderLoadingSkeleton()\r\n  }\r\n\r\n  if (!prompts || prompts.length === 0) {\r\n    return renderEmptyState()\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* 批量操作栏 */}\r\n      <AnimatePresence>\r\n        {selectedPrompts.length > 0 && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -20 }}\r\n            className=\"bg-base-200 rounded-lg p-4 flex items-center justify-between\"\r\n          >\r\n            <div className=\"flex items-center space-x-4\">\r\n              <span className=\"text-sm text-base-content\">\r\n                已选择 {selectedPrompts.length} 个提示词\r\n              </span>\r\n              <motion.button\r\n                onClick={clearSelection}\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost\"\r\n              >\r\n                取消选择\r\n              </motion.button>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-warning\"\r\n              >\r\n                批量编辑\r\n              </motion.button>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-error\"\r\n              >\r\n                批量删除\r\n              </motion.button>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 提示词网格 */}\r\n      <motion.div\r\n        layout\r\n        className={`grid ${getGridCols()} gap-4`}\r\n      >\r\n        <AnimatePresence mode=\"popLayout\">\r\n          {prompts.map((prompt, index) => (\r\n            <motion.div\r\n              key={prompt.id}\r\n              layout\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0.8 }}\r\n              transition={{ \r\n                duration: 0.2,\r\n                delay: index * 0.05,\r\n                layout: { duration: 0.2 }\r\n              }}\r\n              className=\"relative\"\r\n            >\r\n              {/* 选择框 */}\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"absolute top-2 left-2 z-10\"\r\n              >\r\n                <input\r\n                  type=\"checkbox\"\r\n                  className=\"checkbox checkbox-primary checkbox-sm\"\r\n                  checked={selectedPrompts.includes(prompt.id)}\r\n                  onChange={() => handleSelectPrompt(prompt.id)}\r\n                />\r\n              </motion.div>\r\n              \r\n              <PromptCard\r\n                prompt={prompt}\r\n                onEdit={onEdit}\r\n                onDelete={onDelete}\r\n                onUse={onUse}\r\n                showCategory={showCategory}\r\n                showActions={showActions}\r\n              />\r\n            </motion.div>\r\n          ))}\r\n        </AnimatePresence>\r\n      </motion.div>\r\n\r\n      {/* 加载更多按钮 */}\r\n      {prompts.length > 0 && (\r\n        <div className=\"flex justify-center pt-8\">\r\n          <button className=\"btn btn-outline\">\r\n            加载更多\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;;;AALA;;;;AAmBO,MAAM,aAAa;QAAC,EACzB,OAAO,EACP,YAAY,KAAK,EACjB,MAAM,EACN,QAAQ,EACR,KAAK,EACL,eAAe,IAAI,EACnB,cAAc,IAAI,EAClB,eAAe,OAAO,EACtB,UAAU,CAAC,EACK;;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,UAAU;IACV,MAAM,cAAc;QAClB,MAAM,UAAU;YACd,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,EAAE;IACvC;IAEA,UAAU;IACV,MAAM,wBAAwB;QAC5B,qBACE,6LAAC;YAAI,WAAW,AAAC,QAAqB,OAAd,eAAc;sBACnC,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mBAvBb;;;;;;;;;;IA+BlB;IAEA,QAAQ;IACR,MAAM,mBAAmB;QACvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;;8BAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;oBAAE;oBACpB,SAAS;wBAAE,OAAO;oBAAE;oBACpB,YAAY;wBAAE,OAAO;wBAAK,MAAM;wBAAU,WAAW;oBAAI;oBACzD,WAAU;8BAEV,cAAA,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;;;;;;8BAIR,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BAET;;;;;;8BAEH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BACX;;;;;;8BAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,MAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,WAAU;;sCAEV,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAEA;;;;;;;;;;;;;IAKd;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,YACzB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,mBAAmB,EAAE;IACvB;IAEA,UAAU;IACV,MAAM,kBAAkB;QACtB,IAAI,gBAAgB,MAAM,KAAK,QAAQ,MAAM,EAAE;YAC7C;QACF,OAAO;YACL,mBAAmB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC1C;IACF;IAEA,IAAI,WAAW;QACb,OAAO;IACT;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gBAAgB,MAAM,GAAG,mBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;wCAA4B;wCACrC,gBAAgB,MAAM;wCAAC;;;;;;;8CAE9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,MAAM;gBACN,WAAW,AAAC,QAAqB,OAAd,eAAc;0BAEjC,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,MAAM;4BACN,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,QAAQ;oCAAE,UAAU;gCAAI;4BAC1B;4BACA,WAAU;;8CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,WAAU;8CAEV,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;wCAC3C,UAAU,IAAM,mBAAmB,OAAO,EAAE;;;;;;;;;;;8CAIhD,6LAAC,8IAAA,CAAA,aAAU;oCACT,QAAQ;oCACR,QAAQ;oCACR,UAAU;oCACV,OAAO;oCACP,cAAc;oCACd,aAAa;;;;;;;2BAhCV,OAAO,EAAE;;;;;;;;;;;;;;;YAwCrB,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAO,WAAU;8BAAkB;;;;;;;;;;;;;;;;;AAO9C;GA7Qa;KAAA", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/categories/CategoryForm.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CategoryFormProps {\r\n  category?: Category | null\r\n  onClose: () => void\r\n}\r\n\r\nconst DEFAULT_COLORS = [\r\n  '#3b82f6', // blue\r\n  '#ef4444', // red\r\n  '#10b981', // green\r\n  '#f59e0b', // yellow\r\n  '#8b5cf6', // purple\r\n  '#ec4899', // pink\r\n  '#06b6d4', // cyan\r\n  '#84cc16', // lime\r\n  '#f97316', // orange\r\n  '#6b7280', // gray\r\n]\r\n\r\nconst DEFAULT_ICONS = [\r\n  '📁', '📂', '📋', '📝', '💡', '🎯', '🚀', '⭐', '🔥', '💎',\r\n  '🎨', '🔧', '⚙️', '📊', '🎮', '💻', '📱', '🌟', '🎪', '🎭',\r\n]\r\n\r\nexport const CategoryForm = ({ category, onClose }: CategoryFormProps) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    description: '',\r\n    color: DEFAULT_COLORS[0],\r\n    icon: '',\r\n    parentId: null as string | null,\r\n  })\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({})\r\n  const [showColorPicker, setShowColorPicker] = useState(false)\r\n  const [showIconPicker, setShowIconPicker] = useState(false)\r\n  const [customColor, setCustomColor] = useState('')\r\n\r\n  // 获取所有分类（用于父分类选择）\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 创建分类\r\n  const createMutation = api.categories.create.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类创建成功')\r\n      onClose()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('创建失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 更新分类\r\n  const updateMutation = api.categories.update.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类更新成功')\r\n      onClose()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 初始化表单数据\r\n  useEffect(() => {\r\n    if (category) {\r\n      setFormData({\r\n        name: category.name,\r\n        description: category.description || '',\r\n        color: category.color,\r\n        icon: category.icon || '',\r\n        parentId: category.parentId,\r\n      })\r\n    }\r\n  }, [category])\r\n\r\n  // 表单验证\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {}\r\n\r\n    if (!formData.name.trim()) {\r\n      newErrors.name = '分类名称不能为空'\r\n    } else if (formData.name.length > 50) {\r\n      newErrors.name = '分类名称不能超过50个字符'\r\n    }\r\n\r\n    if (formData.description && formData.description.length > 200) {\r\n      newErrors.description = '描述不能超过200个字符'\r\n    }\r\n\r\n    if (!formData.color) {\r\n      newErrors.color = '请选择分类颜色'\r\n    }\r\n\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  // 处理提交\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n\r\n    if (!validateForm()) {\r\n      return\r\n    }\r\n\r\n    const submitData = {\r\n      ...formData,\r\n      description: formData.description || undefined,\r\n      icon: formData.icon || undefined,\r\n      parentId: formData.parentId || undefined,\r\n    }\r\n\r\n    if (category) {\r\n      updateMutation.mutate({\r\n        id: category.id,\r\n        ...submitData,\r\n      })\r\n    } else {\r\n      createMutation.mutate(submitData)\r\n    }\r\n  }\r\n\r\n  // 处理颜色选择\r\n  const handleColorSelect = (color: string) => {\r\n    setFormData(prev => ({ ...prev, color }))\r\n    setShowColorPicker(false)\r\n  }\r\n\r\n  // 处理自定义颜色\r\n  const handleCustomColor = () => {\r\n    if (customColor && /^#[0-9A-F]{6}$/i.test(customColor)) {\r\n      handleColorSelect(customColor)\r\n      setCustomColor('')\r\n    } else {\r\n      toast.error('请输入有效的颜色值 (如: #FF0000)')\r\n    }\r\n  }\r\n\r\n  // 处理图标选择\r\n  const handleIconSelect = (icon: string) => {\r\n    setFormData(prev => ({ ...prev, icon }))\r\n    setShowIconPicker(false)\r\n  }\r\n\r\n  // 获取可用的父分类选项\r\n  const getParentOptions = () => {\r\n    if (!categories) return []\r\n    \r\n    return categories.filter(cat => {\r\n      // 排除当前分类和其子分类\r\n      if (category) {\r\n        return cat.id !== category.id && cat.parentId !== category.id\r\n      }\r\n      return true\r\n    })\r\n  }\r\n\r\n  const isLoading = createMutation.isLoading || updateMutation.isLoading\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\"\r\n      onClick={(e) => e.target === e.currentTarget && onClose()}\r\n    >\r\n      <motion.div\r\n        initial={{ scale: 0.9, opacity: 0 }}\r\n        animate={{ scale: 1, opacity: 1 }}\r\n        exit={{ scale: 0.9, opacity: 0 }}\r\n        className=\"bg-base-100 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto\"\r\n      >\r\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\r\n          {/* 表单头部 */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-semibold text-base-content\">\r\n              {category ? '编辑分类' : '新建分类'}\r\n            </h2>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"btn btn-ghost btn-sm btn-square\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* 分类名称 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">分类名称 *</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={formData.name}\r\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\r\n              className={`input input-bordered ${errors.name ? 'input-error' : ''}`}\r\n              placeholder=\"输入分类名称\"\r\n              maxLength={50}\r\n            />\r\n            {errors.name && (\r\n              <label className=\"label\">\r\n                <span className=\"label-text-alt text-error\">{errors.name}</span>\r\n              </label>\r\n            )}\r\n          </div>\r\n\r\n          {/* 分类描述 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">描述</span>\r\n            </label>\r\n            <textarea\r\n              value={formData.description}\r\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n              className={`textarea textarea-bordered h-20 ${errors.description ? 'textarea-error' : ''}`}\r\n              placeholder=\"输入分类描述（可选）\"\r\n              maxLength={200}\r\n            />\r\n            {errors.description && (\r\n              <label className=\"label\">\r\n                <span className=\"label-text-alt text-error\">{errors.description}</span>\r\n              </label>\r\n            )}\r\n            <label className=\"label\">\r\n              <span className=\"label-text-alt\">{formData.description.length}/200</span>\r\n            </label>\r\n          </div>\r\n\r\n          {/* 父分类选择 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">父分类</span>\r\n            </label>\r\n            <select\r\n              value={formData.parentId || ''}\r\n              onChange={(e) => setFormData(prev => ({ ...prev, parentId: e.target.value || null }))}\r\n              className=\"select select-bordered\"\r\n            >\r\n              <option value=\"\">无（顶级分类）</option>\r\n              {getParentOptions().map(cat => (\r\n                <option key={cat.id} value={cat.id}>\r\n                  {cat.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* 颜色和图标选择 */}\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            {/* 颜色选择 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text\">颜色 *</span>\r\n              </label>\r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowColorPicker(!showColorPicker)}\r\n                  className=\"w-full h-12 rounded-lg border-2 border-base-300 flex items-center justify-center\"\r\n                  style={{ backgroundColor: formData.color }}\r\n                >\r\n                  <span className=\"text-white font-medium\">{formData.color}</span>\r\n                </button>\r\n                \r\n                <AnimatePresence>\r\n                  {showColorPicker && (\r\n                    <motion.div\r\n                      initial={{ opacity: 0, scale: 0.9 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.9 }}\r\n                      className=\"absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-3 shadow-lg z-10\"\r\n                    >\r\n                      <div className=\"grid grid-cols-5 gap-2 mb-3\">\r\n                        {DEFAULT_COLORS.map(color => (\r\n                          <button\r\n                            key={color}\r\n                            type=\"button\"\r\n                            onClick={() => handleColorSelect(color)}\r\n                            className=\"w-8 h-8 rounded border-2 border-base-300 hover:scale-110 transition-transform\"\r\n                            style={{ backgroundColor: color }}\r\n                          />\r\n                        ))}\r\n                      </div>\r\n                      <div className=\"flex space-x-2\">\r\n                        <input\r\n                          type=\"text\"\r\n                          value={customColor}\r\n                          onChange={(e) => setCustomColor(e.target.value)}\r\n                          className=\"input input-xs flex-1\"\r\n                          placeholder=\"#FF0000\"\r\n                        />\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={handleCustomColor}\r\n                          className=\"btn btn-xs btn-primary\"\r\n                        >\r\n                          应用\r\n                        </button>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 图标选择 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text\">图标</span>\r\n              </label>\r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowIconPicker(!showIconPicker)}\r\n                  className=\"w-full h-12 border-2 border-base-300 rounded-lg flex items-center justify-center text-2xl hover:bg-base-200\"\r\n                >\r\n                  {formData.icon || '选择图标'}\r\n                </button>\r\n                \r\n                <AnimatePresence>\r\n                  {showIconPicker && (\r\n                    <motion.div\r\n                      initial={{ opacity: 0, scale: 0.9 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.9 }}\r\n                      className=\"absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-3 shadow-lg z-10\"\r\n                    >\r\n                      <div className=\"grid grid-cols-5 gap-2 mb-2\">\r\n                        {DEFAULT_ICONS.map(icon => (\r\n                          <button\r\n                            key={icon}\r\n                            type=\"button\"\r\n                            onClick={() => handleIconSelect(icon)}\r\n                            className=\"w-8 h-8 flex items-center justify-center text-lg hover:bg-base-200 rounded\"\r\n                          >\r\n                            {icon}\r\n                          </button>\r\n                        ))}\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleIconSelect('')}\r\n                        className=\"btn btn-xs btn-ghost w-full\"\r\n                      >\r\n                        清除图标\r\n                      </button>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 预览 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">预览</span>\r\n            </label>\r\n            <div className=\"flex items-center space-x-3 p-3 bg-base-200 rounded-lg\">\r\n              <div\r\n                className=\"w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold\"\r\n                style={{ backgroundColor: formData.color }}\r\n              >\r\n                {formData.icon || formData.name.charAt(0)}\r\n              </div>\r\n              <div>\r\n                <p className=\"font-medium\">{formData.name || '分类名称'}</p>\r\n                {formData.description && (\r\n                  <p className=\"text-sm text-base-content/70 truncate\">\r\n                    {formData.description}\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 操作按钮 */}\r\n          <div className=\"flex justify-end space-x-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"btn btn-ghost\"\r\n              disabled={isLoading}\r\n            >\r\n              取消\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"btn btn-primary\"\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading && <span className=\"loading loading-spinner loading-sm\"></span>}\r\n              {category ? '更新' : '创建'}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </motion.div>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;;;AANA;;;;;AAaA,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAK;IAAM;IACrD;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CACvD;AAEM,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE,OAAO,EAAqB;;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kBAAkB;IAClB,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;wDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;wDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ,YAAY;oBACV,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW,IAAI;oBACrC,OAAO,SAAS,KAAK;oBACrB,MAAM,SAAS,IAAI,IAAI;oBACvB,UAAU,SAAS,QAAQ;gBAC7B;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YACpC,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,aAAa;YACjB,GAAG,QAAQ;YACX,aAAa,SAAS,WAAW,IAAI;YACrC,MAAM,SAAS,IAAI,IAAI;YACvB,UAAU,SAAS,QAAQ,IAAI;QACjC;QAEA,IAAI,UAAU;YACZ,eAAe,MAAM,CAAC;gBACpB,IAAI,SAAS,EAAE;gBACf,GAAG,UAAU;YACf;QACF,OAAO;YACL,eAAe,MAAM,CAAC;QACxB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAM,CAAC;QACvC,mBAAmB;IACrB;IAEA,UAAU;IACV,MAAM,oBAAoB;QACxB,IAAI,eAAe,kBAAkB,IAAI,CAAC,cAAc;YACtD,kBAAkB;YAClB,eAAe;QACjB,OAAO;YACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;QACtC,kBAAkB;IACpB;IAEA,aAAa;IACb,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,cAAc;YACd,IAAI,UAAU;gBACZ,OAAO,IAAI,EAAE,KAAK,SAAS,EAAE,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC/D;YACA,OAAO;QACT;IACF;IAEA,MAAM,YAAY,eAAe,SAAS,IAAI,eAAe,SAAS;IAEtE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAU;QACV,SAAS,CAAC,IAAM,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI;kBAEhD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAClC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,MAAM;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAC/B,WAAU;sBAEV,cAAA,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,SAAS;;;;;;0CAEvB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACvE,WAAW,AAAC,wBAAwD,OAAjC,OAAO,IAAI,GAAG,gBAAgB;gCACjE,aAAY;gCACZ,WAAW;;;;;;4BAEZ,OAAO,IAAI,kBACV,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;kCAM9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCACC,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC9E,WAAW,AAAC,mCAA6E,OAA3C,OAAO,WAAW,GAAG,mBAAmB;gCACtF,aAAY;gCACZ,WAAW;;;;;;4BAEZ,OAAO,WAAW,kBACjB,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAA6B,OAAO,WAAW;;;;;;;;;;;0CAGnE,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;;wCAAkB,SAAS,WAAW,CAAC,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAKlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCACC,OAAO,SAAS,QAAQ,IAAI;gCAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;wCAAK,CAAC;gCACnF,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,mBAAmB,GAAG,CAAC,CAAA,oBACtB,6LAAC;4CAAoB,OAAO,IAAI,EAAE;sDAC/B,IAAI,IAAI;2CADE,IAAI,EAAE;;;;;;;;;;;;;;;;;kCAQzB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,mBAAmB,CAAC;gDACnC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;0DAEzC,cAAA,6LAAC;oDAAK,WAAU;8DAA0B,SAAS,KAAK;;;;;;;;;;;0DAG1D,6LAAC,4LAAA,CAAA,kBAAe;0DACb,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAC/B,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACZ,eAAe,GAAG,CAAC,CAAA,sBAClB,6LAAC;oEAEC,MAAK;oEACL,SAAS,IAAM,kBAAkB;oEACjC,WAAU;oEACV,OAAO;wEAAE,iBAAiB;oEAAM;mEAJ3B;;;;;;;;;;sEAQX,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC9C,WAAU;oEACV,aAAY;;;;;;8EAEd,6LAAC;oEACC,MAAK;oEACL,SAAS;oEACT,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,kBAAkB,CAAC;gDAClC,WAAU;0DAET,SAAS,IAAI,IAAI;;;;;;0DAGpB,6LAAC,4LAAA,CAAA,kBAAe;0DACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAC/B,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oEAEC,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EAET;mEALI;;;;;;;;;;sEASX,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,SAAS,KAAK;wCAAC;kDAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;kDAEzC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAe,SAAS,IAAI,IAAI;;;;;;4CAC5C,SAAS,WAAW,kBACnB,6LAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,UAAU;;oCAET,2BAAa,6LAAC;wCAAK,WAAU;;;;;;oCAC7B,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjC;GAnYa;KAAA", "debugId": null}}, {"offset": {"line": 2394, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/modals/ConfirmDeleteModal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface ConfirmDeleteModalProps {\r\n  isOpen: boolean\r\n  onClose: () => void\r\n  onConfirm: () => void\r\n  title?: string\r\n  message?: string\r\n  itemName?: string\r\n  itemType?: string\r\n  isDangerous?: boolean\r\n  isLoading?: boolean\r\n  requireConfirmation?: boolean\r\n}\r\n\r\nexport const ConfirmDeleteModal = ({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  itemName,\r\n  itemType = '项目',\r\n  isDangerous = false,\r\n  isLoading = false,\r\n  requireConfirmation = false,\r\n}: ConfirmDeleteModalProps) => {\r\n  const [confirmText, setConfirmText] = useState('')\r\n  const [isConfirmed, setIsConfirmed] = useState(false)\r\n\r\n  const defaultTitle = title || `删除${itemType}`\r\n  const defaultMessage = message || `确定要删除这个${itemType}吗？此操作无法撤销。`\r\n  const confirmationText = itemName || '删除'\r\n  const canConfirm = !requireConfirmation || confirmText === confirmationText\r\n\r\n  const handleConfirm = () => {\r\n    if (canConfirm) {\r\n      onConfirm()\r\n    }\r\n  }\r\n\r\n  const handleClose = () => {\r\n    setConfirmText('')\r\n    setIsConfirmed(false)\r\n    onClose()\r\n  }\r\n\r\n  if (!isOpen) return null\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\r\n        onClick={handleClose}\r\n      >\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          exit={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n          className=\"bg-base-100 rounded-lg shadow-xl w-full max-w-md overflow-hidden\"\r\n          onClick={(e) => e.stopPropagation()}\r\n        >\r\n          {/* 头部 */}\r\n          <div className=\"bg-base-200 border-b border-base-300 p-4\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* 警告图标 */}\r\n              <div className={`p-2 rounded-full ${isDangerous ? 'bg-error/20' : 'bg-warning/20'}`}>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-6 h-6 ${isDangerous ? 'text-error' : 'text-warning'}`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n              \r\n              <div>\r\n                <h3 className=\"text-lg font-semibold text-base-content\">{defaultTitle}</h3>\r\n                <p className=\"text-sm text-base-content/70\">请确认您的操作</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 内容 */}\r\n          <div className=\"p-4 space-y-4\">\r\n            {/* 主要消息 */}\r\n            <div className=\"space-y-2\">\r\n              <p className=\"text-base-content\">{defaultMessage}</p>\r\n              \r\n              {itemName && (\r\n                <div className=\"bg-base-200 rounded-lg p-3\">\r\n                  <p className=\"text-sm text-base-content/70 mb-1\">要删除的{itemType}:</p>\r\n                  <p className=\"font-medium text-base-content break-words\">{itemName}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 危险警告 */}\r\n            {isDangerous && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"bg-error/10 border border-error/20 rounded-lg p-3\"\r\n              >\r\n                <div className=\"flex items-start space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-5 h-5 text-error flex-shrink-0 mt-0.5\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                    />\r\n                  </svg>\r\n                  <div className=\"text-sm\">\r\n                    <p className=\"font-medium text-error\">危险操作</p>\r\n                    <p className=\"text-error/80 mt-1\">\r\n                      此操作将永久删除数据，无法恢复。如果您确定要继续，请在下方输入确认信息。\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            {/* 确认输入 */}\r\n            {requireConfirmation && (\r\n              <div className=\"space-y-2\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">\r\n                    请输入 <code className=\"bg-base-200 px-2 py-1 rounded text-sm\">{confirmationText}</code> 以确认删除:\r\n                  </span>\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={confirmText}\r\n                  onChange={(e) => setConfirmText(e.target.value)}\r\n                  className=\"input input-bordered w-full\"\r\n                  placeholder={`输入 \"${confirmationText}\"`}\r\n                  autoComplete=\"off\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* 附加确认选项 */}\r\n            {isDangerous && (\r\n              <div className=\"form-control\">\r\n                <label className=\"label cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={isConfirmed}\r\n                    onChange={(e) => setIsConfirmed(e.target.checked)}\r\n                    className=\"checkbox checkbox-error\"\r\n                  />\r\n                  <span className=\"label-text ml-2\">我理解此操作的后果并确认删除</span>\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 底部操作 */}\r\n          <div className=\"bg-base-200 border-t border-base-300 p-4 flex items-center justify-end space-x-3\">\r\n            <motion.button\r\n              onClick={handleClose}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"btn btn-ghost\"\r\n              disabled={isLoading}\r\n            >\r\n              取消\r\n            </motion.button>\r\n            \r\n            <motion.button\r\n              onClick={handleConfirm}\r\n              whileHover={{ scale: canConfirm ? 1.05 : 1 }}\r\n              whileTap={{ scale: canConfirm ? 0.95 : 1 }}\r\n              disabled={!canConfirm || isLoading || (isDangerous && !isConfirmed)}\r\n              className={`btn ${isDangerous ? 'btn-error' : 'btn-warning'}`}\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  删除中...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                    />\r\n                  </svg>\r\n                  确认删除\r\n                </>\r\n              )}\r\n            </motion.button>\r\n          </div>\r\n        </motion.div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAkBO,MAAM,qBAAqB;QAAC,EACjC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACR,WAAW,IAAI,EACf,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,sBAAsB,KAAK,EACH;;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,SAAS,AAAC,KAAa,OAAT;IACnC,MAAM,iBAAiB,WAAW,AAAC,UAAkB,OAAT,UAAS;IACrD,MAAM,mBAAmB,YAAY;IACrC,MAAM,aAAa,CAAC,uBAAuB,gBAAgB;IAE3D,MAAM,gBAAgB;QACpB,IAAI,YAAY;YACd;QACF;IACF;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,eAAe;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAK,GAAG;gBAAG;gBACzC,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAG,GAAG;gBAAE;gBACtC,MAAM;oBAAE,SAAS;oBAAG,OAAO;oBAAK,GAAG;gBAAG;gBACtC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;gBAC1D,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,AAAC,oBAAiE,OAA9C,cAAc,gBAAgB;8CAChE,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAW,AAAC,WAAsD,OAA5C,cAAc,eAAe;kDAEnD,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAKR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;oCAEjC,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAoC;oDAAK;oDAAS;;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;4BAM/D,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;4BASzC,qCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;;gDAAyB;8DACnC,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;gDAAwB;;;;;;;;;;;;kDAGzF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,aAAa,AAAC,OAAuB,OAAjB,kBAAiB;wCACrC,cAAa;;;;;;;;;;;;4BAMlB,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;4CAChD,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,UAAU;0CACX;;;;;;0CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO,aAAa,OAAO;gCAAE;gCAC3C,UAAU;oCAAE,OAAO,aAAa,OAAO;gCAAE;gCACzC,UAAU,CAAC,cAAc,aAAc,eAAe,CAAC;gCACvD,WAAW,AAAC,OAAgD,OAA1C,cAAc,cAAc;0CAE7C,0BACC;;sDACE,6LAAC;4CAAK,WAAU;;;;;;wCAA4C;;iEAI9D;;sDACE,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;wCAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GAjNa;KAAA", "debugId": null}}, {"offset": {"line": 2844, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/categories/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { api } from '~/trpc/react'\r\nimport { PromptGrid } from '~/components/prompts/PromptGrid'\r\nimport { CategoryForm } from '~/components/categories/CategoryForm'\r\nimport { ConfirmDeleteModal } from '~/components/modals/ConfirmDeleteModal'\r\nimport { toast } from 'react-hot-toast'\r\n\r\nexport default function CategoryDetailPage() {\r\n  const params = useParams()\r\n  const router = useRouter()\r\n  const categoryId = params.id as string\r\n\r\n  const [isEditing, setIsEditing] = useState(false)\r\n  const [isDeleting, setIsDeleting] = useState(false)\r\n  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'usage'>('updated')\r\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\r\n\r\n  // 获取分类详情\r\n  const { data: category, isLoading, refetch } = api.categories.getById.useQuery({\r\n    id: categoryId,\r\n  })\r\n\r\n  // 获取分类下的提示词\r\n  const { data: prompts, isLoading: promptsLoading } = api.prompts.getByCategory.useQuery({\r\n    categoryId,\r\n    sortBy,\r\n    sortOrder,\r\n  })\r\n\r\n  // 删除分类\r\n  const deleteMutation = api.categories.delete.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类删除成功')\r\n      router.push('/categories')\r\n    },\r\n    onError: (error) => {\r\n      toast.error('删除失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 处理删除\r\n  const handleDelete = () => {\r\n    if (category) {\r\n      deleteMutation.mutate({ id: category.id })\r\n    }\r\n  }\r\n\r\n  // 处理表单关闭\r\n  const handleFormClose = () => {\r\n    setIsEditing(false)\r\n    refetch()\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"animate-pulse space-y-6\">\r\n          {/* 返回按钮骨架 */}\r\n          <div className=\"h-10 w-20 bg-base-300 rounded\"></div>\r\n          \r\n          {/* 分类头部骨架 */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"w-16 h-16 bg-base-300 rounded-lg\"></div>\r\n              <div className=\"space-y-2\">\r\n                <div className=\"h-8 w-48 bg-base-300 rounded\"></div>\r\n                <div className=\"h-4 w-32 bg-base-300 rounded\"></div>\r\n              </div>\r\n            </div>\r\n            <div className=\"h-10 w-24 bg-base-300 rounded\"></div>\r\n          </div>\r\n          \r\n          {/* 统计信息骨架 */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n            {Array.from({ length: 3 }, (_, i) => (\r\n              <div key={i} className=\"bg-base-100 rounded-lg p-4 space-y-3\">\r\n                <div className=\"h-4 w-20 bg-base-300 rounded\"></div>\r\n                <div className=\"h-8 w-16 bg-base-300 rounded\"></div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!category) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-16 h-16 mx-auto text-base-content/50 mb-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n            />\r\n          </svg>\r\n          <h2 className=\"text-xl font-semibold text-base-content mb-2\">分类不存在</h2>\r\n          <p className=\"text-base-content/70 mb-4\">该分类可能已被删除或不存在</p>\r\n          <Link href=\"/categories\" className=\"btn btn-primary\">\r\n            返回分类列表\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-8 space-y-6\">\r\n      {/* 返回按钮 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n      >\r\n        <Link href=\"/categories\" className=\"btn btn-ghost btn-sm\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-4 h-4\"\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18\" />\r\n          </svg>\r\n          返回分类列表\r\n        </Link>\r\n      </motion.div>\r\n\r\n      {/* 分类头部 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div className=\"flex items-center space-x-4\">\r\n          {/* 分类图标 */}\r\n          <div\r\n            className=\"w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-2xl shadow-lg\"\r\n            style={{ backgroundColor: category.color }}\r\n          >\r\n            {category.icon || category.name.charAt(0)}\r\n          </div>\r\n\r\n          {/* 分类信息 */}\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-base-content\">{category.name}</h1>\r\n            {category.description && (\r\n              <p className=\"text-base-content/70 mt-1\">{category.description}</p>\r\n            )}\r\n            <div className=\"flex items-center space-x-4 mt-2 text-sm text-base-content/60\">\r\n              <span>{category.promptCount || 0} 个提示词</span>\r\n              <span>使用 {category.usageCount || 0} 次</span>\r\n              <span>更新于 {new Date(category.updatedAt).toLocaleDateString('zh-CN')}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={() => setIsEditing(true)}\r\n            className=\"btn btn-ghost btn-sm\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n              />\r\n            </svg>\r\n            编辑\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setIsDeleting(true)}\r\n            className=\"btn btn-ghost btn-sm text-error hover:btn-error\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n              />\r\n            </svg>\r\n            删除\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* 统计信息 */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-title\">提示词数量</div>\r\n          <div className=\"stat-value text-primary\">{category.promptCount || 0}</div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-title\">使用次数</div>\r\n          <div className=\"stat-value text-secondary\">{category.usageCount || 0}</div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-title\">平均使用率</div>\r\n          <div className=\"stat-value text-accent\">\r\n            {category.promptCount ? Math.round((category.usageCount || 0) / category.promptCount) : 0}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* 提示词列表 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.4 }}\r\n        className=\"space-y-4\"\r\n      >\r\n        <div className=\"flex items-center justify-between\">\r\n          <h2 className=\"text-xl font-semibold text-base-content\">提示词列表</h2>\r\n          \r\n          <div className=\"flex items-center space-x-3\">\r\n            {/* 排序选择 */}\r\n            <select\r\n              value={`${sortBy}-${sortOrder}`}\r\n              onChange={(e) => {\r\n                const [newSortBy, newSortOrder] = e.target.value.split('-')\r\n                setSortBy(newSortBy as 'updated' | 'created' | 'usage')\r\n                setSortOrder(newSortOrder as 'asc' | 'desc')\r\n              }}\r\n              className=\"select select-bordered select-sm\"\r\n            >\r\n              <option value=\"updated-desc\">最近更新</option>\r\n              <option value=\"created-desc\">最新创建</option>\r\n              <option value=\"usage-desc\">使用最多</option>\r\n              <option value=\"updated-asc\">最早更新</option>\r\n              <option value=\"created-asc\">最早创建</option>\r\n              <option value=\"usage-asc\">使用最少</option>\r\n            </select>\r\n\r\n            {/* 新建提示词按钮 */}\r\n            <Link\r\n              href={`/prompts/new?categoryId=${category.id}`}\r\n              className=\"btn btn-primary btn-sm\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\r\n              </svg>\r\n              新建提示词\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 提示词网格 */}\r\n        <PromptGrid\r\n          prompts={prompts || []}\r\n          loading={promptsLoading}\r\n          showCategory={false}\r\n          emptyMessage=\"该分类下还没有提示词\"\r\n          emptyAction={\r\n            <Link\r\n              href={`/prompts/new?categoryId=${category.id}`}\r\n              className=\"btn btn-primary\"\r\n            >\r\n              创建第一个提示词\r\n            </Link>\r\n          }\r\n        />\r\n      </motion.div>\r\n\r\n      {/* 编辑表单 */}\r\n      <AnimatePresence>\r\n        {isEditing && (\r\n          <CategoryForm\r\n            category={category}\r\n            onClose={handleFormClose}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 删除确认对话框 */}\r\n      <AnimatePresence>\r\n        {isDeleting && (\r\n          <ConfirmDeleteModal\r\n            isOpen={isDeleting}\r\n            onClose={() => setIsDeleting(false)}\r\n            onConfirm={handleDelete}\r\n            title=\"删除分类\"\r\n            message={`确定要删除分类 \"${category.name}\" 吗？删除后该分类下的所有提示词将移至\"未分类\"。`}\r\n            type=\"warning\"\r\n            confirmText=\"删除\"\r\n            isLoading={deleteMutation.isLoading}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,EAAE;IAE5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,SAAS;IACT,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC7E,IAAI;IACN;IAEA,YAAY;IACZ,MAAM,EAAE,MAAM,OAAO,EAAE,WAAW,cAAc,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtF;QACA;QACA;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;8DAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;;QACA,OAAO;8DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,eAAe,MAAM,CAAC;gBAAE,IAAI,SAAS,EAAE;YAAC;QAC1C;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,aAAa;QACb;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;;;;;;;;;;;;IAStB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAc,WAAU;kCAAkB;;;;;;;;;;;;;;;;;IAM7D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;0BAE5B,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAc,WAAU;;sCACjC,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,GAAE;;;;;;;;;;;wBACjD;;;;;;;;;;;;0BAMV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;0CAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;0CAIzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwC,SAAS,IAAI;;;;;;oCAClE,SAAS,WAAW,kBACnB,6LAAC;wCAAE,WAAU;kDAA6B,SAAS,WAAW;;;;;;kDAEhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAM,SAAS,WAAW,IAAI;oDAAE;;;;;;;0DACjC,6LAAC;;oDAAK;oDAAI,SAAS,UAAU,IAAI;oDAAE;;;;;;;0DACnC,6LAAC;;oDAAK;oDAAK,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;0CAIR,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA2B,SAAS,WAAW,IAAI;;;;;;;;;;;;kCAGpE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA6B,SAAS,UAAU,IAAI;;;;;;;;;;;;kCAGrE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CACZ,SAAS,WAAW,GAAG,KAAK,KAAK,CAAC,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,SAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;0BAM9F,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAExD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;wCACpB,UAAU,CAAC;4CACT,MAAM,CAAC,WAAW,aAAa,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;4CACvD,UAAU;4CACV,aAAa;wCACf;wCACA,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;kDAI5B,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,AAAC,2BAAsC,OAAZ,SAAS,EAAE;wCAC5C,WAAU;;0DAEV,6LAAC;gDACC,OAAM;gDACN,MAAK;gDACL,SAAQ;gDACR,aAAa;gDACb,QAAO;gDACP,WAAU;0DAEV,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,GAAE;;;;;;;;;;;4CACjD;;;;;;;;;;;;;;;;;;;kCAOZ,6LAAC,8IAAA,CAAA,aAAU;wBACT,SAAS,WAAW,EAAE;wBACtB,SAAS;wBACT,cAAc;wBACd,cAAa;wBACb,2BACE,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,AAAC,2BAAsC,OAAZ,SAAS,EAAE;4BAC5C,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,mJAAA,CAAA,eAAY;oBACX,UAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,qJAAA,CAAA,qBAAkB;oBACjB,QAAQ;oBACR,SAAS,IAAM,cAAc;oBAC7B,WAAW;oBACX,OAAM;oBACN,SAAS,AAAC,YAAyB,OAAd,SAAS,IAAI,EAAC;oBACnC,MAAK;oBACL,aAAY;oBACZ,WAAW,eAAe,SAAS;;;;;;;;;;;;;;;;;AAM/C;GA5UwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}