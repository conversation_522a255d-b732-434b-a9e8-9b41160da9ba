{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "fc8a3973c9d33596add7383eca84a43b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d94bf3b135e43219ab7f85913a74f1fbfbe8e66162d6a9ad74001f33df7b333e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d7dba641c8d7d525b00a6b6fee0affde9b3c75b135d150262d7fe88659a9b87e"}}}, "instrumentation": null, "functions": {}}