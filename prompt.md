请帮我开发一款个人使用，精美的现代化界面，全中文的提示词管理工具，具体要求如下：

**技术栈要求：**

### 前端技术栈
- **核心框架**: Next.js 15
- **开发语言**: TypeScript
- **UI样式系统**: Tailwind CSS V4
- **UI组件库**: daisyUI
- **状态管理**: Zustand
- **数据获取**: tRPC
- **编辑器**: @uiw/react-textarea-code-editor
- **动画库**: Framer Motion
- **图标库**: Font Awesome

### 后端技术栈
- **服务框架**: Next.js API Routes
- **API架构**: tRPC路由器
- **数据库ORM**: Prisma
- **主数据库**: Supabase PostgreSQL
- **用户认证**: Supabase Auth
- **实时功能**: Supabase Realtime

### 开发工具链
- **代码质量**:
  - ESLint
  - Prettier
- **Git工作流**:
  - Husky + lint-staged + Commitlint
- **测试工具**:
  - 单元测试: Vitest
  - 组件测试: React Testing Library + Jest DOM
  - E2E测试: Playwright
- **部署平台**:
  - 前端部署: Vercel
  - 数据库服务: Supabase
  - 监控: Sentry + Vercel Analytics

### 界面语言
- 中文简体

**核心功能：**
1. **提示词展示**：
   - 以响应式卡片网格布局展示提示词列表
   - 每个卡片显示：标题、简短描述、分类标签、使用次数
   - 支持卡片悬停效果和点击交互

2. **详情查看**：
   - 点击卡片弹出模态框或跳转详情页
   - 显示完整提示词内容、创建时间、最后修改时间
   - 支持内容格式化显示（如代码高亮）

3. **一键复制**：
   - 每个提示词卡片和详情页都有复制按钮
   - 复制成功后显示Toast提示
   - 复制完整提示词内容到剪贴板

4. **分类管理**：
   - 直观的分类导航，左侧边栏分类目录树
   - 支持创建、编辑、删除分类
   - 分类筛选功能，可按分类查看提示词
   - 分类颜色标识和图标支持

5. **搜索功能**：
   - 实时搜索框，支持按标题、内容、标签模糊搜索
   - 搜索结果高亮显示匹配关键词
   - 搜索历史记录功能

6. **编辑功能**：
   - 支持在线编辑提示词标题、内容、分类、标签
   - 提供Markdown编辑器
   - 编辑时自动保存草稿功能

7. **新增功能**：
   - 提供"添加新提示词"按钮和表单
   - 支持批量导入提示词（JSON格式）
   - 表单验证和错误提示

8. **统计功能**：
   - 每个提示词卡片显示使用次数徽章
   - 点击复制时自动增加使用计数

9. **UI界面设计**：
   - 简约现代：简洁的布局，充足的空间感
   - 优雅色彩：使用 daisyUI 的组件，搭配多彩柔和色（不要渐变）
   - 深度交互：丰富的悬停效果和微动画
   - 响应式优先：完美适配各种设备尺寸
   - 视觉层次：清晰的视觉层次和信息优先级

**技术要求：**
- 使用现代前端框架
- 响应式设计，支持移动端
- 数据持久化存储（Supabase PostgreSQL数据库）
- 良好的用户体验和界面设计
- 代码结构清晰，遵循最佳实践
- 支持 Vercel 无服务器部署
- 集成 Supabase 实时数据同步

**交付物：**
- 完整的前端应用代码
- Supabase 数据库表结构设计
- Vercel 部署配置文件
- 环境变量配置说明
- 使用说明文档
- Supabase 配置和 API 集成代码

**开发要求：**
1. 创建完整的 Supabase 数据库表结构和 RLS 策略
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
5. 包含错误处理和数据验证
6. 提供完整的CRUD操作功能
7. 配置 Vercel 自动部署和环境变量
8. 集成 Supabase Auth 用户认证系统
9. 实现数据实时同步功能
10. 应用可通过 Vercel 提供的公网地址访问

请按照任务管理流程创建详细的开发计划和任务列表，并逐步实现所有功能。