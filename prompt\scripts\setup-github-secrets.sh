#!/bin/bash

# GitHub Secrets 设置脚本
# 使用方法: ./scripts/setup-github-secrets.sh [repository]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 GitHub CLI 是否已安装
if ! command -v gh &> /dev/null; then
    print_error "GitHub CLI 未安装"
    print_info "请访问 https://cli.github.com/ 安装 GitHub CLI"
    exit 1
fi

# 检查是否已登录 GitHub
if ! gh auth status &> /dev/null; then
    print_error "未登录 GitHub"
    print_info "请运行: gh auth login"
    exit 1
fi

# 获取仓库信息
REPO=${1:-$(gh repo view --json nameWithOwner --jq .nameWithOwner)}
if [[ -z "$REPO" ]]; then
    print_error "无法获取仓库信息"
    print_info "请确保在 Git 仓库中运行此脚本，或提供仓库名称"
    exit 1
fi

print_info "设置 GitHub Secrets for $REPO"

# 设置 Secret 的函数
set_secret() {
    local name=$1
    local description=$2
    local required=${3:-false}
    
    echo ""
    print_info "设置 $name"
    print_info "描述: $description"
    
    # 检查是否已存在
    if gh secret list --repo "$REPO" | grep -q "^$name"; then
        print_warning "$name 已存在"
        read -p "是否覆盖现有值？ (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "跳过 $name"
            return
        fi
    fi
    
    # 获取用户输入
    if [[ "$name" == *"SECRET"* ]] || [[ "$name" == *"PASSWORD"* ]] || [[ "$name" == *"KEY"* ]] || [[ "$name" == *"TOKEN"* ]]; then
        read -s -p "请输入 $name 的值: " value
        echo ""
    else
        read -p "请输入 $name 的值: " value
    fi
    
    # 验证必需字段
    if [[ "$required" == "true" ]] && [[ -z "$value" ]]; then
        print_error "$name 是必需的，不能为空"
        return 1
    fi
    
    # 设置 Secret
    if [[ -n "$value" ]]; then
        if echo "$value" | gh secret set "$name" --repo "$REPO"; then
            print_success "$name 设置成功"
        else
            print_error "设置 $name 失败"
            return 1
        fi
    else
        print_info "$name 跳过（空值）"
    fi
}

# 设置必需的 Secrets
print_info "========== 必需的 GitHub Secrets =========="

# Vercel 相关
set_secret "VERCEL_TOKEN" "Vercel API Token (从 Vercel Account Settings 获取)" true
set_secret "VERCEL_ORG_ID" "Vercel 组织 ID (从 .vercel/project.json 获取)" true
set_secret "VERCEL_PROJECT_ID" "Vercel 项目 ID (从 .vercel/project.json 获取)" true

# 数据库相关
set_secret "DATABASE_URL" "PostgreSQL 数据库连接字符串" true

# Supabase 相关
set_secret "NEXT_PUBLIC_SUPABASE_URL" "Supabase 项目 URL" true
set_secret "NEXT_PUBLIC_SUPABASE_ANON_KEY" "Supabase 匿名密钥" true
set_secret "SUPABASE_SERVICE_ROLE_KEY" "Supabase 服务角色密钥" true

# 认证相关
set_secret "NEXTAUTH_SECRET" "NextAuth.js 密钥" true

# 询问是否设置可选的 Secrets
echo ""
read -p "是否设置可选的 GitHub Secrets？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "========== 可选的 GitHub Secrets =========="
    
    # 监控和分析
    set_secret "SENTRY_DSN" "Sentry 错误监控 DSN" false
    set_secret "GOOGLE_ANALYTICS_ID" "Google Analytics ID" false
    set_secret "HOTJAR_ID" "Hotjar ID" false
    
    # 通知相关
    set_secret "SLACK_WEBHOOK_URL" "Slack Webhook URL (用于部署通知)" false
    set_secret "DISCORD_WEBHOOK_URL" "Discord Webhook URL (用于部署通知)" false
    
    # 邮件通知
    read -p "是否设置邮件通知？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        set_secret "EMAIL_USERNAME" "邮件服务器用户名" false
        set_secret "EMAIL_PASSWORD" "邮件服务器密码" false
        set_secret "EMAIL_FROM" "发件人邮箱" false
        set_secret "EMAIL_TO" "收件人邮箱" false
    fi
    
    # 安全相关
    set_secret "CSRF_SECRET" "CSRF 保护密钥" false
    set_secret "ENCRYPTION_KEY" "数据加密密钥" false
    set_secret "HEALTH_CHECK_TOKEN" "健康检查令牌" false
    
    # 第三方服务
    set_secret "OPENAI_API_KEY" "OpenAI API Key" false
    set_secret "ANTHROPIC_API_KEY" "Anthropic API Key" false
    
    # 缓存相关
    set_secret "REDIS_URL" "Redis 连接 URL" false
    
    # 文件存储
    set_secret "AWS_ACCESS_KEY_ID" "AWS Access Key ID" false
    set_secret "AWS_SECRET_ACCESS_KEY" "AWS Secret Access Key" false
    set_secret "AWS_REGION" "AWS Region" false
    set_secret "AWS_BUCKET_NAME" "AWS S3 Bucket Name" false
fi

# 显示当前设置的 Secrets
print_info "========== 当前设置的 GitHub Secrets =========="
gh secret list --repo "$REPO"

# 验证 Vercel 配置
print_info "========== 验证 Vercel 配置 =========="
if [[ -f ".vercel/project.json" ]]; then
    print_info "从 .vercel/project.json 读取的配置:"
    cat .vercel/project.json | jq .
    
    # 提取并验证 ID
    ORG_ID=$(cat .vercel/project.json | jq -r .orgId)
    PROJECT_ID=$(cat .vercel/project.json | jq -r .projectId)
    
    print_info "组织 ID: $ORG_ID"
    print_info "项目 ID: $PROJECT_ID"
    
    # 验证 Secrets 中的 ID 是否匹配
    if gh secret list --repo "$REPO" | grep -q "VERCEL_ORG_ID"; then
        print_success "VERCEL_ORG_ID 已设置"
    else
        print_warning "VERCEL_ORG_ID 未设置"
    fi
    
    if gh secret list --repo "$REPO" | grep -q "VERCEL_PROJECT_ID"; then
        print_success "VERCEL_PROJECT_ID 已设置"
    else
        print_warning "VERCEL_PROJECT_ID 未设置"
    fi
else
    print_warning ".vercel/project.json 不存在"
    print_info "请运行 'vercel link' 链接项目"
fi

# 检查 GitHub Actions 工作流
print_info "========== 检查 GitHub Actions 工作流 =========="
if [[ -f ".github/workflows/deploy.yml" ]]; then
    print_success "GitHub Actions 工作流文件存在"
    
    # 检查工作流语法
    if gh workflow view deploy.yml --repo "$REPO" &> /dev/null; then
        print_success "工作流语法正确"
    else
        print_warning "工作流语法可能有问题"
    fi
else
    print_warning "GitHub Actions 工作流文件不存在"
    print_info "请确保 .github/workflows/deploy.yml 文件存在"
fi

# 测试 GitHub Actions
print_info "========== 测试 GitHub Actions =========="
read -p "是否触发测试工作流？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "触发测试工作流..."
    
    # 获取当前分支
    CURRENT_BRANCH=$(git branch --show-current)
    
    # 创建测试提交
    echo "# Test CI/CD Setup" > test-ci-cd.md
    echo "This is a test commit to trigger GitHub Actions workflow." >> test-ci-cd.md
    echo "Created at: $(date)" >> test-ci-cd.md
    
    git add test-ci-cd.md
    git commit -m "test: trigger CI/CD workflow"
    git push origin "$CURRENT_BRANCH"
    
    print_success "测试提交已推送"
    print_info "请检查 GitHub Actions 页面查看工作流状态"
    print_info "URL: https://github.com/$REPO/actions"
    
    # 等待一段时间后检查状态
    sleep 10
    
    # 获取最新的工作流运行状态
    LATEST_RUN=$(gh run list --repo "$REPO" --limit 1 --json databaseId --jq '.[0].databaseId')
    if [[ -n "$LATEST_RUN" ]]; then
        print_info "最新工作流运行 ID: $LATEST_RUN"
        gh run view "$LATEST_RUN" --repo "$REPO"
    fi
    
    # 清理测试文件
    git rm test-ci-cd.md
    git commit -m "chore: clean up test file"
    git push origin "$CURRENT_BRANCH"
fi

print_success "GitHub Secrets 设置完成！"

# 显示后续步骤
print_info "========== 后续步骤 =========="
print_info "1. 检查 GitHub Actions 工作流状态"
print_info "2. 验证部署是否成功"
print_info "3. 检查 Vercel 部署日志"
print_info "4. 测试应用功能"
print_info "5. 设置监控和告警"

# 显示有用的命令
print_info "========== 有用的命令 =========="
print_info "  查看 Secrets: gh secret list --repo $REPO"
print_info "  查看工作流: gh workflow list --repo $REPO"
print_info "  查看运行历史: gh run list --repo $REPO"
print_info "  查看运行详情: gh run view [run-id] --repo $REPO"
print_info "  查看运行日志: gh run view [run-id] --log --repo $REPO"

# 显示重要提醒
print_info "========== 重要提醒 =========="
print_warning "1. 请定期轮换 API 密钥和令牌"
print_warning "2. 不要在代码中硬编码敏感信息"
print_warning "3. 限制 GitHub Actions 的权限"
print_warning "4. 监控 Secrets 的使用情况"
print_warning "5. 备份重要的配置信息"