-- 提示词管理工具 - Supabase RLS 策略
-- 确保用户只能访问自己的数据

-- =============================================
-- 1. categories 表 RLS 策略
-- =============================================

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can view own categories" ON categories;
DROP POLICY IF EXISTS "Users can insert own categories" ON categories;
DROP POLICY IF EXISTS "Users can update own categories" ON categories;
DROP POLICY IF EXISTS "Users can delete own categories" ON categories;

-- 创建新策略
CREATE POLICY "Users can view own categories" ON categories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own categories" ON categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own categories" ON categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own categories" ON categories
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- 2. prompts 表 RLS 策略
-- =============================================

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can view own prompts" ON prompts;
DROP POLICY IF EXISTS "Users can insert own prompts" ON prompts;
DROP POLICY IF EXISTS "Users can update own prompts" ON prompts;
DROP POLICY IF EXISTS "Users can delete own prompts" ON prompts;

-- 创建新策略
CREATE POLICY "Users can view own prompts" ON prompts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own prompts" ON prompts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own prompts" ON prompts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own prompts" ON prompts
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- 3. tags 表 RLS 策略
-- =============================================

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can view own tags" ON tags;
DROP POLICY IF EXISTS "Users can insert own tags" ON tags;
DROP POLICY IF EXISTS "Users can update own tags" ON tags;
DROP POLICY IF EXISTS "Users can delete own tags" ON tags;

-- 创建新策略
CREATE POLICY "Users can view own tags" ON tags
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tags" ON tags
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tags" ON tags
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tags" ON tags
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- 4. prompt_tags 表 RLS 策略
-- =============================================

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can view own prompt tags" ON prompt_tags;
DROP POLICY IF EXISTS "Users can insert own prompt tags" ON prompt_tags;
DROP POLICY IF EXISTS "Users can delete own prompt tags" ON prompt_tags;

-- 创建新策略
CREATE POLICY "Users can view own prompt tags" ON prompt_tags
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM prompts 
            WHERE prompts.id = prompt_tags.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own prompt tags" ON prompt_tags
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM prompts 
            WHERE prompts.id = prompt_tags.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own prompt tags" ON prompt_tags
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM prompts 
            WHERE prompts.id = prompt_tags.prompt_id 
            AND prompts.user_id = auth.uid()
        )
    );

-- =============================================
-- 5. search_history 表 RLS 策略
-- =============================================

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can view own search history" ON search_history;
DROP POLICY IF EXISTS "Users can insert own search history" ON search_history;
DROP POLICY IF EXISTS "Users can delete own search history" ON search_history;

-- 创建新策略
CREATE POLICY "Users can view own search history" ON search_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own search history" ON search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own search history" ON search_history
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- 6. 验证策略是否正确应用
-- =============================================

-- 查看所有表的 RLS 状态
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('categories', 'prompts', 'tags', 'prompt_tags', 'search_history');

-- 查看所有策略
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('categories', 'prompts', 'tags', 'prompt_tags', 'search_history')
ORDER BY tablename, policyname;