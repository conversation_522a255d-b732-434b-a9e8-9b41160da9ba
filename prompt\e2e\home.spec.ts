import { test, expect } from '@playwright/test'

test.describe('首页', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前都访问首页
    await page.goto('/')
  })

  test('应该显示页面标题和描述', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/提示词管理工具/)
    
    // 检查主标题
    await expect(page.locator('h1')).toContainText('提示词管理工具')
    
    // 检查描述
    await expect(page.locator('text=管理和组织您的 AI 提示词，提高工作效率')).toBeVisible()
  })

  test('应该显示统计数据', async ({ page }) => {
    // 检查统计卡片
    await expect(page.locator('[data-testid="stats-total-prompts"]')).toBeVisible()
    await expect(page.locator('[data-testid="stats-total-categories"]')).toBeVisible()
    await expect(page.locator('[data-testid="stats-total-usage"]')).toBeVisible()
    
    // 检查统计数字
    await expect(page.locator('[data-testid="stats-total-prompts"] .stat-value')).toContainText(/\d+/)
    await expect(page.locator('[data-testid="stats-total-categories"] .stat-value')).toContainText(/\d+/)
    await expect(page.locator('[data-testid="stats-total-usage"] .stat-value')).toContainText(/\d+/)
  })

  test('应该显示最近的提示词', async ({ page }) => {
    // 检查最近提示词标题
    await expect(page.locator('text=最近的提示词')).toBeVisible()
    
    // 检查是否有提示词卡片
    const promptCards = page.locator('[data-testid="prompt-card"]')
    await expect(promptCards).toHaveCount(2, { timeout: 10000 })
    
    // 检查卡片内容
    await expect(promptCards.first()).toContainText('代码审查提示')
    await expect(promptCards.last()).toContainText('文档写作助手')
  })

  test('应该显示快速操作按钮', async ({ page }) => {
    // 检查快速操作区域
    await expect(page.locator('text=快速操作')).toBeVisible()
    
    // 检查操作按钮
    await expect(page.locator('[data-testid="quick-action-new-prompt"]')).toBeVisible()
    await expect(page.locator('[data-testid="quick-action-manage-categories"]')).toBeVisible()
    await expect(page.locator('[data-testid="quick-action-import-data"]')).toBeVisible()
  })

  test('应该显示热门标签', async ({ page }) => {
    // 检查热门标签标题
    await expect(page.locator('text=热门标签')).toBeVisible()
    
    // 检查标签
    await expect(page.locator('[data-testid="popular-tags"]')).toBeVisible()
    await expect(page.locator('[data-testid="popular-tags"] .badge')).toHaveCount(6, { timeout: 5000 })
  })

  test('应该处理快速操作点击', async ({ page }) => {
    // 点击新建提示词按钮
    await page.click('[data-testid="quick-action-new-prompt"]')
    
    // 检查是否跳转到新建提示词页面
    await expect(page).toHaveURL('/prompts/new')
    
    // 返回首页
    await page.goto('/')
    
    // 点击管理分类按钮
    await page.click('[data-testid="quick-action-manage-categories"]')
    
    // 检查是否跳转到分类管理页面
    await expect(page).toHaveURL('/categories')
    
    // 返回首页
    await page.goto('/')
    
    // 点击导入数据按钮
    await page.click('[data-testid="quick-action-import-data"]')
    
    // 检查是否跳转到导入页面
    await expect(page).toHaveURL('/prompts/import')
  })

  test('应该处理查看全部点击', async ({ page }) => {
    // 点击查看全部按钮
    await page.click('[data-testid="view-all-prompts"]')
    
    // 检查是否跳转到提示词列表页面
    await expect(page).toHaveURL('/prompts')
  })

  test('应该支持响应式设计', async ({ page }) => {
    // 模拟移动设备
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 检查移动端布局
    await expect(page.locator('.grid')).toHaveClass(/grid-cols-1/)
    
    // 检查菜单按钮在移动端可见
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
    
    // 恢复桌面视口
    await page.setViewportSize({ width: 1920, height: 1080 })
    
    // 检查桌面端布局
    await expect(page.locator('.grid')).toHaveClass(/lg:grid-cols-3/)
  })

  test('应该显示加载状态', async ({ page }) => {
    // 拦截API请求，添加延迟
    await page.route('**/api/trpc/**', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      await route.continue()
    })
    
    // 重新加载页面
    await page.reload()
    
    // 检查加载状态
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible()
    
    // 等待加载完成
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible({ timeout: 5000 })
  })

  test('应该处理空状态', async ({ page }) => {
    // 拦截API请求，返回空数据
    await page.route('**/api/trpc/prompts.getRecent**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ result: { data: [] } })
      })
    })
    
    // 重新加载页面
    await page.reload()
    
    // 检查空状态
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible()
    await expect(page.locator('text=暂无提示词')).toBeVisible()
    await expect(page.locator('text=开始创建您的第一个提示词')).toBeVisible()
  })

  test('应该处理错误状态', async ({ page }) => {
    // 拦截API请求，返回错误
    await page.route('**/api/trpc/**', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: { message: '服务器错误' } })
      })
    })
    
    // 重新加载页面
    await page.reload()
    
    // 检查错误状态
    await expect(page.locator('[data-testid="error-state"]')).toBeVisible()
    await expect(page.locator('text=加载失败，请重试')).toBeVisible()
  })

  test('应该支持键盘导航', async ({ page }) => {
    // 使用 Tab 键导航
    await page.keyboard.press('Tab')
    
    // 检查第一个可聚焦元素
    await expect(page.locator('[data-testid="quick-action-new-prompt"]')).toBeFocused()
    
    // 继续导航
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="quick-action-manage-categories"]')).toBeFocused()
    
    // 使用 Enter 键激活
    await page.keyboard.press('Enter')
    
    // 检查是否跳转
    await expect(page).toHaveURL('/categories')
  })

  test('应该支持搜索功能', async ({ page }) => {
    // 检查搜索框
    const searchInput = page.locator('[data-testid="search-input"]')
    await expect(searchInput).toBeVisible()
    
    // 输入搜索关键词
    await searchInput.fill('代码')
    
    // 按回车搜索
    await page.keyboard.press('Enter')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL('/search?q=代码')
  })

  test('应该支持快捷键操作', async ({ page }) => {
    // 使用 Ctrl+K 快捷键打开搜索
    await page.keyboard.press('Control+k')
    
    // 检查搜索框是否聚焦
    await expect(page.locator('[data-testid="search-input"]')).toBeFocused()
    
    // 使用 Ctrl+N 快捷键创建新提示词
    await page.keyboard.press('Control+n')
    
    // 检查是否跳转到新建提示词页面
    await expect(page).toHaveURL('/prompts/new')
  })

  test('应该支持暗色模式切换', async ({ page }) => {
    // 检查主题切换按钮
    const themeToggle = page.locator('[data-testid="theme-toggle"]')
    await expect(themeToggle).toBeVisible()
    
    // 点击切换到暗色模式
    await themeToggle.click()
    
    // 检查是否应用了暗色主题
    await expect(page.locator('html')).toHaveAttribute('data-theme', 'dark')
    
    // 再次点击切换回亮色模式
    await themeToggle.click()
    
    // 检查是否应用了亮色主题
    await expect(page.locator('html')).toHaveAttribute('data-theme', 'light')
  })

  test('应该支持国际化', async ({ page }) => {
    // 检查语言切换按钮
    const languageToggle = page.locator('[data-testid="language-toggle"]')
    await expect(languageToggle).toBeVisible()
    
    // 点击切换到英文
    await languageToggle.click()
    await page.locator('text=English').click()
    
    // 检查是否切换到英文
    await expect(page.locator('h1')).toContainText('Prompt Management Tool')
    
    // 切换回中文
    await languageToggle.click()
    await page.locator('text=中文').click()
    
    // 检查是否切换回中文
    await expect(page.locator('h1')).toContainText('提示词管理工具')
  })
})

test.describe('首页性能测试', () => {
  test('应该在合理时间内加载', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/')
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    
    // 检查加载时间（应该在3秒内）
    expect(loadTime).toBeLessThan(3000)
  })

  test('应该有良好的性能指标', async ({ page }) => {
    await page.goto('/')
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    
    // 获取性能指标
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
      }
    })
    
    // 检查性能指标
    expect(performanceMetrics.domContentLoaded).toBeLessThan(1000)
    expect(performanceMetrics.loadComplete).toBeLessThan(2000)
    expect(performanceMetrics.firstPaint).toBeLessThan(1000)
    expect(performanceMetrics.firstContentfulPaint).toBeLessThan(1500)
  })
})