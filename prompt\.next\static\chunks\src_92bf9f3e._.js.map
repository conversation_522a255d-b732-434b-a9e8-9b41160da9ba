{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/trpc/query-client.ts"], "sourcesContent": ["import {\n  defaultShouldDehydrateQuery,\n  QueryClient,\n} from \"@tanstack/react-query\";\nimport SuperJSON from \"superjson\";\n\nexport const createQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        // With SSR, we usually want to set some default staleTime\n        // above 0 to avoid refetching immediately on the client\n        staleTime: 30 * 1000,\n      },\n      dehydrate: {\n        serializeData: SuperJSON.serialize,\n        shouldDehydrateQuery: (query) =>\n          defaultShouldDehydrateQuery(query) ||\n          query.state.status === \"pending\",\n      },\n      hydrate: {\n        deserializeData: SuperJSON.deserialize,\n      },\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAIA;;;AAEO,MAAM,oBAAoB,IAC/B,IAAI,gLAAA,CAAA,cAAW,CAAC;QACd,gBAAgB;YACd,SAAS;gBACP,0DAA0D;gBAC1D,wDAAwD;gBACxD,WAAW,KAAK;YAClB;YACA,WAAW;gBACT,eAAe,6IAAA,CAAA,UAAS,CAAC,SAAS;gBAClC,sBAAsB,CAAC,QACrB,CAAA,GAAA,8KAAA,CAAA,8BAA2B,AAAD,EAAE,UAC5B,MAAM,KAAK,CAAC,MAAM,KAAK;YAC3B;YACA,SAAS;gBACP,iBAAiB,6IAAA,CAAA,UAAS,CAAC,WAAW;YACxC;QACF;IACF", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/trpc/react.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClientProvider, type QueryClient } from \"@tanstack/react-query\";\nimport { httpBatchStreamLink, loggerLink } from \"@trpc/client\";\nimport { createTRPCReact } from \"@trpc/react-query\";\nimport { type inferRouterInputs, type inferRouterOutputs } from \"@trpc/server\";\nimport { useState } from \"react\";\nimport SuperJSON from \"superjson\";\n\nimport { type AppRouter } from \"~/server/api/root\";\nimport { createQueryClient } from \"./query-client\";\n\nlet clientQueryClientSingleton: QueryClient | undefined = undefined;\nconst getQueryClient = () => {\n  if (typeof window === \"undefined\") {\n    // Server: always make a new query client\n    return createQueryClient();\n  }\n  // Browser: use singleton pattern to keep the same query client\n  clientQueryClientSingleton ??= createQueryClient();\n\n  return clientQueryClientSingleton;\n};\n\nexport const api = createTRPCReact<AppRouter>();\n\n/**\n * Inference helper for inputs.\n *\n * @example type HelloInput = RouterInputs['example']['hello']\n */\nexport type RouterInputs = inferRouterInputs<AppRouter>;\n\n/**\n * Inference helper for outputs.\n *\n * @example type HelloOutput = RouterOutputs['example']['hello']\n */\nexport type RouterOutputs = inferRouterOutputs<AppRouter>;\n\nexport function TRPCReactProvider(props: { children: React.ReactNode }) {\n  const queryClient = getQueryClient();\n\n  const [trpcClient] = useState(() =>\n    api.createClient({\n      links: [\n        loggerLink({\n          enabled: (op) =>\n            process.env.NODE_ENV === \"development\" ||\n            (op.direction === \"down\" && op.result instanceof Error),\n        }),\n        httpBatchStreamLink({\n          transformer: SuperJSON,\n          url: getBaseUrl() + \"/api/trpc\",\n          headers: () => {\n            const headers = new Headers();\n            headers.set(\"x-trpc-source\", \"nextjs-react\");\n            return headers;\n          },\n        }),\n      ],\n    }),\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <api.Provider client={trpcClient} queryClient={queryClient}>\n        {props.children}\n      </api.Provider>\n    </QueryClientProvider>\n  );\n}\n\nfunction getBaseUrl() {\n  if (typeof window !== \"undefined\") return window.location.origin;\n  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\n  return `http://localhost:${process.env.PORT ?? 3000}`;\n}\n"], "names": [], "mappings": ";;;;AAgDY;;AA9CZ;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AACA;AAGA;;;AAVA;;;;;;;AAYA,IAAI,6BAAsD;AAC1D,MAAM,iBAAiB;IACrB;;IAIA,+DAA+D;IAC/D,uCAAA,wCAAA,6BAAA,6BAA+B,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAE/C,OAAO;AACT;AAEO,MAAM,MAAM,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD;AAgB1B,SAAS,kBAAkB,KAAoC;;IACpE,MAAM,cAAc;IAEpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;sCAAE,IAC5B,IAAI,YAAY,CAAC;gBACf,OAAO;oBACL,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE;wBACT,OAAO;0DAAE,CAAC,KACR,oDAAyB,iBACxB,GAAG,SAAS,KAAK,UAAU,GAAG,MAAM,YAAY;;oBACrD;oBACA,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE;wBAClB,aAAa,6IAAA,CAAA,UAAS;wBACtB,KAAK,eAAe;wBACpB,OAAO;0DAAE;gCACP,MAAM,UAAU,IAAI;gCACpB,QAAQ,GAAG,CAAC,iBAAiB;gCAC7B,OAAO;4BACT;;oBACF;iBACD;YACH;;IAGF,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,6LAAC,IAAI,QAAQ;YAAC,QAAQ;YAAY,aAAa;sBAC5C,MAAM,QAAQ;;;;;;;;;;;AAIvB;GA/BgB;KAAA;AAiChB,SAAS;IACP,wCAAmC,OAAO,OAAO,QAAQ,CAAC,MAAM;;;QAErC;AAC7B", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    DATABASE_URL: z.string().url(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    NEXT_PUBLIC_SUPABASE_URL: z.string().url(),\n    NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\n    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AA8BkB;AA9BlB;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,cAAc,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC5B,UAAU,qKAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;IACb;IAEA;;;;GAIC,GACD,QAAQ;QACN,0BAA0B,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACxC,+BAA+B,qKAAA,CAAA,IAAC,CAAC,MAAM;IACzC;IAEA;;;GAGC,GACD,YAAY;QACV,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY;QACtC,QAAQ;QACR,wBAAwB;QACxB,6BAA6B;IAC/B;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\r\nimport { env } from '~/env'\r\n\r\nexport const supabase = createClientComponentClient({\r\n  supabaseUrl: env.NEXT_PUBLIC_SUPABASE_URL,\r\n  supabaseKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n})\r\n\r\nexport type Database = {\r\n  public: {\r\n    Tables: {\r\n      categories: {\r\n        Row: {\r\n          id: string\r\n          name: string\r\n          description: string | null\r\n          color: string\r\n          icon: string\r\n          user_id: string\r\n          created_at: string\r\n          updated_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          name: string\r\n          description?: string | null\r\n          color?: string\r\n          icon?: string\r\n          user_id: string\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          name?: string\r\n          description?: string | null\r\n          color?: string\r\n          icon?: string\r\n          user_id?: string\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n      }\r\n      prompts: {\r\n        Row: {\r\n          id: string\r\n          title: string\r\n          content: string\r\n          description: string | null\r\n          category_id: string | null\r\n          user_id: string\r\n          usage_count: number\r\n          is_favorite: boolean\r\n          is_public: boolean\r\n          created_at: string\r\n          updated_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          title: string\r\n          content: string\r\n          description?: string | null\r\n          category_id?: string | null\r\n          user_id: string\r\n          usage_count?: number\r\n          is_favorite?: boolean\r\n          is_public?: boolean\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          title?: string\r\n          content?: string\r\n          description?: string | null\r\n          category_id?: string | null\r\n          user_id?: string\r\n          usage_count?: number\r\n          is_favorite?: boolean\r\n          is_public?: boolean\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n      }\r\n      tags: {\r\n        Row: {\r\n          id: string\r\n          name: string\r\n          color: string\r\n          user_id: string\r\n          created_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          name: string\r\n          color?: string\r\n          user_id: string\r\n          created_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          name?: string\r\n          color?: string\r\n          user_id?: string\r\n          created_at?: string\r\n        }\r\n      }\r\n      prompt_tags: {\r\n        Row: {\r\n          prompt_id: string\r\n          tag_id: string\r\n          created_at: string\r\n        }\r\n        Insert: {\r\n          prompt_id: string\r\n          tag_id: string\r\n          created_at?: string\r\n        }\r\n        Update: {\r\n          prompt_id?: string\r\n          tag_id?: string\r\n          created_at?: string\r\n        }\r\n      }\r\n      search_history: {\r\n        Row: {\r\n          id: string\r\n          query: string\r\n          user_id: string\r\n          created_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          query: string\r\n          user_id: string\r\n          created_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          query?: string\r\n          user_id?: string\r\n          created_at?: string\r\n        }\r\n      }\r\n    }\r\n    Views: {\r\n      [_ in never]: never\r\n    }\r\n    Functions: {\r\n      increment_prompt_usage: {\r\n        Args: {\r\n          prompt_uuid: string\r\n        }\r\n        Returns: undefined\r\n      }\r\n    }\r\n    Enums: {\r\n      [_ in never]: never\r\n    }\r\n    CompositeTypes: {\r\n      [_ in never]: never\r\n    }\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,WAAW,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD,EAAE;IAClD,aAAa,6GAAA,CAAA,MAAG,CAAC,wBAAwB;IACzC,aAAa,6GAAA,CAAA,MAAG,CAAC,6BAA6B;AAChD", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/lib/auth/context.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react'\r\nimport { User, Session } from '@supabase/supabase-js'\r\nimport { supabase } from '~/lib/supabase/client'\r\n\r\ninterface AuthContextType {\r\n  user: User | null\r\n  session: Session | null\r\n  loading: boolean\r\n  signIn: (email: string, password: string) => Promise<{ error: any }>\r\n  signUp: (email: string, password: string) => Promise<{ error: any }>\r\n  signOut: () => Promise<void>\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext)\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider')\r\n  }\r\n  return context\r\n}\r\n\r\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const [user, setUser] = useState<User | null>(null)\r\n  const [session, setSession] = useState<Session | null>(null)\r\n  const [loading, setLoading] = useState(true)\r\n\r\n  useEffect(() => {\r\n    // 获取初始会话\r\n    const getInitialSession = async () => {\r\n      const { data: { session } } = await supabase.auth.getSession()\r\n      setSession(session)\r\n      setUser(session?.user ?? null)\r\n      setLoading(false)\r\n    }\r\n\r\n    getInitialSession()\r\n\r\n    // 监听认证状态变化\r\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\r\n      async (event, session) => {\r\n        setSession(session)\r\n        setUser(session?.user ?? null)\r\n        setLoading(false)\r\n      }\r\n    )\r\n\r\n    return () => subscription.unsubscribe()\r\n  }, [])\r\n\r\n  const signIn = async (email: string, password: string) => {\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      email,\r\n      password,\r\n    })\r\n    return { error }\r\n  }\r\n\r\n  const signUp = async (email: string, password: string) => {\r\n    const { error } = await supabase.auth.signUp({\r\n      email,\r\n      password,\r\n    })\r\n    return { error }\r\n  }\r\n\r\n  const signOut = async () => {\r\n    await supabase.auth.signOut()\r\n  }\r\n\r\n  const value = {\r\n    user,\r\n    session,\r\n    loading,\r\n    signIn,\r\n    signUp,\r\n    signOut,\r\n  }\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS;YACT,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;oBAC5D,WAAW;wBACH;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAEA;YAEA,WAAW;YACX,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,WAAW;wBACH;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA9Da;KAAA", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/lib/errors/index.ts"], "sourcesContent": ["import { ZodError } from 'zod'\r\n\r\n// 自定义错误类型\r\nexport class AppError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public code: string,\r\n    public statusCode: number = 500,\r\n    public details?: any\r\n  ) {\r\n    super(message)\r\n    this.name = 'AppError'\r\n  }\r\n}\r\n\r\n// 验证错误\r\nexport class ValidationError extends AppError {\r\n  constructor(message: string, details?: any) {\r\n    super(message, 'VALIDATION_ERROR', 400, details)\r\n    this.name = 'ValidationError'\r\n  }\r\n}\r\n\r\n// 权限错误\r\nexport class AuthorizationError extends AppError {\r\n  constructor(message: string = '权限不足') {\r\n    super(message, 'AUTHORIZATION_ERROR', 403)\r\n    this.name = 'AuthorizationError'\r\n  }\r\n}\r\n\r\n// 认证错误\r\nexport class AuthenticationError extends AppError {\r\n  constructor(message: string = '认证失败') {\r\n    super(message, 'AUTHENTICATION_ERROR', 401)\r\n    this.name = 'AuthenticationError'\r\n  }\r\n}\r\n\r\n// 资源未找到错误\r\nexport class NotFoundError extends AppError {\r\n  constructor(message: string = '资源不存在') {\r\n    super(message, 'NOT_FOUND_ERROR', 404)\r\n    this.name = 'NotFoundError'\r\n  }\r\n}\r\n\r\n// 业务逻辑错误\r\nexport class BusinessError extends AppError {\r\n  constructor(message: string, code: string = 'BUSINESS_ERROR') {\r\n    super(message, code, 400)\r\n    this.name = 'BusinessError'\r\n  }\r\n}\r\n\r\n// 错误处理器\r\nexport class ErrorHandler {\r\n  static handle(error: unknown): {\r\n    message: string\r\n    code: string\r\n    statusCode: number\r\n    details?: any\r\n  } {\r\n    // ZodError 处理\r\n    if (error instanceof ZodError) {\r\n      return {\r\n        message: '数据验证失败',\r\n        code: 'VALIDATION_ERROR',\r\n        statusCode: 400,\r\n        details: error.errors.map(err => ({\r\n          field: err.path.join('.'),\r\n          message: err.message,\r\n          value: err.input,\r\n        })),\r\n      }\r\n    }\r\n\r\n    // 自定义应用错误\r\n    if (error instanceof AppError) {\r\n      return {\r\n        message: error.message,\r\n        code: error.code,\r\n        statusCode: error.statusCode,\r\n        details: error.details,\r\n      }\r\n    }\r\n\r\n    // 数据库错误\r\n    if (error && typeof error === 'object' && 'code' in error) {\r\n      const dbError = error as { code: string; message: string }\r\n      \r\n      switch (dbError.code) {\r\n        case 'P2002':\r\n          return {\r\n            message: '数据已存在，请检查唯一性约束',\r\n            code: 'UNIQUE_CONSTRAINT_ERROR',\r\n            statusCode: 409,\r\n            details: dbError.message,\r\n          }\r\n        case 'P2025':\r\n          return {\r\n            message: '要操作的记录不存在',\r\n            code: 'RECORD_NOT_FOUND',\r\n            statusCode: 404,\r\n            details: dbError.message,\r\n          }\r\n        case 'P2003':\r\n          return {\r\n            message: '外键约束失败',\r\n            code: 'FOREIGN_KEY_CONSTRAINT_ERROR',\r\n            statusCode: 400,\r\n            details: dbError.message,\r\n          }\r\n        default:\r\n          return {\r\n            message: '数据库操作失败',\r\n            code: 'DATABASE_ERROR',\r\n            statusCode: 500,\r\n            details: dbError.message,\r\n          }\r\n      }\r\n    }\r\n\r\n    // 网络错误\r\n    if (error instanceof TypeError && error.message.includes('fetch')) {\r\n      return {\r\n        message: '网络请求失败，请检查网络连接',\r\n        code: 'NETWORK_ERROR',\r\n        statusCode: 502,\r\n      }\r\n    }\r\n\r\n    // 默认错误\r\n    return {\r\n      message: error instanceof Error ? error.message : '未知错误',\r\n      code: 'UNKNOWN_ERROR',\r\n      statusCode: 500,\r\n    }\r\n  }\r\n\r\n  static getErrorMessage(error: unknown): string {\r\n    const handled = this.handle(error)\r\n    return handled.message\r\n  }\r\n\r\n  static getValidationErrors(error: unknown): Array<{\r\n    field: string\r\n    message: string\r\n    value?: any\r\n  }> {\r\n    if (error instanceof ZodError) {\r\n      return error.errors.map(err => ({\r\n        field: err.path.join('.'),\r\n        message: err.message,\r\n        value: err.input,\r\n      }))\r\n    }\r\n\r\n    if (error instanceof ValidationError && error.details) {\r\n      return Array.isArray(error.details) ? error.details : [error.details]\r\n    }\r\n\r\n    return []\r\n  }\r\n}\r\n\r\n// 错误边界组件的错误处理\r\nexport const handleComponentError = (error: Error, errorInfo: { componentStack: string }) => {\r\n  console.error('组件错误:', error)\r\n  console.error('错误信息:', errorInfo)\r\n  \r\n  // 这里可以发送错误报告到监控服务\r\n  // reportError(error, errorInfo)\r\n}\r\n\r\n// 异步操作错误处理\r\nexport const handleAsyncError = (error: unknown, context?: string) => {\r\n  const handled = ErrorHandler.handle(error)\r\n  \r\n  console.error(`异步操作错误${context ? ` (${context})` : ''}:`, handled)\r\n  \r\n  // 这里可以发送错误报告到监控服务\r\n  // reportError(handled, context)\r\n  \r\n  return handled\r\n}\r\n\r\n// 表单验证错误处理\r\nexport const handleFormError = (error: unknown): Record<string, string> => {\r\n  const validationErrors = ErrorHandler.getValidationErrors(error)\r\n  \r\n  return validationErrors.reduce((acc, err) => {\r\n    acc[err.field] = err.message\r\n    return acc\r\n  }, {} as Record<string, string>)\r\n}\r\n\r\n// 错误重试机制\r\nexport const withRetry = async <T>(\r\n  fn: () => Promise<T>,\r\n  maxRetries: number = 3,\r\n  delay: number = 1000\r\n): Promise<T> => {\r\n  let lastError: unknown\r\n  \r\n  for (let i = 0; i < maxRetries; i++) {\r\n    try {\r\n      return await fn()\r\n    } catch (error) {\r\n      lastError = error\r\n      \r\n      // 如果是客户端错误（4xx），不重试\r\n      const handled = ErrorHandler.handle(error)\r\n      if (handled.statusCode >= 400 && handled.statusCode < 500) {\r\n        throw error\r\n      }\r\n      \r\n      // 如果不是最后一次，等待后重试\r\n      if (i < maxRetries - 1) {\r\n        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))\r\n      }\r\n    }\r\n  }\r\n  \r\n  throw lastError\r\n}\r\n\r\n// 错误日志记录\r\nexport const logError = (error: unknown, context?: string) => {\r\n  const handled = ErrorHandler.handle(error)\r\n  \r\n  const logData = {\r\n    timestamp: new Date().toISOString(),\r\n    context,\r\n    error: handled,\r\n    stack: error instanceof Error ? error.stack : undefined,\r\n  }\r\n  \r\n  // 开发环境直接输出到控制台\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.error('应用错误:', logData)\r\n  }\r\n  \r\n  // 生产环境发送到监控服务\r\n  // if (process.env.NODE_ENV === 'production') {\r\n  //   sendToMonitoring(logData)\r\n  // }\r\n}\r\n\r\n// 错误常量\r\nexport const ERROR_MESSAGES = {\r\n  VALIDATION: {\r\n    REQUIRED: '此字段为必填项',\r\n    INVALID_EMAIL: '邮箱格式不正确',\r\n    INVALID_URL: 'URL格式不正确',\r\n    TOO_SHORT: '内容太短',\r\n    TOO_LONG: '内容太长',\r\n    INVALID_FORMAT: '格式不正确',\r\n  },\r\n  AUTH: {\r\n    UNAUTHORIZED: '请先登录',\r\n    FORBIDDEN: '权限不足',\r\n    INVALID_TOKEN: '无效的访问令牌',\r\n    EXPIRED_TOKEN: '访问令牌已过期',\r\n  },\r\n  RESOURCE: {\r\n    NOT_FOUND: '资源不存在',\r\n    ALREADY_EXISTS: '资源已存在',\r\n    CANNOT_DELETE: '无法删除此资源',\r\n    CANNOT_UPDATE: '无法更新此资源',\r\n  },\r\n  SYSTEM: {\r\n    SERVER_ERROR: '服务器内部错误',\r\n    NETWORK_ERROR: '网络连接失败',\r\n    TIMEOUT: '请求超时',\r\n    UNKNOWN: '未知错误',\r\n  },\r\n} as const"], "names": [], "mappings": ";;;;;;;;;;;;;;;AA+OM;;AA/ON;;;AAGO,MAAM,iBAAiB;IAC5B,YACE,OAAe,EACf,AAAO,IAAY,EACnB,AAAO,aAAqB,GAAG,EAC/B,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,wlBAJC,OAAA,WACA,aAAA,iBACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,wBAAwB;IACnC,YAAY,OAAe,EAAE,OAAa,CAAE;QAC1C,KAAK,CAAC,SAAS,oBAAoB,KAAK;QACxC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,2BAA2B;IACtC,YAAY,UAAkB,MAAM,CAAE;QACpC,KAAK,CAAC,SAAS,uBAAuB;QACtC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,4BAA4B;IACvC,YAAY,UAAkB,MAAM,CAAE;QACpC,KAAK,CAAC,SAAS,wBAAwB;QACvC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,YAAY,UAAkB,OAAO,CAAE;QACrC,KAAK,CAAC,SAAS,mBAAmB;QAClC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,YAAY,OAAe,EAAE,OAAe,gBAAgB,CAAE;QAC5D,KAAK,CAAC,SAAS,MAAM;QACrB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM;IACX,OAAO,OAAO,KAAc,EAK1B;QACA,cAAc;QACd,IAAI,iBAAiB,wIAAA,CAAA,WAAQ,EAAE;YAC7B,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,YAAY;gBACZ,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAChC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;wBACrB,SAAS,IAAI,OAAO;wBACpB,OAAO,IAAI,KAAK;oBAClB,CAAC;YACH;QACF;QAEA,UAAU;QACV,IAAI,iBAAiB,UAAU;YAC7B,OAAO;gBACL,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,YAAY,MAAM,UAAU;gBAC5B,SAAS,MAAM,OAAO;YACxB;QACF;QAEA,QAAQ;QACR,IAAI,SAAS,OAAO,UAAU,YAAY,UAAU,OAAO;YACzD,MAAM,UAAU;YAEhB,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;gBACF,KAAK;oBACH,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;gBACF,KAAK;oBACH,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;gBACF;oBACE,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;YACJ;QACF;QAEA,OAAO;QACP,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,YAAY;YACd;QACF;QAEA,OAAO;QACP,OAAO;YACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,MAAM;YACN,YAAY;QACd;IACF;IAEA,OAAO,gBAAgB,KAAc,EAAU;QAC7C,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC;QAC5B,OAAO,QAAQ,OAAO;IACxB;IAEA,OAAO,oBAAoB,KAAc,EAItC;QACD,IAAI,iBAAiB,wIAAA,CAAA,WAAQ,EAAE;YAC7B,OAAO,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;oBACrB,SAAS,IAAI,OAAO;oBACpB,OAAO,IAAI,KAAK;gBAClB,CAAC;QACH;QAEA,IAAI,iBAAiB,mBAAmB,MAAM,OAAO,EAAE;YACrD,OAAO,MAAM,OAAO,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAAG;gBAAC,MAAM,OAAO;aAAC;QACvE;QAEA,OAAO,EAAE;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAc;IACjD,QAAQ,KAAK,CAAC,SAAS;IACvB,QAAQ,KAAK,CAAC,SAAS;AAEvB,kBAAkB;AAClB,gCAAgC;AAClC;AAGO,MAAM,mBAAmB,CAAC,OAAgB;IAC/C,MAAM,UAAU,aAAa,MAAM,CAAC;IAEpC,QAAQ,KAAK,CAAC,AAAC,SAAuC,OAA/B,UAAU,AAAC,KAAY,OAAR,SAAQ,OAAK,IAAG,MAAI;IAE1D,kBAAkB;IAClB,gCAAgC;IAEhC,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,mBAAmB,aAAa,mBAAmB,CAAC;IAE1D,OAAO,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACnC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO;QAC5B,OAAO;IACT,GAAG,CAAC;AACN;AAGO,MAAM,YAAY,eACvB;QACA,8EAAqB,GACrB,yEAAgB;IAEhB,IAAI;IAEJ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,oBAAoB;YACpB,MAAM,UAAU,aAAa,MAAM,CAAC;YACpC,IAAI,QAAQ,UAAU,IAAI,OAAO,QAAQ,UAAU,GAAG,KAAK;gBACzD,MAAM;YACR;YAEA,iBAAiB;YACjB,IAAI,IAAI,aAAa,GAAG;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,CAAC,IAAI,CAAC;YACjE;QACF;IACF;IAEA,MAAM;AACR;AAGO,MAAM,WAAW,CAAC,OAAgB;IACvC,MAAM,UAAU,aAAa,MAAM,CAAC;IAEpC,MAAM,UAAU;QACd,WAAW,IAAI,OAAO,WAAW;QACjC;QACA,OAAO;QACP,OAAO,iBAAiB,QAAQ,MAAM,KAAK,GAAG;IAChD;IAEA,eAAe;IACf,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,SAAS;IACzB;AAEA,cAAc;AACd,+CAA+C;AAC/C,8BAA8B;AAC9B,IAAI;AACN;AAGO,MAAM,iBAAiB;IAC5B,YAAY;QACV,UAAU;QACV,eAAe;QACf,aAAa;QACb,WAAW;QACX,UAAU;QACV,gBAAgB;IAClB;IACA,MAAM;QACJ,cAAc;QACd,WAAW;QACX,eAAe;QACf,eAAe;IACjB;IACA,UAAU;QACR,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,eAAe;IACjB;IACA,QAAQ;QACN,cAAc;QACd,eAAe;QACf,SAAS;QACT,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/error/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { handleComponentError } from '~/lib/errors'\r\n\r\ninterface ErrorBoundaryState {\r\n  hasError: boolean\r\n  error?: Error\r\n  errorInfo?: React.ErrorInfo\r\n}\r\n\r\ninterface ErrorBoundaryProps {\r\n  children: React.ReactNode\r\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void\r\n}\r\n\r\nclass ErrorBoundaryClass extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\r\n  constructor(props: ErrorBoundaryProps) {\r\n    super(props)\r\n    this.state = { hasError: false }\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\r\n    return {\r\n      hasError: true,\r\n      error,\r\n    }\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\r\n    this.setState({\r\n      error,\r\n      errorInfo,\r\n    })\r\n\r\n    // 处理错误\r\n    handleComponentError(error, errorInfo)\r\n    \r\n    // 调用自定义错误处理\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo)\r\n    }\r\n  }\r\n\r\n  retry = () => {\r\n    this.setState({\r\n      hasError: false,\r\n      error: undefined,\r\n      errorInfo: undefined,\r\n    })\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError && this.state.error) {\r\n      // 使用自定义错误组件\r\n      if (this.props.fallback) {\r\n        const FallbackComponent = this.props.fallback\r\n        return <FallbackComponent error={this.state.error} retry={this.retry} />\r\n      }\r\n\r\n      // 默认错误页面\r\n      return <DefaultErrorFallback error={this.state.error} retry={this.retry} />\r\n    }\r\n\r\n    return this.props.children\r\n  }\r\n}\r\n\r\n// 默认错误回退组件\r\nconst DefaultErrorFallback = ({ error, retry }: { error: Error; retry: () => void }) => {\r\n  const isNetworkError = error.message.includes('fetch') || error.message.includes('network')\r\n  const isChunkError = error.message.includes('Loading chunk')\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      className=\"min-h-screen flex items-center justify-center bg-base-200\"\r\n    >\r\n      <div className=\"max-w-md w-full mx-4\">\r\n        <div className=\"bg-base-100 rounded-lg shadow-lg p-8 text-center\">\r\n          {/* 错误图标 */}\r\n          <div className=\"mb-6\">\r\n            {isNetworkError ? (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-16 h-16 mx-auto text-warning\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z\"\r\n                />\r\n              </svg>\r\n            ) : (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-16 h-16 mx-auto text-error\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                />\r\n              </svg>\r\n            )}\r\n          </div>\r\n\r\n          {/* 错误标题 */}\r\n          <h1 className=\"text-2xl font-bold text-base-content mb-4\">\r\n            {isNetworkError ? '网络连接失败' : \r\n             isChunkError ? '页面加载失败' : \r\n             '页面出现错误'}\r\n          </h1>\r\n\r\n          {/* 错误描述 */}\r\n          <p className=\"text-base-content/70 mb-6\">\r\n            {isNetworkError ? '请检查您的网络连接，然后重试。' :\r\n             isChunkError ? '页面资源加载失败，请刷新页面重试。' :\r\n             '页面遇到了意外错误，我们正在努力修复。'}\r\n          </p>\r\n\r\n          {/* 错误详情（开发环境） */}\r\n          {process.env.NODE_ENV === 'development' && (\r\n            <details className=\"mb-6 text-left\">\r\n              <summary className=\"cursor-pointer text-sm font-medium text-base-content/60 mb-2\">\r\n                错误详情\r\n              </summary>\r\n              <pre className=\"text-xs bg-base-200 p-4 rounded overflow-auto max-h-32\">\r\n                {error.stack}\r\n              </pre>\r\n            </details>\r\n          )}\r\n\r\n          {/* 操作按钮 */}\r\n          <div className=\"space-y-3\">\r\n            <button\r\n              onClick={retry}\r\n              className=\"btn btn-primary w-full\"\r\n            >\r\n              重试\r\n            </button>\r\n            \r\n            <button\r\n              onClick={() => window.location.reload()}\r\n              className=\"btn btn-ghost w-full\"\r\n            >\r\n              刷新页面\r\n            </button>\r\n            \r\n            <button\r\n              onClick={() => window.location.href = '/'}\r\n              className=\"btn btn-ghost w-full\"\r\n            >\r\n              返回首页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\n// 简单的错误回退组件\r\nexport const SimpleErrorFallback = ({ error, retry }: { error: Error; retry: () => void }) => (\r\n  <div className=\"bg-error/10 border border-error/20 rounded-lg p-6 text-center\">\r\n    <h3 className=\"text-lg font-semibold text-error mb-2\">加载失败</h3>\r\n    <p className=\"text-base-content/70 mb-4\">\r\n      {error.message || '遇到了未知错误'}\r\n    </p>\r\n    <button onClick={retry} className=\"btn btn-error btn-sm\">\r\n      重试\r\n    </button>\r\n  </div>\r\n)\r\n\r\n// 导出错误边界\r\nexport const ErrorBoundary = ErrorBoundaryClass\r\n\r\n// 错误边界 HOC\r\nexport const withErrorBoundary = <P extends object>(\r\n  Component: React.ComponentType<P>,\r\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\r\n) => {\r\n  const WrappedComponent = (props: P) => (\r\n    <ErrorBoundary fallback={fallback}>\r\n      <Component {...props} />\r\n    </ErrorBoundary>\r\n  )\r\n  \r\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\r\n  \r\n  return WrappedComponent\r\n}\r\n\r\n// 异步错误边界\r\nexport const AsyncErrorBoundary = ({ children, fallback }: {\r\n  children: React.ReactNode\r\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\r\n}) => {\r\n  const [asyncError, setAsyncError] = React.useState<Error | null>(null)\r\n\r\n  // 处理异步错误\r\n  React.useEffect(() => {\r\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\r\n      setAsyncError(new Error(event.reason))\r\n    }\r\n\r\n    window.addEventListener('unhandledrejection', handleUnhandledRejection)\r\n    \r\n    return () => {\r\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection)\r\n    }\r\n  }, [])\r\n\r\n  if (asyncError) {\r\n    const retry = () => setAsyncError(null)\r\n    \r\n    if (fallback) {\r\n      const FallbackComponent = fallback\r\n      return <FallbackComponent error={asyncError} retry={retry} />\r\n    }\r\n    \r\n    return <DefaultErrorFallback error={asyncError} retry={retry} />\r\n  }\r\n\r\n  return (\r\n    <ErrorBoundary fallback={fallback}>\r\n      {children}\r\n    </ErrorBoundary>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;AAqIW;;;AAnIX;AACA;AACA;;;;AAJA;;;;AAkBA,MAAM,2BAA2B,6JAAA,CAAA,UAAK,CAAC,SAAS;IAM9C,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;QAEA,OAAO;QACP,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;QAE5B,YAAY;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAUA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAC3C,YAAY;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,6LAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAE,OAAO,IAAI,CAAC,KAAK;;;;;;YACtE;YAEA,SAAS;YACT,qBAAO,6LAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,KAAK;;;;;;QACzE;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAhDA,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC,QA0BR,+KAAA,SAAQ;YACN,IAAI,CAAC,QAAQ,CAAC;gBACZ,UAAU;gBACV,OAAO;gBACP,WAAW;YACb;QACF;QA/BE,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AA8CF;AAEA,WAAW;AACX,MAAM,uBAAuB;QAAC,EAAE,KAAK,EAAE,KAAK,EAAuC;IACjF,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,MAAM,OAAO,CAAC,QAAQ,CAAC;IACjF,MAAM,eAAe,MAAM,OAAO,CAAC,QAAQ,CAAC;IAE5C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,+BACC,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;qFAIN,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;;;;;;kCAOV,6LAAC;wBAAG,WAAU;kCACX,iBAAiB,WACjB,eAAe,WACf;;;;;;kCAIH,6LAAC;wBAAE,WAAU;kCACV,iBAAiB,oBACjB,eAAe,sBACf;;;;;;oBAIF,oDAAyB,+BACxB,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAQ,WAAU;0CAA+D;;;;;;0CAGlF,6LAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK;;;;;;;;;;;;kCAMlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACtC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KApGM;AAuGC,MAAM,sBAAsB;QAAC,EAAE,KAAK,EAAE,KAAK,EAAuC;yBACvF,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,6LAAC;gBAAE,WAAU;0BACV,MAAM,OAAO,IAAI;;;;;;0BAEpB,6LAAC;gBAAO,SAAS;gBAAO,WAAU;0BAAuB;;;;;;;;;;;;;MANhD;AAaN,MAAM,gBAAgB;AAGtB,MAAM,oBAAoB,CAC/B,WACA;IAEA,MAAM,mBAAmB,CAAC,sBACxB,6LAAC;YAAc,UAAU;sBACvB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,AAAC,qBAA4D,OAAxC,UAAU,WAAW,IAAI,UAAU,IAAI,EAAC;IAE5F,OAAO;AACT;AAGO,MAAM,qBAAqB;QAAC,EAAE,QAAQ,EAAE,QAAQ,EAGtD;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEjE,SAAS;IACT,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,MAAM;yEAA2B,CAAC;oBAChC,cAAc,IAAI,MAAM,MAAM,MAAM;gBACtC;;YAEA,OAAO,gBAAgB,CAAC,sBAAsB;YAE9C;gDAAO;oBACL,OAAO,mBAAmB,CAAC,sBAAsB;gBACnD;;QACF;uCAAG,EAAE;IAEL,IAAI,YAAY;QACd,MAAM,QAAQ,IAAM,cAAc;QAElC,IAAI,UAAU;YACZ,MAAM,oBAAoB;YAC1B,qBAAO,6LAAC;gBAAkB,OAAO;gBAAY,OAAO;;;;;;QACtD;QAEA,qBAAO,6LAAC;YAAqB,OAAO;YAAY,OAAO;;;;;;IACzD;IAEA,qBACE,6LAAC;QAAc,UAAU;kBACtB;;;;;;AAGP;GAnCa;MAAA", "debugId": null}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/common/NetworkStatus.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface NetworkStatusProps {\r\n  className?: string\r\n}\r\n\r\nexport const NetworkStatus = ({ className = '' }: NetworkStatusProps) => {\r\n  const [isOnline, setIsOnline] = useState(true)\r\n  const [showNotification, setShowNotification] = useState(false)\r\n\r\n  useEffect(() => {\r\n    const handleOnline = () => {\r\n      setIsOnline(true)\r\n      setShowNotification(true)\r\n      setTimeout(() => setShowNotification(false), 3000)\r\n    }\r\n\r\n    const handleOffline = () => {\r\n      setIsOnline(false)\r\n      setShowNotification(true)\r\n    }\r\n\r\n    // 初始状态\r\n    setIsOnline(navigator.onLine)\r\n\r\n    // 监听网络状态变化\r\n    window.addEventListener('online', handleOnline)\r\n    window.addEventListener('offline', handleOffline)\r\n\r\n    return () => {\r\n      window.removeEventListener('online', handleOnline)\r\n      window.removeEventListener('offline', handleOffline)\r\n    }\r\n  }, [])\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {showNotification && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -50 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -50 }}\r\n          className={`fixed top-4 right-4 z-50 ${className}`}\r\n        >\r\n          <div className={`alert ${isOnline ? 'alert-success' : 'alert-error'} shadow-lg`}>\r\n            <div className=\"flex items-center space-x-2\">\r\n              {isOnline ? (\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-6 h-6\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z\"\r\n                  />\r\n                </svg>\r\n              ) : (\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-6 h-6\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 01-.923 1.785A5.969 5.969 0 006 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337z\"\r\n                  />\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5\"\r\n                  />\r\n                </svg>\r\n              )}\r\n              <span>\r\n                {isOnline ? '网络连接已恢复' : '网络连接已断开'}\r\n              </span>\r\n            </div>\r\n\r\n            <button\r\n              onClick={() => setShowNotification(false)}\r\n              className=\"btn btn-sm btn-ghost\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 网络状态钩子\r\nexport const useNetworkStatus = () => {\r\n  const [isOnline, setIsOnline] = useState(true)\r\n\r\n  useEffect(() => {\r\n    const handleOnline = () => setIsOnline(true)\r\n    const handleOffline = () => setIsOnline(false)\r\n\r\n    // 初始状态\r\n    setIsOnline(navigator.onLine)\r\n\r\n    // 监听网络状态变化\r\n    window.addEventListener('online', handleOnline)\r\n    window.addEventListener('offline', handleOffline)\r\n\r\n    return () => {\r\n      window.removeEventListener('online', handleOnline)\r\n      window.removeEventListener('offline', handleOffline)\r\n    }\r\n  }, [])\r\n\r\n  return { isOnline }\r\n}\r\n\r\n// 网络状态指示器\r\nexport const NetworkIndicator = ({ className = '' }: { className?: string }) => {\r\n  const { isOnline } = useNetworkStatus()\r\n\r\n  if (isOnline) return null\r\n\r\n  return (\r\n    <div className={`fixed bottom-4 left-4 z-50 ${className}`}>\r\n      <div className=\"flex items-center space-x-2 bg-error text-error-content px-4 py-2 rounded-lg shadow-lg\">\r\n        <div className=\"animate-pulse w-2 h-2 bg-current rounded-full\"></div>\r\n        <span className=\"text-sm\">离线模式</span>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;;;AAHA;;;AASO,MAAM,gBAAgB;QAAC,EAAE,YAAY,EAAE,EAAsB;;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;wDAAe;oBACnB,YAAY;oBACZ,oBAAoB;oBACpB;gEAAW,IAAM,oBAAoB;+DAAQ;gBAC/C;;YAEA,MAAM;yDAAgB;oBACpB,YAAY;oBACZ,oBAAoB;gBACtB;;YAEA,OAAO;YACP,YAAY,UAAU,MAAM;YAE5B,WAAW;YACX,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;2CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;kCAAG,EAAE;IAEL,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,WAAW,AAAC,4BAAqC,OAAV;sBAEvC,cAAA,6LAAC;gBAAI,WAAW,AAAC,SAAmD,OAA3C,WAAW,kBAAkB,eAAc;;kCAClE,6LAAC;wBAAI,WAAU;;4BACZ,yBACC,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;yFAIN,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;;kDAEV,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;kDAEJ,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;0CAIR,6LAAC;0CACE,WAAW,YAAY;;;;;;;;;;;;kCAI5B,6LAAC;wBACC,SAAS,IAAM,oBAAoB;wBACnC,WAAU;kCAEV,cAAA,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrE;GArGa;KAAA;AAwGN,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;2DAAe,IAAM,YAAY;;YACvC,MAAM;4DAAgB,IAAM,YAAY;;YAExC,OAAO;YACP,YAAY,UAAU,MAAM;YAE5B,WAAW;YACX,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;8CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;qCAAG,EAAE;IAEL,OAAO;QAAE;IAAS;AACpB;IArBa;AAwBN,MAAM,mBAAmB;QAAC,EAAE,YAAY,EAAE,EAA0B;;IACzE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,IAAI,UAAU,OAAO;IAErB,qBACE,6LAAC;QAAI,WAAW,AAAC,8BAAuC,OAAV;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;;;;;;AAIlC;IAba;;QACU;;;MADV", "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/common/LoadingManager.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface LoadingManagerProps {\r\n  isLoading: boolean\r\n  message?: string\r\n  timeout?: number\r\n  onTimeout?: () => void\r\n  className?: string\r\n}\r\n\r\nexport const LoadingManager = ({\r\n  isLoading,\r\n  message = '加载中...',\r\n  timeout = 30000, // 30秒超时\r\n  onTimeout,\r\n  className = '',\r\n}: LoadingManagerProps) => {\r\n  const [hasTimedOut, setHasTimedOut] = useState(false)\r\n\r\n  useEffect(() => {\r\n    if (!isLoading) {\r\n      setHasTimedOut(false)\r\n      return\r\n    }\r\n\r\n    const timer = setTimeout(() => {\r\n      setHasTimedOut(true)\r\n      onTimeout?.()\r\n    }, timeout)\r\n\r\n    return () => clearTimeout(timer)\r\n  }, [isLoading, timeout, onTimeout])\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isLoading && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${className}`}\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            exit={{ scale: 0.9, opacity: 0 }}\r\n            className=\"bg-base-100 rounded-lg p-8 max-w-sm w-full mx-4\"\r\n          >\r\n            <div className=\"text-center\">\r\n              {!hasTimedOut ? (\r\n                <>\r\n                  {/* 加载动画 */}\r\n                  <div className=\"mb-4\">\r\n                    <span className=\"loading loading-spinner loading-lg text-primary\"></span>\r\n                  </div>\r\n                  \r\n                  {/* 加载消息 */}\r\n                  <p className=\"text-base-content font-medium mb-2\">{message}</p>\r\n                  <p className=\"text-base-content/60 text-sm\">请稍候...</p>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {/* 超时图标 */}\r\n                  <div className=\"mb-4\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-16 h-16 mx-auto text-warning\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  \r\n                  {/* 超时消息 */}\r\n                  <p className=\"text-base-content font-medium mb-2\">请求超时</p>\r\n                  <p className=\"text-base-content/60 text-sm mb-4\">\r\n                    请检查网络连接，然后重试\r\n                  </p>\r\n                  \r\n                  {/* 重试按钮 */}\r\n                  <button\r\n                    onClick={() => window.location.reload()}\r\n                    className=\"btn btn-primary btn-sm\"\r\n                  >\r\n                    重试\r\n                  </button>\r\n                </>\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 页面级加载组件\r\nexport const PageLoading = ({ message = '页面加载中...' }: { message?: string }) => {\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n      <div className=\"text-center\">\r\n        <div className=\"mb-4\">\r\n          <span className=\"loading loading-spinner loading-lg text-primary\"></span>\r\n        </div>\r\n        <p className=\"text-base-content font-medium\">{message}</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 内容加载组件\r\nexport const ContentLoading = ({ \r\n  message = '加载中...',\r\n  className = '' \r\n}: { \r\n  message?: string\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>\r\n      <div className=\"mb-4\">\r\n        <span className=\"loading loading-spinner loading-md text-primary\"></span>\r\n      </div>\r\n      <p className=\"text-base-content/70 text-sm\">{message}</p>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 骨架屏组件\r\nexport const SkeletonLoader = ({ \r\n  lines = 3, \r\n  className = '' \r\n}: { \r\n  lines?: number\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`animate-pulse space-y-3 ${className}`}>\r\n      {Array.from({ length: lines }, (_, i) => (\r\n        <div key={i} className=\"flex space-x-4\">\r\n          <div className=\"rounded-full bg-base-300 h-10 w-10\"></div>\r\n          <div className=\"flex-1 space-y-2 py-1\">\r\n            <div className=\"h-4 bg-base-300 rounded w-3/4\"></div>\r\n            <div className=\"h-4 bg-base-300 rounded w-1/2\"></div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n\r\n// 表格骨架屏\r\nexport const TableSkeleton = ({ \r\n  rows = 5, \r\n  cols = 4, \r\n  className = '' \r\n}: { \r\n  rows?: number\r\n  cols?: number\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`animate-pulse ${className}`}>\r\n      <div className=\"overflow-x-auto\">\r\n        <table className=\"table w-full\">\r\n          <thead>\r\n            <tr>\r\n              {Array.from({ length: cols }, (_, i) => (\r\n                <th key={i}>\r\n                  <div className=\"h-4 bg-base-300 rounded w-full\"></div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {Array.from({ length: rows }, (_, i) => (\r\n              <tr key={i}>\r\n                {Array.from({ length: cols }, (_, j) => (\r\n                  <td key={j}>\r\n                    <div className=\"h-4 bg-base-300 rounded w-full\"></div>\r\n                  </td>\r\n                ))}\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 卡片骨架屏\r\nexport const CardSkeleton = ({ \r\n  count = 3, \r\n  className = '' \r\n}: { \r\n  count?: number\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>\r\n      {Array.from({ length: count }, (_, i) => (\r\n        <div key={i} className=\"animate-pulse\">\r\n          <div className=\"bg-base-100 rounded-lg p-6 shadow\">\r\n            <div className=\"flex items-center space-x-4 mb-4\">\r\n              <div className=\"w-12 h-12 bg-base-300 rounded-full\"></div>\r\n              <div className=\"space-y-2 flex-1\">\r\n                <div className=\"h-4 bg-base-300 rounded w-3/4\"></div>\r\n                <div className=\"h-3 bg-base-300 rounded w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"h-4 bg-base-300 rounded\"></div>\r\n              <div className=\"h-4 bg-base-300 rounded\"></div>\r\n              <div className=\"h-4 bg-base-300 rounded w-2/3\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n\r\n// 加载状态钩子\r\nexport const useLoadingState = (initialLoading = false) => {\r\n  const [isLoading, setIsLoading] = useState(initialLoading)\r\n  const [error, setError] = useState<Error | null>(null)\r\n\r\n  const startLoading = () => {\r\n    setIsLoading(true)\r\n    setError(null)\r\n  }\r\n\r\n  const stopLoading = () => {\r\n    setIsLoading(false)\r\n  }\r\n\r\n  const setLoadingError = (error: Error) => {\r\n    setError(error)\r\n    setIsLoading(false)\r\n  }\r\n\r\n  return {\r\n    isLoading,\r\n    error,\r\n    startLoading,\r\n    stopLoading,\r\n    setLoadingError,\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;;;AAHA;;;AAaO,MAAM,iBAAiB;QAAC,EAC7B,SAAS,EACT,UAAU,QAAQ,EAClB,UAAU,KAAK,EACf,SAAS,EACT,YAAY,EAAE,EACM;;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,WAAW;gBACd,eAAe;gBACf;YACF;YAEA,MAAM,QAAQ;kDAAW;oBACvB,eAAe;oBACf,sBAAA,gCAAA;gBACF;iDAAG;YAEH;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAW;QAAS;KAAU;IAElC,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAW,AAAC,mEAA4E,OAAV;sBAE9E,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;8BACZ,CAAC,4BACA;;0CAEE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAIlB,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAA+B;;;;;;;qDAG9C;;0CAEE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAMR,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CAKjD,6LAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA3Fa;KAAA;AA8FN,MAAM,cAAc;QAAC,EAAE,UAAU,UAAU,EAAwB;IACxE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;;;;;;;;;;;8BAElB,6LAAC;oBAAE,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAItD;MAXa;AAcN,MAAM,iBAAiB;QAAC,EAC7B,UAAU,QAAQ,EAClB,YAAY,EAAE,EAIf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,mDAA4D,OAAV;;0BACjE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAE,WAAU;0BAAgC;;;;;;;;;;;;AAGnD;MAfa;AAkBN,MAAM,iBAAiB;QAAC,EAC7B,QAAQ,CAAC,EACT,YAAY,EAAE,EAIf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,2BAAoC,OAAV;kBACxC,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;eAJT;;;;;;;;;;AAUlB;MApBa;AAuBN,MAAM,gBAAgB;QAAC,EAC5B,OAAO,CAAC,EACR,OAAO,CAAC,EACR,YAAY,EAAE,EAKf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,iBAA0B,OAAV;kBAC/B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAM,WAAU;;kCACf,6LAAC;kCACC,cAAA,6LAAC;sCACE,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAK,GAAG,CAAC,GAAG,kBAChC,6LAAC;8CACC,cAAA,6LAAC;wCAAI,WAAU;;;;;;mCADR;;;;;;;;;;;;;;;kCAMf,6LAAC;kCACE,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAK,GAAG,CAAC,GAAG,kBAChC,6LAAC;0CACE,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAK,GAAG,CAAC,GAAG,kBAChC,6LAAC;kDACC,cAAA,6LAAC;4CAAI,WAAU;;;;;;uCADR;;;;;+BAFJ;;;;;;;;;;;;;;;;;;;;;;;;;;AAavB;MArCa;AAwCN,MAAM,eAAe;QAAC,EAC3B,QAAQ,CAAC,EACT,YAAY,EAAE,EAIf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,wDAAiE,OAAV;kBACrE,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;eAZX;;;;;;;;;;AAmBlB;MA7Ba;AAgCN,MAAM,kBAAkB;QAAC,kFAAiB;;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,eAAe;QACnB,aAAa;QACb,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa;IACf;IAEA,MAAM,kBAAkB,CAAC;QACvB,SAAS;QACT,aAAa;IACf;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IAzBa", "debugId": null}}]}