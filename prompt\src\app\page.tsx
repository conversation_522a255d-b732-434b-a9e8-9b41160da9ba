'use client'

import { useState } from 'react'
import { PromptGrid } from "~/components/prompts";
import { FilterBar } from "~/components/filters";
import { api } from "~/trpc/react";

export default function Home() {
  // 筛选状态
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<'created' | 'updated' | 'usage' | 'title'>('updated')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showFavorites, setShowFavorites] = useState(false)

  // 获取筛选后的提示词
  const { data: promptsData, isLoading } = api.prompts.getFiltered.useQuery({
    categoryIds: selectedCategories.length > 0 ? selectedCategories : undefined,
    tagIds: selectedTags.length > 0 ? selectedTags : undefined,
    sortBy,
    sortOrder,
    favoritesOnly: showFavorites,
    limit: 12,
  })

  const prompts = promptsData?.prompts || []

  // 获取最近使用的提示词（不受筛选影响）
  const { data: recentPrompts, isLoading: recentLoading } = api.prompts.getRecent.useQuery({
    limit: 6,
  })

  const { data: stats } = api.prompts.getStats.useQuery()

  // 处理排序变化
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy as 'created' | 'updated' | 'usage' | 'title')
    setSortOrder(newSortOrder)
  }

  // 检查是否有筛选条件
  const hasFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites

  return (
    <div>
        {/* 欢迎横幅 - 占满宽度 */}
        <div className="bg-gradient-to-r from-primary to-secondary text-primary-content p-6">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold mb-2">欢迎使用提示词管理工具</h1>
            <p className="text-primary-content/90">
              管理你的 AI 提示词，提升工作效率
            </p>
          </div>
        </div>

        {/* 主要内容区域 - 有内边距 */}
        <div className="p-6 space-y-6">

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="stat bg-base-100 rounded-lg shadow">
            <div className="stat-figure text-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="inline-block w-8 h-8 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div className="stat-title">提示词总数</div>
            <div className="stat-value text-primary">{stats?.totalPrompts || 0}</div>
            <div className="stat-desc">{stats?.totalPrompts ? '个人收藏' : '等待数据加载'}</div>
          </div>

          <div className="stat bg-base-100 rounded-lg shadow">
            <div className="stat-figure text-secondary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="inline-block w-8 h-8 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                />
              </svg>
            </div>
            <div className="stat-title">分类数量</div>
            <div className="stat-value text-secondary">{stats?.totalCategories || 0}</div>
            <div className="stat-desc">{stats?.totalCategories ? '个分类' : '等待数据加载'}</div>
          </div>

          <div className="stat bg-base-100 rounded-lg shadow">
            <div className="stat-figure text-accent">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="inline-block w-8 h-8 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </div>
            <div className="stat-title">收藏数量</div>
            <div className="stat-value text-accent">{stats?.totalFavorites || 0}</div>
            <div className="stat-desc">{stats?.totalFavorites ? '个收藏' : '等待数据加载'}</div>
          </div>

          <div className="stat bg-base-100 rounded-lg shadow">
            <div className="stat-figure text-warning">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="inline-block w-8 h-8 stroke-current"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <div className="stat-title">总使用次数</div>
            <div className="stat-value text-warning">{stats?.totalUsage || 0}</div>
            <div className="stat-desc">{stats?.totalUsage ? '次使用' : '等待数据加载'}</div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="bg-base-100 rounded-lg p-6 shadow">
          <h2 className="text-xl font-semibold mb-4">快速操作</h2>
          <div className="flex flex-wrap gap-4">
            <a href="/prompts/new" className="btn btn-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 4.5v15m7.5-7.5h-15"
                />
              </svg>
              新建提示词
            </a>
            <a href="/categories" className="btn btn-secondary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
                />
              </svg>
              新建分类
            </a>
            <a href="/prompts/import" className="btn btn-accent">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
                />
              </svg>
              批量导入
            </a>
          </div>
        </div>

        {/* 筛选和浏览 */}
        <div className="bg-base-100 rounded-lg p-6 shadow">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">
              {hasFilters ? '筛选结果' : '浏览提示词'}
            </h2>
            <a href="/prompts" className="text-sm text-primary hover:text-primary-focus">
              查看全部
            </a>
          </div>
          
          {/* 筛选栏 */}
          <div className="mb-6">
            <FilterBar
              selectedCategories={selectedCategories}
              onCategoryChange={setSelectedCategories}
              selectedTags={selectedTags}
              onTagChange={setSelectedTags}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
              showFavorites={showFavorites}
              onShowFavoritesChange={setShowFavorites}
            />
          </div>

          {/* 提示词网格 */}
          <PromptGrid
            prompts={prompts}
            loading={isLoading}
            columns={3}
            showCategory={true}
            showActions={true}
            emptyMessage={hasFilters ? "未找到符合条件的提示词" : "暂无提示词"}
          />
        </div>

        {/* 最近使用 */}
        {!hasFilters && (
          <div className="bg-base-100 rounded-lg p-6 shadow">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">最近使用</h2>
              <a href="/prompts" className="text-sm text-primary hover:text-primary-focus">
                查看全部
              </a>
            </div>
            <PromptGrid
              prompts={recentPrompts || []}
              loading={recentLoading}
              columns={3}
              showCategory={true}
              showActions={true}
              emptyMessage="暂无最近使用的提示词"
            />
          </div>
        )}
        </div>
      </div>
  );
}
