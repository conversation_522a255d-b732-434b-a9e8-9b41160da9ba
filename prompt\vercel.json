{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["hkg1", "sin1", "nrt1"], "env": {"NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "DATABASE_URL": "@database_url", "NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NODE_ENV": "production"}, "functions": {"app/api/trpc/[trpc]/route.ts": {"maxDuration": 30}}, "rewrites": [{"source": "/api/trpc/:path*", "destination": "/api/trpc/:path*"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=60, stale-while-revalidate=300"}]}], "redirects": [{"source": "/login", "destination": "/auth/login", "permanent": true}, {"source": "/signup", "destination": "/auth/signup", "permanent": true}], "trailingSlash": false, "cleanUrls": true, "github": {"enabled": true, "autoAlias": true}}