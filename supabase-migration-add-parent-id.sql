-- 数据库迁移脚本：为分类表添加 parent_id 字段
-- 执行时间：2025-07-18
-- 目的：支持分类层级结构

-- 1. 添加 parent_id 字段
ALTER TABLE categories 
ADD COLUMN IF NOT EXISTS parent_id UUID REFERENCES categories(id) ON DELETE CASCADE;

-- 2. 添加索引
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);

-- 3. 验证字段是否添加成功
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'categories' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. 验证索引是否创建成功
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'categories' 
AND schemaname = 'public'
ORDER BY indexname;
