{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/favorites/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { api } from '~/trpc/react'\nimport { PromptGrid } from '~/components/prompts/PromptGrid'\n\nexport default function FavoritesPage() {\n  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'usage' | 'title'>('updated')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n\n  // 获取收藏的提示词\n  const { data: promptsData, isLoading, refetch } = api.prompts.getFiltered.useQuery({\n    favoritesOnly: true,\n    sortBy,\n    sortOrder,\n  })\n\n  const prompts = promptsData?.prompts || []\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 页面标题 */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            strokeWidth={1.5}\n            stroke=\"currentColor\"\n            className=\"w-8 h-8 text-warning\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\n            />\n          </svg>\n          <h1 className=\"text-3xl font-bold text-base-content\">我的收藏</h1>\n        </div>\n        <p className=\"text-base-content/70\">你收藏的所有提示词</p>\n      </div>\n\n      {/* 排序和操作栏 */}\n      <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n        {/* 排序选择 */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm font-medium text-base-content/70\">排序：</span>\n          <select\n            value={`${sortBy}-${sortOrder}`}\n            onChange={(e) => {\n              const [field, order] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder]\n              setSortBy(field)\n              setSortOrder(order)\n            }}\n            className=\"select select-bordered select-sm\"\n          >\n            <option value=\"updated-desc\">最近更新</option>\n            <option value=\"created-desc\">最新收藏</option>\n            <option value=\"usage-desc\">使用最多</option>\n            <option value=\"title-asc\">标题 A-Z</option>\n            <option value=\"title-desc\">标题 Z-A</option>\n          </select>\n        </div>\n\n        {/* 统计信息 */}\n        {!isLoading && prompts.length > 0 && (\n          <div className=\"flex items-center text-sm text-base-content/70\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              strokeWidth={1.5}\n              stroke=\"currentColor\"\n              className=\"w-4 h-4 mr-1\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z\"\n              />\n            </svg>\n            共 {prompts.length} 个收藏\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        <div className=\"flex gap-2 ml-auto\">\n          <button\n            onClick={() => refetch()}\n            className=\"btn btn-outline btn-sm\"\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <span className=\"loading loading-spinner loading-xs\"></span>\n            ) : (\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n                />\n              </svg>\n            )}\n            刷新\n          </button>\n        </div>\n      </div>\n\n      {/* 提示词网格 */}\n      <PromptGrid\n        prompts={prompts}\n        loading={isLoading}\n        columns={3}\n        showCategory={true}\n        showActions={true}\n        emptyMessage=\"还没有收藏任何提示词\"\n        emptyAction={\n          <div className=\"space-y-4\">\n            <p className=\"text-base-content/50\">\n              在提示词卡片上点击星星图标即可收藏\n            </p>\n            <Link href=\"/prompts\" className=\"btn btn-primary\">\n              浏览所有提示词\n            </Link>\n          </div>\n        }\n        onPromptUpdate={refetch}\n      />\n\n      {/* 收藏提示 */}\n      {!isLoading && prompts.length === 0 && (\n        <div className=\"mt-8 text-center\">\n          <div className=\"card bg-base-100 shadow-sm max-w-md mx-auto\">\n            <div className=\"card-body text-center\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-12 h-12 mx-auto text-warning mb-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\n                />\n              </svg>\n              <h3 className=\"text-lg font-medium mb-2\">如何收藏提示词？</h3>\n              <p className=\"text-base-content/70 text-sm mb-4\">\n                在任何提示词卡片上点击星星图标，即可将其添加到收藏夹中，方便快速访问。\n              </p>\n              <div className=\"flex justify-center space-x-2\">\n                <Link href=\"/prompts\" className=\"btn btn-primary btn-sm\">\n                  浏览提示词\n                </Link>\n                <Link href=\"/prompts/new\" className=\"btn btn-outline btn-sm\">\n                  创建提示词\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,WAAW;IACX,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC;QACjF,eAAe;QACf;QACA;IACF;IAEA,MAAM,UAAU,aAAa,WAAW,EAAE;IAE1C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;0CAGN,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;kCAEvD,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;0CAC3D,8OAAC;gCACC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;gCAC/B,UAAU,CAAC;oCACT,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;oCAC5C,UAAU;oCACV,aAAa;gCACf;gCACA,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,8OAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,8OAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAa;;;;;;;;;;;;;;;;;;oBAK9B,CAAC,aAAa,QAAQ,MAAM,GAAG,mBAC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;4BAEA;4BACH,QAAQ,MAAM;4BAAC;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM;4BACf,WAAU;4BACV,UAAU;;gCAET,0BACC,8OAAC;oCAAK,WAAU;;;;;yDAEhB,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;gCAGN;;;;;;;;;;;;;;;;;;0BAOR,8OAAC,2IAAA,CAAA,aAAU;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,cAAc;gBACd,aAAa;gBACb,cAAa;gBACb,2BACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAuB;;;;;;sCAGpC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAAkB;;;;;;;;;;;;gBAKtD,gBAAgB;;;;;;YAIjB,CAAC,aAAa,QAAQ,MAAM,KAAK,mBAChC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;0CAGN,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAyB;;;;;;kDAGzD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7E", "debugId": null}}]}