// 提示词管理工具 - Prisma Schema
// 配置为使用 Supabase PostgreSQL 数据库

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 分类表
model Category {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name        String   @db.VarChar(100)
  description String?  @db.Text
  color       String   @default("#3B82F6") @db.VarChar(7)
  icon        String   @default("folder") @db.VarChar(50)
  userId      String   @map("user_id") @db.Uuid
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  // 关联关系
  prompts Prompt[]

  @@index([userId])
  @@index([userId, name])
  @@map("categories")
}

// 提示词表
model Prompt {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title       String   @db.VarChar(200)
  content     String   @db.Text
  description String?  @db.Text
  categoryId  String?  @map("category_id") @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  usageCount  Int      @default(0) @map("usage_count")
  isFavorite  Boolean  @default(false) @map("is_favorite")
  isPublic    Boolean  @default(false) @map("is_public")
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)

  // 关联关系
  category   Category?   @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  promptTags PromptTag[]

  @@index([userId])
  @@index([categoryId])
  @@index([userId, title])
  @@index([userId, usageCount(sort: Desc)])
  @@index([userId, createdAt(sort: Desc)])
  @@map("prompts")
}

// 标签表
model Tag {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name      String   @db.VarChar(50)
  color     String   @default("#6B7280") @db.VarChar(7)
  userId    String   @map("user_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  // 关联关系
  promptTags PromptTag[]

  @@unique([userId, name], name: "unique_user_tag_name")
  @@index([userId])
  @@map("tags")
}

// 提示词标签关联表
model PromptTag {
  promptId  String   @map("prompt_id") @db.Uuid
  tagId     String   @map("tag_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  // 关联关系
  prompt Prompt @relation(fields: [promptId], references: [id], onDelete: Cascade)
  tag    Tag    @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([promptId, tagId])
  @@index([promptId])
  @@index([tagId])
  @@map("prompt_tags")
}

// 搜索历史表
model SearchHistory {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  query     String   @db.VarChar(200)
  userId    String   @map("user_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  @@index([userId])
  @@index([userId, createdAt(sort: Desc)])
  @@map("search_history")
}
