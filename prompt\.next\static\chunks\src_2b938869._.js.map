{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/categories/CategoryForm.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CategoryFormProps {\r\n  category?: Category | null\r\n  onClose: () => void\r\n}\r\n\r\nconst DEFAULT_COLORS = [\r\n  '#3b82f6', // blue\r\n  '#ef4444', // red\r\n  '#10b981', // green\r\n  '#f59e0b', // yellow\r\n  '#8b5cf6', // purple\r\n  '#ec4899', // pink\r\n  '#06b6d4', // cyan\r\n  '#84cc16', // lime\r\n  '#f97316', // orange\r\n  '#6b7280', // gray\r\n]\r\n\r\nconst DEFAULT_ICONS = [\r\n  '📁', '📂', '📋', '📝', '💡', '🎯', '🚀', '⭐', '🔥', '💎',\r\n  '🎨', '🔧', '⚙️', '📊', '🎮', '💻', '📱', '🌟', '🎪', '🎭',\r\n]\r\n\r\nexport const CategoryForm = ({ category, onClose }: CategoryFormProps) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    description: '',\r\n    color: DEFAULT_COLORS[0],\r\n    icon: '',\r\n    parentId: null as string | null,\r\n  })\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({})\r\n  const [showColorPicker, setShowColorPicker] = useState(false)\r\n  const [showIconPicker, setShowIconPicker] = useState(false)\r\n  const [customColor, setCustomColor] = useState('')\r\n\r\n  // 获取所有分类（用于父分类选择）\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 创建分类\r\n  const createMutation = api.categories.create.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类创建成功')\r\n      onClose()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('创建失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 更新分类\r\n  const updateMutation = api.categories.update.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类更新成功')\r\n      onClose()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 初始化表单数据\r\n  useEffect(() => {\r\n    if (category) {\r\n      setFormData({\r\n        name: category.name,\r\n        description: category.description || '',\r\n        color: category.color,\r\n        icon: category.icon || '',\r\n        parentId: category.parentId,\r\n      })\r\n    }\r\n  }, [category])\r\n\r\n  // 表单验证\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {}\r\n\r\n    if (!formData.name.trim()) {\r\n      newErrors.name = '分类名称不能为空'\r\n    } else if (formData.name.length > 50) {\r\n      newErrors.name = '分类名称不能超过50个字符'\r\n    }\r\n\r\n    if (formData.description && formData.description.length > 200) {\r\n      newErrors.description = '描述不能超过200个字符'\r\n    }\r\n\r\n    if (!formData.color) {\r\n      newErrors.color = '请选择分类颜色'\r\n    }\r\n\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  // 处理提交\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n\r\n    if (!validateForm()) {\r\n      return\r\n    }\r\n\r\n    const submitData = {\r\n      ...formData,\r\n      description: formData.description || undefined,\r\n      icon: formData.icon || undefined,\r\n      parentId: formData.parentId || undefined,\r\n    }\r\n\r\n    if (category) {\r\n      updateMutation.mutate({\r\n        id: category.id,\r\n        ...submitData,\r\n      })\r\n    } else {\r\n      createMutation.mutate(submitData)\r\n    }\r\n  }\r\n\r\n  // 处理颜色选择\r\n  const handleColorSelect = (color: string) => {\r\n    setFormData(prev => ({ ...prev, color }))\r\n    setShowColorPicker(false)\r\n  }\r\n\r\n  // 处理自定义颜色\r\n  const handleCustomColor = () => {\r\n    if (customColor && /^#[0-9A-F]{6}$/i.test(customColor)) {\r\n      handleColorSelect(customColor)\r\n      setCustomColor('')\r\n    } else {\r\n      toast.error('请输入有效的颜色值 (如: #FF0000)')\r\n    }\r\n  }\r\n\r\n  // 处理图标选择\r\n  const handleIconSelect = (icon: string) => {\r\n    setFormData(prev => ({ ...prev, icon }))\r\n    setShowIconPicker(false)\r\n  }\r\n\r\n  // 获取可用的父分类选项\r\n  const getParentOptions = () => {\r\n    if (!categories) return []\r\n    \r\n    return categories.filter(cat => {\r\n      // 排除当前分类和其子分类\r\n      if (category) {\r\n        return cat.id !== category.id && cat.parentId !== category.id\r\n      }\r\n      return true\r\n    })\r\n  }\r\n\r\n  const isLoading = createMutation.isLoading || updateMutation.isLoading\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\"\r\n      onClick={(e) => e.target === e.currentTarget && onClose()}\r\n    >\r\n      <motion.div\r\n        initial={{ scale: 0.9, opacity: 0 }}\r\n        animate={{ scale: 1, opacity: 1 }}\r\n        exit={{ scale: 0.9, opacity: 0 }}\r\n        className=\"bg-base-100 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto\"\r\n      >\r\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\r\n          {/* 表单头部 */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <h2 className=\"text-xl font-semibold text-base-content\">\r\n              {category ? '编辑分类' : '新建分类'}\r\n            </h2>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"btn btn-ghost btn-sm btn-square\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* 分类名称 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">分类名称 *</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              value={formData.name}\r\n              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\r\n              className={`input input-bordered ${errors.name ? 'input-error' : ''}`}\r\n              placeholder=\"输入分类名称\"\r\n              maxLength={50}\r\n            />\r\n            {errors.name && (\r\n              <label className=\"label\">\r\n                <span className=\"label-text-alt text-error\">{errors.name}</span>\r\n              </label>\r\n            )}\r\n          </div>\r\n\r\n          {/* 分类描述 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">描述</span>\r\n            </label>\r\n            <textarea\r\n              value={formData.description}\r\n              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n              className={`textarea textarea-bordered h-20 ${errors.description ? 'textarea-error' : ''}`}\r\n              placeholder=\"输入分类描述（可选）\"\r\n              maxLength={200}\r\n            />\r\n            {errors.description && (\r\n              <label className=\"label\">\r\n                <span className=\"label-text-alt text-error\">{errors.description}</span>\r\n              </label>\r\n            )}\r\n            <label className=\"label\">\r\n              <span className=\"label-text-alt\">{formData.description.length}/200</span>\r\n            </label>\r\n          </div>\r\n\r\n          {/* 父分类选择 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">父分类</span>\r\n            </label>\r\n            <select\r\n              value={formData.parentId || ''}\r\n              onChange={(e) => setFormData(prev => ({ ...prev, parentId: e.target.value || null }))}\r\n              className=\"select select-bordered\"\r\n            >\r\n              <option value=\"\">无（顶级分类）</option>\r\n              {getParentOptions().map(cat => (\r\n                <option key={cat.id} value={cat.id}>\r\n                  {cat.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* 颜色和图标选择 */}\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            {/* 颜色选择 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text\">颜色 *</span>\r\n              </label>\r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowColorPicker(!showColorPicker)}\r\n                  className=\"w-full h-12 rounded-lg border-2 border-base-300 flex items-center justify-center\"\r\n                  style={{ backgroundColor: formData.color }}\r\n                >\r\n                  <span className=\"text-white font-medium\">{formData.color}</span>\r\n                </button>\r\n                \r\n                <AnimatePresence>\r\n                  {showColorPicker && (\r\n                    <motion.div\r\n                      initial={{ opacity: 0, scale: 0.9 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.9 }}\r\n                      className=\"absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-3 shadow-lg z-10\"\r\n                    >\r\n                      <div className=\"grid grid-cols-5 gap-2 mb-3\">\r\n                        {DEFAULT_COLORS.map(color => (\r\n                          <button\r\n                            key={color}\r\n                            type=\"button\"\r\n                            onClick={() => handleColorSelect(color)}\r\n                            className=\"w-8 h-8 rounded border-2 border-base-300 hover:scale-110 transition-transform\"\r\n                            style={{ backgroundColor: color }}\r\n                          />\r\n                        ))}\r\n                      </div>\r\n                      <div className=\"flex space-x-2\">\r\n                        <input\r\n                          type=\"text\"\r\n                          value={customColor}\r\n                          onChange={(e) => setCustomColor(e.target.value)}\r\n                          className=\"input input-xs flex-1\"\r\n                          placeholder=\"#FF0000\"\r\n                        />\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={handleCustomColor}\r\n                          className=\"btn btn-xs btn-primary\"\r\n                        >\r\n                          应用\r\n                        </button>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 图标选择 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text\">图标</span>\r\n              </label>\r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setShowIconPicker(!showIconPicker)}\r\n                  className=\"w-full h-12 border-2 border-base-300 rounded-lg flex items-center justify-center text-2xl hover:bg-base-200\"\r\n                >\r\n                  {formData.icon || '选择图标'}\r\n                </button>\r\n                \r\n                <AnimatePresence>\r\n                  {showIconPicker && (\r\n                    <motion.div\r\n                      initial={{ opacity: 0, scale: 0.9 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.9 }}\r\n                      className=\"absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-3 shadow-lg z-10\"\r\n                    >\r\n                      <div className=\"grid grid-cols-5 gap-2 mb-2\">\r\n                        {DEFAULT_ICONS.map(icon => (\r\n                          <button\r\n                            key={icon}\r\n                            type=\"button\"\r\n                            onClick={() => handleIconSelect(icon)}\r\n                            className=\"w-8 h-8 flex items-center justify-center text-lg hover:bg-base-200 rounded\"\r\n                          >\r\n                            {icon}\r\n                          </button>\r\n                        ))}\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleIconSelect('')}\r\n                        className=\"btn btn-xs btn-ghost w-full\"\r\n                      >\r\n                        清除图标\r\n                      </button>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 预览 */}\r\n          <div className=\"form-control\">\r\n            <label className=\"label\">\r\n              <span className=\"label-text\">预览</span>\r\n            </label>\r\n            <div className=\"flex items-center space-x-3 p-3 bg-base-200 rounded-lg\">\r\n              <div\r\n                className=\"w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold\"\r\n                style={{ backgroundColor: formData.color }}\r\n              >\r\n                {formData.icon || formData.name.charAt(0)}\r\n              </div>\r\n              <div>\r\n                <p className=\"font-medium\">{formData.name || '分类名称'}</p>\r\n                {formData.description && (\r\n                  <p className=\"text-sm text-base-content/70 truncate\">\r\n                    {formData.description}\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 操作按钮 */}\r\n          <div className=\"flex justify-end space-x-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"btn btn-ghost\"\r\n              disabled={isLoading}\r\n            >\r\n              取消\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"btn btn-primary\"\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading && <span className=\"loading loading-spinner loading-sm\"></span>}\r\n              {category ? '更新' : '创建'}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </motion.div>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;;;AANA;;;;;AAaA,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAK;IAAM;IACrD;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CACvD;AAEM,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE,OAAO,EAAqB;;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kBAAkB;IAClB,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;wDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;wDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ,YAAY;oBACV,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW,IAAI;oBACrC,OAAO,SAAS,KAAK;oBACrB,MAAM,SAAS,IAAI,IAAI;oBACvB,UAAU,SAAS,QAAQ;gBAC7B;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YACpC,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,aAAa;YACjB,GAAG,QAAQ;YACX,aAAa,SAAS,WAAW,IAAI;YACrC,MAAM,SAAS,IAAI,IAAI;YACvB,UAAU,SAAS,QAAQ,IAAI;QACjC;QAEA,IAAI,UAAU;YACZ,eAAe,MAAM,CAAC;gBACpB,IAAI,SAAS,EAAE;gBACf,GAAG,UAAU;YACf;QACF,OAAO;YACL,eAAe,MAAM,CAAC;QACxB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAM,CAAC;QACvC,mBAAmB;IACrB;IAEA,UAAU;IACV,MAAM,oBAAoB;QACxB,IAAI,eAAe,kBAAkB,IAAI,CAAC,cAAc;YACtD,kBAAkB;YAClB,eAAe;QACjB,OAAO;YACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;QACtC,kBAAkB;IACpB;IAEA,aAAa;IACb,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,cAAc;YACd,IAAI,UAAU;gBACZ,OAAO,IAAI,EAAE,KAAK,SAAS,EAAE,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC/D;YACA,OAAO;QACT;IACF;IAEA,MAAM,YAAY,eAAe,SAAS,IAAI,eAAe,SAAS;IAEtE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAU;QACV,SAAS,CAAC,IAAM,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI;kBAEhD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAClC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,MAAM;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAC/B,WAAU;sBAEV,cAAA,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,SAAS;;;;;;0CAEvB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACvE,WAAW,AAAC,wBAAwD,OAAjC,OAAO,IAAI,GAAG,gBAAgB;gCACjE,aAAY;gCACZ,WAAW;;;;;;4BAEZ,OAAO,IAAI,kBACV,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;kCAM9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCACC,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC9E,WAAW,AAAC,mCAA6E,OAA3C,OAAO,WAAW,GAAG,mBAAmB;gCACtF,aAAY;gCACZ,WAAW;;;;;;4BAEZ,OAAO,WAAW,kBACjB,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAA6B,OAAO,WAAW;;;;;;;;;;;0CAGnE,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;;wCAAkB,SAAS,WAAW,CAAC,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAKlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCACC,OAAO,SAAS,QAAQ,IAAI;gCAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;wCAAK,CAAC;gCACnF,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,mBAAmB,GAAG,CAAC,CAAA,oBACtB,6LAAC;4CAAoB,OAAO,IAAI,EAAE;sDAC/B,IAAI,IAAI;2CADE,IAAI,EAAE;;;;;;;;;;;;;;;;;kCAQzB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,mBAAmB,CAAC;gDACnC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;0DAEzC,cAAA,6LAAC;oDAAK,WAAU;8DAA0B,SAAS,KAAK;;;;;;;;;;;0DAG1D,6LAAC,4LAAA,CAAA,kBAAe;0DACb,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAC/B,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACZ,eAAe,GAAG,CAAC,CAAA,sBAClB,6LAAC;oEAEC,MAAK;oEACL,SAAS,IAAM,kBAAkB;oEACjC,WAAU;oEACV,OAAO;wEAAE,iBAAiB;oEAAM;mEAJ3B;;;;;;;;;;sEAQX,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC9C,WAAU;oEACV,aAAY;;;;;;8EAEd,6LAAC;oEACC,MAAK;oEACL,SAAS;oEACT,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,kBAAkB,CAAC;gDAClC,WAAU;0DAET,SAAS,IAAI,IAAI;;;;;;0DAGpB,6LAAC,4LAAA,CAAA,kBAAe;0DACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAC/B,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oEAEC,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EAET;mEALI;;;;;;;;;;sEASX,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,SAAS,KAAK;wCAAC;kDAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;kDAEzC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAe,SAAS,IAAI,IAAI;;;;;;4CAC5C,SAAS,WAAW,kBACnB,6LAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,UAAU;;oCAET,2BAAa,6LAAC;wCAAK,WAAU;;;;;;oCAC7B,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjC;GAnYa;KAAA", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/modals/ConfirmDeleteModal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface ConfirmDeleteModalProps {\r\n  isOpen: boolean\r\n  onClose: () => void\r\n  onConfirm: () => void\r\n  title?: string\r\n  message?: string\r\n  itemName?: string\r\n  itemType?: string\r\n  isDangerous?: boolean\r\n  isLoading?: boolean\r\n  requireConfirmation?: boolean\r\n}\r\n\r\nexport const ConfirmDeleteModal = ({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  itemName,\r\n  itemType = '项目',\r\n  isDangerous = false,\r\n  isLoading = false,\r\n  requireConfirmation = false,\r\n}: ConfirmDeleteModalProps) => {\r\n  const [confirmText, setConfirmText] = useState('')\r\n  const [isConfirmed, setIsConfirmed] = useState(false)\r\n\r\n  const defaultTitle = title || `删除${itemType}`\r\n  const defaultMessage = message || `确定要删除这个${itemType}吗？此操作无法撤销。`\r\n  const confirmationText = itemName || '删除'\r\n  const canConfirm = !requireConfirmation || confirmText === confirmationText\r\n\r\n  const handleConfirm = () => {\r\n    if (canConfirm) {\r\n      onConfirm()\r\n    }\r\n  }\r\n\r\n  const handleClose = () => {\r\n    setConfirmText('')\r\n    setIsConfirmed(false)\r\n    onClose()\r\n  }\r\n\r\n  if (!isOpen) return null\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\r\n        onClick={handleClose}\r\n      >\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          exit={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n          className=\"bg-base-100 rounded-lg shadow-xl w-full max-w-md overflow-hidden\"\r\n          onClick={(e) => e.stopPropagation()}\r\n        >\r\n          {/* 头部 */}\r\n          <div className=\"bg-base-200 border-b border-base-300 p-4\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* 警告图标 */}\r\n              <div className={`p-2 rounded-full ${isDangerous ? 'bg-error/20' : 'bg-warning/20'}`}>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-6 h-6 ${isDangerous ? 'text-error' : 'text-warning'}`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n              \r\n              <div>\r\n                <h3 className=\"text-lg font-semibold text-base-content\">{defaultTitle}</h3>\r\n                <p className=\"text-sm text-base-content/70\">请确认您的操作</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 内容 */}\r\n          <div className=\"p-4 space-y-4\">\r\n            {/* 主要消息 */}\r\n            <div className=\"space-y-2\">\r\n              <p className=\"text-base-content\">{defaultMessage}</p>\r\n              \r\n              {itemName && (\r\n                <div className=\"bg-base-200 rounded-lg p-3\">\r\n                  <p className=\"text-sm text-base-content/70 mb-1\">要删除的{itemType}:</p>\r\n                  <p className=\"font-medium text-base-content break-words\">{itemName}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 危险警告 */}\r\n            {isDangerous && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"bg-error/10 border border-error/20 rounded-lg p-3\"\r\n              >\r\n                <div className=\"flex items-start space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-5 h-5 text-error flex-shrink-0 mt-0.5\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                    />\r\n                  </svg>\r\n                  <div className=\"text-sm\">\r\n                    <p className=\"font-medium text-error\">危险操作</p>\r\n                    <p className=\"text-error/80 mt-1\">\r\n                      此操作将永久删除数据，无法恢复。如果您确定要继续，请在下方输入确认信息。\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            {/* 确认输入 */}\r\n            {requireConfirmation && (\r\n              <div className=\"space-y-2\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">\r\n                    请输入 <code className=\"bg-base-200 px-2 py-1 rounded text-sm\">{confirmationText}</code> 以确认删除:\r\n                  </span>\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={confirmText}\r\n                  onChange={(e) => setConfirmText(e.target.value)}\r\n                  className=\"input input-bordered w-full\"\r\n                  placeholder={`输入 \"${confirmationText}\"`}\r\n                  autoComplete=\"off\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* 附加确认选项 */}\r\n            {isDangerous && (\r\n              <div className=\"form-control\">\r\n                <label className=\"label cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={isConfirmed}\r\n                    onChange={(e) => setIsConfirmed(e.target.checked)}\r\n                    className=\"checkbox checkbox-error\"\r\n                  />\r\n                  <span className=\"label-text ml-2\">我理解此操作的后果并确认删除</span>\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 底部操作 */}\r\n          <div className=\"bg-base-200 border-t border-base-300 p-4 flex items-center justify-end space-x-3\">\r\n            <motion.button\r\n              onClick={handleClose}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"btn btn-ghost\"\r\n              disabled={isLoading}\r\n            >\r\n              取消\r\n            </motion.button>\r\n            \r\n            <motion.button\r\n              onClick={handleConfirm}\r\n              whileHover={{ scale: canConfirm ? 1.05 : 1 }}\r\n              whileTap={{ scale: canConfirm ? 0.95 : 1 }}\r\n              disabled={!canConfirm || isLoading || (isDangerous && !isConfirmed)}\r\n              className={`btn ${isDangerous ? 'btn-error' : 'btn-warning'}`}\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  删除中...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                    />\r\n                  </svg>\r\n                  确认删除\r\n                </>\r\n              )}\r\n            </motion.button>\r\n          </div>\r\n        </motion.div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAkBO,MAAM,qBAAqB;QAAC,EACjC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACR,WAAW,IAAI,EACf,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,sBAAsB,KAAK,EACH;;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,SAAS,AAAC,KAAa,OAAT;IACnC,MAAM,iBAAiB,WAAW,AAAC,UAAkB,OAAT,UAAS;IACrD,MAAM,mBAAmB,YAAY;IACrC,MAAM,aAAa,CAAC,uBAAuB,gBAAgB;IAE3D,MAAM,gBAAgB;QACpB,IAAI,YAAY;YACd;QACF;IACF;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,eAAe;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAK,GAAG;gBAAG;gBACzC,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAG,GAAG;gBAAE;gBACtC,MAAM;oBAAE,SAAS;oBAAG,OAAO;oBAAK,GAAG;gBAAG;gBACtC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;gBAC1D,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,AAAC,oBAAiE,OAA9C,cAAc,gBAAgB;8CAChE,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAW,AAAC,WAAsD,OAA5C,cAAc,eAAe;kDAEnD,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAKR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;oCAEjC,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAoC;oDAAK;oDAAS;;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;4BAM/D,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;4BASzC,qCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;;gDAAyB;8DACnC,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;gDAAwB;;;;;;;;;;;;kDAGzF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,aAAa,AAAC,OAAuB,OAAjB,kBAAiB;wCACrC,cAAa;;;;;;;;;;;;4BAMlB,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;4CAChD,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,UAAU;0CACX;;;;;;0CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO,aAAa,OAAO;gCAAE;gCAC3C,UAAU;oCAAE,OAAO,aAAa,OAAO;gCAAE;gCACzC,UAAU,CAAC,cAAc,aAAc,eAAe,CAAC;gCACvD,WAAW,AAAC,OAAgD,OAA1C,cAAc,cAAc;0CAE7C,0BACC;;sDACE,6LAAC;4CAAK,WAAU;;;;;;wCAA4C;;iEAI9D;;sDACE,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;wCAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GAjNa;KAAA", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/categories/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { api } from '~/trpc/react'\r\nimport { PromptGrid } from '~/components/prompts/PromptGrid'\r\nimport { CategoryForm } from '~/components/categories/CategoryForm'\r\nimport { ConfirmDeleteModal } from '~/components/modals/ConfirmDeleteModal'\r\nimport { toast } from 'react-hot-toast'\r\n\r\nexport default function CategoryDetailPage() {\r\n  const params = useParams()\r\n  const router = useRouter()\r\n  const categoryId = params.id as string\r\n\r\n  const [isEditing, setIsEditing] = useState(false)\r\n  const [isDeleting, setIsDeleting] = useState(false)\r\n  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'usage'>('updated')\r\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\r\n\r\n  // 获取分类详情\r\n  const { data: category, isLoading, refetch } = api.categories.getById.useQuery({\r\n    id: categoryId,\r\n  })\r\n\r\n  // 获取分类下的提示词\r\n  const { data: prompts, isLoading: promptsLoading } = api.prompts.getByCategory.useQuery({\r\n    categoryId,\r\n    sortBy,\r\n    sortOrder,\r\n  })\r\n\r\n  // 删除分类\r\n  const deleteMutation = api.categories.delete.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类删除成功')\r\n      router.push('/categories')\r\n    },\r\n    onError: (error) => {\r\n      toast.error('删除失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 处理删除\r\n  const handleDelete = () => {\r\n    if (category) {\r\n      deleteMutation.mutate({ id: category.id })\r\n    }\r\n  }\r\n\r\n  // 处理表单关闭\r\n  const handleFormClose = () => {\r\n    setIsEditing(false)\r\n    refetch()\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"animate-pulse space-y-6\">\r\n          {/* 返回按钮骨架 */}\r\n          <div className=\"h-10 w-20 bg-base-300 rounded\"></div>\r\n          \r\n          {/* 分类头部骨架 */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"w-16 h-16 bg-base-300 rounded-lg\"></div>\r\n              <div className=\"space-y-2\">\r\n                <div className=\"h-8 w-48 bg-base-300 rounded\"></div>\r\n                <div className=\"h-4 w-32 bg-base-300 rounded\"></div>\r\n              </div>\r\n            </div>\r\n            <div className=\"h-10 w-24 bg-base-300 rounded\"></div>\r\n          </div>\r\n          \r\n          {/* 统计信息骨架 */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n            {Array.from({ length: 3 }, (_, i) => (\r\n              <div key={i} className=\"bg-base-100 rounded-lg p-4 space-y-3\">\r\n                <div className=\"h-4 w-20 bg-base-300 rounded\"></div>\r\n                <div className=\"h-8 w-16 bg-base-300 rounded\"></div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!category) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center py-12\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-16 h-16 mx-auto text-base-content/50 mb-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n            />\r\n          </svg>\r\n          <h2 className=\"text-xl font-semibold text-base-content mb-2\">分类不存在</h2>\r\n          <p className=\"text-base-content/70 mb-4\">该分类可能已被删除或不存在</p>\r\n          <Link href=\"/categories\" className=\"btn btn-primary\">\r\n            返回分类列表\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-8 space-y-6\">\r\n      {/* 返回按钮 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n      >\r\n        <Link href=\"/categories\" className=\"btn btn-ghost btn-sm\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-4 h-4\"\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18\" />\r\n          </svg>\r\n          返回分类列表\r\n        </Link>\r\n      </motion.div>\r\n\r\n      {/* 分类头部 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div className=\"flex items-center space-x-4\">\r\n          {/* 分类图标 */}\r\n          <div\r\n            className=\"w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-2xl shadow-lg\"\r\n            style={{ backgroundColor: category.color }}\r\n          >\r\n            {category.icon || category.name.charAt(0)}\r\n          </div>\r\n\r\n          {/* 分类信息 */}\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-base-content\">{category.name}</h1>\r\n            {category.description && (\r\n              <p className=\"text-base-content/70 mt-1\">{category.description}</p>\r\n            )}\r\n            <div className=\"flex items-center space-x-4 mt-2 text-sm text-base-content/60\">\r\n              <span>{category.promptCount || 0} 个提示词</span>\r\n              <span>使用 {category.usageCount || 0} 次</span>\r\n              <span>更新于 {new Date(category.updatedAt).toLocaleDateString('zh-CN')}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={() => setIsEditing(true)}\r\n            className=\"btn btn-ghost btn-sm\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n              />\r\n            </svg>\r\n            编辑\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => setIsDeleting(true)}\r\n            className=\"btn btn-ghost btn-sm text-error hover:btn-error\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n              />\r\n            </svg>\r\n            删除\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* 统计信息 */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-title\">提示词数量</div>\r\n          <div className=\"stat-value text-primary\">{category.promptCount || 0}</div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-title\">使用次数</div>\r\n          <div className=\"stat-value text-secondary\">{category.usageCount || 0}</div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-title\">平均使用率</div>\r\n          <div className=\"stat-value text-accent\">\r\n            {category.promptCount ? Math.round((category.usageCount || 0) / category.promptCount) : 0}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* 提示词列表 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.4 }}\r\n        className=\"space-y-4\"\r\n      >\r\n        <div className=\"flex items-center justify-between\">\r\n          <h2 className=\"text-xl font-semibold text-base-content\">提示词列表</h2>\r\n          \r\n          <div className=\"flex items-center space-x-3\">\r\n            {/* 排序选择 */}\r\n            <select\r\n              value={`${sortBy}-${sortOrder}`}\r\n              onChange={(e) => {\r\n                const [newSortBy, newSortOrder] = e.target.value.split('-')\r\n                setSortBy(newSortBy as 'updated' | 'created' | 'usage')\r\n                setSortOrder(newSortOrder as 'asc' | 'desc')\r\n              }}\r\n              className=\"select select-bordered select-sm\"\r\n            >\r\n              <option value=\"updated-desc\">最近更新</option>\r\n              <option value=\"created-desc\">最新创建</option>\r\n              <option value=\"usage-desc\">使用最多</option>\r\n              <option value=\"updated-asc\">最早更新</option>\r\n              <option value=\"created-asc\">最早创建</option>\r\n              <option value=\"usage-asc\">使用最少</option>\r\n            </select>\r\n\r\n            {/* 新建提示词按钮 */}\r\n            <Link\r\n              href={`/prompts/new?categoryId=${category.id}`}\r\n              className=\"btn btn-primary btn-sm\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\r\n              </svg>\r\n              新建提示词\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 提示词网格 */}\r\n        <PromptGrid\r\n          prompts={prompts || []}\r\n          loading={promptsLoading}\r\n          showCategory={false}\r\n          emptyMessage=\"该分类下还没有提示词\"\r\n          emptyAction={\r\n            <Link\r\n              href={`/prompts/new?categoryId=${category.id}`}\r\n              className=\"btn btn-primary\"\r\n            >\r\n              创建第一个提示词\r\n            </Link>\r\n          }\r\n        />\r\n      </motion.div>\r\n\r\n      {/* 编辑表单 */}\r\n      <AnimatePresence>\r\n        {isEditing && (\r\n          <CategoryForm\r\n            category={category}\r\n            onClose={handleFormClose}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 删除确认对话框 */}\r\n      <AnimatePresence>\r\n        {isDeleting && (\r\n          <ConfirmDeleteModal\r\n            isOpen={isDeleting}\r\n            onClose={() => setIsDeleting(false)}\r\n            onConfirm={handleDelete}\r\n            title=\"删除分类\"\r\n            message={`确定要删除分类 \"${category.name}\" 吗？删除后该分类下的所有提示词将移至\"未分类\"。`}\r\n            type=\"warning\"\r\n            confirmText=\"删除\"\r\n            isLoading={deleteMutation.isLoading}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,OAAO,EAAE;IAE5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,SAAS;IACT,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC;QAC7E,IAAI;IACN;IAEA,YAAY;IACZ,MAAM,EAAE,MAAM,OAAO,EAAE,WAAW,cAAc,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtF;QACA;QACA;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;8DAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;;QACA,OAAO;8DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,eAAe,MAAM,CAAC;gBAAE,IAAI,SAAS,EAAE;YAAC;QAC1C;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,aAAa;QACb;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;;;;;;;;;;;;IAStB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAc,WAAU;kCAAkB;;;;;;;;;;;;;;;;;IAM7D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;0BAE5B,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAc,WAAU;;sCACjC,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,GAAE;;;;;;;;;;;wBACjD;;;;;;;;;;;;0BAMV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,SAAS,KAAK;gCAAC;0CAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;0CAIzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwC,SAAS,IAAI;;;;;;oCAClE,SAAS,WAAW,kBACnB,6LAAC;wCAAE,WAAU;kDAA6B,SAAS,WAAW;;;;;;kDAEhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAM,SAAS,WAAW,IAAI;oDAAE;;;;;;;0DACjC,6LAAC;;oDAAK;oDAAI,SAAS,UAAU,IAAI;oDAAE;;;;;;;0DACnC,6LAAC;;oDAAK;oDAAK,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;0CAIR,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA2B,SAAS,WAAW,IAAI;;;;;;;;;;;;kCAGpE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA6B,SAAS,UAAU,IAAI;;;;;;;;;;;;kCAGrE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CACZ,SAAS,WAAW,GAAG,KAAK,KAAK,CAAC,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,SAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;0BAM9F,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAExD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;wCACpB,UAAU,CAAC;4CACT,MAAM,CAAC,WAAW,aAAa,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;4CACvD,UAAU;4CACV,aAAa;wCACf;wCACA,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;kDAI5B,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,AAAC,2BAAsC,OAAZ,SAAS,EAAE;wCAC5C,WAAU;;0DAEV,6LAAC;gDACC,OAAM;gDACN,MAAK;gDACL,SAAQ;gDACR,aAAa;gDACb,QAAO;gDACP,WAAU;0DAEV,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,GAAE;;;;;;;;;;;4CACjD;;;;;;;;;;;;;;;;;;;kCAOZ,6LAAC,8IAAA,CAAA,aAAU;wBACT,SAAS,WAAW,EAAE;wBACtB,SAAS;wBACT,cAAc;wBACd,cAAa;wBACb,2BACE,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,AAAC,2BAAsC,OAAZ,SAAS,EAAE;4BAC5C,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,6LAAC,4LAAA,CAAA,kBAAe;0BACb,2BACC,6LAAC,mJAAA,CAAA,eAAY;oBACX,UAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,qJAAA,CAAA,qBAAkB;oBACjB,QAAQ;oBACR,SAAS,IAAM,cAAc;oBAC7B,WAAW;oBACX,OAAM;oBACN,SAAS,AAAC,YAAyB,OAAd,SAAS,IAAI,EAAC;oBACnC,MAAK;oBACL,aAAY;oBACZ,WAAW,eAAe,SAAS;;;;;;;;;;;;;;;;;AAM/C;GA5UwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}