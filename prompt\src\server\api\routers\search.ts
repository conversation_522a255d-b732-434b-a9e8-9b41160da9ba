import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

const searchSchema = z.object({
  query: z.string().min(1, "搜索关键词不能为空").max(200, "搜索关键词不能超过200个字符"),
  categoryId: z.string().uuid("分类ID格式不正确").optional(),
  tags: z.array(z.string()).optional(),
  isFavorite: z.boolean().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

const addSearchHistorySchema = z.object({
  query: z.string().min(1, "搜索关键词不能为空").max(200, "搜索关键词不能超过200个字符"),
});

export const searchRouter = createTRPCRouter({
  // 全文搜索提示词
  searchPrompts: protectedProcedure
    .input(searchSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { query, categoryId, tags, isFavorite, limit, offset } = input;

        // 构建搜索条件
        const where: any = {
          userId: ctx.user.id,
          OR: [
            { title: { contains: query, mode: "insensitive" } },
            { content: { contains: query, mode: "insensitive" } },
            { description: { contains: query, mode: "insensitive" } },
          ],
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (isFavorite !== undefined) {
          where.isFavorite = isFavorite;
        }

        if (tags && tags.length > 0) {
          where.promptTags = {
            some: {
              tag: {
                name: { in: tags },
                userId: ctx.user.id,
              },
            },
          };
        }

        // 执行搜索
        const [prompts, total] = await Promise.all([
          ctx.db.prompt.findMany({
            where,
            include: {
              category: true,
              promptTags: {
                include: {
                  tag: true,
                },
              },
            },
            orderBy: [
              // 优先显示标题匹配的结果
              {
                title: {
                  _relevance: {
                    fields: ["title"],
                    search: query,
                    sort: "desc",
                  },
                },
              },
              // 然后按更新时间排序
              { updatedAt: "desc" },
            ],
            skip: offset,
            take: limit,
          }),
          ctx.db.prompt.count({ where }),
        ]);

        // 生成搜索结果高亮
        const processedPrompts = prompts.map((prompt) => {
          const highlightText = (text: string, query: string) => {
            if (!text) return text;
            const regex = new RegExp(`(${query})`, "gi");
            return text.replace(regex, "<mark>$1</mark>");
          };

          return {
            ...prompt,
            title: highlightText(prompt.title, query),
            content: highlightText(prompt.content.slice(0, 200), query),
            description: prompt.description 
              ? highlightText(prompt.description, query) 
              : null,
            tags: prompt.promptTags.map((pt) => pt.tag),
          };
        });

        return {
          prompts: processedPrompts,
          total,
          hasMore: offset + limit < total,
          query,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "搜索提示词失败",
        });
      }
    }),

  // 智能搜索建议
  getSuggestions: protectedProcedure
    .input(
      z.object({
        query: z.string().min(1).max(200),
        limit: z.number().min(1).max(10).default(5),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { query, limit } = input;

        // 搜索相关的提示词标题
        const titleSuggestions = await ctx.db.prompt.findMany({
          where: {
            userId: ctx.user.id,
            title: {
              contains: query,
              mode: "insensitive",
            },
          },
          select: {
            title: true,
          },
          take: limit,
          orderBy: {
            usageCount: "desc",
          },
        });

        // 搜索相关的标签
        const tagSuggestions = await ctx.db.tag.findMany({
          where: {
            userId: ctx.user.id,
            name: {
              contains: query,
              mode: "insensitive",
            },
          },
          select: {
            name: true,
          },
          take: limit,
          orderBy: {
            promptTags: {
              _count: "desc",
            },
          },
        });

        // 搜索相关的分类
        const categorySuggestions = await ctx.db.category.findMany({
          where: {
            userId: ctx.user.id,
            name: {
              contains: query,
              mode: "insensitive",
            },
          },
          select: {
            name: true,
          },
          take: limit,
          orderBy: {
            prompts: {
              _count: "desc",
            },
          },
        });

        return {
          titles: titleSuggestions.map((p) => p.title),
          tags: tagSuggestions.map((t) => t.name),
          categories: categorySuggestions.map((c) => c.name),
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取搜索建议失败",
        });
      }
    }),

  // 添加搜索历史
  addSearchHistory: protectedProcedure
    .input(addSearchHistorySchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查是否已存在相同的搜索记录
        const existingRecord = await ctx.db.searchHistory.findFirst({
          where: {
            query: input.query,
            userId: ctx.user.id,
          },
        });

        if (existingRecord) {
          // 如果已存在，更新时间
          await ctx.db.searchHistory.update({
            where: { id: existingRecord.id },
            data: { createdAt: new Date() },
          });
          return existingRecord;
        }

        // 创建新的搜索记录
        const searchRecord = await ctx.db.searchHistory.create({
          data: {
            query: input.query,
            userId: ctx.user.id,
          },
        });

        // 保持搜索历史记录数量（最多100条）
        const historyCount = await ctx.db.searchHistory.count({
          where: { userId: ctx.user.id },
        });

        if (historyCount > 100) {
          // 删除最老的记录
          const oldestRecords = await ctx.db.searchHistory.findMany({
            where: { userId: ctx.user.id },
            orderBy: { createdAt: "asc" },
            take: historyCount - 100,
          });

          await ctx.db.searchHistory.deleteMany({
            where: {
              id: { in: oldestRecords.map((r) => r.id) },
            },
          });
        }

        return searchRecord;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "添加搜索历史失败",
        });
      }
    }),

  // 获取搜索历史
  getSearchHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(20),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const history = await ctx.db.searchHistory.findMany({
          where: {
            userId: ctx.user.id,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: input.limit,
        });

        return history;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取搜索历史失败",
        });
      }
    }),

  // 清空搜索历史
  clearSearchHistory: protectedProcedure
    .mutation(async ({ ctx }) => {
      try {
        await ctx.db.searchHistory.deleteMany({
          where: {
            userId: ctx.user.id,
          },
        });

        return { success: true };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "清空搜索历史失败",
        });
      }
    }),

  // 删除单个搜索历史记录
  deleteSearchHistory: protectedProcedure
    .input(z.object({ id: z.string().uuid("搜索历史ID格式不正确") }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查记录是否存在且属于当前用户
        const existingRecord = await ctx.db.searchHistory.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
        });

        if (!existingRecord) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "搜索历史记录不存在",
          });
        }

        await ctx.db.searchHistory.delete({
          where: { id: input.id },
        });

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "删除搜索历史失败",
        });
      }
    }),

  // 获取搜索统计
  getSearchStats: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const [
          totalPrompts,
          totalCategories,
          totalTags,
          totalSearches,
          recentSearches,
          popularTags,
        ] = await Promise.all([
          // 总提示词数
          ctx.db.prompt.count({
            where: { userId: ctx.user.id },
          }),
          // 总分类数
          ctx.db.category.count({
            where: { userId: ctx.user.id },
          }),
          // 总标签数
          ctx.db.tag.count({
            where: { userId: ctx.user.id },
          }),
          // 总搜索次数
          ctx.db.searchHistory.count({
            where: { userId: ctx.user.id },
          }),
          // 最近7天的搜索次数
          ctx.db.searchHistory.count({
            where: {
              userId: ctx.user.id,
              createdAt: {
                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
              },
            },
          }),
          // 最受欢迎的标签
          ctx.db.tag.findMany({
            where: { userId: ctx.user.id },
            include: {
              _count: {
                select: {
                  promptTags: true,
                },
              },
            },
            orderBy: {
              promptTags: {
                _count: "desc",
              },
            },
            take: 5,
          }),
        ]);

        return {
          totalPrompts,
          totalCategories,
          totalTags,
          totalSearches,
          recentSearches,
          popularTags: popularTags.map((tag) => ({
            name: tag.name,
            count: tag._count.promptTags,
          })),
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取搜索统计失败",
        });
      }
    }),

  // 获取搜索历史（前端兼容性别名）
  getHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(20),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const history = await ctx.db.searchHistory.findMany({
          where: {
            userId: ctx.user.id,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: input.limit,
        });

        return history;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取搜索历史失败",
        });
      }
    }),
});