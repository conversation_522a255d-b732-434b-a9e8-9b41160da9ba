{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "e72c7f3b41155638dcba4e77e473710f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8f46c0d12fda3f149c1b78df6fd99873aff0e06892f46fa7aa85a17d67a9550a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "828bd88feef5fa1a26f7e192b6767498d180c2676e5378e3de66497676a90cf0"}}}, "sortedMiddleware": ["/"], "functions": {}}