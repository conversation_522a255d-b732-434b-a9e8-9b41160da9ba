{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "6374137a1de68befddfd57b84f311be8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "36f139a7f889d2dbcd2a77dbf723802cd3a3b7a28e5b3dfb5cf28c6c9a855dec", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8b5fc9f7ae19a5902df536e0a44ef8278169056d9d8e934256d46d78b4c95697"}}}, "sortedMiddleware": ["/"], "functions": {}}