{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "e4c0aa61e730ae82b380dc2947cfbf08", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e19bf059f9a1cfb28420d38750a64e43f77c883795b36ca26eabfb0d079b23c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f2e1ca31ab0fc6d8ded9e2f78167332ccaa0321c3de6050824a297397fd72f4e"}}}, "sortedMiddleware": ["/"], "functions": {}}