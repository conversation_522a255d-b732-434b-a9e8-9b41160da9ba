{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "4ce60d4bc2d9a5363ef37d44ae95ee8b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e0c0e8e4a019a3add0a2f80f13383d0f34e29fb9211998b6459d8a2af97f94b3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d4884151ba84062de962284f8dd5be33816ff59e0f315f5c361cbda0be7c533c"}}}, "sortedMiddleware": ["/"], "functions": {}}