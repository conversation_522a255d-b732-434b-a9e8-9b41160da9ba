{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "f309fb98ca0300d49e3cee14e82ee942", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c1712958e35f0d7751dd70a588230074507a14bc7995c5f108dc08d35cd4ba4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9cc88b1bb368c362da7ab5fd13ce0d6dc18cd2340c428d2a263164f9ab5c2cb5"}}}, "sortedMiddleware": ["/"], "functions": {}}