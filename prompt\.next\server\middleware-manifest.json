{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_7fe0d99e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5cd98168.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jCptyaKvceoMram8AwG42We/P1K0kqVQm6I5RAmYVY8=", "__NEXT_PREVIEW_MODE_ID": "fe6d05b2b9eabedc95c18c8d304c52ec", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "43bcc2a1c18200298b76154c503192adc26553c427e1a5a79ca4a19b6c9d5f39", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "882f2f976c355236561afb25c1ee63bb2b05b50b161805dbeecf9719f10e2b15"}}}, "sortedMiddleware": ["/"], "functions": {}}