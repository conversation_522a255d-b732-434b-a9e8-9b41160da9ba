{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/batch/BatchOperationBar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface BatchOperationBarProps {\r\n  selectedItems: string[]\r\n  onClearSelection: () => void\r\n  onRefresh: () => void\r\n  className?: string\r\n}\r\n\r\nexport const BatchOperationBar = ({\r\n  selectedItems,\r\n  onClearSelection,\r\n  onRefresh,\r\n  className = '',\r\n}: BatchOperationBarProps) => {\r\n  const [isDeleting, setIsDeleting] = useState(false)\r\n  const [isExporting, setIsExporting] = useState(false)\r\n  const [showCategorySelect, setShowCategorySelect] = useState(false)\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('')\r\n\r\n  // 获取分类列表\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 批量删除\r\n  const deleteMutation = api.prompts.batchDelete.useMutation({\r\n    onSuccess: (result) => {\r\n      toast.success(`成功删除 ${result.success} 个提示词`)\r\n      onClearSelection()\r\n      onRefresh()\r\n      setIsDeleting(false)\r\n    },\r\n    onError: (error) => {\r\n      toast.error('批量删除失败: ' + error.message)\r\n      setIsDeleting(false)\r\n    },\r\n  })\r\n\r\n  // 批量更新分类\r\n  const updateCategoryMutation = api.prompts.batchUpdateCategory.useMutation({\r\n    onSuccess: (result) => {\r\n      toast.success(`成功更新 ${result.success} 个提示词的分类`)\r\n      onClearSelection()\r\n      onRefresh()\r\n      setShowCategorySelect(false)\r\n    },\r\n    onError: (error) => {\r\n      toast.error('批量更新分类失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 批量设置收藏\r\n  const toggleFavoriteMutation = api.prompts.batchToggleFavorite.useMutation({\r\n    onSuccess: (result) => {\r\n      toast.success(`成功更新 ${result.success} 个提示词的收藏状态`)\r\n      onClearSelection()\r\n      onRefresh()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('批量更新收藏失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 批量导出\r\n  const exportPrompts = async () => {\r\n    setIsExporting(true)\r\n    try {\r\n      const { data: prompts } = await api.prompts.getByIds.useQuery({\r\n        ids: selectedItems,\r\n      })\r\n\r\n      if (prompts) {\r\n        const exportData = prompts.map(prompt => ({\r\n          title: prompt.title,\r\n          content: prompt.content,\r\n          description: prompt.description || '',\r\n          categoryId: prompt.categoryId,\r\n          tags: prompt.tags?.map(tag => tag.name) || [],\r\n          isFavorite: prompt.isFavorite,\r\n          createdAt: prompt.createdAt,\r\n          updatedAt: prompt.updatedAt,\r\n        }))\r\n\r\n        const blob = new Blob([JSON.stringify(exportData, null, 2)], {\r\n          type: 'application/json',\r\n        })\r\n        \r\n        const url = URL.createObjectURL(blob)\r\n        const a = document.createElement('a')\r\n        a.href = url\r\n        a.download = `prompts_export_${new Date().toISOString().split('T')[0]}.json`\r\n        document.body.appendChild(a)\r\n        a.click()\r\n        document.body.removeChild(a)\r\n        URL.revokeObjectURL(url)\r\n\r\n        toast.success(`成功导出 ${prompts.length} 个提示词`)\r\n      }\r\n    } catch (error) {\r\n      toast.error('导出失败: ' + (error instanceof Error ? error.message : '未知错误'))\r\n    } finally {\r\n      setIsExporting(false)\r\n    }\r\n  }\r\n\r\n  // 处理批量删除\r\n  const handleBatchDelete = () => {\r\n    if (window.confirm(`确定要删除选中的 ${selectedItems.length} 个提示词吗？此操作不可撤销。`)) {\r\n      setIsDeleting(true)\r\n      deleteMutation.mutate({ ids: selectedItems })\r\n    }\r\n  }\r\n\r\n  // 处理批量收藏\r\n  const handleBatchFavorite = (isFavorite: boolean) => {\r\n    toggleFavoriteMutation.mutate({\r\n      ids: selectedItems,\r\n      isFavorite,\r\n    })\r\n  }\r\n\r\n  // 处理批量更新分类\r\n  const handleBatchUpdateCategory = () => {\r\n    if (selectedCategory) {\r\n      updateCategoryMutation.mutate({\r\n        ids: selectedItems,\r\n        categoryId: selectedCategory,\r\n      })\r\n    }\r\n  }\r\n\r\n  if (selectedItems.length === 0) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 50 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        exit={{ opacity: 0, y: 50 }}\r\n        className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 ${className}`}\r\n      >\r\n        <div className=\"bg-base-100 border border-base-300 rounded-lg shadow-lg p-4 flex items-center space-x-4\">\r\n          {/* 选择信息 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-6 h-6 bg-primary text-primary-content rounded-full flex items-center justify-center text-sm font-semibold\">\r\n              {selectedItems.length}\r\n            </div>\r\n            <span className=\"text-sm font-medium text-base-content\">\r\n              已选择 {selectedItems.length} 个提示词\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"w-px h-6 bg-base-300\"></div>\r\n\r\n          {/* 操作按钮 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* 分类操作 */}\r\n            <div className=\"relative\">\r\n              <button\r\n                onClick={() => setShowCategorySelect(!showCategorySelect)}\r\n                className=\"btn btn-sm btn-ghost\"\r\n                title=\"批量设置分类\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n                  />\r\n                </svg>\r\n                分类\r\n              </button>\r\n\r\n              <AnimatePresence>\r\n                {showCategorySelect && (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    exit={{ opacity: 0, y: 10 }}\r\n                    className=\"absolute bottom-full left-0 mb-2 bg-base-100 border border-base-300 rounded-lg shadow-lg p-3 min-w-48\"\r\n                  >\r\n                    <div className=\"space-y-2\">\r\n                      <p className=\"text-sm font-medium text-base-content\">选择分类</p>\r\n                      <select\r\n                        value={selectedCategory}\r\n                        onChange={(e) => setSelectedCategory(e.target.value)}\r\n                        className=\"select select-bordered select-sm w-full\"\r\n                      >\r\n                        <option value=\"\">选择分类...</option>\r\n                        {categories?.map(category => (\r\n                          <option key={category.id} value={category.id}>\r\n                            {category.name}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                      <div className=\"flex justify-end space-x-2\">\r\n                        <button\r\n                          onClick={() => setShowCategorySelect(false)}\r\n                          className=\"btn btn-ghost btn-xs\"\r\n                        >\r\n                          取消\r\n                        </button>\r\n                        <button\r\n                          onClick={handleBatchUpdateCategory}\r\n                          disabled={!selectedCategory || updateCategoryMutation.isLoading}\r\n                          className=\"btn btn-primary btn-xs\"\r\n                        >\r\n                          {updateCategoryMutation.isLoading && (\r\n                            <span className=\"loading loading-spinner loading-xs\"></span>\r\n                          )}\r\n                          确定\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n            </div>\r\n\r\n            {/* 收藏操作 */}\r\n            <div className=\"dropdown dropdown-top\">\r\n              <div tabIndex={0} role=\"button\" className=\"btn btn-sm btn-ghost\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\r\n                  />\r\n                </svg>\r\n                收藏\r\n              </div>\r\n              <ul tabIndex={0} className=\"dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow border border-base-300\">\r\n                <li>\r\n                  <button\r\n                    onClick={() => handleBatchFavorite(true)}\r\n                    disabled={toggleFavoriteMutation.isLoading}\r\n                    className=\"text-sm\"\r\n                  >\r\n                    添加收藏\r\n                  </button>\r\n                </li>\r\n                <li>\r\n                  <button\r\n                    onClick={() => handleBatchFavorite(false)}\r\n                    disabled={toggleFavoriteMutation.isLoading}\r\n                    className=\"text-sm\"\r\n                  >\r\n                    取消收藏\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n\r\n            {/* 导出 */}\r\n            <button\r\n              onClick={exportPrompts}\r\n              disabled={isExporting}\r\n              className=\"btn btn-sm btn-ghost\"\r\n              title=\"导出选中项\"\r\n            >\r\n              {isExporting ? (\r\n                <span className=\"loading loading-spinner loading-xs\"></span>\r\n              ) : (\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\r\n                  />\r\n                </svg>\r\n              )}\r\n              导出\r\n            </button>\r\n\r\n            {/* 删除 */}\r\n            <button\r\n              onClick={handleBatchDelete}\r\n              disabled={isDeleting}\r\n              className=\"btn btn-sm btn-error\"\r\n              title=\"删除选中项\"\r\n            >\r\n              {isDeleting ? (\r\n                <span className=\"loading loading-spinner loading-xs\"></span>\r\n              ) : (\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                  />\r\n                </svg>\r\n              )}\r\n              删除\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"w-px h-6 bg-base-300\"></div>\r\n\r\n          {/* 取消选择 */}\r\n          <button\r\n            onClick={onClearSelection}\r\n            className=\"btn btn-sm btn-ghost\"\r\n            title=\"取消选择\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n            取消\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;;;AANA;;;;;AAeO,MAAM,oBAAoB;QAAC,EAChC,aAAa,EACb,gBAAgB,EAChB,SAAS,EACT,YAAY,EAAE,EACS;;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;QACzD,SAAS;6DAAE,CAAC;gBACV,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,QAAsB,OAAf,OAAO,OAAO,EAAC;gBACrC;gBACA;gBACA,cAAc;YAChB;;QACA,OAAO;6DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,aAAa,MAAM,OAAO;gBACtC,cAAc;YAChB;;IACF;IAEA,SAAS;IACT,MAAM,yBAAyB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC;QACzE,SAAS;qEAAE,CAAC;gBACV,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,QAAsB,OAAf,OAAO,OAAO,EAAC;gBACrC;gBACA;gBACA,sBAAsB;YACxB;;QACA,OAAO;qEAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,eAAe,MAAM,OAAO;YAC1C;;IACF;IAEA,SAAS;IACT,MAAM,yBAAyB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC;QACzE,SAAS;qEAAE,CAAC;gBACV,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,QAAsB,OAAf,OAAO,OAAO,EAAC;gBACrC;gBACA;YACF;;QACA,OAAO;qEAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,eAAe,MAAM,OAAO;YAC1C;;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,eAAe;QACf,IAAI;YACF,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC5D,KAAK;YACP;YAEA,IAAI,SAAS;gBACX,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAA;wBAKvB;2BALkC;wBACxC,OAAO,OAAO,KAAK;wBACnB,SAAS,OAAO,OAAO;wBACvB,aAAa,OAAO,WAAW,IAAI;wBACnC,YAAY,OAAO,UAAU;wBAC7B,MAAM,EAAA,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,MAAK,EAAE;wBAC7C,YAAY,OAAO,UAAU;wBAC7B,WAAW,OAAO,SAAS;wBAC3B,WAAW,OAAO,SAAS;oBAC7B;;gBAEA,MAAM,OAAO,IAAI,KAAK;oBAAC,KAAK,SAAS,CAAC,YAAY,MAAM;iBAAG,EAAE;oBAC3D,MAAM;gBACR;gBAEA,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,AAAC,kBAAwD,OAAvC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC;gBACtE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,IAAI,eAAe,CAAC;gBAEpB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,QAAsB,OAAf,QAAQ,MAAM,EAAC;YACvC;QACF,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,MAAM;QACzE,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,IAAI,OAAO,OAAO,CAAC,AAAC,YAAgC,OAArB,cAAc,MAAM,EAAC,qBAAmB;YACrE,cAAc;YACd,eAAe,MAAM,CAAC;gBAAE,KAAK;YAAc;QAC7C;IACF;IAEA,SAAS;IACT,MAAM,sBAAsB,CAAC;QAC3B,uBAAuB,MAAM,CAAC;YAC5B,KAAK;YACL;QACF;IACF;IAEA,WAAW;IACX,MAAM,4BAA4B;QAChC,IAAI,kBAAkB;YACpB,uBAAuB,MAAM,CAAC;gBAC5B,KAAK;gBACL,YAAY;YACd;QACF;IACF;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC1B,WAAW,AAAC,2DAAoE,OAAV;sBAEtE,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM;;;;;;0CAEvB,6LAAC;gCAAK,WAAU;;oCAAwC;oCACjD,cAAc,MAAM;oCAAC;;;;;;;;;;;;;kCAI9B,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,sBAAsB,CAAC;wCACtC,WAAU;wCACV,OAAM;;0DAEN,6LAAC;gDACC,OAAM;gDACN,MAAK;gDACL,SAAQ;gDACR,aAAa;gDACb,QAAO;gDACP,WAAU;0DAEV,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;4CAEA;;;;;;;kDAIR,6LAAC,4LAAA,CAAA,kBAAe;kDACb,oCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC1B,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAwC;;;;;;kEACrD,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACnD,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,uBAAA,iCAAA,WAAY,GAAG,CAAC,CAAA,yBACf,6LAAC;oEAAyB,OAAO,SAAS,EAAE;8EACzC,SAAS,IAAI;mEADH,SAAS,EAAE;;;;;;;;;;;kEAK5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,sBAAsB;gEACrC,WAAU;0EACX;;;;;;0EAGD,6LAAC;gEACC,SAAS;gEACT,UAAU,CAAC,oBAAoB,uBAAuB,SAAS;gEAC/D,WAAU;;oEAET,uBAAuB,SAAS,kBAC/B,6LAAC;wEAAK,WAAU;;;;;;oEAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,UAAU;wCAAG,MAAK;wCAAS,WAAU;;0DACxC,6LAAC;gDACC,OAAM;gDACN,MAAK;gDACL,SAAQ;gDACR,aAAa;gDACb,QAAO;gDACP,WAAU;0DAEV,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;4CAEA;;;;;;;kDAGR,6LAAC;wCAAG,UAAU;wCAAG,WAAU;;0DACzB,6LAAC;0DACC,cAAA,6LAAC;oDACC,SAAS,IAAM,oBAAoB;oDACnC,UAAU,uBAAuB,SAAS;oDAC1C,WAAU;8DACX;;;;;;;;;;;0DAIH,6LAAC;0DACC,cAAA,6LAAC;oDACC,SAAS,IAAM,oBAAoB;oDACnC,UAAU,uBAAuB,SAAS;oDAC1C,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAQP,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;gCACV,OAAM;;oCAEL,4BACC,6LAAC;wCAAK,WAAU;;;;;iGAEhB,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAGN;;;;;;;0CAKJ,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;gCACV,OAAM;;oCAEL,2BACC,6LAAC;wCAAK,WAAU;;;;;iGAEhB,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAGN;;;;;;;;;;;;;kCAKN,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;;0CAEN,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;4BACjD;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GApVa;KAAA", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useBatchSelection.ts"], "sourcesContent": ["import { useState, useCallback } from 'react'\r\n\r\nexport interface UseBatchSelectionOptions {\r\n  onSelectionChange?: (selectedItems: string[]) => void\r\n}\r\n\r\nexport const useBatchSelection = (options: UseBatchSelectionOptions = {}) => {\r\n  const [selectedItems, setSelectedItems] = useState<string[]>([])\r\n  const [isSelectionMode, setIsSelectionMode] = useState(false)\r\n\r\n  // 切换选择模式\r\n  const toggleSelectionMode = useCallback(() => {\r\n    setIsSelectionMode(prev => {\r\n      if (prev) {\r\n        // 退出选择模式时清空选择\r\n        setSelectedItems([])\r\n        options.onSelectionChange?.([])\r\n      }\r\n      return !prev\r\n    })\r\n  }, [options])\r\n\r\n  // 选择单个项目\r\n  const toggleItem = useCallback((id: string) => {\r\n    setSelectedItems(prev => {\r\n      const newSelection = prev.includes(id)\r\n        ? prev.filter(item => item !== id)\r\n        : [...prev, id]\r\n      \r\n      options.onSelectionChange?.(newSelection)\r\n      return newSelection\r\n    })\r\n  }, [options])\r\n\r\n  // 选择多个项目\r\n  const selectItems = useCallback((ids: string[]) => {\r\n    setSelectedItems(prev => {\r\n      const newSelection = [...new Set([...prev, ...ids])]\r\n      options.onSelectionChange?.(newSelection)\r\n      return newSelection\r\n    })\r\n  }, [options])\r\n\r\n  // 取消选择项目\r\n  const deselectItems = useCallback((ids: string[]) => {\r\n    setSelectedItems(prev => {\r\n      const newSelection = prev.filter(id => !ids.includes(id))\r\n      options.onSelectionChange?.(newSelection)\r\n      return newSelection\r\n    })\r\n  }, [options])\r\n\r\n  // 全选\r\n  const selectAll = useCallback((allIds: string[]) => {\r\n    setSelectedItems(allIds)\r\n    options.onSelectionChange?.(allIds)\r\n  }, [options])\r\n\r\n  // 清空选择\r\n  const clearSelection = useCallback(() => {\r\n    setSelectedItems([])\r\n    options.onSelectionChange?.([])\r\n  }, [options])\r\n\r\n  // 反选\r\n  const invertSelection = useCallback((allIds: string[]) => {\r\n    setSelectedItems(prev => {\r\n      const newSelection = allIds.filter(id => !prev.includes(id))\r\n      options.onSelectionChange?.(newSelection)\r\n      return newSelection\r\n    })\r\n  }, [options])\r\n\r\n  // 检查是否选中\r\n  const isSelected = useCallback((id: string) => {\r\n    return selectedItems.includes(id)\r\n  }, [selectedItems])\r\n\r\n  // 检查是否全选\r\n  const isAllSelected = useCallback((allIds: string[]) => {\r\n    return allIds.length > 0 && allIds.every(id => selectedItems.includes(id))\r\n  }, [selectedItems])\r\n\r\n  // 检查是否部分选中\r\n  const isPartiallySelected = useCallback((allIds: string[]) => {\r\n    return selectedItems.length > 0 && selectedItems.length < allIds.length\r\n  }, [selectedItems])\r\n\r\n  return {\r\n    selectedItems,\r\n    isSelectionMode,\r\n    \r\n    // 状态检查\r\n    isSelected,\r\n    isAllSelected,\r\n    isPartiallySelected,\r\n    hasSelection: selectedItems.length > 0,\r\n    selectionCount: selectedItems.length,\r\n    \r\n    // 操作方法\r\n    toggleSelectionMode,\r\n    toggleItem,\r\n    selectItems,\r\n    deselectItems,\r\n    selectAll,\r\n    clearSelection,\r\n    invertSelection,\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;;;AAMO,MAAM,oBAAoB;QAAC,2EAAoC,CAAC;;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,SAAS;IACT,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACtC;sEAAmB,CAAA;oBACjB,IAAI,MAAM;4BAGR;wBAFA,cAAc;wBACd,iBAAiB,EAAE;yBACnB,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B,EAAE;oBAChC;oBACA,OAAO,CAAC;gBACV;;QACF;6DAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B;6DAAiB,CAAA;wBAKf;oBAJA,MAAM,eAAe,KAAK,QAAQ,CAAC,MAC/B,KAAK,MAAM;qEAAC,CAAA,OAAQ,SAAS;sEAC7B;2BAAI;wBAAM;qBAAG;qBAEjB,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B;oBAC5B,OAAO;gBACT;;QACF;oDAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAC/B;8DAAiB,CAAA;wBAEf;oBADA,MAAM,eAAe;2BAAI,IAAI,IAAI;+BAAI;+BAAS;yBAAI;qBAAE;qBACpD,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B;oBAC5B,OAAO;gBACT;;QACF;qDAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACjC;gEAAiB,CAAA;wBAEf;oBADA,MAAM,eAAe,KAAK,MAAM;qFAAC,CAAA,KAAM,CAAC,IAAI,QAAQ,CAAC;;qBACrD,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B;oBAC5B,OAAO;gBACT;;QACF;uDAAG;QAAC;KAAQ;IAEZ,KAAK;IACL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;gBAE7B;YADA,iBAAiB;aACjB,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B;QAC9B;mDAAG;QAAC;KAAQ;IAEZ,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;gBAEjC;YADA,iBAAiB,EAAE;aACnB,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B,EAAE;QAChC;wDAAG;QAAC;KAAQ;IAEZ,KAAK;IACL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACnC;kEAAiB,CAAA;wBAEf;oBADA,MAAM,eAAe,OAAO,MAAM;uFAAC,CAAA,KAAM,CAAC,KAAK,QAAQ,CAAC;;qBACxD,6BAAA,QAAQ,iBAAiB,cAAzB,iDAAA,gCAAA,SAA4B;oBAC5B,OAAO;gBACT;;QACF;yDAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B,OAAO,cAAc,QAAQ,CAAC;QAChC;oDAAG;QAAC;KAAc;IAElB,SAAS;IACT,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACjC,OAAO,OAAO,MAAM,GAAG,KAAK,OAAO,KAAK;gEAAC,CAAA,KAAM,cAAc,QAAQ,CAAC;;QACxE;uDAAG;QAAC;KAAc;IAElB,WAAW;IACX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC,OAAO,cAAc,MAAM,GAAG,KAAK,cAAc,MAAM,GAAG,OAAO,MAAM;QACzE;6DAAG;QAAC;KAAc;IAElB,OAAO;QACL;QACA;QAEA,OAAO;QACP;QACA;QACA;QACA,cAAc,cAAc,MAAM,GAAG;QACrC,gBAAgB,cAAc,MAAM;QAEpC,OAAO;QACP;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAtGa", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptGridWithSelection.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { PromptCard } from './PromptCard'\r\nimport { BatchOperationBar } from '~/components/batch/BatchOperationBar'\r\nimport { useBatchSelection } from '~/hooks/useBatchSelection'\r\nimport { Prompt } from '~/types'\r\n\r\ninterface PromptGridWithSelectionProps {\r\n  prompts: Prompt[]\r\n  loading?: boolean\r\n  columns?: 1 | 2 | 3 | 4\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n  emptyMessage?: string\r\n  emptyAction?: React.ReactNode\r\n  onPromptUpdate?: () => void\r\n  className?: string\r\n}\r\n\r\nexport const PromptGridWithSelection = ({\r\n  prompts,\r\n  loading = false,\r\n  columns = 3,\r\n  showCategory = true,\r\n  showActions = true,\r\n  emptyMessage = \"暂无提示词\",\r\n  emptyAction,\r\n  onPromptUpdate,\r\n  className = '',\r\n}: PromptGridWithSelectionProps) => {\r\n  const {\r\n    selectedItems,\r\n    isSelectionMode,\r\n    isSelected,\r\n    isAllSelected,\r\n    isPartiallySelected,\r\n    hasSelection,\r\n    selectionCount,\r\n    toggleSelectionMode,\r\n    toggleItem,\r\n    selectAll,\r\n    clearSelection,\r\n    invertSelection,\r\n  } = useBatchSelection()\r\n\r\n  const promptIds = prompts.map(p => p.id)\r\n\r\n  // 获取网格列类\r\n  const getGridCols = () => {\r\n    switch (columns) {\r\n      case 1: return 'grid-cols-1'\r\n      case 2: return 'grid-cols-1 md:grid-cols-2'\r\n      case 3: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'\r\n      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'\r\n      default: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'\r\n    }\r\n  }\r\n\r\n  // 处理全选/取消全选\r\n  const handleSelectAll = () => {\r\n    if (isAllSelected(promptIds)) {\r\n      clearSelection()\r\n    } else {\r\n      selectAll(promptIds)\r\n    }\r\n  }\r\n\r\n  // 处理刷新\r\n  const handleRefresh = () => {\r\n    onPromptUpdate?.()\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className={`grid ${getGridCols()} gap-6 ${className}`}>\r\n        {Array.from({ length: columns * 2 }, (_, i) => (\r\n          <div key={i} className=\"animate-pulse\">\r\n            <div className=\"bg-base-200 rounded-lg h-64\"></div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!prompts.length) {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className={`text-center py-12 ${className}`}\r\n      >\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-16 h-16 mx-auto text-base-content/50 mb-4\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n          />\r\n        </svg>\r\n        <h3 className=\"text-lg font-semibold text-base-content mb-2\">\r\n          {emptyMessage}\r\n        </h3>\r\n        {emptyAction}\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className={className}>\r\n      {/* 工具栏 */}\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          {/* 选择模式切换 */}\r\n          <button\r\n            onClick={toggleSelectionMode}\r\n            className={`btn btn-sm ${isSelectionMode ? 'btn-primary' : 'btn-ghost'}`}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n              />\r\n            </svg>\r\n            {isSelectionMode ? '退出选择' : '批量选择'}\r\n          </button>\r\n\r\n          {/* 选择状态显示 */}\r\n          <AnimatePresence>\r\n            {isSelectionMode && (\r\n              <motion.div\r\n                initial={{ opacity: 0, x: -20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                exit={{ opacity: 0, x: -20 }}\r\n                className=\"flex items-center space-x-3\"\r\n              >\r\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={isAllSelected(promptIds)}\r\n                    ref={(el) => {\r\n                      if (el) {\r\n                        el.indeterminate = isPartiallySelected(promptIds)\r\n                      }\r\n                    }}\r\n                    onChange={handleSelectAll}\r\n                    className=\"checkbox checkbox-sm\"\r\n                  />\r\n                  <span className=\"text-sm\">全选</span>\r\n                </label>\r\n\r\n                {hasSelection && (\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectionCount} 个项目\r\n                  </span>\r\n                )}\r\n\r\n                <button\r\n                  onClick={() => invertSelection(promptIds)}\r\n                  className=\"btn btn-ghost btn-xs\"\r\n                >\r\n                  反选\r\n                </button>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n\r\n        {/* 右侧工具 */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <span className=\"text-sm text-base-content/70\">\r\n            共 {prompts.length} 个提示词\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 提示词网格 */}\r\n      <div className={`grid ${getGridCols()} gap-6`}>\r\n        <AnimatePresence>\r\n          {prompts.map((prompt, index) => (\r\n            <motion.div\r\n              key={prompt.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              transition={{ delay: index * 0.05 }}\r\n              className=\"relative\"\r\n            >\r\n              {/* 选择框覆盖层 */}\r\n              <AnimatePresence>\r\n                {isSelectionMode && (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                    className=\"absolute top-2 left-2 z-10\"\r\n                  >\r\n                    <label className=\"cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={isSelected(prompt.id)}\r\n                        onChange={() => toggleItem(prompt.id)}\r\n                        className=\"checkbox checkbox-sm bg-base-100 border-2\"\r\n                      />\r\n                    </label>\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n\r\n              {/* 选择状态覆盖层 */}\r\n              <AnimatePresence>\r\n                {isSelectionMode && isSelected(prompt.id) && (\r\n                  <motion.div\r\n                    initial={{ opacity: 0 }}\r\n                    animate={{ opacity: 1 }}\r\n                    exit={{ opacity: 0 }}\r\n                    className=\"absolute inset-0 bg-primary/10 border-2 border-primary rounded-lg pointer-events-none z-5\"\r\n                  />\r\n                )}\r\n              </AnimatePresence>\r\n\r\n              {/* 提示词卡片 */}\r\n              <div\r\n                className={`${isSelectionMode ? 'cursor-pointer' : ''} ${\r\n                  isSelectionMode && isSelected(prompt.id) ? 'ring-2 ring-primary' : ''\r\n                }`}\r\n                onClick={isSelectionMode ? () => toggleItem(prompt.id) : undefined}\r\n              >\r\n                <PromptCard\r\n                  prompt={prompt}\r\n                  showCategory={showCategory}\r\n                  showActions={showActions && !isSelectionMode}\r\n                  onEdit={() => {\r\n                    // 处理编辑\r\n                  }}\r\n                  onDelete={() => {\r\n                    // 处理删除\r\n                  }}\r\n                  onUse={() => {\r\n                    // 处理使用\r\n                  }}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </AnimatePresence>\r\n      </div>\r\n\r\n      {/* 批量操作栏 */}\r\n      <BatchOperationBar\r\n        selectedItems={selectedItems}\r\n        onClearSelection={clearSelection}\r\n        onRefresh={handleRefresh}\r\n      />\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;;;AANA;;;;;AAqBO,MAAM,0BAA0B;QAAC,EACtC,OAAO,EACP,UAAU,KAAK,EACf,UAAU,CAAC,EACX,eAAe,IAAI,EACnB,cAAc,IAAI,EAClB,eAAe,OAAO,EACtB,WAAW,EACX,cAAc,EACd,YAAY,EAAE,EACe;;IAC7B,MAAM,EACJ,aAAa,EACb,eAAe,EACf,UAAU,EACV,aAAa,EACb,mBAAmB,EACnB,YAAY,EACZ,cAAc,EACd,mBAAmB,EACnB,UAAU,EACV,SAAS,EACT,cAAc,EACd,eAAe,EAChB,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAEvC,SAAS;IACT,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,IAAI,cAAc,YAAY;YAC5B;QACF,OAAO;YACL,UAAU;QACZ;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,2BAAA,qCAAA;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,AAAC,QAA8B,OAAvB,eAAc,WAAmB,OAAV;sBAC5C,MAAM,IAAI,CAAC;gBAAE,QAAQ,UAAU;YAAE,GAAG,CAAC,GAAG,kBACvC,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,QAAQ,MAAM,EAAE;QACnB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAW,AAAC,qBAA8B,OAAV;;8BAEhC,6LAAC;oBACC,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,aAAa;oBACb,QAAO;oBACP,WAAU;8BAEV,cAAA,6LAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,GAAE;;;;;;;;;;;8BAGN,6LAAC;oBAAG,WAAU;8BACX;;;;;;gBAEF;;;;;;;IAGP;IAEA,qBACE,6LAAC;QAAI,WAAW;;0BAEd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAW,AAAC,cAA2D,OAA9C,kBAAkB,gBAAgB;;kDAE3D,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAGL,kBAAkB,SAAS;;;;;;;0CAI9B,6LAAC,4LAAA,CAAA,kBAAe;0CACb,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,WAAU;;sDAEV,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS,cAAc;oDACvB,KAAK,CAAC;wDACJ,IAAI,IAAI;4DACN,GAAG,aAAa,GAAG,oBAAoB;wDACzC;oDACF;oDACA,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;wCAG3B,8BACC,6LAAC;4CAAK,WAAU;;gDAA+B;gDACxC;gDAAe;;;;;;;sDAIxB,6LAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAST,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;gCAA+B;gCAC1C,QAAQ,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAMxB,6LAAC;gBAAI,WAAW,AAAC,QAAqB,OAAd,eAAc;0BACpC,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,OAAO,QAAQ;4BAAK;4BAClC,WAAU;;8CAGV,6LAAC,4LAAA,CAAA,kBAAe;8CACb,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,WAAU;kDAEV,cAAA,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;gDACC,MAAK;gDACL,SAAS,WAAW,OAAO,EAAE;gDAC7B,UAAU,IAAM,WAAW,OAAO,EAAE;gDACpC,WAAU;;;;;;;;;;;;;;;;;;;;;8CAQpB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,mBAAmB,WAAW,OAAO,EAAE,mBACtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,WAAU;;;;;;;;;;;8CAMhB,6LAAC;oCACC,WAAW,AAAC,GACV,OADY,kBAAkB,mBAAmB,IAAG,KAErD,OADC,mBAAmB,WAAW,OAAO,EAAE,IAAI,wBAAwB;oCAErE,SAAS,kBAAkB,IAAM,WAAW,OAAO,EAAE,IAAI;8CAEzD,cAAA,6LAAC,8IAAA,CAAA,aAAU;wCACT,QAAQ;wCACR,cAAc;wCACd,aAAa,eAAe,CAAC;wCAC7B,QAAQ;wCACN,OAAO;wCACT;wCACA,UAAU;wCACR,OAAO;wCACT;wCACA,OAAO;wCACL,OAAO;wCACT;;;;;;;;;;;;2BA3DC,OAAO,EAAE;;;;;;;;;;;;;;;0BAoEtB,6LAAC,mJAAA,CAAA,oBAAiB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,WAAW;;;;;;;;;;;;AAInB;GA1Pa;;QAwBP,oIAAA,CAAA,oBAAiB;;;KAxBV", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/CategoryFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\n\r\ninterface CategoryFilterProps {\r\n  selectedCategories: string[]\r\n  onCategoryChange: (categoryIds: string[]) => void\r\n  showCounts?: boolean\r\n  allowMultiple?: boolean\r\n  placeholder?: string\r\n  className?: string\r\n}\r\n\r\nexport const CategoryFilter = ({\r\n  selectedCategories,\r\n  onCategoryChange,\r\n  showCounts = true,\r\n  allowMultiple = true,\r\n  placeholder = \"选择分类...\",\r\n  className = '',\r\n}: CategoryFilterProps) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n\r\n  // 获取所有分类\r\n  const { data: categories, isLoading } = api.categories.getAll.useQuery()\r\n\r\n  // 过滤分类\r\n  const filteredCategories = categories?.filter(category =>\r\n    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))\r\n  ) || []\r\n\r\n  // 处理分类选择\r\n  const handleCategoryToggle = (categoryId: string) => {\r\n    if (allowMultiple) {\r\n      if (selectedCategories.includes(categoryId)) {\r\n        onCategoryChange(selectedCategories.filter(id => id !== categoryId))\r\n      } else {\r\n        onCategoryChange([...selectedCategories, categoryId])\r\n      }\r\n    } else {\r\n      if (selectedCategories.includes(categoryId)) {\r\n        onCategoryChange([])\r\n      } else {\r\n        onCategoryChange([categoryId])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 清除所有选择\r\n  const clearAll = () => {\r\n    onCategoryChange([])\r\n  }\r\n\r\n  // 获取选中的分类名称\r\n  const getSelectedCategoryNames = () => {\r\n    if (!categories) return []\r\n    return categories\r\n      .filter(cat => selectedCategories.includes(cat.id))\r\n      .map(cat => cat.name)\r\n  }\r\n\r\n  // 显示文本\r\n  const getDisplayText = () => {\r\n    const selectedNames = getSelectedCategoryNames()\r\n    if (selectedNames.length === 0) return placeholder\r\n    if (selectedNames.length === 1) return selectedNames[0]\r\n    return `${selectedNames.length} 个分类`\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 触发按钮 */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 text-sm border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n      >\r\n        <span className={`truncate ${selectedCategories.length === 0 ? 'text-base-content/50' : 'text-base-content'}`}>\r\n          {getDisplayText()}\r\n        </span>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedCategories.length > 0 && (\r\n            <motion.button\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                clearAll()\r\n              }}\r\n              whileHover={{ scale: 1.1 }}\r\n              whileTap={{ scale: 0.9 }}\r\n              className=\"flex items-center justify-center w-4 h-4 bg-base-content/20 rounded-full hover:bg-base-content/30\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n          \r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        </div>\r\n      </button>\r\n\r\n      {/* 下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden\"\r\n          >\r\n            {/* 搜索框 */}\r\n            <div className=\"p-3 border-b border-base-300\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"搜索分类...\"\r\n                  className=\"w-full pl-8 pr-4 py-2 text-sm border border-base-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n                />\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4 absolute left-2.5 top-2.5 text-base-content/50\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 分类列表 */}\r\n            <div className=\"max-h-64 overflow-y-auto\">\r\n              {isLoading ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  <span className=\"ml-2\">加载中...</span>\r\n                </div>\r\n              ) : filteredCategories.length === 0 ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  {searchTerm ? '未找到匹配的分类' : '暂无分类'}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-2 space-y-1\">\r\n                  {filteredCategories.map((category) => (\r\n                    <motion.label\r\n                      key={category.id}\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200 cursor-pointer\"\r\n                    >\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type={allowMultiple ? 'checkbox' : 'radio'}\r\n                          name=\"category\"\r\n                          checked={selectedCategories.includes(category.id)}\r\n                          onChange={() => handleCategoryToggle(category.id)}\r\n                          className=\"sr-only\"\r\n                        />\r\n                        <div\r\n                          className={`w-4 h-4 rounded ${\r\n                            allowMultiple ? 'rounded-sm' : 'rounded-full'\r\n                          } border-2 transition-colors ${\r\n                            selectedCategories.includes(category.id)\r\n                              ? 'bg-primary border-primary'\r\n                              : 'border-base-300'\r\n                          }`}\r\n                        >\r\n                          {selectedCategories.includes(category.id) && (\r\n                            <svg\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                              fill=\"none\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              strokeWidth={3}\r\n                              stroke=\"currentColor\"\r\n                              className=\"w-3 h-3 text-primary-content\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M4.5 12.75l6 6 9-13.5\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 分类图标 */}\r\n                      <div\r\n                        className=\"w-6 h-6 rounded flex items-center justify-center text-white text-xs font-semibold\"\r\n                        style={{ backgroundColor: category.color }}\r\n                      >\r\n                        {category.icon || category.name.charAt(0)}\r\n                      </div>\r\n\r\n                      {/* 分类信息 */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"text-sm font-medium text-base-content truncate\">\r\n                          {category.name}\r\n                        </p>\r\n                        {category.description && (\r\n                          <p className=\"text-xs text-base-content/70 truncate\">\r\n                            {category.description}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* 统计信息 */}\r\n                      {showCounts && (\r\n                        <div className=\"flex items-center space-x-2 text-xs text-base-content/50\">\r\n                          <span>{category.promptCount || 0}</span>\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth={1.5}\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-3 h-3\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                            />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </motion.label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 底部操作 */}\r\n            {selectedCategories.length > 0 && (\r\n              <div className=\"p-3 border-t border-base-300 bg-base-50\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectedCategories.length} 个分类\r\n                  </span>\r\n                  <button\r\n                    onClick={clearAll}\r\n                    className=\"text-sm text-primary hover:text-primary-focus\"\r\n                  >\r\n                    清除全部\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 点击外部关闭 */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAgBO,MAAM,iBAAiB;QAAC,EAC7B,kBAAkB,EAClB,gBAAgB,EAChB,aAAa,IAAI,EACjB,gBAAgB,IAAI,EACpB,cAAc,SAAS,EACvB,YAAY,EAAE,EACM;;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAEtE,OAAO;IACP,MAAM,qBAAqB,CAAA,uBAAA,iCAAA,WAAY,MAAM,CAAC,CAAA,WAC5C,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SACxF,EAAE;IAEP,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe;YACjB,IAAI,mBAAmB,QAAQ,CAAC,aAAa;gBAC3C,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO;YAC1D,OAAO;gBACL,iBAAiB;uBAAI;oBAAoB;iBAAW;YACtD;QACF,OAAO;YACL,IAAI,mBAAmB,QAAQ,CAAC,aAAa;gBAC3C,iBAAiB,EAAE;YACrB,OAAO;gBACL,iBAAiB;oBAAC;iBAAW;YAC/B;QACF;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,YAAY;IACZ,MAAM,2BAA2B;QAC/B,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,OAAO,WACJ,MAAM,CAAC,CAAA,MAAO,mBAAmB,QAAQ,CAAC,IAAI,EAAE,GAChD,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IACxB;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,MAAM,gBAAgB;QACtB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,aAAa,CAAC,EAAE;QACvD,OAAO,AAAC,GAAuB,OAArB,cAAc,MAAM,EAAC;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BAE1B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAK,WAAW,AAAC,YAA0F,OAA/E,mBAAmB,MAAM,KAAK,IAAI,yBAAyB;kCACrF;;;;;;kCAGH,6LAAC;wBAAI,WAAU;;4BACZ,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;0CAK3D,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAW,AAAC,gCAA0D,OAA3B,SAAS,eAAe;0CAEnE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;2EAEvB,mBAAmB,MAAM,KAAK,kBAChC,6LAAC;gCAAI,WAAU;0CACZ,aAAa,aAAa;;;;;yFAG7B,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wCAEX,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAM,gBAAgB,aAAa;wDACnC,MAAK;wDACL,SAAS,mBAAmB,QAAQ,CAAC,SAAS,EAAE;wDAChD,UAAU,IAAM,qBAAqB,SAAS,EAAE;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;wDACC,WAAW,AAAC,mBAGV,OAFA,gBAAgB,eAAe,gBAChC,gCAIA,OAHC,mBAAmB,QAAQ,CAAC,SAAS,EAAE,IACnC,8BACA;kEAGL,mBAAmB,QAAQ,CAAC,SAAS,EAAE,mBACtC,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAQZ,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;0DAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,SAAS,IAAI;;;;;;oDAEf,SAAS,WAAW,kBACnB,6LAAC;wDAAE,WAAU;kEACV,SAAS,WAAW;;;;;;;;;;;;4CAM1B,4BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,SAAS,WAAW,IAAI;;;;;;kEAC/B,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;;;;;;;;uCA5EL,SAAS,EAAE;;;;;;;;;;;;;;;wBAwFzB,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAA+B;4CACxC,mBAAmB,MAAM;4CAAC;;;;;;;kDAEjC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GAlRa;KAAA", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/TagFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Tag } from '~/types'\r\n\r\ninterface TagFilterProps {\r\n  selectedTags: string[]\r\n  onTagChange: (tagIds: string[]) => void\r\n  showCounts?: boolean\r\n  allowMultiple?: boolean\r\n  placeholder?: string\r\n  className?: string\r\n}\r\n\r\nexport const TagFilter = ({\r\n  selectedTags,\r\n  onTagChange,\r\n  showCounts = true,\r\n  allowMultiple = true,\r\n  placeholder = \"选择标签...\",\r\n  className = '',\r\n}: TagFilterProps) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n\r\n  // 获取所有标签\r\n  const { data: tags, isLoading } = api.tags.getAll.useQuery()\r\n\r\n  // 过滤标签\r\n  const filteredTags = tags?.filter(tag =>\r\n    tag.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  ) || []\r\n\r\n  // 处理标签选择\r\n  const handleTagToggle = (tagId: string) => {\r\n    if (allowMultiple) {\r\n      if (selectedTags.includes(tagId)) {\r\n        onTagChange(selectedTags.filter(id => id !== tagId))\r\n      } else {\r\n        onTagChange([...selectedTags, tagId])\r\n      }\r\n    } else {\r\n      if (selectedTags.includes(tagId)) {\r\n        onTagChange([])\r\n      } else {\r\n        onTagChange([tagId])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 清除所有选择\r\n  const clearAll = () => {\r\n    onTagChange([])\r\n  }\r\n\r\n  // 获取选中的标签名称\r\n  const getSelectedTagNames = () => {\r\n    if (!tags) return []\r\n    return tags\r\n      .filter(tag => selectedTags.includes(tag.id))\r\n      .map(tag => tag.name)\r\n  }\r\n\r\n  // 显示文本\r\n  const getDisplayText = () => {\r\n    const selectedNames = getSelectedTagNames()\r\n    if (selectedNames.length === 0) return placeholder\r\n    if (selectedNames.length === 1) return selectedNames[0]\r\n    return `${selectedNames.length} 个标签`\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 触发按钮 */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 text-sm border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n      >\r\n        <span className={`truncate ${selectedTags.length === 0 ? 'text-base-content/50' : 'text-base-content'}`}>\r\n          {getDisplayText()}\r\n        </span>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedTags.length > 0 && (\r\n            <motion.button\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                clearAll()\r\n              }}\r\n              whileHover={{ scale: 1.1 }}\r\n              whileTap={{ scale: 0.9 }}\r\n              className=\"flex items-center justify-center w-4 h-4 bg-base-content/20 rounded-full hover:bg-base-content/30\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n          \r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        </div>\r\n      </button>\r\n\r\n      {/* 下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden\"\r\n          >\r\n            {/* 搜索框 */}\r\n            <div className=\"p-3 border-b border-base-300\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"搜索标签...\"\r\n                  className=\"w-full pl-8 pr-4 py-2 text-sm border border-base-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n                />\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4 absolute left-2.5 top-2.5 text-base-content/50\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 标签列表 */}\r\n            <div className=\"max-h-64 overflow-y-auto\">\r\n              {isLoading ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  <span className=\"ml-2\">加载中...</span>\r\n                </div>\r\n              ) : filteredTags.length === 0 ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  {searchTerm ? '未找到匹配的标签' : '暂无标签'}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-2 space-y-1\">\r\n                  {filteredTags.map((tag) => (\r\n                    <motion.label\r\n                      key={tag.id}\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200 cursor-pointer\"\r\n                    >\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type={allowMultiple ? 'checkbox' : 'radio'}\r\n                          name=\"tag\"\r\n                          checked={selectedTags.includes(tag.id)}\r\n                          onChange={() => handleTagToggle(tag.id)}\r\n                          className=\"sr-only\"\r\n                        />\r\n                        <div\r\n                          className={`w-4 h-4 rounded ${\r\n                            allowMultiple ? 'rounded-sm' : 'rounded-full'\r\n                          } border-2 transition-colors ${\r\n                            selectedTags.includes(tag.id)\r\n                              ? 'bg-primary border-primary'\r\n                              : 'border-base-300'\r\n                          }`}\r\n                        >\r\n                          {selectedTags.includes(tag.id) && (\r\n                            <svg\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                              fill=\"none\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              strokeWidth={3}\r\n                              stroke=\"currentColor\"\r\n                              className=\"w-3 h-3 text-primary-content\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M4.5 12.75l6 6 9-13.5\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 标签图标 */}\r\n                      <div className=\"flex items-center justify-center w-6 h-6 bg-primary/10 rounded-full\">\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          strokeWidth={1.5}\r\n                          stroke=\"currentColor\"\r\n                          className=\"w-3 h-3 text-primary\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n                          />\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n                        </svg>\r\n                      </div>\r\n\r\n                      {/* 标签信息 */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"text-sm font-medium text-base-content truncate\">\r\n                          {tag.name}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* 统计信息 */}\r\n                      {showCounts && (\r\n                        <div className=\"flex items-center space-x-1 text-xs text-base-content/50\">\r\n                          <span>{tag.promptCount || 0}</span>\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth={1.5}\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-3 h-3\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                            />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </motion.label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 底部操作 */}\r\n            {selectedTags.length > 0 && (\r\n              <div className=\"p-3 border-t border-base-300 bg-base-50\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectedTags.length} 个标签\r\n                  </span>\r\n                  <button\r\n                    onClick={clearAll}\r\n                    className=\"text-sm text-primary hover:text-primary-focus\"\r\n                  >\r\n                    清除全部\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 点击外部关闭 */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAgBO,MAAM,YAAY;QAAC,EACxB,YAAY,EACZ,WAAW,EACX,aAAa,IAAI,EACjB,gBAAgB,IAAI,EACpB,cAAc,SAAS,EACvB,YAAY,EAAE,EACC;;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAE1D,OAAO;IACP,MAAM,eAAe,CAAA,iBAAA,2BAAA,KAAM,MAAM,CAAC,CAAA,MAChC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SACnD,EAAE;IAEP,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe;YACjB,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,YAAY,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YAC/C,OAAO;gBACL,YAAY;uBAAI;oBAAc;iBAAM;YACtC;QACF,OAAO;YACL,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,YAAY,EAAE;YAChB,OAAO;gBACL,YAAY;oBAAC;iBAAM;YACrB;QACF;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,YAAY,EAAE;IAChB;IAEA,YAAY;IACZ,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,OAAO,EAAE;QACpB,OAAO,KACJ,MAAM,CAAC,CAAA,MAAO,aAAa,QAAQ,CAAC,IAAI,EAAE,GAC1C,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IACxB;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,MAAM,gBAAgB;QACtB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,aAAa,CAAC,EAAE;QACvD,OAAO,AAAC,GAAuB,OAArB,cAAc,MAAM,EAAC;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BAE1B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAK,WAAW,AAAC,YAAoF,OAAzE,aAAa,MAAM,KAAK,IAAI,yBAAyB;kCAC/E;;;;;;kCAGH,6LAAC;wBAAI,WAAU;;4BACZ,aAAa,MAAM,GAAG,mBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;0CAK3D,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAW,AAAC,gCAA0D,OAA3B,SAAS,eAAe;0CAEnE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;2EAEvB,aAAa,MAAM,KAAK,kBAC1B,6LAAC;gCAAI,WAAU;0CACZ,aAAa,aAAa;;;;;yFAG7B,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wCAEX,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAM,gBAAgB,aAAa;wDACnC,MAAK;wDACL,SAAS,aAAa,QAAQ,CAAC,IAAI,EAAE;wDACrC,UAAU,IAAM,gBAAgB,IAAI,EAAE;wDACtC,WAAU;;;;;;kEAEZ,6LAAC;wDACC,WAAW,AAAC,mBAGV,OAFA,gBAAgB,eAAe,gBAChC,gCAIA,OAHC,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,8BACA;kEAGL,aAAa,QAAQ,CAAC,IAAI,EAAE,mBAC3B,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAQZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAU;;sEAEV,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;sEAEJ,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,GAAE;;;;;;;;;;;;;;;;;0DAKzD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DACV,IAAI,IAAI;;;;;;;;;;;4CAKZ,4BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,IAAI,WAAW,IAAI;;;;;;kEAC1B,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;;;;;;;;uCAlFL,IAAI,EAAE;;;;;;;;;;;;;;;wBA8FpB,aAAa,MAAM,GAAG,mBACrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAA+B;4CACxC,aAAa,MAAM;4CAAC;;;;;;;kDAE3B,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GAvRa;KAAA", "debugId": null}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/FilterBar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { CategoryFilter } from './CategoryFilter'\r\nimport { TagFilter } from './TagFilter'\r\n\r\ninterface FilterBarProps {\r\n  // 分类筛选\r\n  selectedCategories: string[]\r\n  onCategoryChange: (categoryIds: string[]) => void\r\n  \r\n  // 标签筛选\r\n  selectedTags: string[]\r\n  onTagChange: (tagIds: string[]) => void\r\n  \r\n  // 排序\r\n  sortBy: 'created' | 'updated' | 'usage' | 'title'\r\n  sortOrder: 'asc' | 'desc'\r\n  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void\r\n  \r\n  // 其他筛选\r\n  showFavorites?: boolean\r\n  onShowFavoritesChange?: (showFavorites: boolean) => void\r\n  \r\n  // 显示控制\r\n  showCategoryFilter?: boolean\r\n  showTagFilter?: boolean\r\n  showSortFilter?: boolean\r\n  showFavoriteFilter?: boolean\r\n  \r\n  className?: string\r\n}\r\n\r\nexport const FilterBar = ({\r\n  selectedCategories,\r\n  onCategoryChange,\r\n  selectedTags,\r\n  onTagChange,\r\n  sortBy,\r\n  sortOrder,\r\n  onSortChange,\r\n  showFavorites = false,\r\n  onShowFavoritesChange,\r\n  showCategoryFilter = true,\r\n  showTagFilter = true,\r\n  showSortFilter = true,\r\n  showFavoriteFilter = true,\r\n  className = '',\r\n}: FilterBarProps) => {\r\n  const [isExpanded, setIsExpanded] = useState(false)\r\n\r\n  // 排序选项\r\n  const sortOptions = [\r\n    { value: 'updated-desc', label: '最近更新', sortBy: 'updated', sortOrder: 'desc' },\r\n    { value: 'created-desc', label: '最新创建', sortBy: 'created', sortOrder: 'desc' },\r\n    { value: 'usage-desc', label: '使用最多', sortBy: 'usage', sortOrder: 'desc' },\r\n    { value: 'title-asc', label: '标题 A-Z', sortBy: 'title', sortOrder: 'asc' },\r\n    { value: 'title-desc', label: '标题 Z-A', sortBy: 'title', sortOrder: 'desc' },\r\n    { value: 'created-asc', label: '最早创建', sortBy: 'created', sortOrder: 'asc' },\r\n    { value: 'updated-asc', label: '最早更新', sortBy: 'updated', sortOrder: 'asc' },\r\n    { value: 'usage-asc', label: '使用最少', sortBy: 'usage', sortOrder: 'asc' },\r\n  ]\r\n\r\n  // 处理排序变化\r\n  const handleSortChange = (value: string) => {\r\n    const option = sortOptions.find(opt => opt.value === value)\r\n    if (option) {\r\n      onSortChange(option.sortBy, option.sortOrder as 'asc' | 'desc')\r\n    }\r\n  }\r\n\r\n  // 清除所有筛选\r\n  const clearAllFilters = () => {\r\n    onCategoryChange([])\r\n    onTagChange([])\r\n    if (onShowFavoritesChange) {\r\n      onShowFavoritesChange(false)\r\n    }\r\n  }\r\n\r\n  // 检查是否有活跃筛选\r\n  const hasActiveFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n      {/* 主筛选栏 */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 分类筛选 */}\r\n          {showCategoryFilter && (\r\n            <CategoryFilter\r\n              selectedCategories={selectedCategories}\r\n              onCategoryChange={onCategoryChange}\r\n              className=\"w-48\"\r\n            />\r\n          )}\r\n\r\n          {/* 标签筛选 */}\r\n          {showTagFilter && (\r\n            <TagFilter\r\n              selectedTags={selectedTags}\r\n              onTagChange={onTagChange}\r\n              className=\"w-48\"\r\n            />\r\n          )}\r\n\r\n          {/* 收藏筛选 */}\r\n          {showFavoriteFilter && onShowFavoritesChange && (\r\n            <motion.button\r\n              onClick={() => onShowFavoritesChange(!showFavorites)}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className={`flex items-center space-x-2 px-3 py-2 text-sm rounded-lg border transition-colors ${\r\n                showFavorites\r\n                  ? 'bg-warning text-warning-content border-warning'\r\n                  : 'bg-base-100 text-base-content border-base-300 hover:bg-base-200'\r\n              }`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill={showFavorites ? 'currentColor' : 'none'}\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\r\n                />\r\n              </svg>\r\n              <span>收藏</span>\r\n            </motion.button>\r\n          )}\r\n\r\n          {/* 展开/收起高级筛选 */}\r\n          <motion.button\r\n            onClick={() => setIsExpanded(!isExpanded)}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm text-base-content/70 hover:text-base-content\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 12H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 18H7.5\"\r\n              />\r\n            </svg>\r\n            <span>高级筛选</span>\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n            </svg>\r\n          </motion.button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 排序选择 */}\r\n          {showSortFilter && (\r\n            <select\r\n              value={`${sortBy}-${sortOrder}`}\r\n              onChange={(e) => handleSortChange(e.target.value)}\r\n              className=\"select select-bordered select-sm\"\r\n            >\r\n              {sortOptions.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n\r\n          {/* 清除筛选 */}\r\n          {hasActiveFilters && (\r\n            <motion.button\r\n              onClick={clearAllFilters}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"flex items-center space-x-2 px-3 py-2 text-sm text-base-content/70 hover:text-base-content\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              <span>清除筛选</span>\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 高级筛选面板 */}\r\n      <AnimatePresence>\r\n        {isExpanded && (\r\n          <motion.div\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: 'auto' }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"overflow-hidden\"\r\n          >\r\n            <div className=\"bg-base-100 border border-base-300 rounded-lg p-4 space-y-4\">\r\n              <h3 className=\"text-sm font-medium text-base-content\">高级筛选选项</h3>\r\n              \r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                {/* 更多筛选选项可以在这里添加 */}\r\n                \r\n                {/* 日期范围筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">创建日期</label>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"开始日期\"\r\n                    />\r\n                    <span className=\"text-base-content/50\">至</span>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"结束日期\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* 使用次数筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">使用次数</label>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"最少\"\r\n                    />\r\n                    <span className=\"text-base-content/50\">至</span>\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"最多\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* 内容长度筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">内容长度</label>\r\n                  <select className=\"select select-bordered select-sm w-full\">\r\n                    <option value=\"\">全部</option>\r\n                    <option value=\"short\">短 (少于100字)</option>\r\n                    <option value=\"medium\">中 (100-500字)</option>\r\n                    <option value=\"long\">长 (超过500字)</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 应用/重置按钮 */}\r\n              <div className=\"flex justify-end space-x-2\">\r\n                <button className=\"btn btn-ghost btn-sm\">重置</button>\r\n                <button className=\"btn btn-primary btn-sm\">应用筛选</button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 活跃筛选标签 */}\r\n      {hasActiveFilters && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"flex items-center space-x-2 text-sm\"\r\n        >\r\n          <span className=\"text-base-content/70\">活跃筛选:</span>\r\n          \r\n          {/* 分类标签 */}\r\n          {selectedCategories.length > 0 && (\r\n            <span className=\"badge badge-primary badge-sm\">\r\n              {selectedCategories.length} 个分类\r\n            </span>\r\n          )}\r\n          \r\n          {/* 标签标签 */}\r\n          {selectedTags.length > 0 && (\r\n            <span className=\"badge badge-secondary badge-sm\">\r\n              {selectedTags.length} 个标签\r\n            </span>\r\n          )}\r\n          \r\n          {/* 收藏标签 */}\r\n          {showFavorites && (\r\n            <span className=\"badge badge-warning badge-sm\">\r\n              仅收藏\r\n            </span>\r\n          )}\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAkCO,MAAM,YAAY;QAAC,EACxB,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,YAAY,EACZ,gBAAgB,KAAK,EACrB,qBAAqB,EACrB,qBAAqB,IAAI,EACzB,gBAAgB,IAAI,EACpB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,YAAY,EAAE,EACC;;IACf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,OAAO;IACP,MAAM,cAAc;QAClB;YAAE,OAAO;YAAgB,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAO;QAC7E;YAAE,OAAO;YAAgB,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAO;QAC7E;YAAE,OAAO;YAAc,OAAO;YAAQ,QAAQ;YAAS,WAAW;QAAO;QACzE;YAAE,OAAO;YAAa,OAAO;YAAU,QAAQ;YAAS,WAAW;QAAM;QACzE;YAAE,OAAO;YAAc,OAAO;YAAU,QAAQ;YAAS,WAAW;QAAO;QAC3E;YAAE,OAAO;YAAe,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAM;QAC3E;YAAE,OAAO;YAAe,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAM;QAC3E;YAAE,OAAO;YAAa,OAAO;YAAQ,QAAQ;YAAS,WAAW;QAAM;KACxE;IAED,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACrD,IAAI,QAAQ;YACV,aAAa,OAAO,MAAM,EAAE,OAAO,SAAS;QAC9C;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,iBAAiB,EAAE;QACnB,YAAY,EAAE;QACd,IAAI,uBAAuB;YACzB,sBAAsB;QACxB;IACF;IAEA,YAAY;IACZ,MAAM,mBAAmB,mBAAmB,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;IAErF,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAEZ,oCACC,6LAAC,kJAAA,CAAA,iBAAc;gCACb,oBAAoB;gCACpB,kBAAkB;gCAClB,WAAU;;;;;;4BAKb,+BACC,6LAAC,6IAAA,CAAA,YAAS;gCACR,cAAc;gCACd,aAAa;gCACb,WAAU;;;;;;4BAKb,sBAAsB,uCACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,sBAAsB,CAAC;gCACtC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAW,AAAC,qFAIX,OAHC,gBACI,mDACA;;kDAGN,6LAAC;wCACC,OAAM;wCACN,MAAM,gBAAgB,iBAAiB;wCACvC,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDAAK;;;;;;;;;;;;0CAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,cAAc,CAAC;gCAC9B,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAW,AAAC,gCAA8D,OAA/B,aAAa,eAAe;kDAEvE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAK3D,6LAAC;wBAAI,WAAU;;4BAEZ,gCACC,6LAAC;gCACC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;gCACpB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;0CAET,YAAY,GAAG,CAAC,CAAA,uBACf,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;4BAQ9B,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;kDAEvD,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDAIb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;kEACvC,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;kEACvC,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAAuB;;;;;;kDACzC,6LAAC;wCAAO,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpD,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;oBAGtC,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;wBAAK,WAAU;;4BACb,mBAAmB,MAAM;4BAAC;;;;;;;oBAK9B,aAAa,MAAM,GAAG,mBACrB,6LAAC;wBAAK,WAAU;;4BACb,aAAa,MAAM;4BAAC;;;;;;;oBAKxB,+BACC,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;AAQ3D;GA/Ra;KAAA", "debugId": null}}, {"offset": {"line": 2790, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/index.ts"], "sourcesContent": ["export { CategoryFilter } from './CategoryFilter'\r\nexport { TagFilter } from './TagFilter'\r\nexport { FilterBar } from './FilterBar'"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2815, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/prompts/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { api } from '~/trpc/react'\r\nimport { PromptGridWithSelection } from '~/components/prompts/PromptGridWithSelection'\r\nimport { FilterBar } from '~/components/filters'\r\n\r\nexport default function PromptsPage() {\r\n  // 筛选状态\r\n  const [selectedCategories, setSelectedCategories] = useState<string[]>([])\r\n  const [selectedTags, setSelectedTags] = useState<string[]>([])\r\n  const [sortBy, setSortBy] = useState<'created' | 'updated' | 'usage' | 'title'>('updated')\r\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\r\n  const [showFavorites, setShowFavorites] = useState(false)\r\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\r\n\r\n  // 获取提示词列表\r\n  const { data: promptsData, isLoading, refetch } = api.prompts.getFiltered.useQuery({\r\n    categoryIds: selectedCategories.length > 0 ? selectedCategories : undefined,\r\n    tagIds: selectedTags.length > 0 ? selectedTags : undefined,\r\n    sortBy,\r\n    sortOrder,\r\n    favoritesOnly: showFavorites,\r\n  })\r\n\r\n  const prompts = promptsData?.prompts || []\r\n\r\n  // 处理排序变化\r\n  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {\r\n    setSortBy(newSortBy as 'created' | 'updated' | 'usage' | 'title')\r\n    setSortOrder(newSortOrder)\r\n  }\r\n\r\n  // 检查是否有筛选条件\r\n  const hasFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites\r\n\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-8 space-y-6\">\r\n      {/* 页面头部 */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-base-content\">提示词管理</h1>\r\n          <p className=\"text-base-content/70 mt-1\">\r\n            {prompts ? `共 ${prompts.length} 个提示词` : '加载中...'}\r\n          </p>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 视图切换 */}\r\n          <div className=\"btn-group\">\r\n            <button\r\n              onClick={() => setViewMode('grid')}\r\n              className={`btn btn-sm ${viewMode === 'grid' ? 'btn-active' : ''}`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            <button\r\n              onClick={() => setViewMode('list')}\r\n              className={`btn btn-sm ${viewMode === 'list' ? 'btn-active' : ''}`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* 新建按钮 */}\r\n          <Link href=\"/prompts/new\" className=\"btn btn-primary btn-sm\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\r\n            </svg>\r\n            新建提示词\r\n          </Link>\r\n\r\n          {/* 导入按钮 */}\r\n          <Link href=\"/prompts/import\" className=\"btn btn-secondary btn-sm\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5\"\r\n              />\r\n            </svg>\r\n            批量导入\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 筛选栏 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.1 }}\r\n        className=\"bg-base-100 rounded-lg p-4 shadow-sm\"\r\n      >\r\n        <FilterBar\r\n          selectedCategories={selectedCategories}\r\n          onCategoryChange={setSelectedCategories}\r\n          selectedTags={selectedTags}\r\n          onTagChange={setSelectedTags}\r\n          sortBy={sortBy}\r\n          sortOrder={sortOrder}\r\n          onSortChange={handleSortChange}\r\n          showFavorites={showFavorites}\r\n          onShowFavoritesChange={setShowFavorites}\r\n        />\r\n      </motion.div>\r\n\r\n      {/* 提示词列表 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.2 }}\r\n      >\r\n        <PromptGridWithSelection\r\n          prompts={prompts}\r\n          loading={isLoading}\r\n          columns={viewMode === 'grid' ? 3 : 1}\r\n          showCategory={true}\r\n          showActions={true}\r\n          emptyMessage={\r\n            hasFilters\r\n              ? \"未找到符合条件的提示词\"\r\n              : \"还没有创建任何提示词\"\r\n          }\r\n          emptyAction={\r\n            !hasFilters ? (\r\n              <Link href=\"/prompts/new\" className=\"btn btn-primary\">\r\n                创建第一个提示词\r\n              </Link>\r\n            ) : undefined\r\n          }\r\n          onPromptUpdate={refetch}\r\n        />\r\n      </motion.div>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 注意：metadata 不能在客户端组件中导出，已移除"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,OAAO;IACP,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,UAAU;IACV,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC;QACjF,aAAa,mBAAmB,MAAM,GAAG,IAAI,qBAAqB;QAClE,QAAQ,aAAa,MAAM,GAAG,IAAI,eAAe;QACjD;QACA;QACA,eAAe;IACjB;IAEA,MAAM,UAAU,CAAA,wBAAA,kCAAA,YAAa,OAAO,KAAI,EAAE;IAE1C,SAAS;IACT,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,UAAU;QACV,aAAa;IACf;IAEA,YAAY;IACZ,MAAM,aAAa,mBAAmB,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;IAE/E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CACV,uCAAU,AAAC,KAAmB,OAAf,QAAQ,MAAM,EAAC,WAAS;;;;;;;;;;;;kCAI5C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,AAAC,cAAqD,OAAxC,aAAa,SAAS,eAAe;kDAE9D,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,AAAC,cAAqD,OAAxC,aAAa,SAAS,eAAe;kDAE9D,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAOV,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAe,WAAU;;kDAClC,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;oCACjD;;;;;;;0CAKR,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;;kDACrC,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,6LAAC,6IAAA,CAAA,YAAS;oBACR,oBAAoB;oBACpB,kBAAkB;oBAClB,cAAc;oBACd,aAAa;oBACb,QAAQ;oBACR,WAAW;oBACX,cAAc;oBACd,eAAe;oBACf,uBAAuB;;;;;;;;;;;0BAK3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,6LAAC,2JAAA,CAAA,0BAAuB;oBACtB,SAAS;oBACT,SAAS;oBACT,SAAS,aAAa,SAAS,IAAI;oBACnC,cAAc;oBACd,aAAa;oBACb,cACE,aACI,gBACA;oBAEN,aACE,CAAC,2BACC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAe,WAAU;kCAAkB;;;;;iCAGpD;oBAEN,gBAAgB;;;;;;;;;;;;;;;;;AAK1B,EAEA,8BAA8B;GA1KN;KAAA", "debugId": null}}]}