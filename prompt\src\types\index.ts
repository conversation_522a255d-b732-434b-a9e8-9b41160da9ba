// 基础类型定义
export interface Category {
  id: string
  name: string
  description: string | null
  color: string
  icon: string
  parentId: string | null
  userId: string
  createdAt: string
  updatedAt: string
  promptCount?: number
  children?: Category[]
}

export interface Tag {
  id: string
  name: string
  color: string
  userId: string
  createdAt: string
  promptCount?: number
}

export interface Prompt {
  id: string
  title: string
  content: string
  description: string | null
  categoryId: string | null
  userId: string
  usageCount: number
  isFavorite: boolean
  isPublic: boolean
  createdAt: string
  updatedAt: string
  category?: Category | null
  tags?: Tag[]
}

export interface SearchHistory {
  id: string
  query: string
  userId: string
  createdAt: string
}

// API 响应类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  hasMore: boolean
  offset: number
  limit: number
}

export interface SearchResult {
  prompts: Prompt[]
  total: number
  hasMore: boolean
  query: string
}

export interface SearchSuggestions {
  titles: string[]
  tags: string[]
  categories: string[]
}

// 表单类型
export interface CreateCategoryForm {
  name: string
  description?: string
  color?: string
  icon?: string
  parentId?: string
}

export interface UpdateCategoryForm {
  id: string
  name?: string
  description?: string
  color?: string
  icon?: string
  parentId?: string | null
}

export interface CreatePromptForm {
  title: string
  content: string
  description?: string
  categoryId?: string
  tags?: string[]
  isFavorite?: boolean
  isPublic?: boolean
}

export interface UpdatePromptForm {
  id: string
  title?: string
  content?: string
  description?: string
  categoryId?: string | null
  tags?: string[]
  isFavorite?: boolean
  isPublic?: boolean
}

export interface CreateTagForm {
  name: string
  color?: string
}

export interface UpdateTagForm {
  id: string
  name?: string
  color?: string
}

// 筛选和排序类型
export interface PromptFilters {
  categoryId?: string
  tags?: string[]
  isFavorite?: boolean
  sortBy?: 'createdAt' | 'updatedAt' | 'usageCount' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface SearchFilters {
  query: string
  categoryId?: string
  tags?: string[]
  isFavorite?: boolean
}

// UI 状态类型
export interface UIState {
  sidebarOpen: boolean
  currentView: 'grid' | 'list'
  theme: 'light' | 'dark'
  selectedCategory: string | null
  selectedTags: string[]
  searchQuery: string
}

// 加载状态类型
export interface LoadingState {
  categories: boolean
  prompts: boolean
  tags: boolean
  search: boolean
  creating: boolean
  updating: boolean
  deleting: boolean
}

// 错误类型
export interface ErrorState {
  categories: string | null
  prompts: string | null
  tags: string | null
  search: string | null
  general: string | null
}

// Store 状态接口
export interface StoreState {
  // 数据状态
  categories: Category[]
  prompts: Prompt[]
  tags: Tag[]
  searchHistory: SearchHistory[]
  searchResults: SearchResult | null
  searchSuggestions: SearchSuggestions | null
  
  // UI 状态
  ui: UIState
  
  // 加载状态
  loading: LoadingState
  
  // 错误状态
  errors: ErrorState
  
  // 分页状态
  pagination: {
    prompts: {
      offset: number
      limit: number
      total: number
      hasMore: boolean
    }
    search: {
      offset: number
      limit: number
      total: number
      hasMore: boolean
    }
  }
}