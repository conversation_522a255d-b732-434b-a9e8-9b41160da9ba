import "~/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { Toaster } from "react-hot-toast";

import { TRPCReactProvider } from "~/trpc/react";
import { AuthProvider } from "~/lib/auth/context";
import { ErrorBoundary } from "~/components/error";
import { NetworkStatus, NetworkIndicator } from "~/components/common";
import { MainLayout } from "~/components/layout/MainLayout";

export const metadata: Metadata = {
  title: "提示词管理工具",
  description: "现代化的提示词管理和收藏工具",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="zh-CN" className={`${geist.variable}`} data-theme="light">
      <head>
        <script dangerouslySetInnerHTML={{
          __html: `
            // 抑制 Chrome 扩展相关错误
            if (typeof window !== 'undefined') {
              const originalConsoleError = console.error;
              console.error = function(...args) {
                const message = args.join(' ');
                if (
                  message.includes('chrome-extension') ||
                  message.includes('service-worker.js') ||
                  message.includes("Failed to execute 'put' on 'Cache'")
                ) {
                  return;
                }
                originalConsoleError.apply(console, args);
              };
            }
          `
        }} />
      </head>
      <body>
        <ErrorBoundary>
          <AuthProvider>
            <TRPCReactProvider>
              <MainLayout>
                {children}
              </MainLayout>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 3000,
                  style: {
                    borderRadius: '8px',
                    background: '#1f2937',
                    color: '#fff',
                  },
                }}
              />
              <NetworkStatus />
              <NetworkIndicator />
            </TRPCReactProvider>
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
