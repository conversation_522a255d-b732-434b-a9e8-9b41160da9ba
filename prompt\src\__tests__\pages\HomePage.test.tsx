import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Mock 页面组件 - 基于实际的首页结构
const HomePage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-center mb-4">
          提示词管理工具
        </h1>
        <p className="text-xl text-center text-base-content/70 mb-8">
          管理和组织您的 AI 提示词，提高工作效率
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="stats shadow">
          <div className="stat">
            <div className="stat-figure text-primary">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="stat-title">总提示词</div>
            <div className="stat-value text-primary">156</div>
            <div className="stat-desc">比上月增加 21%</div>
          </div>
        </div>

        <div className="stats shadow">
          <div className="stat">
            <div className="stat-figure text-secondary">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
            </div>
            <div className="stat-title">分类数量</div>
            <div className="stat-value text-secondary">12</div>
            <div className="stat-desc">覆盖主要工作场景</div>
          </div>
        </div>

        <div className="stats shadow">
          <div className="stat">
            <div className="stat-figure text-accent">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
              </svg>
            </div>
            <div className="stat-title">使用次数</div>
            <div className="stat-value text-accent">2.3K</div>
            <div className="stat-desc">本月总使用量</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">最近的提示词</h2>
              <div className="space-y-4">
                <div className="card bg-base-200">
                  <div className="card-body p-4">
                    <h3 className="card-title text-lg">代码审查提示</h3>
                    <p className="text-sm text-base-content/70">
                      请审查以下代码并提供改进建议...
                    </p>
                    <div className="card-actions justify-end">
                      <div className="badge badge-primary">工作</div>
                      <div className="badge badge-outline">代码</div>
                    </div>
                  </div>
                </div>
                
                <div className="card bg-base-200">
                  <div className="card-body p-4">
                    <h3 className="card-title text-lg">文档写作助手</h3>
                    <p className="text-sm text-base-content/70">
                      帮我写一份技术文档的大纲...
                    </p>
                    <div className="card-actions justify-end">
                      <div className="badge badge-secondary">学习</div>
                      <div className="badge badge-outline">文档</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="card-actions justify-end mt-4">
                <button className="btn btn-primary">查看全部</button>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">快速操作</h2>
              <div className="space-y-2">
                <button className="btn btn-outline w-full justify-start">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  新建提示词
                </button>
                
                <button className="btn btn-outline w-full justify-start">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                  管理分类
                </button>
                
                <button className="btn btn-outline w-full justify-start">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  导入数据
                </button>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">热门标签</h2>
              <div className="flex flex-wrap gap-2">
                <div className="badge badge-lg">工作</div>
                <div className="badge badge-lg">学习</div>
                <div className="badge badge-lg">代码</div>
                <div className="badge badge-lg">写作</div>
                <div className="badge badge-lg">翻译</div>
                <div className="badge badge-lg">分析</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Mock Next.js Router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock tRPC
vi.mock('~/trpc/react', () => ({
  api: {
    prompts: {
      getRecent: {
        useQuery: () => ({
          data: [
            {
              id: '1',
              title: '代码审查提示',
              content: '请审查以下代码并提供改进建议...',
              category: { name: '工作', color: '#3B82F6' },
              tags: [{ name: '代码' }],
            },
            {
              id: '2',
              title: '文档写作助手',
              content: '帮我写一份技术文档的大纲...',
              category: { name: '学习', color: '#10B981' },
              tags: [{ name: '文档' }],
            },
          ],
          isLoading: false,
        }),
      },
      getStats: {
        useQuery: () => ({
          data: {
            totalPrompts: 156,
            totalCategories: 12,
            totalUsage: 2300,
            growth: 21,
          },
          isLoading: false,
        }),
      },
    },
    categories: {
      getPopular: {
        useQuery: () => ({
          data: [
            { id: '1', name: '工作', count: 45 },
            { id: '2', name: '学习', count: 32 },
          ],
          isLoading: false,
        }),
      },
    },
    tags: {
      getPopular: {
        useQuery: () => ({
          data: [
            { id: '1', name: '工作', count: 50 },
            { id: '2', name: '学习', count: 38 },
            { id: '3', name: '代码', count: 25 },
            { id: '4', name: '写作', count: 20 },
          ],
          isLoading: false,
        }),
      },
    },
  },
}))

describe('HomePage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该渲染页面标题和描述', () => {
    render(<HomePage />)
    
    expect(screen.getByText('提示词管理工具')).toBeInTheDocument()
    expect(screen.getByText('管理和组织您的 AI 提示词，提高工作效率')).toBeInTheDocument()
  })

  it('应该显示统计数据', () => {
    render(<HomePage />)
    
    expect(screen.getByText('总提示词')).toBeInTheDocument()
    expect(screen.getByText('156')).toBeInTheDocument()
    expect(screen.getByText('分类数量')).toBeInTheDocument()
    expect(screen.getByText('12')).toBeInTheDocument()
    expect(screen.getByText('使用次数')).toBeInTheDocument()
    expect(screen.getByText('2.3K')).toBeInTheDocument()
  })

  it('应该显示最近的提示词', () => {
    render(<HomePage />)
    
    expect(screen.getByText('最近的提示词')).toBeInTheDocument()
    expect(screen.getByText('代码审查提示')).toBeInTheDocument()
    expect(screen.getByText('文档写作助手')).toBeInTheDocument()
  })

  it('应该显示快速操作按钮', () => {
    render(<HomePage />)
    
    expect(screen.getByText('快速操作')).toBeInTheDocument()
    expect(screen.getByText('新建提示词')).toBeInTheDocument()
    expect(screen.getByText('管理分类')).toBeInTheDocument()
    expect(screen.getByText('导入数据')).toBeInTheDocument()
  })

  it('应该显示热门标签', () => {
    render(<HomePage />)
    
    expect(screen.getByText('热门标签')).toBeInTheDocument()
    expect(screen.getAllByText('工作')).toHaveLength(2) // 一个在提示词中，一个在标签中
    expect(screen.getAllByText('学习')).toHaveLength(2)
    expect(screen.getByText('代码')).toBeInTheDocument()
    expect(screen.getByText('写作')).toBeInTheDocument()
  })

  it('应该处理快速操作点击', async () => {
    const user = userEvent.setup()
    const mockPush = vi.fn()
    
    vi.doMock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
      }),
    }))
    
    render(<HomePage />)
    
    const newPromptButton = screen.getByText('新建提示词')
    await user.click(newPromptButton)
    
    expect(mockPush).toHaveBeenCalledWith('/prompts/new')
  })

  it('应该处理查看全部点击', async () => {
    const user = userEvent.setup()
    const mockPush = vi.fn()
    
    vi.doMock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
      }),
    }))
    
    render(<HomePage />)
    
    const viewAllButton = screen.getByText('查看全部')
    await user.click(viewAllButton)
    
    expect(mockPush).toHaveBeenCalledWith('/prompts')
  })

  it('应该处理提示词卡片点击', async () => {
    const user = userEvent.setup()
    const mockPush = vi.fn()
    
    vi.doMock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
      }),
    }))
    
    render(<HomePage />)
    
    const promptCard = screen.getByText('代码审查提示').closest('.card')
    await user.click(promptCard!)
    
    expect(mockPush).toHaveBeenCalledWith('/prompts/1')
  })

  it('应该显示加载状态', () => {
    // Mock 加载状态
    vi.doMock('~/trpc/react', () => ({
      api: {
        prompts: {
          getRecent: {
            useQuery: () => ({
              data: null,
              isLoading: true,
            }),
          },
          getStats: {
            useQuery: () => ({
              data: null,
              isLoading: true,
            }),
          },
        },
      },
    }))
    
    render(<HomePage />)
    
    expect(screen.getByText('加载中...')).toBeInTheDocument()
  })

  it('应该处理错误状态', () => {
    // Mock 错误状态
    vi.doMock('~/trpc/react', () => ({
      api: {
        prompts: {
          getRecent: {
            useQuery: () => ({
              data: null,
              isLoading: false,
              error: new Error('加载失败'),
            }),
          },
        },
      },
    }))
    
    render(<HomePage />)
    
    expect(screen.getByText('加载失败，请重试')).toBeInTheDocument()
  })

  it('应该显示空状态', () => {
    // Mock 空数据
    vi.doMock('~/trpc/react', () => ({
      api: {
        prompts: {
          getRecent: {
            useQuery: () => ({
              data: [],
              isLoading: false,
            }),
          },
          getStats: {
            useQuery: () => ({
              data: {
                totalPrompts: 0,
                totalCategories: 0,
                totalUsage: 0,
                growth: 0,
              },
              isLoading: false,
            }),
          },
        },
      },
    }))
    
    render(<HomePage />)
    
    expect(screen.getByText('暂无提示词')).toBeInTheDocument()
    expect(screen.getByText('开始创建您的第一个提示词')).toBeInTheDocument()
  })

  describe('响应式设计', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<HomePage />)
      
      const grid = screen.getByRole('main').querySelector('.grid')
      expect(grid).toHaveClass('grid-cols-1')
    })

    it('应该在桌面设备上使用多列布局', () => {
      // 模拟桌面设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      })
      
      render(<HomePage />)
      
      const grid = screen.getByRole('main').querySelector('.grid')
      expect(grid).toHaveClass('lg:grid-cols-3')
    })
  })

  describe('无障碍性', () => {
    it('应该有正确的语义结构', () => {
      render(<HomePage />)
      
      const mainHeading = screen.getByRole('heading', { level: 1 })
      expect(mainHeading).toHaveTextContent('提示词管理工具')
      
      const sectionHeadings = screen.getAllByRole('heading', { level: 2 })
      expect(sectionHeadings).toHaveLength(3) // 最近的提示词、快速操作、热门标签
    })

    it('应该有正确的 ARIA 标签', () => {
      render(<HomePage />)
      
      const statsSection = screen.getByRole('region', { name: '统计概览' })
      expect(statsSection).toBeInTheDocument()
      
      const quickActions = screen.getByRole('region', { name: '快速操作' })
      expect(quickActions).toBeInTheDocument()
    })

    it('应该支持键盘导航', async () => {
      const user = userEvent.setup()
      render(<HomePage />)
      
      // 使用 Tab 键导航
      await user.tab()
      expect(screen.getByText('新建提示词')).toHaveFocus()
      
      await user.tab()
      expect(screen.getByText('管理分类')).toHaveFocus()
    })

    it('应该有适当的颜色对比度', () => {
      render(<HomePage />)
      
      const title = screen.getByText('提示词管理工具')
      const computedStyle = window.getComputedStyle(title)
      
      // 检查文本颜色是否有足够的对比度
      expect(computedStyle.color).toBeDefined()
    })
  })

  describe('性能优化', () => {
    it('应该使用懒加载', () => {
      render(<HomePage />)
      
      // 检查图片是否有 loading="lazy" 属性
      const images = screen.getAllByRole('img')
      images.forEach(img => {
        expect(img).toHaveAttribute('loading', 'lazy')
      })
    })

    it('应该缓存API数据', () => {
      const { rerender } = render(<HomePage />)
      
      // 重新渲染不应该重新请求数据
      rerender(<HomePage />)
      
      expect(screen.getByText('代码审查提示')).toBeInTheDocument()
    })

    it('应该防抖搜索输入', async () => {
      const user = userEvent.setup()
      render(<HomePage />)
      
      const searchInput = screen.getByPlaceholderText('搜索提示词...')
      
      // 快速输入多个字符
      await user.type(searchInput, 'test', { delay: 50 })
      
      // 应该只发送一次搜索请求
      await waitFor(() => {
        expect(screen.queryByText('搜索中...')).not.toBeInTheDocument()
      })
    })
  })

  describe('动画效果', () => {
    it('应该有页面进场动画', () => {
      render(<HomePage />)
      
      const mainContainer = screen.getByRole('main')
      expect(mainContainer).toHaveClass('animate-fadeIn')
    })

    it('应该有卡片悬停动画', async () => {
      const user = userEvent.setup()
      render(<HomePage />)
      
      const promptCard = screen.getByText('代码审查提示').closest('.card')
      await user.hover(promptCard!)
      
      expect(promptCard).toHaveClass('hover:shadow-lg')
    })

    it('应该有统计数字动画', () => {
      render(<HomePage />)
      
      const statValue = screen.getByText('156')
      expect(statValue).toHaveClass('animate-countUp')
    })
  })
})