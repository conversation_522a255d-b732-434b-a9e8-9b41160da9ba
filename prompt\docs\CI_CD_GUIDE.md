# CI/CD 流程指南

## 1. 概述

本项目使用 GitHub Actions 实现自动化的 CI/CD 流程，包括：
- 代码质量检查
- 自动化测试
- 安全扫描
- 自动部署
- 性能监控

## 2. 工作流程

### 2.1 代码提交流程

```mermaid
graph TD
    A[代码提交] --> B[GitHub Actions 触发]
    B --> C[代码质量检查]
    C --> D[运行测试]
    D --> E[构建应用]
    E --> F{是否为 main 分支?}
    F -->|是| G[部署到生产环境]
    F -->|否| H[部署到预览环境]
    G --> I[运行生产环境测试]
    H --> J[生成预览 URL]
    I --> K[通知部署成功]
    J --> L[在 PR 中评论预览链接]
```

### 2.2 分支策略

- **main 分支**: 生产环境，自动部署
- **develop 分支**: 开发环境，自动部署到预览
- **feature/* 分支**: 功能开发，创建 PR 预览
- **hotfix/* 分支**: 紧急修复，直接部署到生产

## 3. GitHub Actions 配置

### 3.1 工作流文件

主要工作流文件：`.github/workflows/deploy.yml`

#### 触发条件
```yaml
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
```

#### 环境变量
```yaml
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
```

### 3.2 作业定义

#### 测试作业 (test)
- 类型检查
- 代码规范检查
- 单元测试
- E2E 测试

#### 预览部署作业 (deploy-preview)
- 构建应用
- 部署到预览环境
- 生成预览 URL
- 在 PR 中评论

#### 生产部署作业 (deploy-production)
- 构建应用
- 部署到生产环境
- 健康检查
- 部署通知

#### 性能审计作业 (lighthouse)
- Lighthouse 性能测试
- 生成性能报告
- 设置性能阈值

#### 安全扫描作业 (security-scan)
- 漏洞扫描
- 依赖检查
- 代码安全审计

## 4. 必需的 GitHub Secrets

### 4.1 Vercel 相关
```bash
VERCEL_TOKEN          # Vercel API Token
VERCEL_ORG_ID         # Vercel 组织 ID
VERCEL_PROJECT_ID     # Vercel 项目 ID
```

### 4.2 数据库相关
```bash
DATABASE_URL          # 数据库连接字符串
```

### 4.3 Supabase 相关
```bash
NEXT_PUBLIC_SUPABASE_URL      # Supabase URL
NEXT_PUBLIC_SUPABASE_ANON_KEY # Supabase 匿名密钥
SUPABASE_SERVICE_ROLE_KEY     # Supabase 服务角色密钥
```

### 4.4 认证相关
```bash
NEXTAUTH_SECRET       # NextAuth 密钥
```

## 5. 设置步骤

### 5.1 获取 Vercel Token

```bash
# 登录 Vercel
vercel login

# 获取 Token
vercel --token
```

### 5.2 获取项目信息

```bash
# 链接项目
vercel link

# 获取项目信息
cat .vercel/project.json
```

### 5.3 设置 GitHub Secrets

1. 进入 GitHub 仓库设置
2. 选择 "Secrets and variables" > "Actions"
3. 添加所需的 secrets

### 5.4 配置环境变量

使用提供的脚本设置环境变量：

```bash
# 设置生产环境变量
./scripts/setup-env.sh production

# 设置预览环境变量
./scripts/setup-env.sh preview
```

## 6. 部署策略

### 6.1 蓝绿部署

```yaml
# 部署到新环境
- name: Deploy to staging
  run: vercel --prod --token=${{ secrets.VERCEL_TOKEN }}

# 运行健康检查
- name: Health check
  run: curl -f ${{ steps.deploy.outputs.url }}/api/health

# 切换流量
- name: Switch traffic
  run: vercel alias ${{ steps.deploy.outputs.url }} production.com
```

### 6.2 回滚策略

```bash
# 查看部署历史
vercel ls

# 回滚到上一个版本
vercel rollback [deployment-url]
```

## 7. 监控和告警

### 7.1 部署监控

```yaml
- name: Deployment notification
  uses: actions/github-script@v7
  with:
    script: |
      const { owner, repo } = context.repo;
      await github.rest.repos.createDeploymentStatus({
        owner,
        repo,
        deployment_id: context.payload.deployment.id,
        state: 'success',
        environment_url: '${{ steps.deploy.outputs.url }}'
      });
```

### 7.2 性能监控

```yaml
- name: Lighthouse CI
  uses: treosh/lighthouse-ci-action@v10
  with:
    urls: |
      https://your-app.vercel.app
    configPath: './lighthouse.json'
    uploadArtifacts: true
```

### 7.3 错误监控

集成 Sentry 错误监控：

```typescript
// sentry.client.config.ts
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
});
```

## 8. 质量门控

### 8.1 代码质量检查

```yaml
- name: ESLint
  run: npm run lint

- name: TypeScript check
  run: npm run type-check

- name: Prettier check
  run: npm run format:check
```

### 8.2 测试覆盖率

```yaml
- name: Test coverage
  run: npm run test:coverage

- name: Upload coverage
  uses: codecov/codecov-action@v3
```

### 8.3 性能阈值

```json
{
  "ci": {
    "assert": {
      "assertions": {
        "categories:performance": ["error", { "minScore": 0.8 }],
        "categories:accessibility": ["error", { "minScore": 0.9 }],
        "categories:best-practices": ["error", { "minScore": 0.8 }],
        "categories:seo": ["error", { "minScore": 0.8 }]
      }
    }
  }
}
```

## 9. 安全配置

### 9.1 依赖检查

```yaml
- name: Audit dependencies
  run: npm audit --audit-level=high

- name: Check for vulnerabilities
  uses: actions/dependency-review-action@v3
```

### 9.2 代码扫描

```yaml
- name: CodeQL Analysis
  uses: github/codeql-action/analyze@v2
  with:
    languages: javascript, typescript
```

### 9.3 容器扫描

```yaml
- name: Run Trivy vulnerability scanner
  uses: aquasecurity/trivy-action@master
  with:
    scan-type: 'fs'
    format: 'sarif'
    output: 'trivy-results.sarif'
```

## 10. 环境配置

### 10.1 开发环境

```yaml
# .github/workflows/dev.yml
name: Development
on:
  push:
    branches: [develop]
    
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to development
        run: vercel --token=${{ secrets.VERCEL_TOKEN }}
```

### 10.2 预览环境

```yaml
# Pull Request 触发
on:
  pull_request:
    branches: [main]

jobs:
  preview:
    runs-on: ubuntu-latest
    environment: preview
```

### 10.3 生产环境

```yaml
# Main 分支触发
on:
  push:
    branches: [main]

jobs:
  production:
    runs-on: ubuntu-latest
    environment: production
```

## 11. 通知配置

### 11.1 Slack 通知

```yaml
- name: Slack notification
  uses: 8398a7/action-slack@v3
  with:
    status: custom
    custom_payload: |
      {
        text: "Deployment ${{ job.status }}",
        attachments: [{
          color: '${{ job.status }}' === 'success' ? 'good' : 'danger',
          fields: [{
            title: 'Environment',
            value: '${{ github.ref }}',
            short: true
          }]
        }]
      }
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### 11.2 邮件通知

```yaml
- name: Email notification
  uses: dawidd6/action-send-mail@v3
  with:
    server_address: smtp.gmail.com
    server_port: 587
    username: ${{ secrets.EMAIL_USERNAME }}
    password: ${{ secrets.EMAIL_PASSWORD }}
    subject: "Deployment Status: ${{ job.status }}"
    body: |
      Deployment to ${{ github.ref }} ${{ job.status }}
      Time: ${{ github.event.head_commit.timestamp }}
      Commit: ${{ github.sha }}
```

## 12. 最佳实践

### 12.1 工作流优化

✅ 使用并行作业提高速度  
✅ 缓存依赖减少构建时间  
✅ 使用条件执行节省资源  
✅ 设置合理的超时时间  

### 12.2 安全性

✅ 最小权限原则  
✅ 加密敏感信息  
✅ 定期轮换密钥  
✅ 审计访问日志  

### 12.3 可维护性

✅ 模块化工作流  
✅ 文档化配置  
✅ 版本控制  
✅ 监控指标  

## 13. 故障排除

### 13.1 常见问题

**构建失败**
```bash
# 检查构建日志
gh run view [run-id]

# 本地重现问题
npm run build
```

**部署失败**
```bash
# 检查 Vercel 日志
vercel logs [deployment-url]

# 检查环境变量
vercel env ls
```

**测试失败**
```bash
# 运行特定测试
npm test -- --testNamePattern="specific test"

# 查看测试覆盖率
npm run test:coverage
```

### 13.2 调试技巧

1. 使用 `actions/checkout@v4` 的最新版本
2. 添加调试输出 `run: echo "Debug: ${{ github.ref }}"`
3. 使用 `tmate` 进行远程调试
4. 检查 GitHub Actions 日志

## 14. 性能优化

### 14.1 构建优化

```yaml
- name: Cache dependencies
  uses: actions/cache@v3
  with:
    path: ~/.npm
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

- name: Install dependencies
  run: npm ci
```

### 14.2 并行执行

```yaml
strategy:
  matrix:
    node-version: [18, 20]
    os: [ubuntu-latest, windows-latest]
```

### 14.3 条件执行

```yaml
- name: Deploy
  if: github.ref == 'refs/heads/main'
  run: vercel --prod
```

---

## 支持和维护

如需帮助或报告问题，请：
1. 查看 GitHub Actions 日志
2. 检查 Vercel 部署状态
3. 联系团队成员
4. 提交 Issue 或 PR