{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'\r\nimport { NextResponse } from 'next/server'\r\nimport type { NextRequest } from 'next/server'\r\nimport { Database } from '~/lib/supabase/client'\r\n\r\nexport async function middleware(req: NextRequest) {\r\n  const res = NextResponse.next()\r\n  const supabase = createMiddlewareClient<Database>({ req, res })\r\n\r\n  // 刷新会话\r\n  const { data: { session } } = await supabase.auth.getSession()\r\n\r\n  // 受保护的路由\r\n  const protectedRoutes = ['/dashboard', '/prompts', '/categories', '/settings']\r\n  const isProtectedRoute = protectedRoutes.some(route => req.nextUrl.pathname.startsWith(route))\r\n\r\n  // 如果访问受保护的路由但未登录，重定向到登录页面\r\n  if (isProtectedRoute && !session) {\r\n    return NextResponse.redirect(new URL('/auth/login', req.url))\r\n  }\r\n\r\n  // 如果已登录用户访问认证页面，重定向到首页\r\n  if (session && req.nextUrl.pathname.startsWith('/auth/')) {\r\n    return NextResponse.redirect(new URL('/', req.url))\r\n  }\r\n\r\n  return res\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\r\n  ],\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAIO,eAAe,WAAW,GAAgB;IAC/C,MAAM,MAAM,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC7B,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,yBAAsB,AAAD,EAAY;QAAE;QAAK;IAAI;IAE7D,OAAO;IACP,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAE5D,SAAS;IACT,MAAM,kBAAkB;QAAC;QAAc;QAAY;QAAe;KAAY;IAC9E,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAAS,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAEvF,0BAA0B;IAC1B,IAAI,oBAAoB,CAAC,SAAS;QAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,eAAe,IAAI,GAAG;IAC7D;IAEA,uBAAuB;IACvB,IAAI,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QACxD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG;IACnD;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}