import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { PromptCard } from '~/components/prompts/PromptCard'

// Mock 依赖
vi.mock('~/hooks/useCopyToClipboard', () => ({
  useCopyToClipboard: () => ({
    copy: vi.fn(),
    isCopied: false,
    isLoading: false,
    reset: vi.fn(),
  }),
  usePromptCopy: () => ({
    copyPrompt: vi.fn(),
    copyPromptWithTitle: vi.fn(),
    copyPromptAsMarkdown: vi.fn(),
    isCopied: false,
    isLoading: false,
  }),
  useCodeCopy: () => ({
    copyCode: vi.fn(),
    isCopied: false,
    isLoading: false,
  }),
  useLinkCopy: () => ({
    copyLink: vi.fn(),
    copyCurrentUrl: vi.fn(),
    isCopied: false,
    isLoading: false,
  }),
}))

vi.mock('~/trpc/react', () => ({
  api: {
    prompts: {
      incrementUsage: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
      toggleFavorite: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
      delete: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
    },
  },
}))

const mockPrompt = {
  id: '1',
  title: '测试提示词',
  content: '这是一个测试提示词的内容',
  description: '测试描述',
  categoryId: 'cat-1',
  category: {
    id: 'cat-1',
    name: '工作',
    color: '#3B82F6',
    icon: '💼',
  },
  tags: [
    { id: 'tag-1', name: '测试' },
    { id: 'tag-2', name: '开发' },
  ],
  isFavorite: false,
  usageCount: 5,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  userId: 'user-1',
}

describe('PromptCard', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该渲染提示词信息', () => {
    render(<PromptCard prompt={mockPrompt} />)
    
    expect(screen.getByText('测试提示词')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试提示词的内容')).toBeInTheDocument()
    expect(screen.getByText('测试描述')).toBeInTheDocument()
    expect(screen.getByText('工作')).toBeInTheDocument()
    expect(screen.getByText('测试')).toBeInTheDocument()
    expect(screen.getByText('开发')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument() // 使用次数
  })

  it('应该显示分类信息', () => {
    render(<PromptCard prompt={mockPrompt} />)
    
    const categoryElement = screen.getByText('工作')
    expect(categoryElement).toBeInTheDocument()
    expect(categoryElement.closest('.badge')).toHaveStyle({
      backgroundColor: '#3B82F6',
    })
  })

  it('应该显示标签', () => {
    render(<PromptCard prompt={mockPrompt} />)
    
    expect(screen.getByText('测试')).toBeInTheDocument()
    expect(screen.getByText('开发')).toBeInTheDocument()
    
    const tagElements = screen.getAllByText(/测试|开发/)
    expect(tagElements).toHaveLength(2)
  })

  it('应该显示收藏状态', () => {
    const favoritePrompt = { ...mockPrompt, isFavorite: true }
    render(<PromptCard prompt={favoritePrompt} />)
    
    const favoriteButton = screen.getByRole('button', { name: /收藏/ })
    expect(favoriteButton).toBeInTheDocument()
    // 检查是否有填充的心形图标
    expect(favoriteButton).toHaveClass('text-red-500')
  })

  it('应该处理复制功能', async () => {
    const user = userEvent.setup()
    const mockCopy = vi.fn()
    
    // 重新 mock useCopyToClipboard
    vi.doMock('~/hooks/useCopyToClipboard', () => ({
      useCopyToClipboard: () => ({
        copy: mockCopy,
        copied: false,
        error: null,
      }),
    }))
    
    render(<PromptCard prompt={mockPrompt} />)
    
    const copyButton = screen.getByRole('button', { name: /复制/ })
    await user.click(copyButton)
    
    expect(mockCopy).toHaveBeenCalledWith('这是一个测试提示词的内容')
  })

  it('应该处理收藏切换', async () => {
    const user = userEvent.setup()
    const mockToggleFavorite = vi.fn()
    
    // Mock tRPC mutation
    vi.doMock('~/trpc/react', () => ({
      api: {
        prompts: {
          toggleFavorite: {
            useMutation: () => ({
              mutate: mockToggleFavorite,
            }),
          },
        },
      },
    }))
    
    render(<PromptCard prompt={mockPrompt} />)
    
    const favoriteButton = screen.getByRole('button', { name: /收藏/ })
    await user.click(favoriteButton)
    
    expect(mockToggleFavorite).toHaveBeenCalledWith({ id: '1' })
  })

  it('应该处理编辑功能', async () => {
    const user = userEvent.setup()
    const onEdit = vi.fn()
    
    render(<PromptCard prompt={mockPrompt} onEdit={onEdit} />)
    
    const editButton = screen.getByRole('button', { name: /编辑/ })
    await user.click(editButton)
    
    expect(onEdit).toHaveBeenCalledWith(mockPrompt)
  })

  it('应该处理删除功能', async () => {
    const user = userEvent.setup()
    const onDelete = vi.fn()
    
    render(<PromptCard prompt={mockPrompt} onDelete={onDelete} />)
    
    const deleteButton = screen.getByRole('button', { name: /删除/ })
    await user.click(deleteButton)
    
    expect(onDelete).toHaveBeenCalledWith(mockPrompt.id)
  })

  it('应该支持搜索高亮', () => {
    render(<PromptCard prompt={mockPrompt} searchQuery="测试" />)
    
    // 检查标题中的高亮
    const highlightedTitle = screen.getByText('测试')
    expect(highlightedTitle.tagName).toBe('MARK')
  })

  it('应该支持批量选择', async () => {
    const user = userEvent.setup()
    const onSelect = vi.fn()
    
    render(<PromptCard prompt={mockPrompt} onSelect={onSelect} selectable />)
    
    const checkbox = screen.getByRole('checkbox')
    await user.click(checkbox)
    
    expect(onSelect).toHaveBeenCalledWith(mockPrompt.id, true)
  })

  it('应该显示创建时间', () => {
    render(<PromptCard prompt={mockPrompt} />)
    
    // 检查是否显示了日期
    expect(screen.getByText(/2024/)).toBeInTheDocument()
  })

  it('应该处理长内容截断', () => {
    const longContentPrompt = {
      ...mockPrompt,
      content: 'a'.repeat(1000),
    }
    
    render(<PromptCard prompt={longContentPrompt} />)
    
    // 检查是否有省略号或查看更多按钮
    const content = screen.getByText(/a{3,}/)
    expect(content).toBeInTheDocument()
  })

  it('应该处理无分类的情况', () => {
    const noCategoryPrompt = {
      ...mockPrompt,
      categoryId: null,
      category: null,
    }
    
    render(<PromptCard prompt={noCategoryPrompt} />)
    
    expect(screen.getByText('未分类')).toBeInTheDocument()
  })

  it('应该处理空标签的情况', () => {
    const noTagsPrompt = {
      ...mockPrompt,
      tags: [],
    }
    
    render(<PromptCard prompt={noTagsPrompt} />)
    
    // 标签区域应该为空或显示"无标签"
    expect(screen.queryByText('测试')).not.toBeInTheDocument()
    expect(screen.queryByText('开发')).not.toBeInTheDocument()
  })

  it('应该支持紧凑模式', () => {
    render(<PromptCard prompt={mockPrompt} compact />)
    
    // 紧凑模式应该隐藏描述
    expect(screen.queryByText('测试描述')).not.toBeInTheDocument()
    expect(screen.getByText('测试提示词')).toBeInTheDocument()
  })

  it('应该支持悬停效果', async () => {
    const user = userEvent.setup()
    
    render(<PromptCard prompt={mockPrompt} />)
    
    const card = screen.getByRole('article')
    await user.hover(card)
    
    // 检查是否有悬停类名或样式
    expect(card).toHaveClass('hover:shadow-lg')
  })

  it('应该支持键盘导航', async () => {
    const user = userEvent.setup()
    
    render(<PromptCard prompt={mockPrompt} />)
    
    const card = screen.getByRole('article')
    await user.tab()
    
    expect(card).toHaveFocus()
  })

  describe('无障碍性', () => {
    it('应该有正确的 ARIA 属性', () => {
      render(<PromptCard prompt={mockPrompt} />)
      
      const card = screen.getByRole('article')
      expect(card).toHaveAttribute('aria-label', expect.stringContaining('测试提示词'))
    })

    it('应该有正确的语义结构', () => {
      render(<PromptCard prompt={mockPrompt} />)
      
      // 检查标题是否使用了正确的语义标签
      const title = screen.getByRole('heading', { level: 3 })
      expect(title).toHaveTextContent('测试提示词')
    })

    it('应该支持屏幕阅读器', () => {
      render(<PromptCard prompt={mockPrompt} />)
      
      // 检查是否有对屏幕阅读器友好的描述
      expect(screen.getByText('使用次数: 5')).toBeInTheDocument()
    })
  })

  describe('动画效果', () => {
    it('应该有进场动画', () => {
      render(<PromptCard prompt={mockPrompt} />)
      
      const card = screen.getByRole('article')
      // 检查是否有 Framer Motion 的动画属性
      expect(card).toHaveAttribute('style')
    })

    it('应该有交互动画', async () => {
      const user = userEvent.setup()
      
      render(<PromptCard prompt={mockPrompt} />)
      
      const copyButton = screen.getByRole('button', { name: /复制/ })
      await user.hover(copyButton)
      
      // 检查按钮是否有悬停动画
      expect(copyButton).toHaveClass('hover:scale-105')
    })
  })

  describe('响应式设计', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<PromptCard prompt={mockPrompt} />)
      
      const card = screen.getByRole('article')
      expect(card).toHaveClass('w-full')
    })

    it('应该在桌面设备上正确显示', () => {
      // 模拟桌面设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      })
      
      render(<PromptCard prompt={mockPrompt} />)
      
      const card = screen.getByRole('article')
      expect(card).toHaveClass('max-w-sm')
    })
  })

  describe('性能优化', () => {
    it('应该使用 React.memo 进行优化', () => {
      const { rerender } = render(<PromptCard prompt={mockPrompt} />)
      
      // 使用相同的 props 重新渲染
      rerender(<PromptCard prompt={mockPrompt} />)
      
      // 组件应该不会重新渲染
      expect(screen.getByText('测试提示词')).toBeInTheDocument()
    })

    it('应该正确处理大量数据', () => {
      const manyTagsPrompt = {
        ...mockPrompt,
        tags: Array.from({ length: 50 }, (_, i) => ({
          id: `tag-${i}`,
          name: `标签${i}`,
        })),
      }
      
      render(<PromptCard prompt={manyTagsPrompt} />)
      
      // 应该只显示前几个标签
      expect(screen.getByText('标签0')).toBeInTheDocument()
      expect(screen.getByText('标签1')).toBeInTheDocument()
      // 检查是否有"更多"指示器
      expect(screen.getByText(/\+\d+/)).toBeInTheDocument()
    })
  })
})