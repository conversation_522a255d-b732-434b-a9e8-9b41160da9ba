import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SearchInput } from '~/components/search/SearchInput'

// Mock 依赖
vi.mock('~/trpc/react', () => ({
  api: {
    search: {
      getSuggestions: {
        useQuery: () => ({
          data: [
            { id: '1', title: '测试提示词1', content: '内容1' },
            { id: '2', title: '测试提示词2', content: '内容2' },
          ],
          isLoading: false,
        }),
      },
      getHistory: {
        useQuery: () => ({
          data: [
            { id: '1', query: '历史搜索1', timestamp: new Date() },
            { id: '2', query: '历史搜索2', timestamp: new Date() },
          ],
          isLoading: false,
        }),
      },
      saveHistory: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
    },
  },
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(),
  }),
}))

describe('SearchInput', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该渲染搜索输入框', () => {
    render(<SearchInput />)
    
    expect(screen.getByPlaceholderText('搜索提示词...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '搜索' })).toBeInTheDocument()
  })

  it('应该处理输入变化', async () => {
    const user = userEvent.setup()
    const onSearch = vi.fn()
    
    render(<SearchInput onSearch={onSearch} />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    
    expect(input).toHaveValue('测试')
  })

  it('应该在按下回车时搜索', async () => {
    const user = userEvent.setup()
    const onSearch = vi.fn()
    
    render(<SearchInput onSearch={onSearch} />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    await user.keyboard('{Enter}')
    
    expect(onSearch).toHaveBeenCalledWith('测试')
  })

  it('应该在点击搜索按钮时搜索', async () => {
    const user = userEvent.setup()
    const onSearch = vi.fn()
    
    render(<SearchInput onSearch={onSearch} />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    
    const searchButton = screen.getByRole('button', { name: '搜索' })
    await user.click(searchButton)
    
    expect(onSearch).toHaveBeenCalledWith('测试')
  })

  it('应该显示搜索建议', async () => {
    const user = userEvent.setup()
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    
    // 等待建议出现
    await waitFor(() => {
      expect(screen.getByText('测试提示词1')).toBeInTheDocument()
      expect(screen.getByText('测试提示词2')).toBeInTheDocument()
    })
  })

  it('应该处理建议点击', async () => {
    const user = userEvent.setup()
    const onSearch = vi.fn()
    
    render(<SearchInput onSearch={onSearch} />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    
    await waitFor(() => {
      expect(screen.getByText('测试提示词1')).toBeInTheDocument()
    })
    
    await user.click(screen.getByText('测试提示词1'))
    
    expect(onSearch).toHaveBeenCalledWith('测试提示词1')
  })

  it('应该显示搜索历史', async () => {
    const user = userEvent.setup()
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.focus(input)
    
    // 当输入框为空时应该显示历史记录
    await waitFor(() => {
      expect(screen.getByText('历史搜索1')).toBeInTheDocument()
      expect(screen.getByText('历史搜索2')).toBeInTheDocument()
    })
  })

  it('应该支持键盘导航', async () => {
    const user = userEvent.setup()
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    
    await waitFor(() => {
      expect(screen.getByText('测试提示词1')).toBeInTheDocument()
    })
    
    // 使用箭头键导航
    await user.keyboard('{ArrowDown}')
    expect(screen.getByText('测试提示词1')).toHaveClass('bg-base-200')
    
    await user.keyboard('{ArrowDown}')
    expect(screen.getByText('测试提示词2')).toHaveClass('bg-base-200')
    
    await user.keyboard('{Enter}')
    expect(input).toHaveValue('测试提示词2')
  })

  it('应该支持清空输入', async () => {
    const user = userEvent.setup()
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '测试')
    
    const clearButton = screen.getByRole('button', { name: '清空' })
    await user.click(clearButton)
    
    expect(input).toHaveValue('')
  })

  it('应该支持快捷键', async () => {
    const user = userEvent.setup()
    render(<SearchInput />)
    
    // 模拟 Ctrl+K 快捷键
    await user.keyboard('{Control>}k{/Control}')
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    expect(input).toHaveFocus()
  })

  it('应该防抖搜索请求', async () => {
    const user = userEvent.setup()
    const mockGetSuggestions = vi.fn()
    
    vi.doMock('~/trpc/react', () => ({
      api: {
        search: {
          getSuggestions: {
            useQuery: mockGetSuggestions,
          },
        },
      },
    }))
    
    render(<SearchInput />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    
    // 快速输入多个字符
    await user.type(input, 'abcd', { delay: 100 })
    
    // 应该只调用一次搜索
    await waitFor(() => {
      expect(mockGetSuggestions).toHaveBeenCalledTimes(1)
    }, { timeout: 1000 })
  })

  it('应该处理空搜索', async () => {
    const user = userEvent.setup()
    const onSearch = vi.fn()
    
    render(<SearchInput onSearch={onSearch} />)
    
    const input = screen.getByPlaceholderText('搜索提示词...')
    await user.type(input, '   ')
    await user.keyboard('{Enter}')
    
    // 不应该触发搜索
    expect(onSearch).not.toHaveBeenCalled()
  })

  it('应该显示搜索状态', () => {
    // Mock 加载状态
    vi.doMock('~/trpc/react', () => ({
      api: {
        search: {
          getSuggestions: {
            useQuery: () => ({
              data: [],
              isLoading: true,
            }),
          },
        },
      },
    }))
    
    render(<SearchInput />)
    
    expect(screen.getByText('搜索中...')).toBeInTheDocument()
  })

  it('应该处理搜索错误', () => {
    // Mock 错误状态
    vi.doMock('~/trpc/react', () => ({
      api: {
        search: {
          getSuggestions: {
            useQuery: () => ({
              data: null,
              isLoading: false,
              error: new Error('搜索失败'),
            }),
          },
        },
      },
    }))
    
    render(<SearchInput />)
    
    expect(screen.getByText('搜索失败，请重试')).toBeInTheDocument()
  })

  it('应该支持高级搜索', async () => {
    const user = userEvent.setup()
    render(<SearchInput />)
    
    const advancedButton = screen.getByRole('button', { name: '高级搜索' })
    await user.click(advancedButton)
    
    expect(screen.getByText('高级搜索选项')).toBeInTheDocument()
    expect(screen.getByLabelText('分类')).toBeInTheDocument()
    expect(screen.getByLabelText('标签')).toBeInTheDocument()
    expect(screen.getByLabelText('创建时间')).toBeInTheDocument()
  })

  it('应该支持搜索筛选器', async () => {
    const user = userEvent.setup()
    const onFilterChange = vi.fn()
    
    render(<SearchInput onFilterChange={onFilterChange} />)
    
    const filterButton = screen.getByRole('button', { name: '筛选' })
    await user.click(filterButton)
    
    const categoryFilter = screen.getByLabelText('工作')
    await user.click(categoryFilter)
    
    expect(onFilterChange).toHaveBeenCalledWith({
      categories: ['工作'],
      tags: [],
      dateRange: null,
    })
  })

  describe('搜索建议', () => {
    it('应该高亮匹配文本', async () => {
      const user = userEvent.setup()
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.type(input, '测试')
      
      await waitFor(() => {
        const highlightedText = screen.getByText('测试')
        expect(highlightedText.tagName).toBe('MARK')
      })
    })

    it('应该按相关性排序', async () => {
      const user = userEvent.setup()
      
      // Mock 相关性排序的数据
      vi.doMock('~/trpc/react', () => ({
        api: {
          search: {
            getSuggestions: {
              useQuery: () => ({
                data: [
                  { id: '1', title: '测试标题', content: '内容', score: 0.9 },
                  { id: '2', title: '另一个标题', content: '测试内容', score: 0.7 },
                ],
                isLoading: false,
              }),
            },
          },
        },
      }))
      
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.type(input, '测试')
      
      await waitFor(() => {
        const suggestions = screen.getAllByRole('option')
        expect(suggestions[0]).toHaveTextContent('测试标题')
        expect(suggestions[1]).toHaveTextContent('另一个标题')
      })
    })

    it('应该显示建议类型', async () => {
      const user = userEvent.setup()
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.type(input, '测试')
      
      await waitFor(() => {
        expect(screen.getByText('提示词')).toBeInTheDocument()
        expect(screen.getByText('标签')).toBeInTheDocument()
      })
    })
  })

  describe('搜索历史', () => {
    it('应该保存搜索历史', async () => {
      const user = userEvent.setup()
      const mockSaveHistory = vi.fn()
      
      vi.doMock('~/trpc/react', () => ({
        api: {
          search: {
            saveHistory: {
              useMutation: () => ({
                mutate: mockSaveHistory,
              }),
            },
          },
        },
      }))
      
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.type(input, '新搜索')
      await user.keyboard('{Enter}')
      
      expect(mockSaveHistory).toHaveBeenCalledWith({ query: '新搜索' })
    })

    it('应该支持删除历史记录', async () => {
      const user = userEvent.setup()
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.focus(input)
      
      await waitFor(() => {
        expect(screen.getByText('历史搜索1')).toBeInTheDocument()
      })
      
      const deleteButton = screen.getByRole('button', { name: '删除历史记录' })
      await user.click(deleteButton)
      
      expect(screen.queryByText('历史搜索1')).not.toBeInTheDocument()
    })

    it('应该支持清空所有历史', async () => {
      const user = userEvent.setup()
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.focus(input)
      
      const clearAllButton = screen.getByRole('button', { name: '清空所有历史' })
      await user.click(clearAllButton)
      
      expect(screen.queryByText('历史搜索1')).not.toBeInTheDocument()
      expect(screen.queryByText('历史搜索2')).not.toBeInTheDocument()
    })
  })

  describe('无障碍性', () => {
    it('应该有正确的 ARIA 属性', () => {
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      expect(input).toHaveAttribute('role', 'combobox')
      expect(input).toHaveAttribute('aria-expanded', 'false')
      expect(input).toHaveAttribute('aria-autocomplete', 'list')
    })

    it('应该在显示建议时更新 ARIA 属性', async () => {
      const user = userEvent.setup()
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.type(input, '测试')
      
      await waitFor(() => {
        expect(input).toHaveAttribute('aria-expanded', 'true')
        expect(input).toHaveAttribute('aria-activedescendant')
      })
    })

    it('应该支持屏幕阅读器', () => {
      render(<SearchInput />)
      
      expect(screen.getByText('搜索提示词')).toBeInTheDocument()
      expect(screen.getByText('使用 Ctrl+K 快速打开搜索')).toBeInTheDocument()
    })
  })

  describe('响应式设计', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<SearchInput />)
      
      const container = screen.getByRole('search')
      expect(container).toHaveClass('w-full')
    })

    it('应该在移动设备上显示简化的界面', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<SearchInput />)
      
      // 移动设备上应该隐藏快捷键提示
      expect(screen.queryByText('Ctrl+K')).not.toBeInTheDocument()
    })
  })

  describe('性能优化', () => {
    it('应该虚拟化长建议列表', async () => {
      const user = userEvent.setup()
      
      // Mock 大量建议数据
      const largeSuggestions = Array.from({ length: 1000 }, (_, i) => ({
        id: i.toString(),
        title: `建议 ${i}`,
        content: `内容 ${i}`,
      }))
      
      vi.doMock('~/trpc/react', () => ({
        api: {
          search: {
            getSuggestions: {
              useQuery: () => ({
                data: largeSuggestions,
                isLoading: false,
              }),
            },
          },
        },
      }))
      
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      await user.type(input, '建议')
      
      // 应该只渲染可见的建议
      await waitFor(() => {
        const visibleSuggestions = screen.getAllByRole('option')
        expect(visibleSuggestions.length).toBeLessThan(20)
      })
    })

    it('应该缓存搜索结果', async () => {
      const user = userEvent.setup()
      const mockGetSuggestions = vi.fn().mockReturnValue({
        data: [{ id: '1', title: '缓存测试', content: '内容' }],
        isLoading: false,
      })
      
      vi.doMock('~/trpc/react', () => ({
        api: {
          search: {
            getSuggestions: {
              useQuery: mockGetSuggestions,
            },
          },
        },
      }))
      
      render(<SearchInput />)
      
      const input = screen.getByPlaceholderText('搜索提示词...')
      
      // 第一次搜索
      await user.type(input, '缓存')
      await user.clear(input)
      
      // 第二次搜索相同内容
      await user.type(input, '缓存')
      
      // 应该使用缓存，不重复请求
      expect(mockGetSuggestions).toHaveBeenCalledTimes(1)
    })
  })
})