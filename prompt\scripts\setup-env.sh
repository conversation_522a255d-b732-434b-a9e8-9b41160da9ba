#!/bin/bash

# 环境变量设置脚本
# 使用方法: ./scripts/setup-env.sh [environment]
# 环境: development, preview, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境参数
ENVIRONMENT=${1:-"development"}
if [[ ! "$ENVIRONMENT" =~ ^(development|preview|production)$ ]]; then
    print_error "无效的环境参数: $ENVIRONMENT"
    print_info "支持的环境: development, preview, production"
    exit 1
fi

print_info "设置 $ENVIRONMENT 环境的环境变量..."

# 检查 Vercel CLI 是否已安装
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI 未安装"
    print_info "请运行: npm install -g vercel"
    exit 1
fi

# 检查是否已登录 Vercel
if ! vercel whoami &> /dev/null; then
    print_error "未登录 Vercel"
    print_info "请运行: vercel login"
    exit 1
fi

# 检查项目是否已链接
if [ ! -f ".vercel/project.json" ]; then
    print_warning "项目未链接到 Vercel"
    print_info "开始链接项目..."
    vercel link
fi

# 环境变量设置函数
set_env_var() {
    local name=$1
    local description=$2
    local required=${3:-false}
    
    echo ""
    print_info "设置 $name"
    print_info "描述: $description"
    
    # 检查是否已存在
    if vercel env get "$name" "$ENVIRONMENT" &> /dev/null; then
        print_warning "$name 已存在"
        read -p "是否覆盖现有值？ (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "跳过 $name"
            return
        fi
    fi
    
    # 获取用户输入
    if [[ "$name" == *"SECRET"* ]] || [[ "$name" == *"PASSWORD"* ]] || [[ "$name" == *"KEY"* ]]; then
        read -s -p "请输入 $name 的值: " value
        echo ""
    else
        read -p "请输入 $name 的值: " value
    fi
    
    # 验证必需字段
    if [[ "$required" == "true" ]] && [[ -z "$value" ]]; then
        print_error "$name 是必需的，不能为空"
        return 1
    fi
    
    # 设置环境变量
    if [[ -n "$value" ]]; then
        if echo "$value" | vercel env add "$name" "$ENVIRONMENT" > /dev/null 2>&1; then
            print_success "$name 设置成功"
        else
            print_error "设置 $name 失败"
            return 1
        fi
    else
        print_info "$name 跳过（空值）"
    fi
}

# 设置必需的环境变量
print_info "========== 必需的环境变量 =========="

set_env_var "DATABASE_URL" "PostgreSQL 数据库连接字符串 (postgresql://user:pass@host:port/db)" true
set_env_var "NEXT_PUBLIC_SUPABASE_URL" "Supabase 项目 URL (https://xxx.supabase.co)" true
set_env_var "NEXT_PUBLIC_SUPABASE_ANON_KEY" "Supabase 公开 API 密钥" true
set_env_var "SUPABASE_SERVICE_ROLE_KEY" "Supabase 服务角色密钥" true
set_env_var "NEXTAUTH_SECRET" "NextAuth.js 密钥" true

# 根据环境设置不同的 NEXTAUTH_URL
if [[ "$ENVIRONMENT" == "production" ]]; then
    set_env_var "NEXTAUTH_URL" "生产环境 URL (https://your-domain.com)" true
elif [[ "$ENVIRONMENT" == "preview" ]]; then
    set_env_var "NEXTAUTH_URL" "预览环境 URL (https://preview-xxx.vercel.app)" true
else
    set_env_var "NEXTAUTH_URL" "开发环境 URL (http://localhost:3000)" true
fi

# 询问是否设置可选的环境变量
echo ""
read -p "是否设置可选的环境变量？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "========== 可选的环境变量 =========="
    
    # 应用配置
    set_env_var "NEXT_PUBLIC_APP_NAME" "应用名称" false
    set_env_var "NEXT_PUBLIC_APP_VERSION" "应用版本" false
    set_env_var "NEXT_PUBLIC_APP_URL" "应用完整 URL" false
    
    # 功能开关
    set_env_var "NEXT_PUBLIC_ENABLE_ANALYTICS" "启用分析 (true/false)" false
    set_env_var "NEXT_PUBLIC_ENABLE_SENTRY" "启用错误监控 (true/false)" false
    set_env_var "NEXT_PUBLIC_ENABLE_HOTJAR" "启用热图分析 (true/false)" false
    
    # 第三方服务
    set_env_var "SENTRY_DSN" "Sentry 错误监控 DSN" false
    set_env_var "GOOGLE_ANALYTICS_ID" "Google Analytics ID" false
    set_env_var "HOTJAR_ID" "Hotjar ID" false
    
    # 安全配置
    set_env_var "CSRF_SECRET" "CSRF 保护密钥" false
    set_env_var "ENCRYPTION_KEY" "数据加密密钥" false
    set_env_var "HEALTH_CHECK_TOKEN" "健康检查令牌" false
    
    # 邮件配置
    read -p "是否设置邮件配置？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        set_env_var "EMAIL_FROM" "发件人邮箱" false
        set_env_var "EMAIL_FROM_NAME" "发件人姓名" false
        set_env_var "SMTP_HOST" "SMTP 服务器地址" false
        set_env_var "SMTP_PORT" "SMTP 端口" false
        set_env_var "SMTP_USER" "SMTP 用户名" false
        set_env_var "SMTP_PASSWORD" "SMTP 密码" false
    fi
    
    # 缓存配置
    read -p "是否设置缓存配置？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        set_env_var "REDIS_URL" "Redis 连接 URL" false
        set_env_var "CACHE_TTL" "缓存过期时间（秒）" false
    fi
    
    # 文件上传配置
    read -p "是否设置文件上传配置？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        set_env_var "UPLOAD_MAX_SIZE" "最大文件大小（字节）" false
        set_env_var "ALLOWED_FILE_TYPES" "允许的文件类型" false
    fi
    
    # 速率限制
    read -p "是否设置速率限制？ (y/N): " -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        set_env_var "RATE_LIMIT_WINDOW" "速率限制时间窗口（毫秒）" false
        set_env_var "RATE_LIMIT_MAX" "速率限制最大请求数" false
    fi
fi

# 验证设置
print_info "========== 验证环境变量 =========="
print_info "当前 $ENVIRONMENT 环境的环境变量："
vercel env ls --environment="$ENVIRONMENT"

# 生成本地环境变量文件
echo ""
read -p "是否生成本地环境变量文件？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "生成本地环境变量文件..."
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        vercel env pull .env.local --environment="$ENVIRONMENT"
        print_success "本地环境变量文件已生成: .env.local"
    else
        vercel env pull ".env.$ENVIRONMENT" --environment="$ENVIRONMENT"
        print_success "环境变量文件已生成: .env.$ENVIRONMENT"
    fi
fi

# 测试环境变量
echo ""
read -p "是否测试环境变量？ (y/N): " -r
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "测试环境变量..."
    
    # 测试数据库连接
    if vercel env get DATABASE_URL "$ENVIRONMENT" &> /dev/null; then
        print_success "DATABASE_URL 已设置"
    else
        print_error "DATABASE_URL 未设置"
    fi
    
    # 测试 Supabase 配置
    if vercel env get NEXT_PUBLIC_SUPABASE_URL "$ENVIRONMENT" &> /dev/null; then
        print_success "NEXT_PUBLIC_SUPABASE_URL 已设置"
    else
        print_error "NEXT_PUBLIC_SUPABASE_URL 未设置"
    fi
    
    # 测试认证配置
    if vercel env get NEXTAUTH_SECRET "$ENVIRONMENT" &> /dev/null; then
        print_success "NEXTAUTH_SECRET 已设置"
    else
        print_error "NEXTAUTH_SECRET 未设置"
    fi
fi

print_success "环境变量设置完成！"

# 显示后续步骤
print_info "========== 后续步骤 =========="
print_info "1. 验证环境变量: vercel env ls --environment=$ENVIRONMENT"
print_info "2. 部署应用: vercel --prod (生产环境) 或 vercel (预览环境)"
print_info "3. 测试应用: 访问部署 URL 并检查健康状态"
print_info "4. 监控日志: vercel logs"

# 显示有用的命令
print_info "========== 有用的命令 =========="
print_info "  查看环境变量: vercel env ls"
print_info "  获取单个变量: vercel env get VARIABLE_NAME $ENVIRONMENT"
print_info "  删除变量: vercel env rm VARIABLE_NAME $ENVIRONMENT"
print_info "  拉取变量到本地: vercel env pull .env.local"