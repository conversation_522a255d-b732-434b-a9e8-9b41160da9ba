export default {".tooltip":{"position":"relative","display":"inline-block","--tt-bg":"var(--color-neutral)","--tt-off":"calc(100% + 0.5rem)","--tt-tail":"calc(100% + 1px + 0.25rem)","> :where(.tooltip-content), &:where([data-tip]):before":{"position":"absolute","max-width":"20rem","border-radius":"var(--radius-field)","padding-inline":"calc(0.25rem * 2)","padding-block":"calc(0.25rem * 1)","text-align":"center","white-space":"normal","color":"var(--color-neutral-content)","opacity":"0%","font-size":"0.875rem","line-height":1.25,"transition":"opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms","background-color":"var(--tt-bg)","width":"max-content","pointer-events":"none","z-index":2,"--tw-content":"attr(data-tip)","content":"var(--tw-content)"},"&:after":{"position":["absolute","absolute"],"opacity":"0%","background-color":"var(--tt-bg)","transition":"opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms","content":"\"\"","pointer-events":"none","width":"0.625rem","height":"0.25rem","display":"block","mask-repeat":"no-repeat","mask-position":"-1px 0","--mask-tooltip":"url(\"data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A\")","mask-image":"var(--mask-tooltip)"},"&.tooltip-open, &[data-tip]:not([data-tip=\"\"]):hover, &:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover, &:has(:focus-visible)":{"> .tooltip-content, &[data-tip]:before, &:after":{"opacity":"100%","--tt-pos":"0rem","transition":"opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0ms"}}},".tooltip, .tooltip-top":{"> .tooltip-content, &[data-tip]:before":{"transform":"translateX(-50%) translateY(var(--tt-pos, 0.25rem))","inset":"auto auto var(--tt-off) 50%"},"&:after":{"transform":"translateX(-50%) translateY(var(--tt-pos, 0.25rem))","inset":"auto auto var(--tt-tail) 50%"}},".tooltip-bottom":{"> .tooltip-content, &[data-tip]:before":{"transform":"translateX(-50%) translateY(var(--tt-pos, -0.25rem))","inset":"var(--tt-off) auto auto 50%"},"&:after":{"transform":"translateX(-50%) translateY(var(--tt-pos, -0.25rem)) rotate(180deg)","inset":"var(--tt-tail) auto auto 50%"}},".tooltip-left":{"> .tooltip-content, &[data-tip]:before":{"transform":"translateX(calc(var(--tt-pos, 0.25rem) - 0.25rem)) translateY(-50%)","inset":"50% var(--tt-off) auto auto"},"&:after":{"transform":"translateX(var(--tt-pos, 0.25rem)) translateY(-50%) rotate(-90deg)","inset":"50% calc(var(--tt-tail) + 1px) auto auto"}},".tooltip-right":{"> .tooltip-content, &[data-tip]:before":{"transform":"translateX(calc(var(--tt-pos, -0.25rem) + 0.25rem)) translateY(-50%)","inset":"50% auto auto var(--tt-off)"},"&:after":{"transform":"translateX(var(--tt-pos, -0.25rem)) translateY(-50%) rotate(90deg)","inset":"50% auto auto calc(var(--tt-tail) + 1px)"}},".tooltip-primary":{"--tt-bg":"var(--color-primary)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-primary-content)"}},".tooltip-secondary":{"--tt-bg":"var(--color-secondary)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-secondary-content)"}},".tooltip-accent":{"--tt-bg":"var(--color-accent)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-accent-content)"}},".tooltip-info":{"--tt-bg":"var(--color-info)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-info-content)"}},".tooltip-success":{"--tt-bg":"var(--color-success)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-success-content)"}},".tooltip-warning":{"--tt-bg":"var(--color-warning)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-warning-content)"}},".tooltip-error":{"--tt-bg":"var(--color-error)","> .tooltip-content, &[data-tip]:before":{"color":"var(--color-error-content)"}}};