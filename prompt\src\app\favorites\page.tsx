'use client'

import { useState } from 'react'
import Link from 'next/link'
import { api } from '~/trpc/react'
import { PromptGrid } from '~/components/prompts/PromptGrid'

export default function FavoritesPage() {
  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'usage' | 'title'>('updated')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 获取收藏的提示词
  const { data: promptsData, isLoading, refetch } = api.prompts.getFiltered.useQuery({
    favoritesOnly: true,
    sortBy,
    sortOrder,
  })

  const prompts = promptsData?.prompts || []

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-8 h-8 text-warning"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z"
            />
          </svg>
          <h1 className="text-3xl font-bold text-base-content">我的收藏</h1>
        </div>
        <p className="text-base-content/70">你收藏的所有提示词</p>
      </div>

      {/* 排序和操作栏 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        {/* 排序选择 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-base-content/70">排序：</span>
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder]
              setSortBy(field)
              setSortOrder(order)
            }}
            className="select select-bordered select-sm"
          >
            <option value="updated-desc">最近更新</option>
            <option value="created-desc">最新收藏</option>
            <option value="usage-desc">使用最多</option>
            <option value="title-asc">标题 A-Z</option>
            <option value="title-desc">标题 Z-A</option>
          </select>
        </div>

        {/* 统计信息 */}
        {!isLoading && prompts.length > 0 && (
          <div className="flex items-center text-sm text-base-content/70">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4 mr-1"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z"
              />
            </svg>
            共 {prompts.length} 个收藏
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2 ml-auto">
          <button
            onClick={() => refetch()}
            className="btn btn-outline btn-sm"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading loading-spinner loading-xs"></span>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
                />
              </svg>
            )}
            刷新
          </button>
        </div>
      </div>

      {/* 提示词网格 */}
      <PromptGrid
        prompts={prompts}
        loading={isLoading}
        columns={3}
        showCategory={true}
        showActions={true}
        emptyMessage="还没有收藏任何提示词"
        emptyAction={
          <div className="space-y-4">
            <p className="text-base-content/50">
              在提示词卡片上点击星星图标即可收藏
            </p>
            <Link href="/prompts" className="btn btn-primary">
              浏览所有提示词
            </Link>
          </div>
        }
        onPromptUpdate={refetch}
      />

      {/* 收藏提示 */}
      {!isLoading && prompts.length === 0 && (
        <div className="mt-8 text-center">
          <div className="card bg-base-100 shadow-sm max-w-md mx-auto">
            <div className="card-body text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-12 h-12 mx-auto text-warning mb-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z"
                />
              </svg>
              <h3 className="text-lg font-medium mb-2">如何收藏提示词？</h3>
              <p className="text-base-content/70 text-sm mb-4">
                在任何提示词卡片上点击星星图标，即可将其添加到收藏夹中，方便快速访问。
              </p>
              <div className="flex justify-center space-x-2">
                <Link href="/prompts" className="btn btn-primary btn-sm">
                  浏览提示词
                </Link>
                <Link href="/prompts/new" className="btn btn-outline btn-sm">
                  创建提示词
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
