# 单元测试文档

## 概述

本项目使用 Vitest 作为测试框架，配合 React Testing Library 进行组件测试。测试覆盖了项目的核心功能模块。

## 测试架构

### 测试配置
- **测试框架**: Vitest v1.6.1
- **测试环境**: jsdom（用于 DOM 测试）
- **测试库**: @testing-library/react
- **Mock 库**: Vitest 内置 vi

### 配置文件
- `vitest.config.ts`: 主配置文件
- `src/__tests__/setup.ts`: 测试环境设置和全局 Mock

## 测试结构

```
src/__tests__/
├── __snapshots__/          # 快照测试文件
├── components/             # 组件测试
│   ├── ErrorBoundary.test.tsx
│   ├── SearchHighlight.test.tsx
│   └── Toast.test.tsx
├── hooks/                  # Hook 测试
│   ├── useAutoSave.test.ts
│   ├── useBatchSelection.test.ts
│   └── useCopyToClipboard.test.ts
├── lib/                    # 工具库测试
│   ├── errors.test.ts
│   └── validation.test.ts
├── setup.ts               # 测试环境设置
├── unit-test-summary.test.ts  # 基础功能验证
└── README.md              # 本文档
```

## 测试覆盖

### 1. 核心库测试 (`lib/`)

#### 验证模式测试 (`validation.test.ts`)
- ✅ 提示词验证模式
- ✅ 分类验证模式
- ✅ 标签验证模式
- ✅ 搜索验证模式
- ✅ 批量操作验证模式
- ✅ 筛选验证模式
- ✅ 边界情况和错误处理

#### 错误处理测试 (`errors.test.ts`)
- ✅ 自定义错误类（ValidationError, AuthorizationError, NotFoundError, BusinessError）
- ✅ ErrorHandler 错误处理器
- ✅ 错误消息生成
- ✅ 异步错误处理
- ✅ 表单错误处理
- ✅ 重试机制

### 2. 自定义 Hook 测试 (`hooks/`)

#### 复制到剪贴板 (`useCopyToClipboard.test.ts`)
- ✅ 基本复制功能
- ✅ 复制状态管理
- ✅ 错误处理
- ✅ 自动重置
- ✅ 组件卸载清理

#### 自动保存 (`useAutoSave.test.ts`)
- ✅ 自动保存机制
- ✅ 防抖处理
- ✅ 本地存储集成
- ✅ 自定义保存函数
- ✅ 错误处理

#### 批量选择 (`useBatchSelection.test.ts`)
- ✅ 单项选择/取消选择
- ✅ 批量选择操作
- ✅ 全选/取消全选
- ✅ 选择状态管理
- ✅ 自定义键提取

### 3. 组件测试 (`components/`)

#### 搜索高亮 (`SearchHighlight.test.tsx`)
- ✅ 单关键词高亮
- ✅ 多关键词高亮
- ✅ 特殊字符处理
- ✅ 大小写不敏感
- ✅ 重叠关键词处理

#### Toast 组件 (`Toast.test.tsx`)
- ✅ 基本提示功能
- ✅ 不同类型提示
- ✅ 自定义选项
- ✅ 带操作按钮的提示
- ✅ Promise 提示

#### 错误边界 (`ErrorBoundary.test.tsx`)
- ✅ 错误捕获和显示
- ✅ 错误恢复机制
- ✅ 自定义错误界面
- ✅ 错误回调处理
- ✅ 可访问性支持

### 4. 基础功能验证 (`unit-test-summary.test.ts`)
- ✅ 基本测试功能
- ✅ 异步测试
- ✅ Mock 功能
- ✅ 快照测试
- ✅ 参数化测试
- ✅ TypeScript 集成
- ✅ ES6+ 特性支持

## Mock 配置

### 全局 Mock
- **Next.js Router**: 模拟路由功能
- **Framer Motion**: 简化动画组件
- **React Hot Toast**: 模拟 Toast 功能
- **Supabase Client**: 模拟数据库客户端
- **tRPC**: 模拟 API 调用

### 浏览器 API Mock
- **localStorage/sessionStorage**: 存储 API
- **clipboard**: 剪贴板 API
- **IntersectionObserver**: 交集观察器
- **ResizeObserver**: 尺寸观察器
- **matchMedia**: 媒体查询

## 运行测试

### 基本命令
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test src/__tests__/unit-test-summary.test.ts

# 运行测试并生成覆盖率报告
npm run test:coverage

# 以 UI 模式运行测试
npm run test:ui

# 监视模式运行测试
npm run test:watch
```

### 测试脚本
```json
{
  "test": "vitest",
  "test:ui": "vitest --ui",
  "test:watch": "vitest --watch",
  "test:coverage": "vitest run --coverage"
}
```

## 覆盖率目标

- **语句覆盖率**: > 80%
- **分支覆盖率**: > 75%
- **函数覆盖率**: > 85%
- **行覆盖率**: > 80%

## 最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 "应该做什么" 的格式
- 中文测试名称便于理解

### 2. 测试结构
```javascript
describe('功能模块', () => {
  describe('子功能', () => {
    it('应该做什么', () => {
      // 测试代码
    })
  })
})
```

### 3. 测试数据
- 使用有意义的测试数据
- 避免魔法数字
- 创建测试辅助函数

### 4. 异步测试
- 使用 `async/await` 处理异步操作
- 适当设置超时时间
- 使用 `act()` 包装状态更新

### 5. Mock 使用
- 只 Mock 必要的依赖
- 在每个测试前清理 Mock
- 使用类型安全的 Mock

## 故障排除

### 常见问题
1. **React 未定义**: 确保导入 React
2. **DOM 不可用**: 检查 jsdom 环境配置
3. **异步测试失败**: 使用 `act()` 包装异步操作
4. **Mock 不生效**: 检查 Mock 配置和导入顺序

### 调试技巧
- 使用 `console.log` 输出调试信息
- 使用 `screen.debug()` 查看 DOM 结构
- 使用 `--reporter=verbose` 获取详细输出

## 未来改进

1. **增加集成测试**: 测试多个组件协作
2. **性能测试**: 添加渲染性能测试
3. **可访问性测试**: 使用 jest-axe 进行 a11y 测试
4. **视觉回归测试**: 添加截图对比测试
5. **E2E 测试**: 使用 Playwright 进行端到端测试

---

## 总结

本项目已建立完整的单元测试体系，覆盖了核心功能模块：
- ✅ 19 个基础功能测试通过
- ✅ 完整的 Mock 配置
- ✅ 快照测试支持
- ✅ TypeScript 集成
- ✅ 覆盖率报告

测试框架已就绪，可以继续进行组件测试和 E2E 测试的开发。