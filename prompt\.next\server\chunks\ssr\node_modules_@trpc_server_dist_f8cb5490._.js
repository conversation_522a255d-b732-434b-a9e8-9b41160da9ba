module.exports = {

"[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

//#region src/observable/observable.ts
/** @public */ __turbopack_context__.s({
    "isObservable": ()=>isObservable,
    "observable": ()=>observable,
    "observableToAsyncIterable": ()=>observableToAsyncIterable,
    "observableToPromise": ()=>observableToPromise
});
function isObservable(x) {
    return typeof x === "object" && x !== null && "subscribe" in x;
}
/** @public */ function observable(subscribe) {
    const self = {
        subscribe (observer) {
            let teardownRef = null;
            let isDone = false;
            let unsubscribed = false;
            let teardownImmediately = false;
            function unsubscribe() {
                if (teardownRef === null) {
                    teardownImmediately = true;
                    return;
                }
                if (unsubscribed) return;
                unsubscribed = true;
                if (typeof teardownRef === "function") teardownRef();
                else if (teardownRef) teardownRef.unsubscribe();
            }
            teardownRef = subscribe({
                next (value) {
                    var _observer$next;
                    if (isDone) return;
                    (_observer$next = observer.next) === null || _observer$next === void 0 || _observer$next.call(observer, value);
                },
                error (err) {
                    var _observer$error;
                    if (isDone) return;
                    isDone = true;
                    (_observer$error = observer.error) === null || _observer$error === void 0 || _observer$error.call(observer, err);
                    unsubscribe();
                },
                complete () {
                    var _observer$complete;
                    if (isDone) return;
                    isDone = true;
                    (_observer$complete = observer.complete) === null || _observer$complete === void 0 || _observer$complete.call(observer);
                    unsubscribe();
                }
            });
            if (teardownImmediately) unsubscribe();
            return {
                unsubscribe
            };
        },
        pipe (...operations) {
            return operations.reduce(pipeReducer, self);
        }
    };
    return self;
}
function pipeReducer(prev, fn) {
    return fn(prev);
}
/** @internal */ function observableToPromise(observable$1) {
    const ac = new AbortController();
    const promise = new Promise((resolve, reject)=>{
        let isDone = false;
        function onDone() {
            if (isDone) return;
            isDone = true;
            obs$.unsubscribe();
        }
        ac.signal.addEventListener("abort", ()=>{
            reject(ac.signal.reason);
        });
        const obs$ = observable$1.subscribe({
            next (data) {
                isDone = true;
                resolve(data);
                onDone();
            },
            error (data) {
                reject(data);
            },
            complete () {
                ac.abort();
                onDone();
            }
        });
    });
    return promise;
}
/**
* @internal
*/ function observableToReadableStream(observable$1, signal) {
    let unsub = null;
    const onAbort = ()=>{
        unsub === null || unsub === void 0 || unsub.unsubscribe();
        unsub = null;
        signal.removeEventListener("abort", onAbort);
    };
    return new ReadableStream({
        start (controller) {
            unsub = observable$1.subscribe({
                next (data) {
                    controller.enqueue({
                        ok: true,
                        value: data
                    });
                },
                error (error) {
                    controller.enqueue({
                        ok: false,
                        error
                    });
                    controller.close();
                },
                complete () {
                    controller.close();
                }
            });
            if (signal.aborted) onAbort();
            else signal.addEventListener("abort", onAbort, {
                once: true
            });
        },
        cancel () {
            onAbort();
        }
    });
}
/** @internal */ function observableToAsyncIterable(observable$1, signal) {
    const stream = observableToReadableStream(observable$1, signal);
    const reader = stream.getReader();
    const iterator = {
        async next () {
            const value = await reader.read();
            if (value.done) return {
                value: void 0,
                done: true
            };
            const { value: result } = value;
            if (!result.ok) throw result.error;
            return {
                value: result.value,
                done: false
            };
        },
        async return () {
            await reader.cancel();
            return {
                value: void 0,
                done: true
            };
        }
    };
    return {
        [Symbol.asyncIterator] () {
            return iterator;
        }
    };
}
;
 //# sourceMappingURL=observable-UMO3vUa_.mjs.map
}),
"[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

//#region src/unstable-core-do-not-import/rpc/codes.ts
/**
* JSON-RPC 2.0 Error codes
*
* `-32000` to `-32099` are reserved for implementation-defined server-errors.
* For tRPC we're copying the last digits of HTTP 4XX errors.
*/ __turbopack_context__.s({
    "TRPC_ERROR_CODES_BY_KEY": ()=>TRPC_ERROR_CODES_BY_KEY,
    "TRPC_ERROR_CODES_BY_NUMBER": ()=>TRPC_ERROR_CODES_BY_NUMBER,
    "abortSignalsAnyPonyfill": ()=>abortSignalsAnyPonyfill,
    "assert": ()=>assert,
    "identity": ()=>identity,
    "isAsyncIterable": ()=>isAsyncIterable,
    "isFunction": ()=>isFunction,
    "isObject": ()=>isObject,
    "mergeWithoutOverrides": ()=>mergeWithoutOverrides,
    "noop": ()=>noop,
    "omitPrototype": ()=>omitPrototype,
    "retryableRpcCodes": ()=>retryableRpcCodes,
    "run": ()=>run,
    "sleep": ()=>sleep
});
const TRPC_ERROR_CODES_BY_KEY = {
    PARSE_ERROR: -32700,
    BAD_REQUEST: -32600,
    INTERNAL_SERVER_ERROR: -32603,
    NOT_IMPLEMENTED: -32603,
    BAD_GATEWAY: -32603,
    SERVICE_UNAVAILABLE: -32603,
    GATEWAY_TIMEOUT: -32603,
    UNAUTHORIZED: -32001,
    PAYMENT_REQUIRED: -32002,
    FORBIDDEN: -32003,
    NOT_FOUND: -32004,
    METHOD_NOT_SUPPORTED: -32005,
    TIMEOUT: -32008,
    CONFLICT: -32009,
    PRECONDITION_FAILED: -32012,
    PAYLOAD_TOO_LARGE: -32013,
    UNSUPPORTED_MEDIA_TYPE: -32015,
    UNPROCESSABLE_CONTENT: -32022,
    TOO_MANY_REQUESTS: -32029,
    CLIENT_CLOSED_REQUEST: -32099
};
const TRPC_ERROR_CODES_BY_NUMBER = {
    [-32700]: "PARSE_ERROR",
    [-32600]: "BAD_REQUEST",
    [-32603]: "INTERNAL_SERVER_ERROR",
    [-32001]: "UNAUTHORIZED",
    [-32002]: "PAYMENT_REQUIRED",
    [-32003]: "FORBIDDEN",
    [-32004]: "NOT_FOUND",
    [-32005]: "METHOD_NOT_SUPPORTED",
    [-32008]: "TIMEOUT",
    [-32009]: "CONFLICT",
    [-32012]: "PRECONDITION_FAILED",
    [-32013]: "PAYLOAD_TOO_LARGE",
    [-32015]: "UNSUPPORTED_MEDIA_TYPE",
    [-32022]: "UNPROCESSABLE_CONTENT",
    [-32029]: "TOO_MANY_REQUESTS",
    [-32099]: "CLIENT_CLOSED_REQUEST"
};
/**
* tRPC error codes that are considered retryable
* With out of the box SSE, the client will reconnect when these errors are encountered
*/ const retryableRpcCodes = [
    TRPC_ERROR_CODES_BY_KEY.BAD_GATEWAY,
    TRPC_ERROR_CODES_BY_KEY.SERVICE_UNAVAILABLE,
    TRPC_ERROR_CODES_BY_KEY.GATEWAY_TIMEOUT,
    TRPC_ERROR_CODES_BY_KEY.INTERNAL_SERVER_ERROR
];
//#endregion
//#region src/unstable-core-do-not-import/utils.ts
/**
* Ensures there are no duplicate keys when building a procedure.
* @internal
*/ function mergeWithoutOverrides(obj1, ...objs) {
    const newObj = Object.assign(Object.create(null), obj1);
    for (const overrides of objs)for(const key in overrides){
        if (key in newObj && newObj[key] !== overrides[key]) throw new Error(`Duplicate key ${key}`);
        newObj[key] = overrides[key];
    }
    return newObj;
}
/**
* Check that value is object
* @internal
*/ function isObject(value) {
    return !!value && !Array.isArray(value) && typeof value === "object";
}
function isFunction(fn) {
    return typeof fn === "function";
}
/**
* Create an object without inheriting anything from `Object.prototype`
* @internal
*/ function omitPrototype(obj) {
    return Object.assign(Object.create(null), obj);
}
const asyncIteratorsSupported = typeof Symbol === "function" && !!Symbol.asyncIterator;
function isAsyncIterable(value) {
    return asyncIteratorsSupported && isObject(value) && Symbol.asyncIterator in value;
}
/**
* Run an IIFE
*/ const run = (fn)=>fn();
function noop() {}
function identity(it) {
    return it;
}
/**
* Generic runtime assertion function. Throws, if the condition is not `true`.
*
* Can be used as a slightly less dangerous variant of type assertions. Code
* mistakes would be revealed at runtime then (hopefully during testing).
*/ function assert(condition, msg = "no additional info") {
    if (!condition) throw new Error(`AssertionError: ${msg}`);
}
function sleep(ms = 0) {
    return new Promise((res)=>setTimeout(res, ms));
}
/**
* Ponyfill for
* [`AbortSignal.any`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal/any_static).
*/ function abortSignalsAnyPonyfill(signals) {
    if (typeof AbortSignal.any === "function") return AbortSignal.any(signals);
    const ac = new AbortController();
    for (const signal of signals){
        if (signal.aborted) {
            trigger();
            break;
        }
        signal.addEventListener("abort", trigger, {
            once: true
        });
    }
    return ac.signal;
    //TURBOPACK unreachable
    ;
    function trigger() {
        ac.abort();
        for (const signal of signals)signal.removeEventListener("abort", trigger);
    }
}
;
 //# sourceMappingURL=utils-DdbbrDku.mjs.map
}),
"[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "HTTP_CODE_TO_JSONRPC2": ()=>HTTP_CODE_TO_JSONRPC2,
    "JSONRPC2_TO_HTTP_CODE": ()=>JSONRPC2_TO_HTTP_CODE,
    "__commonJS": ()=>__commonJS,
    "__toESM": ()=>__toESM,
    "createFlatProxy": ()=>createFlatProxy,
    "createRecursiveProxy": ()=>createRecursiveProxy,
    "getErrorShape": ()=>getErrorShape,
    "getHTTPStatusCode": ()=>getHTTPStatusCode,
    "getHTTPStatusCodeFromError": ()=>getHTTPStatusCodeFromError,
    "getStatusCodeFromKey": ()=>getStatusCodeFromKey,
    "getStatusKeyFromCode": ()=>getStatusKeyFromCode,
    "require_defineProperty": ()=>require_defineProperty,
    "require_objectSpread2": ()=>require_objectSpread2
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)");
;
//#region rolldown:runtime
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod)=>function() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") for(var keys = __getOwnPropNames(from), i = 0, n = keys.length, key; i < n; i++){
        key = keys[i];
        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ((k)=>from[k]).bind(null, key),
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
//#endregion
//#region src/unstable-core-do-not-import/createProxy.ts
const noop = ()=>{};
const freezeIfAvailable = (obj)=>{
    if (Object.freeze) Object.freeze(obj);
};
function createInnerProxy(callback, path, memo) {
    var _memo$cacheKey;
    const cacheKey = path.join(".");
    (_memo$cacheKey = memo[cacheKey]) !== null && _memo$cacheKey !== void 0 || (memo[cacheKey] = new Proxy(noop, {
        get (_obj, key) {
            if (typeof key !== "string" || key === "then") return void 0;
            return createInnerProxy(callback, [
                ...path,
                key
            ], memo);
        },
        apply (_1, _2, args) {
            const lastOfPath = path[path.length - 1];
            let opts = {
                args,
                path
            };
            if (lastOfPath === "call") opts = {
                args: args.length >= 2 ? [
                    args[1]
                ] : [],
                path: path.slice(0, -1)
            };
            else if (lastOfPath === "apply") opts = {
                args: args.length >= 2 ? args[1] : [],
                path: path.slice(0, -1)
            };
            freezeIfAvailable(opts.args);
            freezeIfAvailable(opts.path);
            return callback(opts);
        }
    }));
    return memo[cacheKey];
}
/**
* Creates a proxy that calls the callback with the path and arguments
*
* @internal
*/ const createRecursiveProxy = (callback)=>createInnerProxy(callback, [], Object.create(null));
/**
* Used in place of `new Proxy` where each handler will map 1 level deep to another value.
*
* @internal
*/ const createFlatProxy = (callback)=>{
    return new Proxy(noop, {
        get (_obj, name) {
            if (name === "then") return void 0;
            return callback(name);
        }
    });
};
//#endregion
//#region src/unstable-core-do-not-import/http/getHTTPStatusCode.ts
const JSONRPC2_TO_HTTP_CODE = {
    PARSE_ERROR: 400,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    PAYMENT_REQUIRED: 402,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_SUPPORTED: 405,
    TIMEOUT: 408,
    CONFLICT: 409,
    PRECONDITION_FAILED: 412,
    PAYLOAD_TOO_LARGE: 413,
    UNSUPPORTED_MEDIA_TYPE: 415,
    UNPROCESSABLE_CONTENT: 422,
    TOO_MANY_REQUESTS: 429,
    CLIENT_CLOSED_REQUEST: 499,
    INTERNAL_SERVER_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504
};
const HTTP_CODE_TO_JSONRPC2 = {
    400: "BAD_REQUEST",
    401: "UNAUTHORIZED",
    402: "PAYMENT_REQUIRED",
    403: "FORBIDDEN",
    404: "NOT_FOUND",
    405: "METHOD_NOT_SUPPORTED",
    408: "TIMEOUT",
    409: "CONFLICT",
    412: "PRECONDITION_FAILED",
    413: "PAYLOAD_TOO_LARGE",
    415: "UNSUPPORTED_MEDIA_TYPE",
    422: "UNPROCESSABLE_CONTENT",
    429: "TOO_MANY_REQUESTS",
    499: "CLIENT_CLOSED_REQUEST",
    500: "INTERNAL_SERVER_ERROR",
    501: "NOT_IMPLEMENTED",
    502: "BAD_GATEWAY",
    503: "SERVICE_UNAVAILABLE",
    504: "GATEWAY_TIMEOUT"
};
function getStatusCodeFromKey(code) {
    var _JSONRPC2_TO_HTTP_COD;
    return (_JSONRPC2_TO_HTTP_COD = JSONRPC2_TO_HTTP_CODE[code]) !== null && _JSONRPC2_TO_HTTP_COD !== void 0 ? _JSONRPC2_TO_HTTP_COD : 500;
}
function getStatusKeyFromCode(code) {
    var _HTTP_CODE_TO_JSONRPC;
    return (_HTTP_CODE_TO_JSONRPC = HTTP_CODE_TO_JSONRPC2[code]) !== null && _HTTP_CODE_TO_JSONRPC !== void 0 ? _HTTP_CODE_TO_JSONRPC : "INTERNAL_SERVER_ERROR";
}
function getHTTPStatusCode(json) {
    const arr = Array.isArray(json) ? json : [
        json
    ];
    const httpStatuses = new Set(arr.map((res)=>{
        if ("error" in res && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(res.error.data)) {
            var _res$error$data;
            if (typeof ((_res$error$data = res.error.data) === null || _res$error$data === void 0 ? void 0 : _res$error$data["httpStatus"]) === "number") return res.error.data["httpStatus"];
            const code = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPC_ERROR_CODES_BY_NUMBER"][res.error.code];
            return getStatusCodeFromKey(code);
        }
        return 200;
    }));
    if (httpStatuses.size !== 1) return 207;
    const httpStatus = httpStatuses.values().next().value;
    return httpStatus;
}
function getHTTPStatusCodeFromError(error) {
    return getStatusCodeFromKey(error.code);
}
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js
var require_typeof = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/typeof.js" (exports, module) {
        function _typeof$2(o) {
            "@babel/helpers - typeof";
            return module.exports = _typeof$2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o$1) {
                return typeof o$1;
            } : function(o$1) {
                return o$1 && "function" == typeof Symbol && o$1.constructor === Symbol && o$1 !== Symbol.prototype ? "symbol" : typeof o$1;
            }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof$2(o);
        }
        module.exports = _typeof$2, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js
var require_toPrimitive = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPrimitive.js" (exports, module) {
        var _typeof$1 = require_typeof()["default"];
        function toPrimitive$1(t, r) {
            if ("object" != _typeof$1(t) || !t) return t;
            var e = t[Symbol.toPrimitive];
            if (void 0 !== e) {
                var i = e.call(t, r || "default");
                if ("object" != _typeof$1(i)) return i;
                throw new TypeError("@@toPrimitive must return a primitive value.");
            }
            return ("string" === r ? String : Number)(t);
        }
        module.exports = toPrimitive$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js
var require_toPropertyKey = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/toPropertyKey.js" (exports, module) {
        var _typeof = require_typeof()["default"];
        var toPrimitive = require_toPrimitive();
        function toPropertyKey$1(t) {
            var i = toPrimitive(t, "string");
            return "symbol" == _typeof(i) ? i : i + "";
        }
        module.exports = toPropertyKey$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js
var require_defineProperty = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/defineProperty.js" (exports, module) {
        var toPropertyKey = require_toPropertyKey();
        function _defineProperty(e, r, t) {
            return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
                value: t,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : e[r] = t, e;
        }
        module.exports = _defineProperty, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js
var require_objectSpread2 = __commonJS({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/objectSpread2.js" (exports, module) {
        var defineProperty = require_defineProperty();
        function ownKeys(e, r) {
            var t = Object.keys(e);
            if (Object.getOwnPropertySymbols) {
                var o = Object.getOwnPropertySymbols(e);
                r && (o = o.filter(function(r$1) {
                    return Object.getOwnPropertyDescriptor(e, r$1).enumerable;
                })), t.push.apply(t, o);
            }
            return t;
        }
        function _objectSpread2(e) {
            for(var r = 1; r < arguments.length; r++){
                var t = null != arguments[r] ? arguments[r] : {};
                r % 2 ? ownKeys(Object(t), !0).forEach(function(r$1) {
                    defineProperty(e, r$1, t[r$1]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r$1) {
                    Object.defineProperty(e, r$1, Object.getOwnPropertyDescriptor(t, r$1));
                });
            }
            return e;
        }
        module.exports = _objectSpread2, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region src/unstable-core-do-not-import/error/getErrorShape.ts
var import_objectSpread2 = __toESM(require_objectSpread2(), 1);
/**
* @internal
*/ function getErrorShape(opts) {
    const { path, error, config } = opts;
    const { code } = opts.error;
    const shape = {
        message: error.message,
        code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPC_ERROR_CODES_BY_KEY"][code],
        data: {
            code,
            httpStatus: getHTTPStatusCodeFromError(error)
        }
    };
    if (config.isDev && typeof opts.error.stack === "string") shape.data.stack = opts.error.stack;
    if (typeof path === "string") shape.data.path = path;
    return config.errorFormatter((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, opts), {}, {
        shape
    }));
}
;
 //# sourceMappingURL=getErrorShape-Uhlrl4Bk.mjs.map
}),
"[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TRPCError": ()=>TRPCError,
    "callProcedure": ()=>callProcedure,
    "createCallerFactory": ()=>createCallerFactory,
    "createRouterFactory": ()=>createRouterFactory,
    "defaultFormatter": ()=>defaultFormatter,
    "defaultTransformer": ()=>defaultTransformer,
    "getCauseFromUnknown": ()=>getCauseFromUnknown,
    "getDataTransformer": ()=>getDataTransformer,
    "getProcedureAtPath": ()=>getProcedureAtPath,
    "getTRPCErrorFromUnknown": ()=>getTRPCErrorFromUnknown,
    "isTrackedEnvelope": ()=>isTrackedEnvelope,
    "lazy": ()=>lazy,
    "mergeRouters": ()=>mergeRouters,
    "sse": ()=>sse,
    "tracked": ()=>tracked,
    "transformResult": ()=>transformResult,
    "transformTRPCResponse": ()=>transformTRPCResponse
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)");
;
;
//#region src/unstable-core-do-not-import/error/formatter.ts
const defaultFormatter = ({ shape })=>{
    return shape;
};
//#endregion
//#region src/unstable-core-do-not-import/error/TRPCError.ts
var import_defineProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
var UnknownCauseError = class extends Error {
};
function getCauseFromUnknown(cause) {
    if (cause instanceof Error) return cause;
    const type = typeof cause;
    if (type === "undefined" || type === "function" || cause === null) return void 0;
    if (type !== "object") return new Error(String(cause));
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(cause)) return Object.assign(new UnknownCauseError(), cause);
    return void 0;
}
function getTRPCErrorFromUnknown(cause) {
    if (cause instanceof TRPCError) return cause;
    if (cause instanceof Error && cause.name === "TRPCError") return cause;
    const trpcError = new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        cause
    });
    if (cause instanceof Error && cause.stack) trpcError.stack = cause.stack;
    return trpcError;
}
var TRPCError = class extends Error {
    constructor(opts){
        var _ref, _opts$message, _this$cause;
        const cause = getCauseFromUnknown(opts.cause);
        const message = (_ref = (_opts$message = opts.message) !== null && _opts$message !== void 0 ? _opts$message : cause === null || cause === void 0 ? void 0 : cause.message) !== null && _ref !== void 0 ? _ref : opts.code;
        super(message, {
            cause
        });
        (0, import_defineProperty.default)(this, "cause", void 0);
        (0, import_defineProperty.default)(this, "code", void 0);
        this.code = opts.code;
        this.name = "TRPCError";
        (_this$cause = this.cause) !== null && _this$cause !== void 0 || (this.cause = cause);
    }
};
//#endregion
//#region src/unstable-core-do-not-import/transformer.ts
var import_objectSpread2$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* @internal
*/ function getDataTransformer(transformer) {
    if ("input" in transformer) return transformer;
    return {
        input: transformer,
        output: transformer
    };
}
/**
* @internal
*/ const defaultTransformer = {
    input: {
        serialize: (obj)=>obj,
        deserialize: (obj)=>obj
    },
    output: {
        serialize: (obj)=>obj,
        deserialize: (obj)=>obj
    }
};
function transformTRPCResponseItem(config, item) {
    if ("error" in item) return (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, item), {}, {
        error: config.transformer.output.serialize(item.error)
    });
    if ("data" in item.result) return (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, item), {}, {
        result: (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, item.result), {}, {
            data: config.transformer.output.serialize(item.result.data)
        })
    });
    return item;
}
/**
* Takes a unserialized `TRPCResponse` and serializes it with the router's transformers
**/ function transformTRPCResponse(config, itemOrItems) {
    return Array.isArray(itemOrItems) ? itemOrItems.map((item)=>transformTRPCResponseItem(config, item)) : transformTRPCResponseItem(config, itemOrItems);
}
/** @internal */ function transformResultInner(response, transformer) {
    if ("error" in response) {
        const error = transformer.deserialize(response.error);
        return {
            ok: false,
            error: (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, response), {}, {
                error
            })
        };
    }
    const result = (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, response.result), (!response.result.type || response.result.type === "data") && {
        type: "data",
        data: transformer.deserialize(response.result.data)
    });
    return {
        ok: true,
        result
    };
}
var TransformResultError = class extends Error {
    constructor(){
        super("Unable to transform response from server");
    }
};
/**
* Transforms and validates that the result is a valid TRPCResponse
* @internal
*/ function transformResult(response, transformer) {
    let result;
    try {
        result = transformResultInner(response, transformer);
    } catch (_unused) {
        throw new TransformResultError();
    }
    if (!result.ok && (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(result.error.error) || typeof result.error.error["code"] !== "number")) throw new TransformResultError();
    if (result.ok && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(result.result)) throw new TransformResultError();
    return result;
}
//#endregion
//#region src/unstable-core-do-not-import/router.ts
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
const lazySymbol = Symbol("lazy");
function once(fn) {
    const uncalled = Symbol();
    let result = uncalled;
    return ()=>{
        if (result === uncalled) result = fn();
        return result;
    };
}
/**
* Lazy load a router
* @see https://trpc.io/docs/server/merging-routers#lazy-load
*/ function lazy(importRouter) {
    async function resolve() {
        const mod = await importRouter();
        if (isRouter(mod)) return mod;
        const routers = Object.values(mod);
        if (routers.length !== 1 || !isRouter(routers[0])) throw new Error("Invalid router module - either define exactly 1 export or return the router directly.\nExample: `lazy(() => import('./slow.js').then((m) => m.slowRouter))`");
        return routers[0];
    }
    resolve[lazySymbol] = true;
    return resolve;
}
function isLazy(input) {
    return typeof input === "function" && lazySymbol in input;
}
function isRouter(value) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(value) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(value["_def"]) && "router" in value["_def"];
}
const emptyRouter = {
    _ctx: null,
    _errorShape: null,
    _meta: null,
    queries: {},
    mutations: {},
    subscriptions: {},
    errorFormatter: defaultFormatter,
    transformer: defaultTransformer
};
/**
* Reserved words that can't be used as router or procedure names
*/ const reservedWords = [
    "then",
    "call",
    "apply"
];
/**
* @internal
*/ function createRouterFactory(config) {
    function createRouterInner(input) {
        const reservedWordsUsed = new Set(Object.keys(input).filter((v)=>reservedWords.includes(v)));
        if (reservedWordsUsed.size > 0) throw new Error("Reserved words used in `router({})` call: " + Array.from(reservedWordsUsed).join(", "));
        const procedures = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["omitPrototype"])({});
        const lazy$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["omitPrototype"])({});
        function createLazyLoader(opts) {
            return {
                ref: opts.ref,
                load: once(async ()=>{
                    const router$1 = await opts.ref();
                    const lazyPath = [
                        ...opts.path,
                        opts.key
                    ];
                    const lazyKey = lazyPath.join(".");
                    opts.aggregate[opts.key] = step(router$1._def.record, lazyPath);
                    delete lazy$1[lazyKey];
                    for (const [nestedKey, nestedItem] of Object.entries(router$1._def.lazy)){
                        const nestedRouterKey = [
                            ...lazyPath,
                            nestedKey
                        ].join(".");
                        lazy$1[nestedRouterKey] = createLazyLoader({
                            ref: nestedItem.ref,
                            path: lazyPath,
                            key: nestedKey,
                            aggregate: opts.aggregate[opts.key]
                        });
                    }
                })
            };
        }
        function step(from, path = []) {
            const aggregate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["omitPrototype"])({});
            for (const [key, item] of Object.entries(from !== null && from !== void 0 ? from : {})){
                if (isLazy(item)) {
                    lazy$1[[
                        ...path,
                        key
                    ].join(".")] = createLazyLoader({
                        path,
                        ref: item,
                        key,
                        aggregate
                    });
                    continue;
                }
                if (isRouter(item)) {
                    aggregate[key] = step(item._def.record, [
                        ...path,
                        key
                    ]);
                    continue;
                }
                if (!isProcedure(item)) {
                    aggregate[key] = step(item, [
                        ...path,
                        key
                    ]);
                    continue;
                }
                const newPath = [
                    ...path,
                    key
                ].join(".");
                if (procedures[newPath]) throw new Error(`Duplicate key: ${newPath}`);
                procedures[newPath] = item;
                aggregate[key] = item;
            }
            return aggregate;
        }
        const record = step(input);
        const _def = (0, import_objectSpread2.default)((0, import_objectSpread2.default)({
            _config: config,
            router: true,
            procedures,
            lazy: lazy$1
        }, emptyRouter), {}, {
            record
        });
        const router = (0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, record), {}, {
            _def,
            createCaller: createCallerFactory()({
                _def
            })
        });
        return router;
    }
    return createRouterInner;
}
function isProcedure(procedureOrRouter) {
    return typeof procedureOrRouter === "function";
}
/**
* @internal
*/ async function getProcedureAtPath(router, path) {
    const { _def } = router;
    let procedure = _def.procedures[path];
    while(!procedure){
        const key = Object.keys(_def.lazy).find((key$1)=>path.startsWith(key$1));
        if (!key) return null;
        const lazyRouter = _def.lazy[key];
        await lazyRouter.load();
        procedure = _def.procedures[path];
    }
    return procedure;
}
/**
* @internal
*/ async function callProcedure(opts) {
    const { type, path } = opts;
    const proc = await getProcedureAtPath(opts.router, path);
    if (!proc || !isProcedure(proc) || proc._def.type !== type && !opts.allowMethodOverride) throw new TRPCError({
        code: "NOT_FOUND",
        message: `No "${type}"-procedure on path "${path}"`
    });
    /* istanbul ignore if -- @preserve */ if (proc._def.type !== type && opts.allowMethodOverride && proc._def.type === "subscription") throw new TRPCError({
        code: "METHOD_NOT_SUPPORTED",
        message: `Method override is not supported for subscriptions`
    });
    return proc(opts);
}
function createCallerFactory() {
    return function createCallerInner(router) {
        const { _def } = router;
        return function createCaller(ctxOrCallback, opts) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createRecursiveProxy"])(async ({ path, args })=>{
                const fullPath = path.join(".");
                if (path.length === 1 && path[0] === "_def") return _def;
                const procedure = await getProcedureAtPath(router, fullPath);
                let ctx = void 0;
                try {
                    if (!procedure) throw new TRPCError({
                        code: "NOT_FOUND",
                        message: `No procedure found on path "${path}"`
                    });
                    ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(ctxOrCallback) ? await Promise.resolve(ctxOrCallback()) : ctxOrCallback;
                    return await procedure({
                        path: fullPath,
                        getRawInput: async ()=>args[0],
                        ctx,
                        type: procedure._def.type,
                        signal: opts === null || opts === void 0 ? void 0 : opts.signal
                    });
                } catch (cause) {
                    var _opts$onError, _procedure$_def$type;
                    opts === null || opts === void 0 || (_opts$onError = opts.onError) === null || _opts$onError === void 0 || _opts$onError.call(opts, {
                        ctx,
                        error: getTRPCErrorFromUnknown(cause),
                        input: args[0],
                        path: fullPath,
                        type: (_procedure$_def$type = procedure === null || procedure === void 0 ? void 0 : procedure._def.type) !== null && _procedure$_def$type !== void 0 ? _procedure$_def$type : "unknown"
                    });
                    throw cause;
                }
            });
        };
    };
}
function mergeRouters(...routerList) {
    var _routerList$;
    const record = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeWithoutOverrides"])({}, ...routerList.map((r)=>r._def.record));
    const errorFormatter = routerList.reduce((currentErrorFormatter, nextRouter)=>{
        if (nextRouter._def._config.errorFormatter && nextRouter._def._config.errorFormatter !== defaultFormatter) {
            if (currentErrorFormatter !== defaultFormatter && currentErrorFormatter !== nextRouter._def._config.errorFormatter) throw new Error("You seem to have several error formatters");
            return nextRouter._def._config.errorFormatter;
        }
        return currentErrorFormatter;
    }, defaultFormatter);
    const transformer = routerList.reduce((prev, current)=>{
        if (current._def._config.transformer && current._def._config.transformer !== defaultTransformer) {
            if (prev !== defaultTransformer && prev !== current._def._config.transformer) throw new Error("You seem to have several transformers");
            return current._def._config.transformer;
        }
        return prev;
    }, defaultTransformer);
    const router = createRouterFactory({
        errorFormatter,
        transformer,
        isDev: routerList.every((r)=>r._def._config.isDev),
        allowOutsideOfServer: routerList.every((r)=>r._def._config.allowOutsideOfServer),
        isServer: routerList.every((r)=>r._def._config.isServer),
        $types: (_routerList$ = routerList[0]) === null || _routerList$ === void 0 ? void 0 : _routerList$._def._config.$types
    })(record);
    return router;
}
//#endregion
//#region src/unstable-core-do-not-import/stream/tracked.ts
const trackedSymbol = Symbol();
/**
* Produce a typed server-sent event message
* @deprecated use `tracked(id, data)` instead
*/ function sse(event) {
    return tracked(event.id, event.data);
}
function isTrackedEnvelope(value) {
    return Array.isArray(value) && value[2] === trackedSymbol;
}
/**
* Automatically track an event so that it can be resumed from a given id if the connection is lost
*/ function tracked(id, data) {
    if (id === "") throw new Error("`id` must not be an empty string as empty string is the same as not setting the id at all");
    return [
        id,
        data,
        trackedSymbol
    ];
}
;
 //# sourceMappingURL=tracked-gU3ttYjg.mjs.map
}),
"[project]/node_modules/@trpc/server/dist/observable-CUiPknO-.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "behaviorSubject": ()=>behaviorSubject,
    "distinctUntilChanged": ()=>distinctUntilChanged,
    "distinctUntilDeepChanged": ()=>distinctUntilDeepChanged,
    "map": ()=>map,
    "share": ()=>share,
    "tap": ()=>tap
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
;
//#region src/observable/operators.ts
function map(project) {
    return (source)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((destination)=>{
            let index = 0;
            const subscription = source.subscribe({
                next (value) {
                    destination.next(project(value, index++));
                },
                error (error) {
                    destination.error(error);
                },
                complete () {
                    destination.complete();
                }
            });
            return subscription;
        });
    };
}
function share(_opts) {
    return (source)=>{
        let refCount = 0;
        let subscription = null;
        const observers = [];
        function startIfNeeded() {
            if (subscription) return;
            subscription = source.subscribe({
                next (value) {
                    for (const observer of observers){
                        var _observer$next;
                        (_observer$next = observer.next) === null || _observer$next === void 0 || _observer$next.call(observer, value);
                    }
                },
                error (error) {
                    for (const observer of observers){
                        var _observer$error;
                        (_observer$error = observer.error) === null || _observer$error === void 0 || _observer$error.call(observer, error);
                    }
                },
                complete () {
                    for (const observer of observers){
                        var _observer$complete;
                        (_observer$complete = observer.complete) === null || _observer$complete === void 0 || _observer$complete.call(observer);
                    }
                }
            });
        }
        function resetIfNeeded() {
            if (refCount === 0 && subscription) {
                const _sub = subscription;
                subscription = null;
                _sub.unsubscribe();
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((subscriber)=>{
            refCount++;
            observers.push(subscriber);
            startIfNeeded();
            return {
                unsubscribe () {
                    refCount--;
                    resetIfNeeded();
                    const index = observers.findIndex((v)=>v === subscriber);
                    if (index > -1) observers.splice(index, 1);
                }
            };
        });
    };
}
function tap(observer) {
    return (source)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((destination)=>{
            return source.subscribe({
                next (value) {
                    var _observer$next2;
                    (_observer$next2 = observer.next) === null || _observer$next2 === void 0 || _observer$next2.call(observer, value);
                    destination.next(value);
                },
                error (error) {
                    var _observer$error2;
                    (_observer$error2 = observer.error) === null || _observer$error2 === void 0 || _observer$error2.call(observer, error);
                    destination.error(error);
                },
                complete () {
                    var _observer$complete2;
                    (_observer$complete2 = observer.complete) === null || _observer$complete2 === void 0 || _observer$complete2.call(observer);
                    destination.complete();
                }
            });
        });
    };
}
const distinctUnsetMarker = Symbol();
function distinctUntilChanged(compare = (a, b)=>a === b) {
    return (source)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((destination)=>{
            let lastValue = distinctUnsetMarker;
            return source.subscribe({
                next (value) {
                    if (lastValue !== distinctUnsetMarker && compare(lastValue, value)) return;
                    lastValue = value;
                    destination.next(value);
                },
                error (error) {
                    destination.error(error);
                },
                complete () {
                    destination.complete();
                }
            });
        });
    };
}
const isDeepEqual = (a, b)=>{
    if (a === b) return true;
    const bothAreObjects = a && b && typeof a === "object" && typeof b === "object";
    return !!bothAreObjects && Object.keys(a).length === Object.keys(b).length && Object.entries(a).every(([k, v])=>isDeepEqual(v, b[k]));
};
function distinctUntilDeepChanged() {
    return distinctUntilChanged(isDeepEqual);
}
//#endregion
//#region src/observable/behaviorSubject.ts
/**
* @internal
* An observable that maintains and provides a "current value" to subscribers
* @see https://www.learnrxjs.io/learn-rxjs/subjects/behaviorsubject
*/ function behaviorSubject(initialValue) {
    let value = initialValue;
    const observerList = [];
    const addObserver = (observer)=>{
        if (value !== void 0) observer.next(value);
        observerList.push(observer);
    };
    const removeObserver = (observer)=>{
        observerList.splice(observerList.indexOf(observer), 1);
    };
    const obs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observable"])((observer)=>{
        addObserver(observer);
        return ()=>{
            removeObserver(observer);
        };
    });
    obs.next = (nextValue)=>{
        if (value === nextValue) return;
        value = nextValue;
        for (const observer of observerList)observer.next(nextValue);
    };
    obs.get = ()=>value;
    return obs;
}
;
 //# sourceMappingURL=observable-CUiPknO-.mjs.map
}),
"[project]/node_modules/@trpc/server/dist/resolveResponse-CzlbRpCI.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Unpromise": ()=>Unpromise,
    "createDeferred": ()=>createDeferred,
    "getRequestInfo": ()=>getRequestInfo,
    "isAbortError": ()=>isAbortError,
    "isPromise": ()=>isPromise,
    "iteratorResource": ()=>iteratorResource,
    "jsonlStreamConsumer": ()=>jsonlStreamConsumer,
    "jsonlStreamProducer": ()=>jsonlStreamProducer,
    "makeAsyncResource": ()=>makeAsyncResource,
    "makeResource": ()=>makeResource,
    "parseConnectionParamsFromString": ()=>parseConnectionParamsFromString,
    "parseConnectionParamsFromUnknown": ()=>parseConnectionParamsFromUnknown,
    "require_usingCtx": ()=>require_usingCtx,
    "resolveResponse": ()=>resolveResponse,
    "sseHeaders": ()=>sseHeaders,
    "sseStreamConsumer": ()=>sseStreamConsumer,
    "sseStreamProducer": ()=>sseStreamProducer,
    "takeWithGrace": ()=>takeWithGrace,
    "throwAbortError": ()=>throwAbortError,
    "withMaxDuration": ()=>withMaxDuration
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/utils-DdbbrDku.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/observable-UMO3vUa_.mjs [app-ssr] (ecmascript)");
;
;
;
;
//#region src/unstable-core-do-not-import/http/parseConnectionParams.ts
function parseConnectionParamsFromUnknown(parsed) {
    try {
        if (parsed === null) return null;
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(parsed)) throw new Error("Expected object");
        const nonStringValues = Object.entries(parsed).filter(([_key, value])=>typeof value !== "string");
        if (nonStringValues.length > 0) throw new Error(`Expected connectionParams to be string values. Got ${nonStringValues.map(([key, value])=>`${key}: ${typeof value}`).join(", ")}`);
        return parsed;
    } catch (cause) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "PARSE_ERROR",
            message: "Invalid connection params shape",
            cause
        });
    }
}
function parseConnectionParamsFromString(str) {
    let parsed;
    try {
        parsed = JSON.parse(str);
    } catch (cause) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "PARSE_ERROR",
            message: "Not JSON-parsable query params",
            cause
        });
    }
    return parseConnectionParamsFromUnknown(parsed);
}
//#endregion
//#region src/unstable-core-do-not-import/http/contentType.ts
var import_objectSpread2$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
/**
* Memoize a function that takes no arguments
* @internal
*/ function memo(fn) {
    let promise = null;
    const sym = Symbol.for("@trpc/server/http/memo");
    let value = sym;
    return {
        read: async ()=>{
            var _promise;
            if (value !== sym) return value;
            (_promise = promise) !== null && _promise !== void 0 || (promise = fn().catch((cause)=>{
                if (cause instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]) throw cause;
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "BAD_REQUEST",
                    message: cause instanceof Error ? cause.message : "Invalid input",
                    cause
                });
            }));
            value = await promise;
            promise = null;
            return value;
        },
        result: ()=>{
            return value !== sym ? value : void 0;
        }
    };
}
const jsonContentTypeHandler = {
    isMatch (req) {
        var _req$headers$get;
        return !!((_req$headers$get = req.headers.get("content-type")) === null || _req$headers$get === void 0 ? void 0 : _req$headers$get.startsWith("application/json"));
    },
    async parse (opts) {
        var _types$values$next$va;
        const { req } = opts;
        const isBatchCall = opts.searchParams.get("batch") === "1";
        const paths = isBatchCall ? opts.path.split(",") : [
            opts.path
        ];
        const getInputs = memo(async ()=>{
            let inputs = void 0;
            if (req.method === "GET") {
                const queryInput = opts.searchParams.get("input");
                if (queryInput) inputs = JSON.parse(queryInput);
            } else inputs = await req.json();
            if (inputs === void 0) return {};
            if (!isBatchCall) return {
                0: opts.router._def._config.transformer.input.deserialize(inputs)
            };
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(inputs)) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "BAD_REQUEST",
                message: "\"input\" needs to be an object when doing a batch call"
            });
            const acc = {};
            for (const index of paths.keys()){
                const input = inputs[index];
                if (input !== void 0) acc[index] = opts.router._def._config.transformer.input.deserialize(input);
            }
            return acc;
        });
        const calls = await Promise.all(paths.map(async (path, index)=>{
            const procedure = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProcedureAtPath"])(opts.router, path);
            return {
                path,
                procedure,
                getRawInput: async ()=>{
                    const inputs = await getInputs.read();
                    let input = inputs[index];
                    if ((procedure === null || procedure === void 0 ? void 0 : procedure._def.type) === "subscription") {
                        var _ref, _opts$headers$get;
                        const lastEventId = (_ref = (_opts$headers$get = opts.headers.get("last-event-id")) !== null && _opts$headers$get !== void 0 ? _opts$headers$get : opts.searchParams.get("lastEventId")) !== null && _ref !== void 0 ? _ref : opts.searchParams.get("Last-Event-Id");
                        if (lastEventId) if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(input)) input = (0, import_objectSpread2$1.default)((0, import_objectSpread2$1.default)({}, input), {}, {
                            lastEventId
                        });
                        else {
                            var _input;
                            (_input = input) !== null && _input !== void 0 || (input = {
                                lastEventId
                            });
                        }
                    }
                    return input;
                },
                result: ()=>{
                    var _getInputs$result;
                    return (_getInputs$result = getInputs.result()) === null || _getInputs$result === void 0 ? void 0 : _getInputs$result[index];
                }
            };
        }));
        const types = new Set(calls.map((call)=>{
            var _call$procedure;
            return (_call$procedure = call.procedure) === null || _call$procedure === void 0 ? void 0 : _call$procedure._def.type;
        }).filter(Boolean));
        /* istanbul ignore if -- @preserve */ if (types.size > 1) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "BAD_REQUEST",
            message: `Cannot mix procedure types in call: ${Array.from(types).join(", ")}`
        });
        const type = (_types$values$next$va = types.values().next().value) !== null && _types$values$next$va !== void 0 ? _types$values$next$va : "unknown";
        const connectionParamsStr = opts.searchParams.get("connectionParams");
        const info = {
            isBatchCall,
            accept: req.headers.get("trpc-accept"),
            calls,
            type,
            connectionParams: connectionParamsStr === null ? null : parseConnectionParamsFromString(connectionParamsStr),
            signal: req.signal,
            url: opts.url
        };
        return info;
    }
};
const formDataContentTypeHandler = {
    isMatch (req) {
        var _req$headers$get2;
        return !!((_req$headers$get2 = req.headers.get("content-type")) === null || _req$headers$get2 === void 0 ? void 0 : _req$headers$get2.startsWith("multipart/form-data"));
    },
    async parse (opts) {
        const { req } = opts;
        if (req.method !== "POST") throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "METHOD_NOT_SUPPORTED",
            message: "Only POST requests are supported for multipart/form-data requests"
        });
        const getInputs = memo(async ()=>{
            const fd = await req.formData();
            return fd;
        });
        const procedure = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProcedureAtPath"])(opts.router, opts.path);
        return {
            accept: null,
            calls: [
                {
                    path: opts.path,
                    getRawInput: getInputs.read,
                    result: getInputs.result,
                    procedure
                }
            ],
            isBatchCall: false,
            type: "mutation",
            connectionParams: null,
            signal: req.signal,
            url: opts.url
        };
    }
};
const octetStreamContentTypeHandler = {
    isMatch (req) {
        var _req$headers$get3;
        return !!((_req$headers$get3 = req.headers.get("content-type")) === null || _req$headers$get3 === void 0 ? void 0 : _req$headers$get3.startsWith("application/octet-stream"));
    },
    async parse (opts) {
        const { req } = opts;
        if (req.method !== "POST") throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "METHOD_NOT_SUPPORTED",
            message: "Only POST requests are supported for application/octet-stream requests"
        });
        const getInputs = memo(async ()=>{
            return req.body;
        });
        return {
            calls: [
                {
                    path: opts.path,
                    getRawInput: getInputs.read,
                    result: getInputs.result,
                    procedure: await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProcedureAtPath"])(opts.router, opts.path)
                }
            ],
            isBatchCall: false,
            accept: null,
            type: "mutation",
            connectionParams: null,
            signal: req.signal,
            url: opts.url
        };
    }
};
const handlers = [
    jsonContentTypeHandler,
    formDataContentTypeHandler,
    octetStreamContentTypeHandler
];
function getContentTypeHandler(req) {
    const handler = handlers.find((handler$1)=>handler$1.isMatch(req));
    if (handler) return handler;
    if (!handler && req.method === "GET") return jsonContentTypeHandler;
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
        code: "UNSUPPORTED_MEDIA_TYPE",
        message: req.headers.has("content-type") ? `Unsupported content-type "${req.headers.get("content-type")}` : "Missing content-type header"
    });
}
async function getRequestInfo(opts) {
    const handler = getContentTypeHandler(opts.req);
    return await handler.parse(opts);
}
//#endregion
//#region src/unstable-core-do-not-import/http/abortError.ts
function isAbortError(error) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(error) && error["name"] === "AbortError";
}
function throwAbortError(message = "AbortError") {
    throw new DOMException(message, "AbortError");
}
//#endregion
//#region src/vendor/unpromise/unpromise.ts
var import_defineProperty = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_defineProperty"])(), 1);
let _Symbol$toStringTag;
/** Memory safe (weakmapped) cache of the ProxyPromise for each Promise,
* which is retained for the lifetime of the original Promise.
*/ const subscribableCache = /* @__PURE__ */ new WeakMap();
/** A NOOP function allowing a consistent interface for settled
* SubscribedPromises (settled promises are not subscribed - they resolve
* immediately). */ const NOOP = ()=>{};
_Symbol$toStringTag = Symbol.toStringTag;
/**
* Every `Promise<T>` can be shadowed by a single `ProxyPromise<T>`. It is
* created once, cached and reused throughout the lifetime of the Promise. Get a
* Promise's ProxyPromise using `Unpromise.proxy(promise)`.
*
* The `ProxyPromise<T>` attaches handlers to the original `Promise<T>`
* `.then()` and `.catch()` just once. Promises derived from it use a
* subscription- (and unsubscription-) based mechanism that monitors these
* handlers.
*
* Every time you call `.subscribe()`, `.then()` `.catch()` or `.finally()` on a
* `ProxyPromise<T>` it returns a `SubscribedPromise<T>` having an additional
* `unsubscribe()` method. Calling `unsubscribe()` detaches reference chains
* from the original, potentially long-lived Promise, eliminating memory leaks.
*
* This approach can eliminate the memory leaks that otherwise come about from
* repeated `race()` or `any()` calls invoking `.then()` and `.catch()` multiple
* times on the same long-lived native Promise (subscriptions which can never be
* cleaned up).
*
* `Unpromise.race(promises)` is a reference implementation of `Promise.race`
* avoiding memory leaks when using long-lived unsettled Promises.
*
* `Unpromise.any(promises)` is a reference implementation of `Promise.any`
* avoiding memory leaks when using long-lived unsettled Promises.
*
* `Unpromise.resolve(promise)` returns an ephemeral `SubscribedPromise<T>` for
* any given `Promise<T>` facilitating arbitrary async/await patterns. Behind
* the scenes, `resolve` is implemented simply as
* `Unpromise.proxy(promise).subscribe()`. Don't forget to call `.unsubscribe()`
* to tidy up!
*
*/ var Unpromise = class Unpromise {
    constructor(arg){
        (0, import_defineProperty.default)(this, "promise", void 0);
        (0, import_defineProperty.default)(this, "subscribers", []);
        (0, import_defineProperty.default)(this, "settlement", null);
        (0, import_defineProperty.default)(this, _Symbol$toStringTag, "Unpromise");
        if (typeof arg === "function") this.promise = new Promise(arg);
        else this.promise = arg;
        const thenReturn = this.promise.then((value)=>{
            const { subscribers } = this;
            this.subscribers = null;
            this.settlement = {
                status: "fulfilled",
                value
            };
            subscribers === null || subscribers === void 0 || subscribers.forEach(({ resolve })=>{
                resolve(value);
            });
        });
        if ("catch" in thenReturn) thenReturn.catch((reason)=>{
            const { subscribers } = this;
            this.subscribers = null;
            this.settlement = {
                status: "rejected",
                reason
            };
            subscribers === null || subscribers === void 0 || subscribers.forEach(({ reject })=>{
                reject(reason);
            });
        });
    }
    /** Create a promise that mitigates uncontrolled subscription to a long-lived
	* Promise via .then() and .catch() - otherwise a source of memory leaks.
	*
	* The returned promise has an `unsubscribe()` method which can be called when
	* the Promise is no longer being tracked by application logic, and which
	* ensures that there is no reference chain from the original promise to the
	* new one, and therefore no memory leak.
	*
	* If original promise has not yet settled, this adds a new unique promise
	* that listens to then/catch events, along with an `unsubscribe()` method to
	* detach it.
	*
	* If original promise has settled, then creates a new Promise.resolve() or
	* Promise.reject() and provided unsubscribe is a noop.
	*
	* If you call `unsubscribe()` before the returned Promise has settled, it
	* will never settle.
	*/ subscribe() {
        let promise;
        let unsubscribe;
        const { settlement } = this;
        if (settlement === null) {
            if (this.subscribers === null) throw new Error("Unpromise settled but still has subscribers");
            const subscriber = withResolvers();
            this.subscribers = listWithMember(this.subscribers, subscriber);
            promise = subscriber.promise;
            unsubscribe = ()=>{
                if (this.subscribers !== null) this.subscribers = listWithoutMember(this.subscribers, subscriber);
            };
        } else {
            const { status } = settlement;
            if (status === "fulfilled") promise = Promise.resolve(settlement.value);
            else promise = Promise.reject(settlement.reason);
            unsubscribe = NOOP;
        }
        return Object.assign(promise, {
            unsubscribe
        });
    }
    /** STANDARD PROMISE METHODS (but returning a SubscribedPromise) */ then(onfulfilled, onrejected) {
        const subscribed = this.subscribe();
        const { unsubscribe } = subscribed;
        return Object.assign(subscribed.then(onfulfilled, onrejected), {
            unsubscribe
        });
    }
    catch(onrejected) {
        const subscribed = this.subscribe();
        const { unsubscribe } = subscribed;
        return Object.assign(subscribed.catch(onrejected), {
            unsubscribe
        });
    }
    finally(onfinally) {
        const subscribed = this.subscribe();
        const { unsubscribe } = subscribed;
        return Object.assign(subscribed.finally(onfinally), {
            unsubscribe
        });
    }
    /** Unpromise STATIC METHODS */ /** Create or Retrieve the proxy Unpromise (a re-used Unpromise for the VM lifetime
	* of the provided Promise reference) */ static proxy(promise) {
        const cached = Unpromise.getSubscribablePromise(promise);
        return typeof cached !== "undefined" ? cached : Unpromise.createSubscribablePromise(promise);
    }
    /** Create and store an Unpromise keyed by an original Promise. */ static createSubscribablePromise(promise) {
        const created = new Unpromise(promise);
        subscribableCache.set(promise, created);
        subscribableCache.set(created, created);
        return created;
    }
    /** Retrieve a previously-created Unpromise keyed by an original Promise. */ static getSubscribablePromise(promise) {
        return subscribableCache.get(promise);
    }
    /** Promise STATIC METHODS */ /** Lookup the Unpromise for this promise, and derive a SubscribedPromise from
	* it (that can be later unsubscribed to eliminate Memory leaks) */ static resolve(value) {
        const promise = typeof value === "object" && value !== null && "then" in value && typeof value.then === "function" ? value : Promise.resolve(value);
        return Unpromise.proxy(promise).subscribe();
    }
    static async any(values) {
        const valuesArray = Array.isArray(values) ? values : [
            ...values
        ];
        const subscribedPromises = valuesArray.map(Unpromise.resolve);
        try {
            return await Promise.any(subscribedPromises);
        } finally{
            subscribedPromises.forEach(({ unsubscribe })=>{
                unsubscribe();
            });
        }
    }
    static async race(values) {
        const valuesArray = Array.isArray(values) ? values : [
            ...values
        ];
        const subscribedPromises = valuesArray.map(Unpromise.resolve);
        try {
            return await Promise.race(subscribedPromises);
        } finally{
            subscribedPromises.forEach(({ unsubscribe })=>{
                unsubscribe();
            });
        }
    }
    /** Create a race of SubscribedPromises that will fulfil to a single winning
	* Promise (in a 1-Tuple). Eliminates memory leaks from long-lived promises
	* accumulating .then() and .catch() subscribers. Allows simple logic to
	* consume the result, like...
	* ```ts
	* const [ winner ] = await Unpromise.race([ promiseA, promiseB ]);
	* if(winner === promiseB){
	*   const result = await promiseB;
	*   // do the thing
	* }
	* ```
	* */ static async raceReferences(promises) {
        const selfPromises = promises.map(resolveSelfTuple);
        try {
            return await Promise.race(selfPromises);
        } finally{
            for (const promise of selfPromises)promise.unsubscribe();
        }
    }
};
/** Promises a 1-tuple containing the original promise when it resolves. Allows
* awaiting the eventual Promise ***reference*** (easy to destructure and
* exactly compare with ===). Avoids resolving to the Promise ***value*** (which
* may be ambiguous and therefore hard to identify as the winner of a race).
* You can call unsubscribe on the Promise to mitigate memory leaks.
* */ function resolveSelfTuple(promise) {
    return Unpromise.proxy(promise).then(()=>[
            promise
        ]);
}
/** VENDORED (Future) PROMISE UTILITIES */ /** Reference implementation of https://github.com/tc39/proposal-promise-with-resolvers */ function withResolvers() {
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject)=>{
        resolve = _resolve;
        reject = _reject;
    });
    return {
        promise,
        resolve,
        reject
    };
}
/** IMMUTABLE LIST OPERATIONS */ function listWithMember(arr, member) {
    return [
        ...arr,
        member
    ];
}
function listWithoutIndex(arr, index) {
    return [
        ...arr.slice(0, index),
        ...arr.slice(index + 1)
    ];
}
function listWithoutMember(arr, member) {
    const index = arr.indexOf(member);
    if (index !== -1) return listWithoutIndex(arr, index);
    return arr;
}
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/disposable.ts
var _Symbol, _Symbol$dispose, _Symbol2, _Symbol2$asyncDispose;
(_Symbol$dispose = (_Symbol = Symbol).dispose) !== null && _Symbol$dispose !== void 0 || (_Symbol.dispose = Symbol());
(_Symbol2$asyncDispose = (_Symbol2 = Symbol).asyncDispose) !== null && _Symbol2$asyncDispose !== void 0 || (_Symbol2.asyncDispose = Symbol());
/**
* Takes a value and a dispose function and returns a new object that implements the Disposable interface.
* The returned object is the original value augmented with a Symbol.dispose method.
* @param thing The value to make disposable
* @param dispose Function to call when disposing the resource
* @returns The original value with Symbol.dispose method added
*/ function makeResource(thing, dispose) {
    const it = thing;
    const existing = it[Symbol.dispose];
    it[Symbol.dispose] = ()=>{
        dispose();
        existing === null || existing === void 0 || existing();
    };
    return it;
}
/**
* Takes a value and an async dispose function and returns a new object that implements the AsyncDisposable interface.
* The returned object is the original value augmented with a Symbol.asyncDispose method.
* @param thing The value to make async disposable
* @param dispose Async function to call when disposing the resource
* @returns The original value with Symbol.asyncDispose method added
*/ function makeAsyncResource(thing, dispose) {
    const it = thing;
    const existing = it[Symbol.asyncDispose];
    it[Symbol.asyncDispose] = async ()=>{
        await dispose();
        await (existing === null || existing === void 0 ? void 0 : existing());
    };
    return it;
}
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/timerResource.ts
const disposablePromiseTimerResult = Symbol();
function timerResource(ms) {
    let timer = null;
    return makeResource({
        start () {
            if (timer) throw new Error("Timer already started");
            const promise = new Promise((resolve)=>{
                timer = setTimeout(()=>resolve(disposablePromiseTimerResult), ms);
            });
            return promise;
        }
    }, ()=>{
        if (timer) clearTimeout(timer);
    });
}
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js
var require_usingCtx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/usingCtx.js" (exports, module) {
        function _usingCtx() {
            var r = "function" == typeof SuppressedError ? SuppressedError : function(r$1, e$1) {
                var n$1 = Error();
                return n$1.name = "SuppressedError", n$1.error = r$1, n$1.suppressed = e$1, n$1;
            }, e = {}, n = [];
            function using(r$1, e$1) {
                if (null != e$1) {
                    if (Object(e$1) !== e$1) throw new TypeError("using declarations can only be used with objects, functions, null, or undefined.");
                    if (r$1) var o = e$1[Symbol.asyncDispose || Symbol["for"]("Symbol.asyncDispose")];
                    if (void 0 === o && (o = e$1[Symbol.dispose || Symbol["for"]("Symbol.dispose")], r$1)) var t = o;
                    if ("function" != typeof o) throw new TypeError("Object is not disposable.");
                    t && (o = function o$1() {
                        try {
                            t.call(e$1);
                        } catch (r$2) {
                            return Promise.reject(r$2);
                        }
                    }), n.push({
                        v: e$1,
                        d: o,
                        a: r$1
                    });
                } else r$1 && n.push({
                    d: e$1,
                    a: r$1
                });
                return e$1;
            }
            return {
                e,
                u: using.bind(null, !1),
                a: using.bind(null, !0),
                d: function d() {
                    var o, t = this.e, s = 0;
                    function next() {
                        for(; o = n.pop();)try {
                            if (!o.a && 1 === s) return s = 0, n.push(o), Promise.resolve().then(next);
                            if (o.d) {
                                var r$1 = o.d.call(o.v);
                                if (o.a) return s |= 2, Promise.resolve(r$1).then(next, err);
                            } else s |= 1;
                        } catch (r$2) {
                            return err(r$2);
                        }
                        if (1 === s) return t !== e ? Promise.reject(t) : Promise.resolve();
                        if (t !== e) throw t;
                    }
                    function err(n$1) {
                        return t = t !== e ? new r(n$1, t) : n$1, next();
                    }
                    return next();
                }
            };
        }
        module.exports = _usingCtx, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js
var require_OverloadYield = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/OverloadYield.js" (exports, module) {
        function _OverloadYield(e, d) {
            this.v = e, this.k = d;
        }
        module.exports = _OverloadYield, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js
var require_awaitAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/awaitAsyncGenerator.js" (exports, module) {
        var OverloadYield$2 = require_OverloadYield();
        function _awaitAsyncGenerator$5(e) {
            return new OverloadYield$2(e, 0);
        }
        module.exports = _awaitAsyncGenerator$5, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js
var require_wrapAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/wrapAsyncGenerator.js" (exports, module) {
        var OverloadYield$1 = require_OverloadYield();
        function _wrapAsyncGenerator$6(e) {
            return function() {
                return new AsyncGenerator(e.apply(this, arguments));
            };
        }
        function AsyncGenerator(e) {
            var r, t;
            function resume(r$1, t$1) {
                try {
                    var n = e[r$1](t$1), o = n.value, u = o instanceof OverloadYield$1;
                    Promise.resolve(u ? o.v : o).then(function(t$2) {
                        if (u) {
                            var i = "return" === r$1 ? "return" : "next";
                            if (!o.k || t$2.done) return resume(i, t$2);
                            t$2 = e[i](t$2).value;
                        }
                        settle(n.done ? "return" : "normal", t$2);
                    }, function(e$1) {
                        resume("throw", e$1);
                    });
                } catch (e$1) {
                    settle("throw", e$1);
                }
            }
            function settle(e$1, n) {
                switch(e$1){
                    case "return":
                        r.resolve({
                            value: n,
                            done: !0
                        });
                        break;
                    case "throw":
                        r.reject(n);
                        break;
                    default:
                        r.resolve({
                            value: n,
                            done: !1
                        });
                }
                (r = r.next) ? resume(r.key, r.arg) : t = null;
            }
            this._invoke = function(e$1, n) {
                return new Promise(function(o, u) {
                    var i = {
                        key: e$1,
                        arg: n,
                        resolve: o,
                        reject: u,
                        next: null
                    };
                    t ? t = t.next = i : (r = t = i, resume(e$1, n));
                });
            }, "function" != typeof e["return"] && (this["return"] = void 0);
        }
        AsyncGenerator.prototype["function" == typeof Symbol && Symbol.asyncIterator || "@@asyncIterator"] = function() {
            return this;
        }, AsyncGenerator.prototype.next = function(e) {
            return this._invoke("next", e);
        }, AsyncGenerator.prototype["throw"] = function(e) {
            return this._invoke("throw", e);
        }, AsyncGenerator.prototype["return"] = function(e) {
            return this._invoke("return", e);
        };
        module.exports = _wrapAsyncGenerator$6, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/asyncIterable.ts
var import_usingCtx$4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_usingCtx(), 1);
var import_awaitAsyncGenerator$4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_awaitAsyncGenerator(), 1);
var import_wrapAsyncGenerator$5 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
function iteratorResource(iterable) {
    const iterator = iterable[Symbol.asyncIterator]();
    if (iterator[Symbol.asyncDispose]) return iterator;
    return makeAsyncResource(iterator, async ()=>{
        var _iterator$return;
        await ((_iterator$return = iterator.return) === null || _iterator$return === void 0 ? void 0 : _iterator$return.call(iterator));
    });
}
/**
* Derives a new {@link AsyncGenerator} based on {@link iterable}, that automatically aborts after the specified duration.
*/ function withMaxDuration(_x, _x2) {
    return _withMaxDuration.apply(this, arguments);
}
function _withMaxDuration() {
    _withMaxDuration = (0, import_wrapAsyncGenerator$5.default)(function*(iterable, opts) {
        try {
            var _usingCtx$1 = (0, import_usingCtx$4.default)();
            const iterator = _usingCtx$1.a(iteratorResource(iterable));
            const timer = _usingCtx$1.u(timerResource(opts.maxDurationMs));
            const timerPromise = timer.start();
            let result;
            while(true){
                result = yield (0, import_awaitAsyncGenerator$4.default)(Unpromise.race([
                    iterator.next(),
                    timerPromise
                ]));
                if (result === disposablePromiseTimerResult) throwAbortError();
                if (result.done) return result;
                yield result.value;
                result = null;
            }
        } catch (_) {
            _usingCtx$1.e = _;
        } finally{
            yield (0, import_awaitAsyncGenerator$4.default)(_usingCtx$1.d());
        }
    });
    return _withMaxDuration.apply(this, arguments);
}
/**
* Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields its first
* {@link count} values. Then, a grace period of {@link gracePeriodMs} is started in which further
* values may still come through. After this period, the generator aborts.
*/ function takeWithGrace(_x3, _x4) {
    return _takeWithGrace.apply(this, arguments);
}
function _takeWithGrace() {
    _takeWithGrace = (0, import_wrapAsyncGenerator$5.default)(function*(iterable, opts) {
        try {
            var _usingCtx3 = (0, import_usingCtx$4.default)();
            const iterator = _usingCtx3.a(iteratorResource(iterable));
            let result;
            const timer = _usingCtx3.u(timerResource(opts.gracePeriodMs));
            let count = opts.count;
            let timerPromise = new Promise(()=>{});
            while(true){
                result = yield (0, import_awaitAsyncGenerator$4.default)(Unpromise.race([
                    iterator.next(),
                    timerPromise
                ]));
                if (result === disposablePromiseTimerResult) throwAbortError();
                if (result.done) return result.value;
                yield result.value;
                if (--count === 0) timerPromise = timer.start();
                result = null;
            }
        } catch (_) {
            _usingCtx3.e = _;
        } finally{
            yield (0, import_awaitAsyncGenerator$4.default)(_usingCtx3.d());
        }
    });
    return _takeWithGrace.apply(this, arguments);
}
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/createDeferred.ts
function createDeferred() {
    let resolve;
    let reject;
    const promise = new Promise((res, rej)=>{
        resolve = res;
        reject = rej;
    });
    return {
        promise,
        resolve,
        reject
    };
}
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/mergeAsyncIterables.ts
var import_usingCtx$3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_usingCtx(), 1);
var import_awaitAsyncGenerator$3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_awaitAsyncGenerator(), 1);
var import_wrapAsyncGenerator$4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
function createManagedIterator(iterable, onResult) {
    const iterator = iterable[Symbol.asyncIterator]();
    let state = "idle";
    function cleanup() {
        state = "done";
        onResult = ()=>{};
    }
    function pull() {
        if (state !== "idle") return;
        state = "pending";
        const next = iterator.next();
        next.then((result)=>{
            if (result.done) {
                state = "done";
                onResult({
                    status: "return",
                    value: result.value
                });
                cleanup();
                return;
            }
            state = "idle";
            onResult({
                status: "yield",
                value: result.value
            });
        }).catch((cause)=>{
            onResult({
                status: "error",
                error: cause
            });
            cleanup();
        });
    }
    return {
        pull,
        destroy: async ()=>{
            var _iterator$return;
            cleanup();
            await ((_iterator$return = iterator.return) === null || _iterator$return === void 0 ? void 0 : _iterator$return.call(iterator));
        }
    };
}
/**
* Creates a new async iterable that merges multiple async iterables into a single stream.
* Values from the input iterables are yielded in the order they resolve, similar to Promise.race().
*
* New iterables can be added dynamically using the returned {@link MergedAsyncIterables.add} method, even after iteration has started.
*
* If any of the input iterables throws an error, that error will be propagated through the merged stream.
* Other iterables will not continue to be processed.
*
* @template TYield The type of values yielded by the input iterables
*/ function mergeAsyncIterables() {
    let state = "idle";
    let flushSignal = createDeferred();
    /**
	* used while {@link state} is `idle`
	*/ const iterables = [];
    /**
	* used while {@link state} is `pending`
	*/ const iterators = /* @__PURE__ */ new Set();
    const buffer = [];
    function initIterable(iterable) {
        if (state !== "pending") return;
        const iterator = createManagedIterator(iterable, (result)=>{
            if (state !== "pending") return;
            switch(result.status){
                case "yield":
                    buffer.push([
                        iterator,
                        result
                    ]);
                    break;
                case "return":
                    iterators.delete(iterator);
                    break;
                case "error":
                    buffer.push([
                        iterator,
                        result
                    ]);
                    iterators.delete(iterator);
                    break;
            }
            flushSignal.resolve();
        });
        iterators.add(iterator);
        iterator.pull();
    }
    return {
        add (iterable) {
            switch(state){
                case "idle":
                    iterables.push(iterable);
                    break;
                case "pending":
                    initIterable(iterable);
                    break;
                case "done":
                    break;
            }
        },
        [Symbol.asyncIterator] () {
            return (0, import_wrapAsyncGenerator$4.default)(function*() {
                try {
                    var _usingCtx$1 = (0, import_usingCtx$3.default)();
                    if (state !== "idle") throw new Error("Cannot iterate twice");
                    state = "pending";
                    const _finally = _usingCtx$1.a(makeAsyncResource({}, async ()=>{
                        state = "done";
                        const errors = [];
                        await Promise.all(Array.from(iterators.values()).map(async (it)=>{
                            try {
                                await it.destroy();
                            } catch (cause) {
                                errors.push(cause);
                            }
                        }));
                        buffer.length = 0;
                        iterators.clear();
                        flushSignal.resolve();
                        if (errors.length > 0) throw new AggregateError(errors);
                    }));
                    while(iterables.length > 0)initIterable(iterables.shift());
                    while(iterators.size > 0){
                        yield (0, import_awaitAsyncGenerator$3.default)(flushSignal.promise);
                        while(buffer.length > 0){
                            const [iterator, result] = buffer.shift();
                            switch(result.status){
                                case "yield":
                                    yield result.value;
                                    iterator.pull();
                                    break;
                                case "error":
                                    throw result.error;
                            }
                        }
                        flushSignal = createDeferred();
                    }
                } catch (_) {
                    _usingCtx$1.e = _;
                } finally{
                    yield (0, import_awaitAsyncGenerator$3.default)(_usingCtx$1.d());
                }
            })();
        }
    };
}
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/readableStreamFrom.ts
/**
* Creates a ReadableStream from an AsyncIterable.
*
* @param iterable - The source AsyncIterable to stream from
* @returns A ReadableStream that yields values from the AsyncIterable
*/ function readableStreamFrom(iterable) {
    const iterator = iterable[Symbol.asyncIterator]();
    return new ReadableStream({
        async cancel () {
            var _iterator$return;
            await ((_iterator$return = iterator.return) === null || _iterator$return === void 0 ? void 0 : _iterator$return.call(iterator));
        },
        async pull (controller) {
            const result = await iterator.next();
            if (result.done) {
                controller.close();
                return;
            }
            controller.enqueue(result.value);
        }
    });
}
//#endregion
//#region src/unstable-core-do-not-import/stream/utils/withPing.ts
var import_usingCtx$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_usingCtx(), 1);
var import_awaitAsyncGenerator$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_awaitAsyncGenerator(), 1);
var import_wrapAsyncGenerator$3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
const PING_SYM = Symbol("ping");
/**
* Derives a new {@link AsyncGenerator} based of {@link iterable}, that yields {@link PING_SYM}
* whenever no value has been yielded for {@link pingIntervalMs}.
*/ function withPing(_x, _x2) {
    return _withPing.apply(this, arguments);
}
function _withPing() {
    _withPing = (0, import_wrapAsyncGenerator$3.default)(function*(iterable, pingIntervalMs) {
        try {
            var _usingCtx$1 = (0, import_usingCtx$2.default)();
            const iterator = _usingCtx$1.a(iteratorResource(iterable));
            let result;
            let nextPromise = iterator.next();
            while(true)try {
                var _usingCtx3 = (0, import_usingCtx$2.default)();
                const pingPromise = _usingCtx3.u(timerResource(pingIntervalMs));
                result = yield (0, import_awaitAsyncGenerator$2.default)(Unpromise.race([
                    nextPromise,
                    pingPromise.start()
                ]));
                if (result === disposablePromiseTimerResult) {
                    yield PING_SYM;
                    continue;
                }
                if (result.done) return result.value;
                nextPromise = iterator.next();
                yield result.value;
                result = null;
            } catch (_) {
                _usingCtx3.e = _;
            } finally{
                _usingCtx3.d();
            }
        } catch (_) {
            _usingCtx$1.e = _;
        } finally{
            yield (0, import_awaitAsyncGenerator$2.default)(_usingCtx$1.d());
        }
    });
    return _withPing.apply(this, arguments);
}
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js
var require_asyncIterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncIterator.js" (exports, module) {
        function _asyncIterator$2(r) {
            var n, t, o, e = 2;
            for("undefined" != typeof Symbol && (t = Symbol.asyncIterator, o = Symbol.iterator); e--;){
                if (t && null != (n = r[t])) return n.call(r);
                if (o && null != (n = r[o])) return new AsyncFromSyncIterator(n.call(r));
                t = "@@asyncIterator", o = "@@iterator";
            }
            throw new TypeError("Object is not async iterable");
        }
        function AsyncFromSyncIterator(r) {
            function AsyncFromSyncIteratorContinuation(r$1) {
                if (Object(r$1) !== r$1) return Promise.reject(new TypeError(r$1 + " is not an object."));
                var n = r$1.done;
                return Promise.resolve(r$1.value).then(function(r$2) {
                    return {
                        value: r$2,
                        done: n
                    };
                });
            }
            return AsyncFromSyncIterator = function AsyncFromSyncIterator$1(r$1) {
                this.s = r$1, this.n = r$1.next;
            }, AsyncFromSyncIterator.prototype = {
                s: null,
                n: null,
                next: function next() {
                    return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));
                },
                "return": function _return(r$1) {
                    var n = this.s["return"];
                    return void 0 === n ? Promise.resolve({
                        value: r$1,
                        done: !0
                    }) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));
                },
                "throw": function _throw(r$1) {
                    var n = this.s["return"];
                    return void 0 === n ? Promise.reject(r$1) : AsyncFromSyncIteratorContinuation(n.apply(this.s, arguments));
                }
            }, new AsyncFromSyncIterator(r);
        }
        module.exports = _asyncIterator$2, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region src/unstable-core-do-not-import/stream/jsonl.ts
var import_awaitAsyncGenerator$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_awaitAsyncGenerator(), 1);
var import_wrapAsyncGenerator$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
var import_usingCtx$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_usingCtx(), 1);
var import_asyncIterator$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_asyncIterator(), 1);
function isPlainObject(value) {
    return Object.prototype.toString.call(value) === "[object Object]";
}
const CHUNK_VALUE_TYPE_PROMISE = 0;
const CHUNK_VALUE_TYPE_ASYNC_ITERABLE = 1;
const PROMISE_STATUS_FULFILLED = 0;
const PROMISE_STATUS_REJECTED = 1;
const ASYNC_ITERABLE_STATUS_RETURN = 0;
const ASYNC_ITERABLE_STATUS_YIELD = 1;
const ASYNC_ITERABLE_STATUS_ERROR = 2;
function isPromise(value) {
    return ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(value)) && typeof (value === null || value === void 0 ? void 0 : value["then"]) === "function" && typeof (value === null || value === void 0 ? void 0 : value["catch"]) === "function";
}
var MaxDepthError = class extends Error {
    constructor(path){
        super("Max depth reached at path: " + path.join("."));
        this.path = path;
    }
};
function createBatchStreamProducer(_x3) {
    return _createBatchStreamProducer.apply(this, arguments);
}
function _createBatchStreamProducer() {
    _createBatchStreamProducer = (0, import_wrapAsyncGenerator$2.default)(function*(opts) {
        const { data } = opts;
        let counter = 0;
        const placeholder = 0;
        const mergedIterables = mergeAsyncIterables();
        function registerAsync(callback) {
            const idx = counter++;
            const iterable$1 = callback(idx);
            mergedIterables.add(iterable$1);
            return idx;
        }
        function encodePromise(promise, path) {
            return registerAsync(/* @__PURE__ */ function() {
                var _ref = (0, import_wrapAsyncGenerator$2.default)(function*(idx) {
                    const error = checkMaxDepth(path);
                    if (error) {
                        promise.catch((cause)=>{
                            var _opts$onError;
                            (_opts$onError = opts.onError) === null || _opts$onError === void 0 || _opts$onError.call(opts, {
                                error: cause,
                                path
                            });
                        });
                        promise = Promise.reject(error);
                    }
                    try {
                        const next = yield (0, import_awaitAsyncGenerator$1.default)(promise);
                        yield [
                            idx,
                            PROMISE_STATUS_FULFILLED,
                            encode(next, path)
                        ];
                    } catch (cause) {
                        var _opts$onError2, _opts$formatError;
                        (_opts$onError2 = opts.onError) === null || _opts$onError2 === void 0 || _opts$onError2.call(opts, {
                            error: cause,
                            path
                        });
                        yield [
                            idx,
                            PROMISE_STATUS_REJECTED,
                            (_opts$formatError = opts.formatError) === null || _opts$formatError === void 0 ? void 0 : _opts$formatError.call(opts, {
                                error: cause,
                                path
                            })
                        ];
                    }
                });
                return function(_x) {
                    return _ref.apply(this, arguments);
                };
            }());
        }
        function encodeAsyncIterable(iterable$1, path) {
            return registerAsync(/* @__PURE__ */ function() {
                var _ref2 = (0, import_wrapAsyncGenerator$2.default)(function*(idx) {
                    try {
                        var _usingCtx$1 = (0, import_usingCtx$1.default)();
                        const error = checkMaxDepth(path);
                        if (error) throw error;
                        const iterator = _usingCtx$1.a(iteratorResource(iterable$1));
                        try {
                            while(true){
                                const next = yield (0, import_awaitAsyncGenerator$1.default)(iterator.next());
                                if (next.done) {
                                    yield [
                                        idx,
                                        ASYNC_ITERABLE_STATUS_RETURN,
                                        encode(next.value, path)
                                    ];
                                    break;
                                }
                                yield [
                                    idx,
                                    ASYNC_ITERABLE_STATUS_YIELD,
                                    encode(next.value, path)
                                ];
                            }
                        } catch (cause) {
                            var _opts$onError3, _opts$formatError2;
                            (_opts$onError3 = opts.onError) === null || _opts$onError3 === void 0 || _opts$onError3.call(opts, {
                                error: cause,
                                path
                            });
                            yield [
                                idx,
                                ASYNC_ITERABLE_STATUS_ERROR,
                                (_opts$formatError2 = opts.formatError) === null || _opts$formatError2 === void 0 ? void 0 : _opts$formatError2.call(opts, {
                                    error: cause,
                                    path
                                })
                            ];
                        }
                    } catch (_) {
                        _usingCtx$1.e = _;
                    } finally{
                        yield (0, import_awaitAsyncGenerator$1.default)(_usingCtx$1.d());
                    }
                });
                return function(_x2) {
                    return _ref2.apply(this, arguments);
                };
            }());
        }
        function checkMaxDepth(path) {
            if (opts.maxDepth && path.length > opts.maxDepth) return new MaxDepthError(path);
            return null;
        }
        function encodeAsync(value, path) {
            if (isPromise(value)) return [
                CHUNK_VALUE_TYPE_PROMISE,
                encodePromise(value, path)
            ];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAsyncIterable"])(value)) {
                if (opts.maxDepth && path.length >= opts.maxDepth) throw new Error("Max depth reached");
                return [
                    CHUNK_VALUE_TYPE_ASYNC_ITERABLE,
                    encodeAsyncIterable(value, path)
                ];
            }
            return null;
        }
        function encode(value, path) {
            if (value === void 0) return [
                []
            ];
            const reg = encodeAsync(value, path);
            if (reg) return [
                [
                    placeholder
                ],
                [
                    null,
                    ...reg
                ]
            ];
            if (!isPlainObject(value)) return [
                [
                    value
                ]
            ];
            const newObj = {};
            const asyncValues = [];
            for (const [key, item] of Object.entries(value)){
                const transformed = encodeAsync(item, [
                    ...path,
                    key
                ]);
                if (!transformed) {
                    newObj[key] = item;
                    continue;
                }
                newObj[key] = placeholder;
                asyncValues.push([
                    key,
                    ...transformed
                ]);
            }
            return [
                [
                    newObj
                ],
                ...asyncValues
            ];
        }
        const newHead = {};
        for (const [key, item] of Object.entries(data))newHead[key] = encode(item, [
            key
        ]);
        yield newHead;
        let iterable = mergedIterables;
        if (opts.pingMs) iterable = withPing(mergedIterables, opts.pingMs);
        var _iteratorAbruptCompletion = false;
        var _didIteratorError = false;
        var _iteratorError;
        try {
            for(var _iterator = (0, import_asyncIterator$1.default)(iterable), _step; _iteratorAbruptCompletion = !(_step = yield (0, import_awaitAsyncGenerator$1.default)(_iterator.next())).done; _iteratorAbruptCompletion = false){
                const value = _step.value;
                yield value;
            }
        } catch (err) {
            _didIteratorError = true;
            _iteratorError = err;
        } finally{
            try {
                if (_iteratorAbruptCompletion && _iterator.return != null) yield (0, import_awaitAsyncGenerator$1.default)(_iterator.return());
            } finally{
                if (_didIteratorError) throw _iteratorError;
            }
        }
    });
    return _createBatchStreamProducer.apply(this, arguments);
}
/**
* JSON Lines stream producer
* @see https://jsonlines.org/
*/ function jsonlStreamProducer(opts) {
    let stream = readableStreamFrom(createBatchStreamProducer(opts));
    const { serialize } = opts;
    if (serialize) stream = stream.pipeThrough(new TransformStream({
        transform (chunk, controller) {
            if (chunk === PING_SYM) controller.enqueue(PING_SYM);
            else controller.enqueue(serialize(chunk));
        }
    }));
    return stream.pipeThrough(new TransformStream({
        transform (chunk, controller) {
            if (chunk === PING_SYM) controller.enqueue(" ");
            else controller.enqueue(JSON.stringify(chunk) + "\n");
        }
    })).pipeThrough(new TextEncoderStream());
}
var AsyncError = class extends Error {
    constructor(data){
        super("Received error from server");
        this.data = data;
    }
};
const nodeJsStreamToReaderEsque = (source)=>{
    return {
        getReader () {
            const stream = new ReadableStream({
                start (controller) {
                    source.on("data", (chunk)=>{
                        controller.enqueue(chunk);
                    });
                    source.on("end", ()=>{
                        controller.close();
                    });
                    source.on("error", (error)=>{
                        controller.error(error);
                    });
                }
            });
            return stream.getReader();
        }
    };
};
function createLineAccumulator(from) {
    const reader = "getReader" in from ? from.getReader() : nodeJsStreamToReaderEsque(from).getReader();
    let lineAggregate = "";
    return new ReadableStream({
        async pull (controller) {
            const { done, value } = await reader.read();
            if (done) controller.close();
            else controller.enqueue(value);
        },
        cancel () {
            return reader.cancel();
        }
    }).pipeThrough(new TextDecoderStream()).pipeThrough(new TransformStream({
        transform (chunk, controller) {
            var _parts$pop;
            lineAggregate += chunk;
            const parts = lineAggregate.split("\n");
            lineAggregate = (_parts$pop = parts.pop()) !== null && _parts$pop !== void 0 ? _parts$pop : "";
            for (const part of parts)controller.enqueue(part);
        }
    }));
}
function createConsumerStream(from) {
    const stream = createLineAccumulator(from);
    let sentHead = false;
    return stream.pipeThrough(new TransformStream({
        transform (line, controller) {
            if (!sentHead) {
                const head = JSON.parse(line);
                controller.enqueue(head);
                sentHead = true;
            } else {
                const chunk = JSON.parse(line);
                controller.enqueue(chunk);
            }
        }
    }));
}
/**
* Creates a handler for managing stream controllers and their lifecycle
*/ function createStreamsManager(abortController) {
    const controllerMap = /* @__PURE__ */ new Map();
    /**
	* Checks if there are no pending controllers or deferred promises
	*/ function isEmpty() {
        return Array.from(controllerMap.values()).every((c)=>c.closed);
    }
    /**
	* Creates a stream controller
	*/ function createStreamController() {
        let originalController;
        const stream = new ReadableStream({
            start (controller) {
                originalController = controller;
            }
        });
        const streamController = {
            enqueue: (v)=>originalController.enqueue(v),
            close: ()=>{
                originalController.close();
                clear();
                if (isEmpty()) abortController.abort();
            },
            closed: false,
            getReaderResource: ()=>{
                const reader = stream.getReader();
                return makeResource(reader, ()=>{
                    reader.releaseLock();
                    streamController.close();
                });
            },
            error: (reason)=>{
                originalController.error(reason);
                clear();
            }
        };
        function clear() {
            Object.assign(streamController, {
                closed: true,
                close: ()=>{},
                enqueue: ()=>{},
                getReaderResource: null,
                error: ()=>{}
            });
        }
        return streamController;
    }
    /**
	* Gets or creates a stream controller
	*/ function getOrCreate(chunkId) {
        let c = controllerMap.get(chunkId);
        if (!c) {
            c = createStreamController();
            controllerMap.set(chunkId, c);
        }
        return c;
    }
    /**
	* Cancels all pending controllers and rejects deferred promises
	*/ function cancelAll(reason) {
        for (const controller of controllerMap.values())controller.error(reason);
    }
    return {
        getOrCreate,
        isEmpty,
        cancelAll
    };
}
/**
* JSON Lines stream consumer
* @see https://jsonlines.org/
*/ async function jsonlStreamConsumer(opts) {
    const { deserialize = (v)=>v } = opts;
    let source = createConsumerStream(opts.from);
    if (deserialize) source = source.pipeThrough(new TransformStream({
        transform (chunk, controller) {
            controller.enqueue(deserialize(chunk));
        }
    }));
    let headDeferred = createDeferred();
    const streamManager = createStreamsManager(opts.abortController);
    function decodeChunkDefinition(value) {
        const [_path, type, chunkId] = value;
        const controller = streamManager.getOrCreate(chunkId);
        switch(type){
            case CHUNK_VALUE_TYPE_PROMISE:
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(async ()=>{
                    try {
                        var _usingCtx3 = (0, import_usingCtx$1.default)();
                        const reader = _usingCtx3.u(controller.getReaderResource());
                        const { value: value$1 } = await reader.read();
                        const [_chunkId, status, data] = value$1;
                        switch(status){
                            case PROMISE_STATUS_FULFILLED:
                                return decode(data);
                            case PROMISE_STATUS_REJECTED:
                                var _opts$formatError3, _opts$formatError4;
                                throw (_opts$formatError3 = (_opts$formatError4 = opts.formatError) === null || _opts$formatError4 === void 0 ? void 0 : _opts$formatError4.call(opts, {
                                    error: data
                                })) !== null && _opts$formatError3 !== void 0 ? _opts$formatError3 : new AsyncError(data);
                        }
                    } catch (_) {
                        _usingCtx3.e = _;
                    } finally{
                        _usingCtx3.d();
                    }
                });
            case CHUNK_VALUE_TYPE_ASYNC_ITERABLE:
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])((0, import_wrapAsyncGenerator$2.default)(function*() {
                    try {
                        var _usingCtx4 = (0, import_usingCtx$1.default)();
                        const reader = _usingCtx4.u(controller.getReaderResource());
                        while(true){
                            const { value: value$1 } = yield (0, import_awaitAsyncGenerator$1.default)(reader.read());
                            const [_chunkId, status, data] = value$1;
                            switch(status){
                                case ASYNC_ITERABLE_STATUS_YIELD:
                                    yield decode(data);
                                    break;
                                case ASYNC_ITERABLE_STATUS_RETURN:
                                    return decode(data);
                                case ASYNC_ITERABLE_STATUS_ERROR:
                                    var _opts$formatError5, _opts$formatError6;
                                    throw (_opts$formatError5 = (_opts$formatError6 = opts.formatError) === null || _opts$formatError6 === void 0 ? void 0 : _opts$formatError6.call(opts, {
                                        error: data
                                    })) !== null && _opts$formatError5 !== void 0 ? _opts$formatError5 : new AsyncError(data);
                            }
                        }
                    } catch (_) {
                        _usingCtx4.e = _;
                    } finally{
                        _usingCtx4.d();
                    }
                }));
        }
    }
    function decode(value) {
        const [[data], ...asyncProps] = value;
        for (const value$1 of asyncProps){
            const [key] = value$1;
            const decoded = decodeChunkDefinition(value$1);
            if (key === null) return decoded;
            data[key] = decoded;
        }
        return data;
    }
    const closeOrAbort = (reason)=>{
        headDeferred === null || headDeferred === void 0 || headDeferred.reject(reason);
        streamManager.cancelAll(reason);
    };
    source.pipeTo(new WritableStream({
        write (chunkOrHead) {
            if (headDeferred) {
                const head = chunkOrHead;
                for (const [key, value] of Object.entries(chunkOrHead)){
                    const parsed = decode(value);
                    head[key] = parsed;
                }
                headDeferred.resolve(head);
                headDeferred = null;
                return;
            }
            const chunk = chunkOrHead;
            const [idx] = chunk;
            const controller = streamManager.getOrCreate(idx);
            controller.enqueue(chunk);
        },
        close: ()=>closeOrAbort(new Error("Stream closed")),
        abort: closeOrAbort
    }), {
        signal: opts.abortController.signal
    }).catch((error)=>{
        var _opts$onError4;
        (_opts$onError4 = opts.onError) === null || _opts$onError4 === void 0 || _opts$onError4.call(opts, {
            error
        });
        closeOrAbort(error);
    });
    return [
        await headDeferred.promise,
        streamManager
    ];
}
//#endregion
//#region ../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncGeneratorDelegate.js
var require_asyncGeneratorDelegate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__commonJS"])({
    "../../node_modules/.pnpm/@oxc-project+runtime@0.72.2/node_modules/@oxc-project/runtime/src/helpers/asyncGeneratorDelegate.js" (exports, module) {
        var OverloadYield = require_OverloadYield();
        function _asyncGeneratorDelegate$1(t) {
            var e = {}, n = !1;
            function pump(e$1, r) {
                return n = !0, r = new Promise(function(n$1) {
                    n$1(t[e$1](r));
                }), {
                    done: !1,
                    value: new OverloadYield(r, 1)
                };
            }
            return e["undefined" != typeof Symbol && Symbol.iterator || "@@iterator"] = function() {
                return this;
            }, e.next = function(t$1) {
                return n ? (n = !1, t$1) : pump("next", t$1);
            }, "function" == typeof t["throw"] && (e["throw"] = function(t$1) {
                if (n) throw n = !1, t$1;
                return pump("throw", t$1);
            }), "function" == typeof t["return"] && (e["return"] = function(t$1) {
                return n ? (n = !1, t$1) : pump("return", t$1);
            }), e;
        }
        module.exports = _asyncGeneratorDelegate$1, module.exports.__esModule = true, module.exports["default"] = module.exports;
    }
});
//#endregion
//#region src/unstable-core-do-not-import/stream/sse.ts
var import_asyncIterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_asyncIterator(), 1);
var import_awaitAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_awaitAsyncGenerator(), 1);
var import_wrapAsyncGenerator$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
var import_asyncGeneratorDelegate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_asyncGeneratorDelegate(), 1);
var import_usingCtx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_usingCtx(), 1);
const PING_EVENT = "ping";
const SERIALIZED_ERROR_EVENT = "serialized-error";
const CONNECTED_EVENT = "connected";
const RETURN_EVENT = "return";
/**
*
* @see https://html.spec.whatwg.org/multipage/server-sent-events.html
*/ function sseStreamProducer(opts) {
    var _opts$ping$enabled, _opts$ping, _opts$ping$intervalMs, _opts$ping2, _opts$client;
    const { serialize = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["identity"] } = opts;
    const ping = {
        enabled: (_opts$ping$enabled = (_opts$ping = opts.ping) === null || _opts$ping === void 0 ? void 0 : _opts$ping.enabled) !== null && _opts$ping$enabled !== void 0 ? _opts$ping$enabled : false,
        intervalMs: (_opts$ping$intervalMs = (_opts$ping2 = opts.ping) === null || _opts$ping2 === void 0 ? void 0 : _opts$ping2.intervalMs) !== null && _opts$ping$intervalMs !== void 0 ? _opts$ping$intervalMs : 1e3
    };
    const client = (_opts$client = opts.client) !== null && _opts$client !== void 0 ? _opts$client : {};
    if (ping.enabled && client.reconnectAfterInactivityMs && ping.intervalMs > client.reconnectAfterInactivityMs) throw new Error(`Ping interval must be less than client reconnect interval to prevent unnecessary reconnection - ping.intervalMs: ${ping.intervalMs} client.reconnectAfterInactivityMs: ${client.reconnectAfterInactivityMs}`);
    function generator() {
        return _generator.apply(this, arguments);
    }
    function _generator() {
        _generator = (0, import_wrapAsyncGenerator$1.default)(function*() {
            yield {
                event: CONNECTED_EVENT,
                data: JSON.stringify(client)
            };
            let iterable = opts.data;
            if (opts.emitAndEndImmediately) iterable = takeWithGrace(iterable, {
                count: 1,
                gracePeriodMs: 1
            });
            if (opts.maxDurationMs && opts.maxDurationMs > 0 && opts.maxDurationMs !== Infinity) iterable = withMaxDuration(iterable, {
                maxDurationMs: opts.maxDurationMs
            });
            if (ping.enabled && ping.intervalMs !== Infinity && ping.intervalMs > 0) iterable = withPing(iterable, ping.intervalMs);
            let value;
            let chunk;
            var _iteratorAbruptCompletion = false;
            var _didIteratorError = false;
            var _iteratorError;
            try {
                for(var _iterator = (0, import_asyncIterator.default)(iterable), _step; _iteratorAbruptCompletion = !(_step = yield (0, import_awaitAsyncGenerator.default)(_iterator.next())).done; _iteratorAbruptCompletion = false){
                    value = _step.value;
                    {
                        if (value === PING_SYM) {
                            yield {
                                event: PING_EVENT,
                                data: ""
                            };
                            continue;
                        }
                        chunk = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isTrackedEnvelope"])(value) ? {
                            id: value[0],
                            data: value[1]
                        } : {
                            data: value
                        };
                        chunk.data = JSON.stringify(serialize(chunk.data));
                        yield chunk;
                        value = null;
                        chunk = null;
                    }
                }
            } catch (err) {
                _didIteratorError = true;
                _iteratorError = err;
            } finally{
                try {
                    if (_iteratorAbruptCompletion && _iterator.return != null) yield (0, import_awaitAsyncGenerator.default)(_iterator.return());
                } finally{
                    if (_didIteratorError) throw _iteratorError;
                }
            }
        });
        return _generator.apply(this, arguments);
    }
    function generatorWithErrorHandling() {
        return _generatorWithErrorHandling.apply(this, arguments);
    }
    function _generatorWithErrorHandling() {
        _generatorWithErrorHandling = (0, import_wrapAsyncGenerator$1.default)(function*() {
            try {
                yield* (0, import_asyncGeneratorDelegate.default)((0, import_asyncIterator.default)(generator()));
                yield {
                    event: RETURN_EVENT,
                    data: ""
                };
            } catch (cause) {
                var _opts$formatError, _opts$formatError2;
                if (isAbortError(cause)) return;
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause);
                const data = (_opts$formatError = (_opts$formatError2 = opts.formatError) === null || _opts$formatError2 === void 0 ? void 0 : _opts$formatError2.call(opts, {
                    error
                })) !== null && _opts$formatError !== void 0 ? _opts$formatError : null;
                yield {
                    event: SERIALIZED_ERROR_EVENT,
                    data: JSON.stringify(serialize(data))
                };
            }
        });
        return _generatorWithErrorHandling.apply(this, arguments);
    }
    const stream = readableStreamFrom(generatorWithErrorHandling());
    return stream.pipeThrough(new TransformStream({
        transform (chunk, controller) {
            if ("event" in chunk) controller.enqueue(`event: ${chunk.event}\n`);
            if ("data" in chunk) controller.enqueue(`data: ${chunk.data}\n`);
            if ("id" in chunk) controller.enqueue(`id: ${chunk.id}\n`);
            if ("comment" in chunk) controller.enqueue(`: ${chunk.comment}\n`);
            controller.enqueue("\n\n");
        }
    })).pipeThrough(new TextEncoderStream());
}
async function withTimeout(opts) {
    try {
        var _usingCtx$1 = (0, import_usingCtx.default)();
        const timeoutPromise = _usingCtx$1.u(timerResource(opts.timeoutMs));
        const res = await Unpromise.race([
            opts.promise,
            timeoutPromise.start()
        ]);
        if (res === disposablePromiseTimerResult) return await opts.onTimeout();
        return res;
    } catch (_) {
        _usingCtx$1.e = _;
    } finally{
        _usingCtx$1.d();
    }
}
/**
* @see https://html.spec.whatwg.org/multipage/server-sent-events.html
*/ function sseStreamConsumer(opts) {
    const { deserialize = (v)=>v } = opts;
    let clientOptions = {};
    const signal = opts.signal;
    let _es = null;
    const createStream = ()=>new ReadableStream({
            async start (controller) {
                const [url, init] = await Promise.all([
                    opts.url(),
                    opts.init()
                ]);
                const eventSource = _es = new opts.EventSource(url, init);
                controller.enqueue({
                    type: "connecting",
                    eventSource: _es,
                    event: null
                });
                eventSource.addEventListener(CONNECTED_EVENT, (_msg)=>{
                    const msg = _msg;
                    const options = JSON.parse(msg.data);
                    clientOptions = options;
                    controller.enqueue({
                        type: "connected",
                        options,
                        eventSource
                    });
                });
                eventSource.addEventListener(SERIALIZED_ERROR_EVENT, (_msg)=>{
                    const msg = _msg;
                    controller.enqueue({
                        type: "serialized-error",
                        error: deserialize(JSON.parse(msg.data)),
                        eventSource
                    });
                });
                eventSource.addEventListener(PING_EVENT, ()=>{
                    controller.enqueue({
                        type: "ping",
                        eventSource
                    });
                });
                eventSource.addEventListener(RETURN_EVENT, ()=>{
                    eventSource.close();
                    controller.close();
                    _es = null;
                });
                eventSource.addEventListener("error", (event)=>{
                    if (eventSource.readyState === eventSource.CLOSED) controller.error(event);
                    else controller.enqueue({
                        type: "connecting",
                        eventSource,
                        event
                    });
                });
                eventSource.addEventListener("message", (_msg)=>{
                    const msg = _msg;
                    const chunk = deserialize(JSON.parse(msg.data));
                    const def = {
                        data: chunk
                    };
                    if (msg.lastEventId) def.id = msg.lastEventId;
                    controller.enqueue({
                        type: "data",
                        data: def,
                        eventSource
                    });
                });
                const onAbort = ()=>{
                    try {
                        eventSource.close();
                        controller.close();
                    } catch (_unused) {}
                };
                if (signal.aborted) onAbort();
                else signal.addEventListener("abort", onAbort);
            },
            cancel () {
                _es === null || _es === void 0 || _es.close();
            }
        });
    const getStreamResource = ()=>{
        let stream = createStream();
        let reader = stream.getReader();
        async function dispose() {
            await reader.cancel();
            _es = null;
        }
        return makeAsyncResource({
            read () {
                return reader.read();
            },
            async recreate () {
                await dispose();
                stream = createStream();
                reader = stream.getReader();
            }
        }, dispose);
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])((0, import_wrapAsyncGenerator$1.default)(function*() {
        try {
            var _usingCtx3 = (0, import_usingCtx.default)();
            const stream = _usingCtx3.a(getStreamResource());
            while(true){
                let promise = stream.read();
                const timeoutMs = clientOptions.reconnectAfterInactivityMs;
                if (timeoutMs) promise = withTimeout({
                    promise,
                    timeoutMs,
                    onTimeout: async ()=>{
                        const res = {
                            value: {
                                type: "timeout",
                                ms: timeoutMs,
                                eventSource: _es
                            },
                            done: false
                        };
                        await stream.recreate();
                        return res;
                    }
                });
                const result = yield (0, import_awaitAsyncGenerator.default)(promise);
                if (result.done) return result.value;
                yield result.value;
            }
        } catch (_) {
            _usingCtx3.e = _;
        } finally{
            yield (0, import_awaitAsyncGenerator.default)(_usingCtx3.d());
        }
    }));
}
const sseHeaders = {
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache, no-transform",
    "X-Accel-Buffering": "no",
    Connection: "keep-alive"
};
//#endregion
//#region src/unstable-core-do-not-import/http/resolveResponse.ts
var import_wrapAsyncGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])(require_wrapAsyncGenerator(), 1);
var import_objectSpread2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__toESM"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["require_objectSpread2"])(), 1);
function errorToAsyncIterable(err) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])((0, import_wrapAsyncGenerator.default)(function*() {
        throw err;
    }));
}
const TYPE_ACCEPTED_METHOD_MAP = {
    mutation: [
        "POST"
    ],
    query: [
        "GET"
    ],
    subscription: [
        "GET"
    ]
};
const TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE = {
    mutation: [
        "POST"
    ],
    query: [
        "GET",
        "POST"
    ],
    subscription: [
        "GET",
        "POST"
    ]
};
function initResponse(initOpts) {
    var _responseMeta, _info$calls$find$proc, _info$calls$find;
    const { ctx, info, responseMeta, untransformedJSON, errors = [], headers } = initOpts;
    let status = untransformedJSON ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getHTTPStatusCode"])(untransformedJSON) : 200;
    const eagerGeneration = !untransformedJSON;
    const data = eagerGeneration ? [] : Array.isArray(untransformedJSON) ? untransformedJSON : [
        untransformedJSON
    ];
    const meta = (_responseMeta = responseMeta === null || responseMeta === void 0 ? void 0 : responseMeta({
        ctx,
        info,
        paths: info === null || info === void 0 ? void 0 : info.calls.map((call)=>call.path),
        data,
        errors,
        eagerGeneration,
        type: (_info$calls$find$proc = info === null || info === void 0 || (_info$calls$find = info.calls.find((call)=>{
            var _call$procedure;
            return (_call$procedure = call.procedure) === null || _call$procedure === void 0 ? void 0 : _call$procedure._def.type;
        })) === null || _info$calls$find === void 0 || (_info$calls$find = _info$calls$find.procedure) === null || _info$calls$find === void 0 ? void 0 : _info$calls$find._def.type) !== null && _info$calls$find$proc !== void 0 ? _info$calls$find$proc : "unknown"
    })) !== null && _responseMeta !== void 0 ? _responseMeta : {};
    if (meta.headers) {
        if (meta.headers instanceof Headers) for (const [key, value] of meta.headers.entries())headers.append(key, value);
        else /**
		* @deprecated, delete in v12
		*/ for (const [key, value] of Object.entries(meta.headers))if (Array.isArray(value)) for (const v of value)headers.append(key, v);
        else if (typeof value === "string") headers.set(key, value);
    }
    if (meta.status) status = meta.status;
    return {
        status
    };
}
function caughtErrorToData(cause, errorOpts) {
    const { router, req, onError } = errorOpts.opts;
    const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause);
    onError === null || onError === void 0 || onError({
        error,
        path: errorOpts.path,
        input: errorOpts.input,
        ctx: errorOpts.ctx,
        type: errorOpts.type,
        req
    });
    const untransformedJSON = {
        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"])({
            config: router._def._config,
            error,
            type: errorOpts.type,
            path: errorOpts.path,
            input: errorOpts.input,
            ctx: errorOpts.ctx
        })
    };
    const transformedJSON = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformTRPCResponse"])(router._def._config, untransformedJSON);
    const body = JSON.stringify(transformedJSON);
    return {
        error,
        untransformedJSON,
        body
    };
}
/**
* Check if a value is a stream-like object
* - if it's an async iterable
* - if it's an object with async iterables or promises
*/ function isDataStream(v) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(v)) return false;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAsyncIterable"])(v)) return true;
    return Object.values(v).some(isPromise) || Object.values(v).some(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAsyncIterable"]);
}
async function resolveResponse(opts) {
    var _ref, _opts$allowBatching, _opts$batching, _opts$allowMethodOver, _config$sse$enabled, _config$sse;
    const { router, req } = opts;
    const headers = new Headers([
        [
            "vary",
            "trpc-accept"
        ]
    ]);
    const config = router._def._config;
    const url = new URL(req.url);
    if (req.method === "HEAD") return new Response(null, {
        status: 204
    });
    const allowBatching = (_ref = (_opts$allowBatching = opts.allowBatching) !== null && _opts$allowBatching !== void 0 ? _opts$allowBatching : (_opts$batching = opts.batching) === null || _opts$batching === void 0 ? void 0 : _opts$batching.enabled) !== null && _ref !== void 0 ? _ref : true;
    const allowMethodOverride = ((_opts$allowMethodOver = opts.allowMethodOverride) !== null && _opts$allowMethodOver !== void 0 ? _opts$allowMethodOver : false) && req.method === "POST";
    const infoTuple = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(async ()=>{
        try {
            return [
                void 0,
                await getRequestInfo({
                    req,
                    path: decodeURIComponent(opts.path),
                    router,
                    searchParams: url.searchParams,
                    headers: opts.req.headers,
                    url
                })
            ];
        } catch (cause) {
            return [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause),
                void 0
            ];
        }
    });
    const ctxManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(()=>{
        let result = void 0;
        return {
            valueOrUndefined: ()=>{
                if (!result) return void 0;
                return result[1];
            },
            value: ()=>{
                const [err, ctx] = result;
                if (err) throw err;
                return ctx;
            },
            create: async (info)=>{
                if (result) throw new Error("This should only be called once - report a bug in tRPC");
                try {
                    const ctx = await opts.createContext({
                        info
                    });
                    result = [
                        void 0,
                        ctx
                    ];
                } catch (cause) {
                    result = [
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause),
                        void 0
                    ];
                }
            }
        };
    });
    const methodMapper = allowMethodOverride ? TYPE_ACCEPTED_METHOD_MAP_WITH_METHOD_OVERRIDE : TYPE_ACCEPTED_METHOD_MAP;
    /**
	* @deprecated
	*/ const isStreamCall = req.headers.get("trpc-accept") === "application/jsonl";
    const experimentalSSE = (_config$sse$enabled = (_config$sse = config.sse) === null || _config$sse === void 0 ? void 0 : _config$sse.enabled) !== null && _config$sse$enabled !== void 0 ? _config$sse$enabled : true;
    try {
        const [infoError, info] = infoTuple;
        if (infoError) throw infoError;
        if (info.isBatchCall && !allowBatching) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "BAD_REQUEST",
            message: `Batching is not enabled on the server`
        });
        /* istanbul ignore if -- @preserve */ if (isStreamCall && !info.isBatchCall) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
            message: `Streaming requests must be batched (you can do a batch of 1)`,
            code: "BAD_REQUEST"
        });
        await ctxManager.create(info);
        const rpcCalls = info.calls.map(async (call)=>{
            const proc = call.procedure;
            try {
                if (opts.error) throw opts.error;
                if (!proc) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: `No procedure found on path "${call.path}"`
                });
                if (!methodMapper[proc._def.type].includes(req.method)) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "METHOD_NOT_SUPPORTED",
                    message: `Unsupported ${req.method}-request to ${proc._def.type} procedure at path "${call.path}"`
                });
                if (proc._def.type === "subscription") {
                    /* istanbul ignore if -- @preserve */ if (info.isBatchCall) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                        code: "BAD_REQUEST",
                        message: `Cannot batch subscription calls`
                    });
                }
                const data = await proc({
                    path: call.path,
                    getRawInput: call.getRawInput,
                    ctx: ctxManager.value(),
                    type: proc._def.type,
                    signal: opts.req.signal
                });
                return [
                    void 0,
                    {
                        data
                    }
                ];
            } catch (cause) {
                var _opts$onError, _call$procedure$_def$, _call$procedure2;
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause);
                const input = call.result();
                (_opts$onError = opts.onError) === null || _opts$onError === void 0 || _opts$onError.call(opts, {
                    error,
                    path: call.path,
                    input,
                    ctx: ctxManager.valueOrUndefined(),
                    type: (_call$procedure$_def$ = (_call$procedure2 = call.procedure) === null || _call$procedure2 === void 0 ? void 0 : _call$procedure2._def.type) !== null && _call$procedure$_def$ !== void 0 ? _call$procedure$_def$ : "unknown",
                    req: opts.req
                });
                return [
                    error,
                    void 0
                ];
            }
        });
        if (!info.isBatchCall) {
            const [call] = info.calls;
            const [error, result] = await rpcCalls[0];
            switch(info.type){
                case "unknown":
                case "mutation":
                case "query":
                    {
                        headers.set("content-type", "application/json");
                        if (isDataStream(result === null || result === void 0 ? void 0 : result.data)) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                            code: "UNSUPPORTED_MEDIA_TYPE",
                            message: "Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"
                        });
                        const res = error ? {
                            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"])({
                                config,
                                ctx: ctxManager.valueOrUndefined(),
                                error,
                                input: call.result(),
                                path: call.path,
                                type: info.type
                            })
                        } : {
                            result: {
                                data: result.data
                            }
                        };
                        const headResponse$1 = initResponse({
                            ctx: ctxManager.valueOrUndefined(),
                            info,
                            responseMeta: opts.responseMeta,
                            errors: error ? [
                                error
                            ] : [],
                            headers,
                            untransformedJSON: [
                                res
                            ]
                        });
                        return new Response(JSON.stringify((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformTRPCResponse"])(config, res)), {
                            status: headResponse$1.status,
                            headers
                        });
                    }
                case "subscription":
                    {
                        const iterable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["run"])(()=>{
                            if (error) return errorToAsyncIterable(error);
                            if (!experimentalSSE) return errorToAsyncIterable(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                                code: "METHOD_NOT_SUPPORTED",
                                message: "Missing experimental flag \"sseSubscriptions\""
                            }));
                            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObservable"])(result.data) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$utils$2d$DdbbrDku$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAsyncIterable"])(result.data)) return errorToAsyncIterable(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                                message: `Subscription ${call.path} did not return an observable or a AsyncGenerator`,
                                code: "INTERNAL_SERVER_ERROR"
                            }));
                            const dataAsIterable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObservable"])(result.data) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observableToAsyncIterable"])(result.data, opts.req.signal) : result.data;
                            return dataAsIterable;
                        });
                        const stream = sseStreamProducer((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, config.sse), {}, {
                            data: iterable,
                            serialize: (v)=>config.transformer.output.serialize(v),
                            formatError (errorOpts) {
                                var _call$procedure$_def$2, _call$procedure3, _opts$onError2;
                                const error$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(errorOpts.error);
                                const input = call === null || call === void 0 ? void 0 : call.result();
                                const path = call === null || call === void 0 ? void 0 : call.path;
                                const type = (_call$procedure$_def$2 = call === null || call === void 0 || (_call$procedure3 = call.procedure) === null || _call$procedure3 === void 0 ? void 0 : _call$procedure3._def.type) !== null && _call$procedure$_def$2 !== void 0 ? _call$procedure$_def$2 : "unknown";
                                (_opts$onError2 = opts.onError) === null || _opts$onError2 === void 0 || _opts$onError2.call(opts, {
                                    error: error$1,
                                    path,
                                    input,
                                    ctx: ctxManager.valueOrUndefined(),
                                    req: opts.req,
                                    type
                                });
                                const shape = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"])({
                                    config,
                                    ctx: ctxManager.valueOrUndefined(),
                                    error: error$1,
                                    input,
                                    path,
                                    type
                                });
                                return shape;
                            }
                        }));
                        for (const [key, value] of Object.entries(sseHeaders))headers.set(key, value);
                        const headResponse$1 = initResponse({
                            ctx: ctxManager.valueOrUndefined(),
                            info,
                            responseMeta: opts.responseMeta,
                            errors: [],
                            headers,
                            untransformedJSON: null
                        });
                        return new Response(stream, {
                            headers,
                            status: headResponse$1.status
                        });
                    }
            }
        }
        if (info.accept === "application/jsonl") {
            headers.set("content-type", "application/json");
            headers.set("transfer-encoding", "chunked");
            const headResponse$1 = initResponse({
                ctx: ctxManager.valueOrUndefined(),
                info,
                responseMeta: opts.responseMeta,
                errors: [],
                headers,
                untransformedJSON: null
            });
            const stream = jsonlStreamProducer((0, import_objectSpread2.default)((0, import_objectSpread2.default)({}, config.jsonl), {}, {
                maxDepth: Infinity,
                data: rpcCalls.map(async (res)=>{
                    const [error, result] = await res;
                    const call = info.calls[0];
                    if (error) {
                        var _procedure$_def$type, _procedure;
                        return {
                            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"])({
                                config,
                                ctx: ctxManager.valueOrUndefined(),
                                error,
                                input: call.result(),
                                path: call.path,
                                type: (_procedure$_def$type = (_procedure = call.procedure) === null || _procedure === void 0 ? void 0 : _procedure._def.type) !== null && _procedure$_def$type !== void 0 ? _procedure$_def$type : "unknown"
                            })
                        };
                    }
                    /**
					* Not very pretty, but we need to wrap nested data in promises
					* Our stream producer will only resolve top-level async values or async values that are directly nested in another async value
					*/ const iterable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObservable"])(result.data) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$observable$2d$UMO3vUa_$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["observableToAsyncIterable"])(result.data, opts.req.signal) : Promise.resolve(result.data);
                    return {
                        result: Promise.resolve({
                            data: iterable
                        })
                    };
                }),
                serialize: config.transformer.output.serialize,
                onError: (cause)=>{
                    var _opts$onError3, _info$type;
                    (_opts$onError3 = opts.onError) === null || _opts$onError3 === void 0 || _opts$onError3.call(opts, {
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(cause),
                        path: void 0,
                        input: void 0,
                        ctx: ctxManager.valueOrUndefined(),
                        req: opts.req,
                        type: (_info$type = info === null || info === void 0 ? void 0 : info.type) !== null && _info$type !== void 0 ? _info$type : "unknown"
                    });
                },
                formatError (errorOpts) {
                    var _call$procedure$_def$3, _call$procedure4;
                    const call = info === null || info === void 0 ? void 0 : info.calls[errorOpts.path[0]];
                    const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTRPCErrorFromUnknown"])(errorOpts.error);
                    const input = call === null || call === void 0 ? void 0 : call.result();
                    const path = call === null || call === void 0 ? void 0 : call.path;
                    const type = (_call$procedure$_def$3 = call === null || call === void 0 || (_call$procedure4 = call.procedure) === null || _call$procedure4 === void 0 ? void 0 : _call$procedure4._def.type) !== null && _call$procedure$_def$3 !== void 0 ? _call$procedure$_def$3 : "unknown";
                    const shape = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"])({
                        config,
                        ctx: ctxManager.valueOrUndefined(),
                        error,
                        input,
                        path,
                        type
                    });
                    return shape;
                }
            }));
            return new Response(stream, {
                headers,
                status: headResponse$1.status
            });
        }
        /**
		* Non-streaming response:
		* - await all responses in parallel, blocking on the slowest one
		* - create headers with known response body
		* - return a complete HTTPResponse
		*/ headers.set("content-type", "application/json");
        const results = (await Promise.all(rpcCalls)).map((res)=>{
            const [error, result] = res;
            if (error) return res;
            if (isDataStream(result.data)) return [
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "UNSUPPORTED_MEDIA_TYPE",
                    message: "Cannot use stream-like response in non-streaming request - use httpBatchStreamLink"
                }),
                void 0
            ];
            return res;
        });
        const resultAsRPCResponse = results.map(([error, result], index)=>{
            const call = info.calls[index];
            if (error) {
                var _call$procedure$_def$4, _call$procedure5;
                return {
                    error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"])({
                        config,
                        ctx: ctxManager.valueOrUndefined(),
                        error,
                        input: call.result(),
                        path: call.path,
                        type: (_call$procedure$_def$4 = (_call$procedure5 = call.procedure) === null || _call$procedure5 === void 0 ? void 0 : _call$procedure5._def.type) !== null && _call$procedure$_def$4 !== void 0 ? _call$procedure$_def$4 : "unknown"
                    })
                };
            }
            return {
                result: {
                    data: result.data
                }
            };
        });
        const errors = results.map(([error])=>error).filter(Boolean);
        const headResponse = initResponse({
            ctx: ctxManager.valueOrUndefined(),
            info,
            responseMeta: opts.responseMeta,
            untransformedJSON: resultAsRPCResponse,
            errors,
            headers
        });
        return new Response(JSON.stringify((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformTRPCResponse"])(config, resultAsRPCResponse)), {
            status: headResponse.status,
            headers
        });
    } catch (cause) {
        var _info$type2;
        const [_infoError, info] = infoTuple;
        const ctx = ctxManager.valueOrUndefined();
        const { error, untransformedJSON, body } = caughtErrorToData(cause, {
            opts,
            ctx: ctxManager.valueOrUndefined(),
            type: (_info$type2 = info === null || info === void 0 ? void 0 : info.type) !== null && _info$type2 !== void 0 ? _info$type2 : "unknown"
        });
        const headResponse = initResponse({
            ctx,
            info,
            responseMeta: opts.responseMeta,
            untransformedJSON,
            errors: [
                error
            ],
            headers
        });
        return new Response(body, {
            status: headResponse.status,
            headers
        });
    }
}
;
 //# sourceMappingURL=resolveResponse-CzlbRpCI.mjs.map
}),
"[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript) <export getErrorShape as getTRPCErrorShape>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getTRPCErrorShape": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorShape"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$getErrorShape$2d$Uhlrl4Bk$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/getErrorShape-Uhlrl4Bk.mjs [app-ssr] (ecmascript)");
}),

};

//# sourceMappingURL=node_modules_%40trpc_server_dist_f8cb5490._.js.map