import { test, expect } from '@playwright/test'
import { testConfig } from './test-data'

test.describe('无障碍性测试', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前访问首页
    await page.goto('/')
  })

  test('应该有正确的页面标题结构', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/提示词管理工具/)

    // 检查主标题结构
    const h1Elements = page.locator('h1')
    await expect(h1Elements).toHaveCount(1)
    await expect(h1Elements.first()).toContainText('提示词管理工具')

    // 检查标题层级
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all()
    
    for (let i = 0; i < headings.length; i++) {
      const heading = headings[i]
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase())
      const text = await heading.textContent()
      
      console.log(`标题 ${i + 1}: ${tagName} - "${text}"`)
      
      // 标题应该有内容
      expect(text?.trim()).toBeTruthy()
    }
  })

  test('应该支持键盘导航', async ({ page }) => {
    // 使用 Tab 键进行导航
    await page.keyboard.press('Tab')
    
    // 检查第一个可聚焦元素
    const firstFocusable = await page.locator(':focus').first()
    await expect(firstFocusable).toBeVisible()
    
    // 继续导航几个元素
    const focusableElements: string[] = []
    
    for (let i = 0; i < 10; i++) {
      const focused = await page.locator(':focus').first()
      const elementInfo = await focused.evaluate(el => ({
        tagName: el.tagName,
        id: el.id,
        className: el.className,
        text: el.textContent?.trim()
      }))
      
      focusableElements.push(`${elementInfo.tagName}${elementInfo.id ? '#' + elementInfo.id : ''}`)
      
      await page.keyboard.press('Tab')
      await page.waitForTimeout(100)
    }
    
    console.log('焦点顺序:', focusableElements)
    
    // 检查是否能使用 Shift+Tab 反向导航
    await page.keyboard.press('Shift+Tab')
    const backwardFocused = await page.locator(':focus').first()
    await expect(backwardFocused).toBeVisible()
  })

  test('应该支持 Enter 和 Space 键激活', async ({ page }) => {
    // 导航到一个按钮
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab') // 可能需要多次Tab到按钮
    
    const focusedElement = page.locator(':focus')
    const tagName = await focusedElement.evaluate(el => el.tagName.toLowerCase())
    
    if (tagName === 'button' || tagName === 'a') {
      // 使用 Enter 键激活
      await page.keyboard.press('Enter')
      await page.waitForTimeout(500)
      
      // 检查是否有相应的操作发生
      console.log('Enter 键激活成功')
    }
  })

  test('应该有正确的 ARIA 属性', async ({ page }) => {
    // 检查导航区域
    const nav = page.locator('nav').first()
    if (await nav.isVisible()) {
      await expect(nav).toHaveAttribute('role', 'navigation')
    }

    // 检查主要内容区域
    const main = page.locator('main').first()
    if (await main.isVisible()) {
      await expect(main).toHaveAttribute('role', 'main')
    }

    // 检查按钮的 ARIA 标签
    const buttons = page.locator('button')
    const buttonCount = await buttons.count()
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i)
      const hasAriaLabel = await button.getAttribute('aria-label')
      const hasText = await button.textContent()
      
      // 按钮应该有文本内容或 aria-label
      expect(hasAriaLabel || hasText?.trim()).toBeTruthy()
    }

    // 检查输入框的标签
    const inputs = page.locator('input, textarea')
    const inputCount = await inputs.count()
    
    for (let i = 0; i < Math.min(inputCount, 3); i++) {
      const input = inputs.nth(i)
      const hasLabel = await input.getAttribute('aria-label')
      const hasLabelledBy = await input.getAttribute('aria-labelledby')
      const id = await input.getAttribute('id')
      
      if (id) {
        const label = page.locator(`label[for="${id}"]`)
        const hasLabelElement = await label.count() > 0
        
        // 输入框应该有标签、aria-label 或 aria-labelledby
        expect(hasLabel || hasLabelledBy || hasLabelElement).toBeTruthy()
      }
    }
  })

  test('应该有足够的颜色对比度', async ({ page }) => {
    // 检查主要文本的颜色对比度
    const textElements = page.locator('p, span, div').filter({ hasText: /\w+/ })
    const elementCount = await textElements.count()
    
    for (let i = 0; i < Math.min(elementCount, 5); i++) {
      const element = textElements.nth(i)
      
      const styles = await element.evaluate(el => {
        const computed = window.getComputedStyle(el)
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          fontSize: computed.fontSize
        }
      })
      
      console.log(`元素 ${i + 1} 样式:`, styles)
      
      // 这里可以添加颜色对比度计算逻辑
      // 确保文本颜色和背景颜色有足够的对比度
    }
  })

  test('应该支持屏幕阅读器', async ({ page }) => {
    // 检查是否有跳转到主内容的链接
    const skipLink = page.locator('a[href="#main"], a:has-text("跳转到主内容")')
    if (await skipLink.count() > 0) {
      await expect(skipLink.first()).toBeVisible()
    }

    // 检查图片的 alt 属性
    const images = page.locator('img')
    const imageCount = await images.count()
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i)
      const alt = await img.getAttribute('alt')
      const src = await img.getAttribute('src')
      
      console.log(`图片 ${i + 1}: src="${src}", alt="${alt}"`)
      
      // 装饰性图片可以有空的 alt，但所有图片都应该有 alt 属性
      expect(alt !== null).toBeTruthy()
    }

    // 检查表单标签关联
    const labels = page.locator('label')
    const labelCount = await labels.count()
    
    for (let i = 0; i < labelCount; i++) {
      const label = labels.nth(i)
      const forAttr = await label.getAttribute('for')
      const text = await label.textContent()
      
      if (forAttr) {
        const associatedInput = page.locator(`#${forAttr}`)
        await expect(associatedInput).toBeVisible()
      }
      
      // 标签应该有文本内容
      expect(text?.trim()).toBeTruthy()
    }
  })

  test('应该处理焦点管理', async ({ page }) => {
    // 打开模态框
    const modalTrigger = page.locator('[data-testid="new-prompt-button"], [data-testid="quick-action-new-prompt"]').first()
    if (await modalTrigger.isVisible()) {
      await modalTrigger.click()
      
      // 检查焦点是否转移到模态框内
      await page.waitForTimeout(500)
      const focusedElement = page.locator(':focus')
      
      // 焦点应该在模态框内的某个元素上
      const modal = page.locator('[role="dialog"], .modal, [data-testid*="modal"]').first()
      if (await modal.isVisible()) {
        const focusWithinModal = await modal.locator(':focus').count() > 0
        expect(focusWithinModal).toBeTruthy()
        
        // 测试 Tab 陷阱
        const initialFocus = await page.locator(':focus').first()
        
        // 多次按 Tab 键
        for (let i = 0; i < 10; i++) {
          await page.keyboard.press('Tab')
          await page.waitForTimeout(100)
          
          const currentFocus = page.locator(':focus')
          const stillInModal = await modal.locator(':focus').count() > 0
          
          // 焦点应该始终保持在模态框内
          expect(stillInModal).toBeTruthy()
        }
        
        // 使用 Escape 键关闭模态框
        await page.keyboard.press('Escape')
        await page.waitForTimeout(500)
        
        // 检查焦点是否返回到触发元素
        const returnedFocus = page.locator(':focus')
        // 理想情况下焦点应该返回到原来的触发元素
      }
    }
  })

  test('应该支持高对比度模式', async ({ page }) => {
    // 模拟高对比度模式
    await page.addStyleTag({
      content: `
        @media (prefers-contrast: high) {
          * {
            filter: contrast(200%) !important;
          }
        }
      `
    })
    
    await page.reload()
    
    // 检查页面在高对比度模式下是否仍然可用
    await expect(page.locator('body')).toBeVisible()
    
    // 检查重要元素是否仍然可见
    await expect(page.locator('h1')).toBeVisible()
    
    const buttons = page.locator('button')
    if (await buttons.count() > 0) {
      await expect(buttons.first()).toBeVisible()
    }
  })

  test('应该支持缩放到 200%', async ({ page }) => {
    // 设置页面缩放到 200%
    await page.setViewportSize({ 
      width: Math.floor(testConfig.viewports.desktop.width / 2), 
      height: Math.floor(testConfig.viewports.desktop.height / 2) 
    })
    
    // 模拟浏览器缩放
    await page.evaluate(() => {
      document.body.style.zoom = '2'
    })
    
    await page.waitForTimeout(1000)
    
    // 检查页面在放大状态下是否仍然可用
    await expect(page.locator('body')).toBeVisible()
    
    // 检查是否有水平滚动条（应该避免）
    const hasHorizontalScroll = await page.evaluate(() => {
      return document.body.scrollWidth > window.innerWidth
    })
    
    console.log('是否有水平滚动:', hasHorizontalScroll)
    
    // 检查重要内容是否仍然可见
    await expect(page.locator('h1')).toBeVisible()
    
    // 恢复缩放
    await page.evaluate(() => {
      document.body.style.zoom = '1'
    })
  })

  test('应该支持语言切换', async ({ page }) => {
    // 查找语言切换控件
    const langToggle = page.locator('[data-testid="language-toggle"], [aria-label*="语言"], [title*="语言"]').first()
    
    if (await langToggle.isVisible()) {
      await langToggle.click()
      
      // 检查语言选项
      const langOptions = page.locator('[data-testid*="lang"], [lang], [hreflang]')
      if (await langOptions.count() > 0) {
        // 尝试切换到英文
        const englishOption = page.locator('text=English, text=EN, [lang="en"]').first()
        if (await englishOption.isVisible()) {
          await englishOption.click()
          
          // 检查页面是否切换到英文
          await page.waitForTimeout(1000)
          const pageTitle = await page.textContent('h1')
          console.log('切换后的标题:', pageTitle)
        }
      }
    }
  })

  test('应该支持减少动画', async ({ page }) => {
    // 模拟用户偏好减少动画
    await page.addStyleTag({
      content: `
        @media (prefers-reduced-motion: reduce) {
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `
    })
    
    await page.reload()
    
    // 触发一些可能有动画的操作
    const animatedElement = page.locator('[data-testid="prompt-card"]').first()
    if (await animatedElement.isVisible()) {
      await animatedElement.hover()
      await page.waitForTimeout(100)
      
      // 检查元素是否仍然可用
      await expect(animatedElement).toBeVisible()
    }
  })

  test('应该有明确的错误消息', async ({ page }) => {
    // 触发表单验证错误
    await page.goto('/prompts/new')
    
    // 提交空表单
    await page.click('[data-testid="save-prompt-button"]')
    
    // 检查错误消息的可访问性
    const errorMessages = page.locator('[role="alert"], .error, [data-testid*="error"]')
    const errorCount = await errorMessages.count()
    
    for (let i = 0; i < errorCount; i++) {
      const error = errorMessages.nth(i)
      const text = await error.textContent()
      const ariaLive = await error.getAttribute('aria-live')
      const role = await error.getAttribute('role')
      
      console.log(`错误消息 ${i + 1}: "${text}", role="${role}", aria-live="${ariaLive}"`)
      
      // 错误消息应该有内容
      expect(text?.trim()).toBeTruthy()
      
      // 错误消息应该有适当的 ARIA 属性
      expect(role === 'alert' || ariaLive === 'polite' || ariaLive === 'assertive').toBeTruthy()
    }
  })

  test('应该支持触摸设备', async ({ page }) => {
    // 设置移动设备视口
    await page.setViewportSize(testConfig.viewports.mobile)
    
    // 检查触摸目标大小
    const interactiveElements = page.locator('button, a, input, [role="button"]')
    const elementCount = await interactiveElements.count()
    
    for (let i = 0; i < Math.min(elementCount, 5); i++) {
      const element = interactiveElements.nth(i)
      
      const boundingBox = await element.boundingBox()
      if (boundingBox) {
        console.log(`元素 ${i + 1} 尺寸: ${boundingBox.width}x${boundingBox.height}`)
        
        // 触摸目标应该至少 44x44 像素（WCAG 建议）
        expect(boundingBox.width).toBeGreaterThanOrEqual(44)
        expect(boundingBox.height).toBeGreaterThanOrEqual(44)
      }
    }
    
    // 测试触摸滚动
    await page.touchscreen.tap(200, 300)
    await page.evaluate(() => {
      window.scrollBy(0, 100)
    })
    
    // 检查页面是否响应触摸操作
    const scrollY = await page.evaluate(() => window.scrollY)
    expect(scrollY).toBeGreaterThan(0)
  })
})