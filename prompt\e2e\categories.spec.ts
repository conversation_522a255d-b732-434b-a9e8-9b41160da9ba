import { test, expect } from '@playwright/test'

test.describe('分类管理', () => {
  test.beforeEach(async ({ page }) => {
    // 访问分类管理页面
    await page.goto('/categories')
  })

  test('应该显示分类列表', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h1')).toContainText('分类管理')
    
    // 检查分类卡片
    await expect(page.locator('[data-testid="category-card"]')).toHaveCount(2, { timeout: 5000 })
    
    // 检查分类内容
    await expect(page.locator('[data-testid="category-card"]')).toContainText('工作')
    await expect(page.locator('[data-testid="category-card"]')).toContainText('学习')
  })

  test('应该能够创建新分类', async ({ page }) => {
    // 点击新建分类按钮
    await page.click('[data-testid="new-category-button"]')
    
    // 检查是否打开新建分类模态框
    await expect(page.locator('[data-testid="create-category-modal"]')).toBeVisible()
    
    // 填写分类表单
    await page.fill('[data-testid="category-name-input"]', '测试分类')
    await page.fill('[data-testid="category-description-input"]', '这是一个测试分类')
    
    // 选择颜色
    await page.click('[data-testid="color-picker-button"]')
    await page.click('[data-testid="color-preset-red"]')
    
    // 选择图标
    await page.click('[data-testid="icon-picker-button"]')
    await page.click('[data-testid="icon-option-test"]')
    
    // 保存分类
    await page.click('[data-testid="save-category-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=分类创建成功')).toBeVisible()
    
    // 检查模态框是否关闭
    await expect(page.locator('[data-testid="create-category-modal"]')).not.toBeVisible()
    
    // 检查新分类是否出现在列表中
    await expect(page.locator('[data-testid="category-card"]')).toContainText('测试分类')
  })

  test('应该能够编辑分类', async ({ page }) => {
    // 点击第一个分类的编辑按钮
    await page.click('[data-testid="category-card"]:first-child [data-testid="edit-category-button"]')
    
    // 检查是否打开编辑分类模态框
    await expect(page.locator('[data-testid="edit-category-modal"]')).toBeVisible()
    
    // 检查表单是否填充了现有数据
    await expect(page.locator('[data-testid="category-name-input"]')).toHaveValue('工作')
    
    // 修改分类名称
    await page.fill('[data-testid="category-name-input"]', '修改后的工作')
    
    // 修改描述
    await page.fill('[data-testid="category-description-input"]', '修改后的描述')
    
    // 保存修改
    await page.click('[data-testid="save-category-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=分类更新成功')).toBeVisible()
    
    // 检查修改是否生效
    await expect(page.locator('[data-testid="category-card"]')).toContainText('修改后的工作')
  })

  test('应该能够删除分类', async ({ page }) => {
    // 点击第一个分类的删除按钮
    await page.click('[data-testid="category-card"]:first-child [data-testid="delete-category-button"]')
    
    // 检查是否出现确认删除对话框
    await expect(page.locator('[data-testid="confirm-delete-modal"]')).toBeVisible()
    await expect(page.locator('text=确认删除此分类吗？')).toBeVisible()
    
    // 点击确认删除
    await page.click('[data-testid="confirm-delete-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=分类删除成功')).toBeVisible()
    
    // 检查对话框是否关闭
    await expect(page.locator('[data-testid="confirm-delete-modal"]')).not.toBeVisible()
  })

  test('应该能够查看分类详情', async ({ page }) => {
    // 点击第一个分类卡片
    await page.click('[data-testid="category-card"]:first-child')
    
    // 检查是否跳转到分类详情页面
    await expect(page).toHaveURL(/\/categories\/\d+/)
    
    // 检查分类详情页面内容
    await expect(page.locator('[data-testid="category-detail-name"]')).toContainText('工作')
    await expect(page.locator('[data-testid="category-detail-description"]')).toBeVisible()
    await expect(page.locator('[data-testid="category-prompts-list"]')).toBeVisible()
    
    // 检查分类下的提示词列表
    await expect(page.locator('[data-testid="category-prompt-card"]')).toHaveCount(3, { timeout: 5000 })
  })

  test('应该支持分类视图切换', async ({ page }) => {
    // 检查默认为网格视图
    await expect(page.locator('[data-testid="category-grid-view"]')).toBeVisible()
    
    // 切换到列表视图
    await page.click('[data-testid="list-view-button"]')
    
    // 检查是否切换到列表视图
    await expect(page.locator('[data-testid="category-list-view"]')).toBeVisible()
    await expect(page.locator('[data-testid="category-grid-view"]')).not.toBeVisible()
    
    // 切换回网格视图
    await page.click('[data-testid="grid-view-button"]')
    
    // 检查是否切换回网格视图
    await expect(page.locator('[data-testid="category-grid-view"]')).toBeVisible()
    await expect(page.locator('[data-testid="category-list-view"]')).not.toBeVisible()
  })

  test('应该支持分类排序', async ({ page }) => {
    // 点击排序按钮
    await page.click('[data-testid="sort-button"]')
    
    // 检查排序选项
    await expect(page.locator('[data-testid="sort-options"]')).toBeVisible()
    
    // 选择按名称排序
    await page.click('[data-testid="sort-by-name"]')
    
    // 检查排序是否生效
    await expect(page.locator('[data-testid="sort-indicator"]')).toContainText('名称')
    
    // 改变排序方向
    await page.click('[data-testid="sort-direction-button"]')
    
    // 检查排序方向是否改变
    await expect(page.locator('[data-testid="sort-direction-button"]')).toHaveClass(/desc/)
  })

  test('应该支持分类搜索', async ({ page }) => {
    // 在搜索框中输入关键词
    await page.fill('[data-testid="category-search-input"]', '工作')
    
    // 检查搜索结果
    await expect(page.locator('[data-testid="category-card"]')).toHaveCount(1)
    await expect(page.locator('[data-testid="category-card"]')).toContainText('工作')
    
    // 清除搜索
    await page.click('[data-testid="clear-search-button"]')
    
    // 检查搜索是否清除
    await expect(page.locator('[data-testid="category-search-input"]')).toHaveValue('')
    await expect(page.locator('[data-testid="category-card"]')).toHaveCount(2)
  })

  test('应该显示分类统计信息', async ({ page }) => {
    // 检查分类统计卡片
    await expect(page.locator('[data-testid="category-stats"]')).toBeVisible()
    
    // 检查统计数据
    await expect(page.locator('[data-testid="total-categories"]')).toContainText(/\d+/)
    await expect(page.locator('[data-testid="total-prompts-in-categories"]')).toContainText(/\d+/)
    await expect(page.locator('[data-testid="average-prompts-per-category"]')).toContainText(/\d+/)
  })

  test('应该支持分类层级管理', async ({ page }) => {
    // 点击新建分类按钮
    await page.click('[data-testid="new-category-button"]')
    
    // 填写分类信息
    await page.fill('[data-testid="category-name-input"]', '子分类')
    
    // 选择父分类
    await page.click('[data-testid="parent-category-select"]')
    await page.click('[data-testid="parent-category-option-工作"]')
    
    // 保存分类
    await page.click('[data-testid="save-category-button"]')
    
    // 检查子分类是否正确创建
    await expect(page.locator('[data-testid="category-card"]')).toContainText('子分类')
    
    // 检查父子关系显示
    await expect(page.locator('[data-testid="category-hierarchy"]')).toContainText('工作 > 子分类')
  })

  test('应该处理分类表单验证', async ({ page }) => {
    // 点击新建分类按钮
    await page.click('[data-testid="new-category-button"]')
    
    // 不填写任何内容直接保存
    await page.click('[data-testid="save-category-button"]')
    
    // 检查验证错误消息
    await expect(page.locator('[data-testid="name-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="name-error"]')).toContainText('分类名称不能为空')
    
    await expect(page.locator('[data-testid="color-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="color-error"]')).toContainText('请选择颜色')
    
    // 填写过长的名称
    await page.fill('[data-testid="category-name-input"]', 'a'.repeat(51))
    
    // 检查长度验证错误
    await expect(page.locator('[data-testid="name-error"]')).toContainText('分类名称长度不能超过50个字符')
  })

  test('应该支持颜色选择器', async ({ page }) => {
    // 点击新建分类按钮
    await page.click('[data-testid="new-category-button"]')
    
    // 点击颜色选择器
    await page.click('[data-testid="color-picker-button"]')
    
    // 检查颜色选择器是否打开
    await expect(page.locator('[data-testid="color-picker-panel"]')).toBeVisible()
    
    // 检查预设颜色
    await expect(page.locator('[data-testid="color-preset"]')).toHaveCount(12)
    
    // 选择一个预设颜色
    await page.click('[data-testid="color-preset-blue"]')
    
    // 检查颜色是否选中
    await expect(page.locator('[data-testid="selected-color"]')).toHaveAttribute('style', /background-color.*blue/)
    
    // 打开自定义颜色面板
    await page.click('[data-testid="custom-color-tab"]')
    
    // 检查自定义颜色控件
    await expect(page.locator('[data-testid="color-hue-slider"]')).toBeVisible()
    await expect(page.locator('[data-testid="color-saturation-slider"]')).toBeVisible()
    await expect(page.locator('[data-testid="color-lightness-slider"]')).toBeVisible()
    
    // 调整色相
    await page.click('[data-testid="color-hue-slider"]')
    
    // 检查颜色是否更新
    await expect(page.locator('[data-testid="color-preview"]')).toBeVisible()
  })

  test('应该支持图标选择器', async ({ page }) => {
    // 点击新建分类按钮
    await page.click('[data-testid="new-category-button"]')
    
    // 点击图标选择器
    await page.click('[data-testid="icon-picker-button"]')
    
    // 检查图标选择器是否打开
    await expect(page.locator('[data-testid="icon-picker-panel"]')).toBeVisible()
    
    // 检查图标分类标签
    await expect(page.locator('[data-testid="icon-category-work"]')).toBeVisible()
    await expect(page.locator('[data-testid="icon-category-study"]')).toBeVisible()
    await expect(page.locator('[data-testid="icon-category-life"]')).toBeVisible()
    
    // 切换到学习分类
    await page.click('[data-testid="icon-category-study"]')
    
    // 检查学习相关图标
    await expect(page.locator('[data-testid="icon-option-book"]')).toBeVisible()
    await expect(page.locator('[data-testid="icon-option-pencil"]')).toBeVisible()
    
    // 选择一个图标
    await page.click('[data-testid="icon-option-book"]')
    
    // 检查图标是否选中
    await expect(page.locator('[data-testid="selected-icon"]')).toContainText('📚')
    
    // 测试图标搜索
    await page.fill('[data-testid="icon-search-input"]', '工作')
    
    // 检查搜索结果
    await expect(page.locator('[data-testid="icon-option-briefcase"]')).toBeVisible()
    await expect(page.locator('[data-testid="icon-option-computer"]')).toBeVisible()
  })

  test('应该显示分类预览', async ({ page }) => {
    // 点击新建分类按钮
    await page.click('[data-testid="new-category-button"]')
    
    // 填写分类信息
    await page.fill('[data-testid="category-name-input"]', '预览测试')
    await page.fill('[data-testid="category-description-input"]', '这是预览描述')
    
    // 选择颜色
    await page.click('[data-testid="color-picker-button"]')
    await page.click('[data-testid="color-preset-green"]')
    
    // 选择图标
    await page.click('[data-testid="icon-picker-button"]')
    await page.click('[data-testid="icon-option-star"]')
    
    // 检查实时预览
    await expect(page.locator('[data-testid="category-preview"]')).toBeVisible()
    await expect(page.locator('[data-testid="category-preview-name"]')).toContainText('预览测试')
    await expect(page.locator('[data-testid="category-preview-description"]')).toContainText('这是预览描述')
    await expect(page.locator('[data-testid="category-preview-icon"]')).toContainText('⭐')
    await expect(page.locator('[data-testid="category-preview"]')).toHaveAttribute('style', /background-color.*green/)
  })

  test('应该支持批量操作', async ({ page }) => {
    // 选择多个分类
    await page.click('[data-testid="category-card"]:first-child [data-testid="select-checkbox"]')
    await page.click('[data-testid="category-card"]:nth-child(2) [data-testid="select-checkbox"]')
    
    // 检查批量操作按钮是否出现
    await expect(page.locator('[data-testid="batch-actions"]')).toBeVisible()
    
    // 点击批量删除
    await page.click('[data-testid="batch-delete-button"]')
    
    // 检查确认对话框
    await expect(page.locator('[data-testid="confirm-batch-delete-modal"]')).toBeVisible()
    await expect(page.locator('text=确认删除选中的 2 个分类吗？')).toBeVisible()
    
    // 取消删除
    await page.click('[data-testid="cancel-batch-delete-button"]')
    
    // 检查对话框是否关闭
    await expect(page.locator('[data-testid="confirm-batch-delete-modal"]')).not.toBeVisible()
  })

  test('应该显示空状态', async ({ page }) => {
    // 拦截API请求，返回空数据
    await page.route('**/api/trpc/categories.getAll**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ result: { data: [] } })
      })
    })
    
    // 重新加载页面
    await page.reload()
    
    // 检查空状态
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible()
    await expect(page.locator('text=暂无分类')).toBeVisible()
    await expect(page.locator('text=开始创建您的第一个分类')).toBeVisible()
    
    // 检查创建按钮
    await expect(page.locator('[data-testid="create-first-category-button"]')).toBeVisible()
  })

  test('应该支持分类导出', async ({ page }) => {
    // 点击导出按钮
    await page.click('[data-testid="export-categories-button"]')
    
    // 检查导出选项
    await expect(page.locator('[data-testid="export-options"]')).toBeVisible()
    
    // 选择导出格式
    await page.click('[data-testid="export-format-json"]')
    
    // 确认导出
    await page.click('[data-testid="confirm-export-button"]')
    
    // 检查下载是否开始
    const downloadPromise = page.waitForEvent('download')
    const download = await downloadPromise
    
    // 检查下载文件名
    expect(download.suggestedFilename()).toContain('categories')
    expect(download.suggestedFilename()).toContain('.json')
  })

  test('应该支持分类导入', async ({ page }) => {
    // 点击导入按钮
    await page.click('[data-testid="import-categories-button"]')
    
    // 检查导入模态框
    await expect(page.locator('[data-testid="import-categories-modal"]')).toBeVisible()
    
    // 上传文件
    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles('test-data/categories.json')
    
    // 检查文件预览
    await expect(page.locator('[data-testid="import-preview"]')).toBeVisible()
    await expect(page.locator('[data-testid="import-category-item"]')).toHaveCount(3)
    
    // 确认导入
    await page.click('[data-testid="confirm-import-button"]')
    
    // 检查导入结果
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=成功导入 3 个分类')).toBeVisible()
  })
})

test.describe('分类管理无障碍性', () => {
  test('应该支持键盘导航', async ({ page }) => {
    await page.goto('/categories')
    
    // 使用 Tab 键导航
    await page.keyboard.press('Tab')
    
    // 检查第一个可聚焦元素
    await expect(page.locator('[data-testid="new-category-button"]')).toBeFocused()
    
    // 继续导航
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="category-card"]:first-child')).toBeFocused()
    
    // 使用回车键激活
    await page.keyboard.press('Enter')
    
    // 检查是否跳转到分类详情
    await expect(page).toHaveURL(/\/categories\/\d+/)
  })

  test('应该有正确的 ARIA 属性', async ({ page }) => {
    await page.goto('/categories')
    
    // 检查分类卡片的 ARIA 属性
    await expect(page.locator('[data-testid="category-card"]')).toHaveAttribute('role', 'button')
    await expect(page.locator('[data-testid="category-card"]')).toHaveAttribute('tabindex', '0')
    
    // 检查分类列表的 ARIA 属性
    await expect(page.locator('[data-testid="categories-list"]')).toHaveAttribute('role', 'grid')
    
    // 检查分类统计的 ARIA 属性
    await expect(page.locator('[data-testid="category-stats"]')).toHaveAttribute('role', 'region')
    await expect(page.locator('[data-testid="category-stats"]')).toHaveAttribute('aria-label', '分类统计')
  })

  test('应该支持屏幕阅读器', async ({ page }) => {
    await page.goto('/categories')
    
    // 检查页面结构
    await expect(page.locator('h1')).toContainText('分类管理')
    
    // 检查分类描述
    await expect(page.locator('[data-testid="category-card"] [data-testid="category-description"]')).toBeVisible()
    
    // 检查操作按钮的标签
    await expect(page.locator('[data-testid="edit-category-button"]')).toHaveAttribute('aria-label', '编辑分类')
    await expect(page.locator('[data-testid="delete-category-button"]')).toHaveAttribute('aria-label', '删除分类')
  })
})