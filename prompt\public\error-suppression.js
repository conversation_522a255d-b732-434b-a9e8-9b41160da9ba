
// 抑制 Chrome 扩展相关错误
if (typeof window !== 'undefined') {
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    // 忽略 Chrome 扩展相关错误
    if (
      message.includes('chrome-extension') ||
      message.includes('service-worker.js') ||
      message.includes("Failed to execute 'put' on 'Cache'")
    ) {
      return;
    }
    originalConsoleError.apply(console, args);
  };
}
