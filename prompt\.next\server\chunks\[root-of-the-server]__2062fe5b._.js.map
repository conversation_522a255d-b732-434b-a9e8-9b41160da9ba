{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    DATABASE_URL: z.string().url(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    NEXT_PUBLIC_SUPABASE_URL: z.string().url(),\n    NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\n    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC5B,UAAU,oKAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;IACb;IAEA;;;;GAIC,GACD,QAAQ;QACN,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACxC,+BAA+B,oKAAA,CAAA,IAAC,CAAC,MAAM;IACzC;IAEA;;;GAGC,GACD,YAAY;QACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACtC,QAAQ;QACR,wBAAwB;QACxB,6BAA6B;IAC/B;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\nimport { env } from \"~/env\";\n\nconst createPrismaClient = () =>\n  new PrismaClient({\n    log:\n      env.NODE_ENV === \"development\" ? [\"query\", \"error\", \"warn\"] : [\"error\"],\n  });\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: ReturnType<typeof createPrismaClient> | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? createPrismaClient();\n\nif (env.NODE_ENV !== \"production\") globalForPrisma.prisma = db;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,qBAAqB,IACzB,IAAI,6HAAA,CAAA,eAAY,CAAC;QACf,KACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBAAgB;YAAC;YAAS;YAAS;SAAO,GAAG;YAAC;SAAQ;IAC3E;AAEF,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI;AAE5C,IAAI,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,cAAc,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/api/trpc.ts"], "sourcesContent": ["/**\n * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:\n * 1. You want to modify request context (see Part 1).\n * 2. You want to create a new middleware or type of procedure (see Part 3).\n *\n * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will\n * need to use are documented accordingly near the end.\n */\n\nimport { initTRPC, TRPCError } from \"@trpc/server\";\nimport superjson from \"superjson\";\nimport { ZodError } from \"zod\";\nimport { cookies } from \"next/headers\";\nimport { createServerComponentClient } from \"@supabase/auth-helpers-nextjs\";\n\nimport { db } from \"~/server/db\";\nimport { Database } from \"~/lib/supabase/client\";\n\n/**\n * 1. CONTEXT\n *\n * This section defines the \"contexts\" that are available in the backend API.\n *\n * These allow you to access things when processing a request, like the database, the session, etc.\n *\n * This helper generates the \"internals\" for a tRPC context. The API handler and RSC clients each\n * wrap this and provides the required context.\n *\n * @see https://trpc.io/docs/server/context\n */\nexport const createTRPCContext = async (opts: { headers: Headers }) => {\n  const cookieStore = cookies();\n  const supabase = createServerComponentClient<Database>({\n    cookies: () => cookieStore,\n  });\n\n  const { data: { session } } = await supabase.auth.getSession();\n  const { data: { user } } = await supabase.auth.getUser();\n\n  return {\n    db,\n    session,\n    user,\n    supabase,\n    ...opts,\n  };\n};\n\n/**\n * 2. INITIALIZATION\n *\n * This is where the tRPC API is initialized, connecting the context and transformer. We also parse\n * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation\n * errors on the backend.\n */\nconst t = initTRPC.context<typeof createTRPCContext>().create({\n  transformer: superjson,\n  errorFormatter({ shape, error }) {\n    return {\n      ...shape,\n      data: {\n        ...shape.data,\n        zodError:\n          error.cause instanceof ZodError ? error.cause.flatten() : null,\n      },\n    };\n  },\n});\n\n/**\n * Create a server-side caller.\n *\n * @see https://trpc.io/docs/server/server-side-calls\n */\nexport const createCallerFactory = t.createCallerFactory;\n\n/**\n * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)\n *\n * These are the pieces you use to build your tRPC API. You should import these a lot in the\n * \"/src/server/api/routers\" directory.\n */\n\n/**\n * This is how you create new routers and sub-routers in your tRPC API.\n *\n * @see https://trpc.io/docs/router\n */\nexport const createTRPCRouter = t.router;\n\n/**\n * Middleware for timing procedure execution and adding an artificial delay in development.\n *\n * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating\n * network latency that would occur in production but not in local development.\n */\nconst timingMiddleware = t.middleware(async ({ next, path }) => {\n  const start = Date.now();\n\n  if (t._config.isDev) {\n    // artificial delay in dev\n    const waitMs = Math.floor(Math.random() * 400) + 100;\n    await new Promise((resolve) => setTimeout(resolve, waitMs));\n  }\n\n  const result = await next();\n\n  const end = Date.now();\n  console.log(`[TRPC] ${path} took ${end - start}ms to execute`);\n\n  return result;\n});\n\n/**\n * Public (unauthenticated) procedure\n *\n * This is the base piece you use to build new queries and mutations on your tRPC API. It does not\n * guarantee that a user querying is authorized, but you can still access user session data if they\n * are logged in.\n */\nexport const publicProcedure = t.procedure.use(timingMiddleware);\n\n/**\n * Protected (authenticated) procedure\n *\n * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies\n * the session is valid and guarantees `ctx.user` is not null.\n *\n * @see https://trpc.io/docs/procedures\n */\nexport const protectedProcedure = t.procedure\n  .use(timingMiddleware)\n  .use(({ ctx, next }) => {\n    if (!ctx.user || !ctx.session) {\n      throw new TRPCError({ \n        code: \"UNAUTHORIZED\", \n        message: \"You must be logged in to perform this action\" \n      });\n    }\n    return next({\n      ctx: {\n        // infers the `user` and `session` as non-nullable\n        ...ctx,\n        user: ctx.user,\n        session: ctx.session,\n      },\n    });\n  });\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AAED;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;AAeO,MAAM,oBAAoB,OAAO;IACtC,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,8BAA2B,AAAD,EAAY;QACrD,SAAS,IAAM;IACjB;IAEA,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAEtD,OAAO;QACL,IAAA,qHAAA,CAAA,KAAE;QACF;QACA;QACA;QACA,GAAG,IAAI;IACT;AACF;AAEA;;;;;;CAMC,GACD,MAAM,IAAI,mKAAA,CAAA,WAAQ,CAAC,OAAO,GAA6B,MAAM,CAAC;IAC5D,aAAa,4IAAA,CAAA,UAAS;IACtB,gBAAe,EAAE,KAAK,EAAE,KAAK,EAAE;QAC7B,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,UACE,MAAM,KAAK,YAAY,uIAAA,CAAA,WAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,KAAK;YAC9D;QACF;IACF;AACF;AAOO,MAAM,sBAAsB,EAAE,mBAAmB;AAcjD,MAAM,mBAAmB,EAAE,MAAM;AAExC;;;;;CAKC,GACD,MAAM,mBAAmB,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACzD,MAAM,QAAQ,KAAK,GAAG;IAEtB,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE;QACnB,0BAA0B;QAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACjD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;IACrD;IAEA,MAAM,SAAS,MAAM;IAErB,MAAM,MAAM,KAAK,GAAG;IACpB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,MAAM,EAAE,MAAM,MAAM,aAAa,CAAC;IAE7D,OAAO;AACT;AASO,MAAM,kBAAkB,EAAE,SAAS,CAAC,GAAG,CAAC;AAUxC,MAAM,qBAAqB,EAAE,SAAS,CAC1C,GAAG,CAAC,kBACJ,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE;IACjB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,EAAE;QAC7B,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;YAClB,MAAM;YACN,SAAS;QACX;IACF;IACA,OAAO,KAAK;QACV,KAAK;YACH,kDAAkD;YAClD,GAAG,GAAG;YACN,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,OAAO;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/api/routers/categories.ts"], "sourcesContent": ["import { z } from \"zod\";\r\nimport { createTRPCRouter, protectedProcedure } from \"~/server/api/trpc\";\r\nimport { TRPCError } from \"@trpc/server\";\r\n\r\nconst createCategorySchema = z.object({\r\n  name: z.string().min(1, \"分类名称不能为空\").max(100, \"分类名称不能超过100个字符\"),\r\n  description: z.string().optional(),\r\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").default(\"#3B82F6\"),\r\n  icon: z.string().min(1, \"图标不能为空\").max(50, \"图标名称不能超过50个字符\").default(\"folder\"),\r\n});\r\n\r\nconst updateCategorySchema = z.object({\r\n  id: z.string().uuid(\"分类ID格式不正确\"),\r\n  name: z.string().min(1, \"分类名称不能为空\").max(100, \"分类名称不能超过100个字符\").optional(),\r\n  description: z.string().optional(),\r\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").optional(),\r\n  icon: z.string().min(1, \"图标不能为空\").max(50, \"图标名称不能超过50个字符\").optional(),\r\n});\r\n\r\nexport const categoriesRouter = createTRPCRouter({\r\n  // 获取用户的所有分类\r\n  getAll: protectedProcedure.query(async ({ ctx }) => {\r\n    try {\r\n      const categories = await ctx.db.category.findMany({\r\n        where: {\r\n          userId: ctx.user.id,\r\n        },\r\n        include: {\r\n          _count: {\r\n            select: {\r\n              prompts: true,\r\n            },\r\n          },\r\n        },\r\n        orderBy: {\r\n          createdAt: \"desc\",\r\n        },\r\n      });\r\n\r\n      return categories.map((category) => ({\r\n        ...category,\r\n        promptCount: category._count.prompts,\r\n      }));\r\n    } catch (error) {\r\n      throw new TRPCError({\r\n        code: \"INTERNAL_SERVER_ERROR\",\r\n        message: \"获取分类列表失败\",\r\n      });\r\n    }\r\n  }),\r\n\r\n  // 根据ID获取分类\r\n  getById: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"分类ID格式不正确\") }))\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const category = await ctx.db.category.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n          include: {\r\n            _count: {\r\n              select: {\r\n                prompts: true,\r\n              },\r\n            },\r\n          },\r\n        });\r\n\r\n        if (!category) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"分类不存在\",\r\n          });\r\n        }\r\n\r\n        return {\r\n          ...category,\r\n          promptCount: category._count.prompts,\r\n        };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取分类详情失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 创建分类\r\n  create: protectedProcedure\r\n    .input(createCategorySchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查同名分类是否存在\r\n        const existingCategory = await ctx.db.category.findFirst({\r\n          where: {\r\n            name: input.name,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (existingCategory) {\r\n          throw new TRPCError({\r\n            code: \"CONFLICT\",\r\n            message: \"同名分类已存在\",\r\n          });\r\n        }\r\n\r\n        const category = await ctx.db.category.create({\r\n          data: {\r\n            ...input,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        return category;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"创建分类失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 更新分类\r\n  update: protectedProcedure\r\n    .input(updateCategorySchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        const { id, ...updateData } = input;\r\n\r\n        // 检查分类是否存在且属于当前用户\r\n        const existingCategory = await ctx.db.category.findFirst({\r\n          where: {\r\n            id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingCategory) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"分类不存在\",\r\n          });\r\n        }\r\n\r\n        // 如果更新名称，检查是否与其他分类重名\r\n        if (updateData.name && updateData.name !== existingCategory.name) {\r\n          const duplicateCategory = await ctx.db.category.findFirst({\r\n            where: {\r\n              name: updateData.name,\r\n              userId: ctx.user.id,\r\n              id: { not: id },\r\n            },\r\n          });\r\n\r\n          if (duplicateCategory) {\r\n            throw new TRPCError({\r\n              code: \"CONFLICT\",\r\n              message: \"同名分类已存在\",\r\n            });\r\n          }\r\n        }\r\n\r\n        const updatedCategory = await ctx.db.category.update({\r\n          where: { id },\r\n          data: updateData,\r\n        });\r\n\r\n        return updatedCategory;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"更新分类失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 删除分类\r\n  delete: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"分类ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查分类是否存在且属于当前用户\r\n        const existingCategory = await ctx.db.category.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n          include: {\r\n            _count: {\r\n              select: {\r\n                prompts: true,\r\n              },\r\n            },\r\n          },\r\n        });\r\n\r\n        if (!existingCategory) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"分类不存在\",\r\n          });\r\n        }\r\n\r\n        // 检查是否有关联的提示词\r\n        if (existingCategory._count.prompts > 0) {\r\n          throw new TRPCError({\r\n            code: \"PRECONDITION_FAILED\",\r\n            message: `该分类下还有 ${existingCategory._count.prompts} 个提示词，无法删除`,\r\n          });\r\n        }\r\n\r\n        await ctx.db.category.delete({\r\n          where: { id: input.id },\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"删除分类失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 强制删除分类（将关联的提示词分类设为null）\r\n  forceDelete: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"分类ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查分类是否存在且属于当前用户\r\n        const existingCategory = await ctx.db.category.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingCategory) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"分类不存在\",\r\n          });\r\n        }\r\n\r\n        // 使用事务确保数据一致性\r\n        await ctx.db.$transaction(async (tx) => {\r\n          // 先将关联的提示词分类设为null\r\n          await tx.prompt.updateMany({\r\n            where: {\r\n              categoryId: input.id,\r\n              userId: ctx.user.id,\r\n            },\r\n            data: {\r\n              categoryId: null,\r\n            },\r\n          });\r\n\r\n          // 然后删除分类\r\n          await tx.category.delete({\r\n            where: { id: input.id },\r\n          });\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"强制删除分类失败\",\r\n        });\r\n      }\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,uBAAuB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK;IAC7C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,OAAO,CAAC;IAC9D,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,iBAAiB,OAAO,CAAC;AACrE;AAEA,MAAM,uBAAuB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,KAAK,kBAAkB,QAAQ;IACvE,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,QAAQ;IAC9D,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,iBAAiB,QAAQ;AACrE;AAEO,MAAM,mBAAmB,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC/C,YAAY;IACZ,QAAQ,8HAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC7C,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAChD,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,SAAS;wBACX;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;gBACb;YACF;YAEA,OAAO,WAAW,GAAG,CAAC,CAAC,WAAa,CAAC;oBACnC,GAAG,QAAQ;oBACX,aAAa,SAAS,MAAM,CAAC,OAAO;gBACtC,CAAC;QACH,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEA,WAAW;IACX,SAAS,8HAAA,CAAA,qBAAkB,CACxB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAa,IAClD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC/C,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,GAAG,QAAQ;gBACX,aAAa,SAAS,MAAM,CAAC,OAAO;YACtC;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,sBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,aAAa;YACb,MAAM,mBAAmB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACvD,OAAO;oBACL,MAAM,MAAM,IAAI;oBAChB,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,kBAAkB;gBACpB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC5C,MAAM;oBACJ,GAAG,KAAK;oBACR,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,sBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;YAE9B,kBAAkB;YAClB,MAAM,mBAAmB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACvD,OAAO;oBACL;oBACA,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,kBAAkB;gBACrB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,qBAAqB;YACrB,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI,KAAK,iBAAiB,IAAI,EAAE;gBAChE,MAAM,oBAAoB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACxD,OAAO;wBACL,MAAM,WAAW,IAAI;wBACrB,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACnB,IAAI;4BAAE,KAAK;wBAAG;oBAChB;gBACF;gBAEA,IAAI,mBAAmB;oBACrB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;wBAClB,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;YAEA,MAAM,kBAAkB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACnD,OAAO;oBAAE;gBAAG;gBACZ,MAAM;YACR;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAa,IAClD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,kBAAkB;YAClB,MAAM,mBAAmB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACvD,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,IAAI,CAAC,kBAAkB;gBACrB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,cAAc;YACd,IAAI,iBAAiB,MAAM,CAAC,OAAO,GAAG,GAAG;gBACvC,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS,CAAC,OAAO,EAAE,iBAAiB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAChE;YACF;YAEA,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;YACxB;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,0BAA0B;IAC1B,aAAa,8HAAA,CAAA,qBAAkB,CAC5B,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAa,IAClD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,kBAAkB;YAClB,MAAM,mBAAmB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACvD,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,kBAAkB;gBACrB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,cAAc;YACd,MAAM,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC/B,mBAAmB;gBACnB,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;oBACzB,OAAO;wBACL,YAAY,MAAM,EAAE;wBACpB,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACrB;oBACA,MAAM;wBACJ,YAAY;oBACd;gBACF;gBAEA,SAAS;gBACT,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,MAAM,EAAE;oBAAC;gBACxB;YACF;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;AACJ", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/api/routers/prompts.ts"], "sourcesContent": ["import { z } from \"zod\";\r\nimport { createTRPCRouter, protectedProcedure } from \"~/server/api/trpc\";\r\nimport { TRPCError } from \"@trpc/server\";\r\n\r\nconst createPromptSchema = z.object({\r\n  title: z.string().min(1, \"标题不能为空\").max(200, \"标题不能超过200个字符\"),\r\n  content: z.string().min(1, \"内容不能为空\"),\r\n  description: z.string().optional(),\r\n  categoryId: z.string().uuid(\"分类ID格式不正确\").optional(),\r\n  tags: z.array(z.string().min(1, \"标签不能为空\")).optional(),\r\n  isFavorite: z.boolean().default(false),\r\n  isPublic: z.boolean().default(false),\r\n});\r\n\r\nconst updatePromptSchema = z.object({\r\n  id: z.string().uuid(\"提示词ID格式不正确\"),\r\n  title: z.string().min(1, \"标题不能为空\").max(200, \"标题不能超过200个字符\").optional(),\r\n  content: z.string().min(1, \"内容不能为空\").optional(),\r\n  description: z.string().optional(),\r\n  categoryId: z.string().uuid(\"分类ID格式不正确\").optional().nullable(),\r\n  tags: z.array(z.string().min(1, \"标签不能为空\")).optional(),\r\n  isFavorite: z.boolean().optional(),\r\n  isPublic: z.boolean().optional(),\r\n});\r\n\r\nconst searchPromptsSchema = z.object({\r\n  query: z.string().min(1, \"搜索关键词不能为空\").max(200, \"搜索关键词不能超过200个字符\"),\r\n  categoryId: z.string().uuid(\"分类ID格式不正确\").optional(),\r\n  tags: z.array(z.string()).optional(),\r\n  isFavorite: z.boolean().optional(),\r\n  limit: z.number().min(1).max(100).default(20),\r\n  offset: z.number().min(0).default(0),\r\n});\r\n\r\nconst getPromptsSchema = z.object({\r\n  categoryId: z.string().uuid(\"分类ID格式不正确\").optional(),\r\n  tags: z.array(z.string()).optional(),\r\n  isFavorite: z.boolean().optional(),\r\n  sortBy: z.enum([\"createdAt\", \"updatedAt\", \"usageCount\", \"title\"]).default(\"createdAt\"),\r\n  sortOrder: z.enum([\"asc\", \"desc\"]).default(\"desc\"),\r\n  limit: z.number().min(1).max(100).default(20),\r\n  offset: z.number().min(0).default(0),\r\n});\r\n\r\nexport const promptsRouter = createTRPCRouter({\r\n  // 获取提示词列表\r\n  getAll: protectedProcedure\r\n    .input(getPromptsSchema)\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const {\r\n          categoryId,\r\n          tags,\r\n          isFavorite,\r\n          sortBy,\r\n          sortOrder,\r\n          limit,\r\n          offset,\r\n        } = input;\r\n\r\n        const where: any = {\r\n          userId: ctx.user.id,\r\n        };\r\n\r\n        if (categoryId) {\r\n          where.categoryId = categoryId;\r\n        }\r\n\r\n        if (isFavorite !== undefined) {\r\n          where.isFavorite = isFavorite;\r\n        }\r\n\r\n        if (tags && tags.length > 0) {\r\n          where.promptTags = {\r\n            some: {\r\n              tag: {\r\n                name: { in: tags },\r\n                userId: ctx.user.id,\r\n              },\r\n            },\r\n          };\r\n        }\r\n\r\n        const orderBy: any = {};\r\n        orderBy[sortBy] = sortOrder;\r\n\r\n        const [prompts, total] = await Promise.all([\r\n          ctx.db.prompt.findMany({\r\n            where,\r\n            include: {\r\n              category: true,\r\n              promptTags: {\r\n                include: {\r\n                  tag: true,\r\n                },\r\n              },\r\n            },\r\n            orderBy,\r\n            skip: offset,\r\n            take: limit,\r\n          }),\r\n          ctx.db.prompt.count({ where }),\r\n        ]);\r\n\r\n        return {\r\n          prompts: prompts.map((prompt) => ({\r\n            ...prompt,\r\n            tags: prompt.promptTags.map((pt) => pt.tag),\r\n          })),\r\n          total,\r\n          hasMore: offset + limit < total,\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取提示词列表失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 根据ID获取提示词\r\n  getById: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"提示词ID格式不正确\") }))\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const prompt = await ctx.db.prompt.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n          include: {\r\n            category: true,\r\n            promptTags: {\r\n              include: {\r\n                tag: true,\r\n              },\r\n            },\r\n          },\r\n        });\r\n\r\n        if (!prompt) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"提示词不存在\",\r\n          });\r\n        }\r\n\r\n        return {\r\n          ...prompt,\r\n          tags: prompt.promptTags.map((pt) => pt.tag),\r\n        };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取提示词详情失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 搜索提示词\r\n  search: protectedProcedure\r\n    .input(searchPromptsSchema)\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const { query, categoryId, tags, isFavorite, limit, offset } = input;\r\n\r\n        const where: any = {\r\n          userId: ctx.user.id,\r\n          OR: [\r\n            { title: { contains: query, mode: \"insensitive\" } },\r\n            { content: { contains: query, mode: \"insensitive\" } },\r\n            { description: { contains: query, mode: \"insensitive\" } },\r\n          ],\r\n        };\r\n\r\n        if (categoryId) {\r\n          where.categoryId = categoryId;\r\n        }\r\n\r\n        if (isFavorite !== undefined) {\r\n          where.isFavorite = isFavorite;\r\n        }\r\n\r\n        if (tags && tags.length > 0) {\r\n          where.promptTags = {\r\n            some: {\r\n              tag: {\r\n                name: { in: tags },\r\n                userId: ctx.user.id,\r\n              },\r\n            },\r\n          };\r\n        }\r\n\r\n        const [prompts, total] = await Promise.all([\r\n          ctx.db.prompt.findMany({\r\n            where,\r\n            include: {\r\n              category: true,\r\n              promptTags: {\r\n                include: {\r\n                  tag: true,\r\n                },\r\n              },\r\n            },\r\n            orderBy: { updatedAt: \"desc\" },\r\n            skip: offset,\r\n            take: limit,\r\n          }),\r\n          ctx.db.prompt.count({ where }),\r\n        ]);\r\n\r\n        return {\r\n          prompts: prompts.map((prompt) => ({\r\n            ...prompt,\r\n            tags: prompt.promptTags.map((pt) => pt.tag),\r\n          })),\r\n          total,\r\n          hasMore: offset + limit < total,\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"搜索提示词失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 创建提示词\r\n  create: protectedProcedure\r\n    .input(createPromptSchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        const { tags, ...promptData } = input;\r\n\r\n        // 验证分类是否存在且属于当前用户\r\n        if (promptData.categoryId) {\r\n          const category = await ctx.db.category.findFirst({\r\n            where: {\r\n              id: promptData.categoryId,\r\n              userId: ctx.user.id,\r\n            },\r\n          });\r\n\r\n          if (!category) {\r\n            throw new TRPCError({\r\n              code: \"NOT_FOUND\",\r\n              message: \"分类不存在\",\r\n            });\r\n          }\r\n        }\r\n\r\n        const result = await ctx.db.$transaction(async (tx) => {\r\n          // 创建提示词\r\n          const prompt = await tx.prompt.create({\r\n            data: {\r\n              ...promptData,\r\n              userId: ctx.user.id,\r\n            },\r\n          });\r\n\r\n          // 处理标签\r\n          if (tags && tags.length > 0) {\r\n            const tagPromises = tags.map(async (tagName) => {\r\n              // 查找或创建标签\r\n              const existingTag = await tx.tag.findFirst({\r\n                where: {\r\n                  name: tagName,\r\n                  userId: ctx.user.id,\r\n                },\r\n              });\r\n\r\n              const tag = existingTag || await tx.tag.create({\r\n                data: {\r\n                  name: tagName,\r\n                  userId: ctx.user.id,\r\n                },\r\n              });\r\n\r\n              // 创建关联关系\r\n              await tx.promptTag.create({\r\n                data: {\r\n                  promptId: prompt.id,\r\n                  tagId: tag.id,\r\n                },\r\n              });\r\n\r\n              return tag;\r\n            });\r\n\r\n            await Promise.all(tagPromises);\r\n          }\r\n\r\n          return prompt;\r\n        });\r\n\r\n        return result;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"创建提示词失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 更新提示词\r\n  update: protectedProcedure\r\n    .input(updatePromptSchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        const { id, tags, ...updateData } = input;\r\n\r\n        // 检查提示词是否存在且属于当前用户\r\n        const existingPrompt = await ctx.db.prompt.findFirst({\r\n          where: {\r\n            id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingPrompt) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"提示词不存在\",\r\n          });\r\n        }\r\n\r\n        // 验证分类是否存在且属于当前用户\r\n        if (updateData.categoryId) {\r\n          const category = await ctx.db.category.findFirst({\r\n            where: {\r\n              id: updateData.categoryId,\r\n              userId: ctx.user.id,\r\n            },\r\n          });\r\n\r\n          if (!category) {\r\n            throw new TRPCError({\r\n              code: \"NOT_FOUND\",\r\n              message: \"分类不存在\",\r\n            });\r\n          }\r\n        }\r\n\r\n        const result = await ctx.db.$transaction(async (tx) => {\r\n          // 更新提示词\r\n          const updatedPrompt = await tx.prompt.update({\r\n            where: { id },\r\n            data: updateData,\r\n          });\r\n\r\n          // 处理标签更新\r\n          if (tags !== undefined) {\r\n            // 删除现有的标签关联\r\n            await tx.promptTag.deleteMany({\r\n              where: { promptId: id },\r\n            });\r\n\r\n            // 创建新的标签关联\r\n            if (tags.length > 0) {\r\n              const tagPromises = tags.map(async (tagName) => {\r\n                // 查找或创建标签\r\n                const existingTag = await tx.tag.findFirst({\r\n                  where: {\r\n                    name: tagName,\r\n                    userId: ctx.user.id,\r\n                  },\r\n                });\r\n\r\n                const tag = existingTag || await tx.tag.create({\r\n                  data: {\r\n                    name: tagName,\r\n                    userId: ctx.user.id,\r\n                  },\r\n                });\r\n\r\n                // 创建关联关系\r\n                await tx.promptTag.create({\r\n                  data: {\r\n                    promptId: id,\r\n                    tagId: tag.id,\r\n                  },\r\n                });\r\n\r\n                return tag;\r\n              });\r\n\r\n              await Promise.all(tagPromises);\r\n            }\r\n          }\r\n\r\n          return updatedPrompt;\r\n        });\r\n\r\n        return result;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"更新提示词失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 删除提示词\r\n  delete: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"提示词ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查提示词是否存在且属于当前用户\r\n        const existingPrompt = await ctx.db.prompt.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingPrompt) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"提示词不存在\",\r\n          });\r\n        }\r\n\r\n        await ctx.db.prompt.delete({\r\n          where: { id: input.id },\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"删除提示词失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 增加使用次数\r\n  incrementUsage: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"提示词ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查提示词是否存在且属于当前用户\r\n        const existingPrompt = await ctx.db.prompt.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingPrompt) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"提示词不存在\",\r\n          });\r\n        }\r\n\r\n        const updatedPrompt = await ctx.db.prompt.update({\r\n          where: { id: input.id },\r\n          data: {\r\n            usageCount: { increment: 1 },\r\n          },\r\n        });\r\n\r\n        return updatedPrompt;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"更新使用次数失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 批量导入提示词\r\n  bulkImport: protectedProcedure\r\n    .input(\r\n      z.object({\r\n        prompts: z.array(\r\n          z.object({\r\n            title: z.string().min(1, \"标题不能为空\").max(200, \"标题不能超过200个字符\"),\r\n            content: z.string().min(1, \"内容不能为空\"),\r\n            description: z.string().optional(),\r\n            categoryName: z.string().optional(),\r\n            tags: z.array(z.string()).optional(),\r\n          })\r\n        ),\r\n      })\r\n    )\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        const results = await ctx.db.$transaction(async (tx) => {\r\n          const importedPrompts = [];\r\n\r\n          for (const promptData of input.prompts) {\r\n            const { categoryName, tags, ...promptInfo } = promptData;\r\n\r\n            // 处理分类\r\n            let categoryId = null;\r\n            if (categoryName) {\r\n              let category = await tx.category.findFirst({\r\n                where: {\r\n                  name: categoryName,\r\n                  userId: ctx.user.id,\r\n                },\r\n              });\r\n\r\n              if (!category) {\r\n                category = await tx.category.create({\r\n                  data: {\r\n                    name: categoryName,\r\n                    userId: ctx.user.id,\r\n                  },\r\n                });\r\n              }\r\n\r\n              categoryId = category.id;\r\n            }\r\n\r\n            // 创建提示词\r\n            const prompt = await tx.prompt.create({\r\n              data: {\r\n                ...promptInfo,\r\n                categoryId,\r\n                userId: ctx.user.id,\r\n              },\r\n            });\r\n\r\n            // 处理标签\r\n            if (tags && tags.length > 0) {\r\n              const tagPromises = tags.map(async (tagName) => {\r\n                let tag = await tx.tag.findFirst({\r\n                  where: {\r\n                    name: tagName,\r\n                    userId: ctx.user.id,\r\n                  },\r\n                });\r\n\r\n                if (!tag) {\r\n                  tag = await tx.tag.create({\r\n                    data: {\r\n                      name: tagName,\r\n                      userId: ctx.user.id,\r\n                    },\r\n                  });\r\n                }\r\n\r\n                await tx.promptTag.create({\r\n                  data: {\r\n                    promptId: prompt.id,\r\n                    tagId: tag.id,\r\n                  },\r\n                });\r\n\r\n                return tag;\r\n              });\r\n\r\n              await Promise.all(tagPromises);\r\n            }\r\n\r\n            importedPrompts.push(prompt);\r\n          }\r\n\r\n          return importedPrompts;\r\n        });\r\n\r\n        return {\r\n          success: true,\r\n          importedCount: results.length,\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"批量导入提示词失败\",\r\n        });\r\n      }\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC5C,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;IACjD,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,QAAQ;IACnD,YAAY,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,UAAU,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAEA,MAAM,qBAAqB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IACpE,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,QAAQ;IAC7C,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ,GAAG,QAAQ;IAC5D,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,QAAQ;IACnD,YAAY,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,UAAU,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AAChC;AAEA,MAAM,sBAAsB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,KAAK;IAC/C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;IACjD,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAClC,YAAY,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;IAC1C,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;AACpC;AAEA,MAAM,mBAAmB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;IACjD,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAClC,YAAY,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,QAAQ,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAa;QAAc;KAAQ,EAAE,OAAO,CAAC;IAC1E,WAAW,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;IAC1C,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;AACpC;AAEO,MAAM,gBAAgB,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5C,UAAU;IACV,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,kBACN,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,EACJ,UAAU,EACV,IAAI,EACJ,UAAU,EACV,MAAM,EACN,SAAS,EACT,KAAK,EACL,MAAM,EACP,GAAG;YAEJ,MAAM,QAAa;gBACjB,QAAQ,IAAI,IAAI,CAAC,EAAE;YACrB;YAEA,IAAI,YAAY;gBACd,MAAM,UAAU,GAAG;YACrB;YAEA,IAAI,eAAe,WAAW;gBAC5B,MAAM,UAAU,GAAG;YACrB;YAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,MAAM,UAAU,GAAG;oBACjB,MAAM;wBACJ,KAAK;4BACH,MAAM;gCAAE,IAAI;4BAAK;4BACjB,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACrB;oBACF;gBACF;YACF;YAEA,MAAM,UAAe,CAAC;YACtB,OAAO,CAAC,OAAO,GAAG;YAElB,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzC,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;oBACrB;oBACA,SAAS;wBACP,UAAU;wBACV,YAAY;4BACV,SAAS;gCACP,KAAK;4BACP;wBACF;oBACF;oBACA;oBACA,MAAM;oBACN,MAAM;gBACR;gBACA,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;oBAAE;gBAAM;aAC7B;YAED,OAAO;gBACL,SAAS,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;wBAChC,GAAG,MAAM;wBACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,KAAO,GAAG,GAAG;oBAC5C,CAAC;gBACD;gBACA,SAAS,SAAS,QAAQ;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,YAAY;IACZ,SAAS,8HAAA,CAAA,qBAAkB,CACxB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAc,IACnD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC3C,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,UAAU;oBACV,YAAY;wBACV,SAAS;4BACP,KAAK;wBACP;oBACF;gBACF;YACF;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,GAAG,MAAM;gBACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,KAAO,GAAG,GAAG;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,qBACN,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YAE/D,MAAM,QAAa;gBACjB,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACnB,IAAI;oBACF;wBAAE,OAAO;4BAAE,UAAU;4BAAO,MAAM;wBAAc;oBAAE;oBAClD;wBAAE,SAAS;4BAAE,UAAU;4BAAO,MAAM;wBAAc;oBAAE;oBACpD;wBAAE,aAAa;4BAAE,UAAU;4BAAO,MAAM;wBAAc;oBAAE;iBACzD;YACH;YAEA,IAAI,YAAY;gBACd,MAAM,UAAU,GAAG;YACrB;YAEA,IAAI,eAAe,WAAW;gBAC5B,MAAM,UAAU,GAAG;YACrB;YAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,MAAM,UAAU,GAAG;oBACjB,MAAM;wBACJ,KAAK;4BACH,MAAM;gCAAE,IAAI;4BAAK;4BACjB,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACrB;oBACF;gBACF;YACF;YAEA,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzC,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;oBACrB;oBACA,SAAS;wBACP,UAAU;wBACV,YAAY;4BACV,SAAS;gCACP,KAAK;4BACP;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAO;oBAC7B,MAAM;oBACN,MAAM;gBACR;gBACA,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;oBAAE;gBAAM;aAC7B;YAED,OAAO;gBACL,SAAS,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;wBAChC,GAAG,MAAM;wBACT,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,KAAO,GAAG,GAAG;oBAC5C,CAAC;gBACD;gBACA,SAAS,SAAS,QAAQ;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG;YAEhC,kBAAkB;YAClB,IAAI,WAAW,UAAU,EAAE;gBACzB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAC/C,OAAO;wBACL,IAAI,WAAW,UAAU;wBACzB,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACrB;gBACF;gBAEA,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;wBAClB,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC9C,QAAQ;gBACR,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBACpC,MAAM;wBACJ,GAAG,UAAU;wBACb,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACrB;gBACF;gBAEA,OAAO;gBACP,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;oBAC3B,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO;wBAClC,UAAU;wBACV,MAAM,cAAc,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC;4BACzC,OAAO;gCACL,MAAM;gCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;4BACrB;wBACF;wBAEA,MAAM,MAAM,eAAe,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;4BAC7C,MAAM;gCACJ,MAAM;gCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;4BACrB;wBACF;wBAEA,SAAS;wBACT,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;4BACxB,MAAM;gCACJ,UAAU,OAAO,EAAE;gCACnB,OAAO,IAAI,EAAE;4BACf;wBACF;wBAEA,OAAO;oBACT;oBAEA,MAAM,QAAQ,GAAG,CAAC;gBACpB;gBAEA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG;YAEpC,mBAAmB;YACnB,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBACnD,OAAO;oBACL;oBACA,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,gBAAgB;gBACnB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,kBAAkB;YAClB,IAAI,WAAW,UAAU,EAAE;gBACzB,MAAM,WAAW,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAC/C,OAAO;wBACL,IAAI,WAAW,UAAU;wBACzB,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACrB;gBACF;gBAEA,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;wBAClB,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC9C,QAAQ;gBACR,MAAM,gBAAgB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC3C,OAAO;wBAAE;oBAAG;oBACZ,MAAM;gBACR;gBAEA,SAAS;gBACT,IAAI,SAAS,WAAW;oBACtB,YAAY;oBACZ,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;wBAC5B,OAAO;4BAAE,UAAU;wBAAG;oBACxB;oBAEA,WAAW;oBACX,IAAI,KAAK,MAAM,GAAG,GAAG;wBACnB,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO;4BAClC,UAAU;4BACV,MAAM,cAAc,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC;gCACzC,OAAO;oCACL,MAAM;oCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;gCACrB;4BACF;4BAEA,MAAM,MAAM,eAAe,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;gCAC7C,MAAM;oCACJ,MAAM;oCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;gCACrB;4BACF;4BAEA,SAAS;4BACT,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gCACxB,MAAM;oCACJ,UAAU;oCACV,OAAO,IAAI,EAAE;gCACf;4BACF;4BAEA,OAAO;wBACT;wBAEA,MAAM,QAAQ,GAAG,CAAC;oBACpB;gBACF;gBAEA,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,QAAQ;IACR,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAc,IACnD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,mBAAmB;YACnB,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBACnD,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,gBAAgB;gBACnB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;gBACzB,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;YACxB;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,gBAAgB,8HAAA,CAAA,qBAAkB,CAC/B,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAc,IACnD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,mBAAmB;YACnB,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;gBACnD,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,gBAAgB;gBACnB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,MAAM,gBAAgB,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC/C,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;gBACtB,MAAM;oBACJ,YAAY;wBAAE,WAAW;oBAAE;gBAC7B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,UAAU;IACV,YAAY,8HAAA,CAAA,qBAAkB,CAC3B,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,SAAS,oKAAA,CAAA,IAAC,CAAC,KAAK,CACd,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;YAC5C,SAAS,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC3B,aAAa,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAChC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACjC,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;QACpC;IAEJ,IAED,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC/C,MAAM,kBAAkB,EAAE;gBAE1B,KAAK,MAAM,cAAc,MAAM,OAAO,CAAE;oBACtC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,YAAY,GAAG;oBAE9C,OAAO;oBACP,IAAI,aAAa;oBACjB,IAAI,cAAc;wBAChB,IAAI,WAAW,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC;4BACzC,OAAO;gCACL,MAAM;gCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;4BACrB;wBACF;wBAEA,IAAI,CAAC,UAAU;4BACb,WAAW,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;gCAClC,MAAM;oCACJ,MAAM;oCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;gCACrB;4BACF;wBACF;wBAEA,aAAa,SAAS,EAAE;oBAC1B;oBAEA,QAAQ;oBACR,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;wBACpC,MAAM;4BACJ,GAAG,UAAU;4BACb;4BACA,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACrB;oBACF;oBAEA,OAAO;oBACP,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;wBAC3B,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO;4BAClC,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC;gCAC/B,OAAO;oCACL,MAAM;oCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;gCACrB;4BACF;4BAEA,IAAI,CAAC,KAAK;gCACR,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;oCACxB,MAAM;wCACJ,MAAM;wCACN,QAAQ,IAAI,IAAI,CAAC,EAAE;oCACrB;gCACF;4BACF;4BAEA,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gCACxB,MAAM;oCACJ,UAAU,OAAO,EAAE;oCACnB,OAAO,IAAI,EAAE;gCACf;4BACF;4BAEA,OAAO;wBACT;wBAEA,MAAM,QAAQ,GAAG,CAAC;oBACpB;oBAEA,gBAAgB,IAAI,CAAC;gBACvB;gBAEA,OAAO;YACT;YAEA,OAAO;gBACL,SAAS;gBACT,eAAe,QAAQ,MAAM;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;AACJ", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/api/routers/tags.ts"], "sourcesContent": ["import { z } from \"zod\";\r\nimport { createTRPCRouter, protectedProcedure } from \"~/server/api/trpc\";\r\nimport { TRPCError } from \"@trpc/server\";\r\n\r\nconst createTagSchema = z.object({\r\n  name: z.string().min(1, \"标签名称不能为空\").max(50, \"标签名称不能超过50个字符\"),\r\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").default(\"#6B7280\"),\r\n});\r\n\r\nconst updateTagSchema = z.object({\r\n  id: z.string().uuid(\"标签ID格式不正确\"),\r\n  name: z.string().min(1, \"标签名称不能为空\").max(50, \"标签名称不能超过50个字符\").optional(),\r\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").optional(),\r\n});\r\n\r\nexport const tagsRouter = createTRPCRouter({\r\n  // 获取用户的所有标签\r\n  getAll: protectedProcedure.query(async ({ ctx }) => {\r\n    try {\r\n      const tags = await ctx.db.tag.findMany({\r\n        where: {\r\n          userId: ctx.user.id,\r\n        },\r\n        include: {\r\n          _count: {\r\n            select: {\r\n              promptTags: true,\r\n            },\r\n          },\r\n        },\r\n        orderBy: {\r\n          createdAt: \"desc\",\r\n        },\r\n      });\r\n\r\n      return tags.map((tag) => ({\r\n        ...tag,\r\n        promptCount: tag._count.promptTags,\r\n      }));\r\n    } catch (error) {\r\n      throw new TRPCError({\r\n        code: \"INTERNAL_SERVER_ERROR\",\r\n        message: \"获取标签列表失败\",\r\n      });\r\n    }\r\n  }),\r\n\r\n  // 根据ID获取标签\r\n  getById: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"标签ID格式不正确\") }))\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const tag = await ctx.db.tag.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n          include: {\r\n            _count: {\r\n              select: {\r\n                promptTags: true,\r\n              },\r\n            },\r\n          },\r\n        });\r\n\r\n        if (!tag) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"标签不存在\",\r\n          });\r\n        }\r\n\r\n        return {\r\n          ...tag,\r\n          promptCount: tag._count.promptTags,\r\n        };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取标签详情失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 根据名称搜索标签\r\n  search: protectedProcedure\r\n    .input(\r\n      z.object({\r\n        query: z.string().min(1, \"搜索关键词不能为空\").max(50, \"搜索关键词不能超过50个字符\"),\r\n        limit: z.number().min(1).max(50).default(10),\r\n      })\r\n    )\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const tags = await ctx.db.tag.findMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n            name: {\r\n              contains: input.query,\r\n              mode: \"insensitive\",\r\n            },\r\n          },\r\n          include: {\r\n            _count: {\r\n              select: {\r\n                promptTags: true,\r\n              },\r\n            },\r\n          },\r\n          take: input.limit,\r\n          orderBy: {\r\n            createdAt: \"desc\",\r\n          },\r\n        });\r\n\r\n        return tags.map((tag) => ({\r\n          ...tag,\r\n          promptCount: tag._count.promptTags,\r\n        }));\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"搜索标签失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 创建标签\r\n  create: protectedProcedure\r\n    .input(createTagSchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查同名标签是否存在\r\n        const existingTag = await ctx.db.tag.findFirst({\r\n          where: {\r\n            name: input.name,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (existingTag) {\r\n          throw new TRPCError({\r\n            code: \"CONFLICT\",\r\n            message: \"同名标签已存在\",\r\n          });\r\n        }\r\n\r\n        const tag = await ctx.db.tag.create({\r\n          data: {\r\n            ...input,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        return tag;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"创建标签失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 更新标签\r\n  update: protectedProcedure\r\n    .input(updateTagSchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        const { id, ...updateData } = input;\r\n\r\n        // 检查标签是否存在且属于当前用户\r\n        const existingTag = await ctx.db.tag.findFirst({\r\n          where: {\r\n            id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingTag) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"标签不存在\",\r\n          });\r\n        }\r\n\r\n        // 如果更新名称，检查是否与其他标签重名\r\n        if (updateData.name && updateData.name !== existingTag.name) {\r\n          const duplicateTag = await ctx.db.tag.findFirst({\r\n            where: {\r\n              name: updateData.name,\r\n              userId: ctx.user.id,\r\n              id: { not: id },\r\n            },\r\n          });\r\n\r\n          if (duplicateTag) {\r\n            throw new TRPCError({\r\n              code: \"CONFLICT\",\r\n              message: \"同名标签已存在\",\r\n            });\r\n          }\r\n        }\r\n\r\n        const updatedTag = await ctx.db.tag.update({\r\n          where: { id },\r\n          data: updateData,\r\n        });\r\n\r\n        return updatedTag;\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"更新标签失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 删除标签\r\n  delete: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"标签ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查标签是否存在且属于当前用户\r\n        const existingTag = await ctx.db.tag.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n          include: {\r\n            _count: {\r\n              select: {\r\n                promptTags: true,\r\n              },\r\n            },\r\n          },\r\n        });\r\n\r\n        if (!existingTag) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"标签不存在\",\r\n          });\r\n        }\r\n\r\n        // 检查是否有关联的提示词\r\n        if (existingTag._count.promptTags > 0) {\r\n          throw new TRPCError({\r\n            code: \"PRECONDITION_FAILED\",\r\n            message: `该标签关联了 ${existingTag._count.promptTags} 个提示词，无法删除`,\r\n          });\r\n        }\r\n\r\n        await ctx.db.tag.delete({\r\n          where: { id: input.id },\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"删除标签失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 强制删除标签（同时删除所有关联关系）\r\n  forceDelete: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"标签ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查标签是否存在且属于当前用户\r\n        const existingTag = await ctx.db.tag.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingTag) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"标签不存在\",\r\n          });\r\n        }\r\n\r\n        // 使用事务确保数据一致性\r\n        await ctx.db.$transaction(async (tx) => {\r\n          // 先删除所有关联关系\r\n          await tx.promptTag.deleteMany({\r\n            where: {\r\n              tagId: input.id,\r\n            },\r\n          });\r\n\r\n          // 然后删除标签\r\n          await tx.tag.delete({\r\n            where: { id: input.id },\r\n          });\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"强制删除标签失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 获取热门标签\r\n  getPopular: protectedProcedure\r\n    .input(\r\n      z.object({\r\n        limit: z.number().min(1).max(50).default(10),\r\n      })\r\n    )\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const tags = await ctx.db.tag.findMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n          },\r\n          include: {\r\n            _count: {\r\n              select: {\r\n                promptTags: true,\r\n              },\r\n            },\r\n          },\r\n          orderBy: {\r\n            promptTags: {\r\n              _count: \"desc\",\r\n            },\r\n          },\r\n          take: input.limit,\r\n        });\r\n\r\n        return tags\r\n          .filter((tag) => tag._count.promptTags > 0)\r\n          .map((tag) => ({\r\n            ...tag,\r\n            promptCount: tag._count.promptTags,\r\n          }));\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取热门标签失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 批量创建标签\r\n  bulkCreate: protectedProcedure\r\n    .input(\r\n      z.object({\r\n        tags: z.array(\r\n          z.object({\r\n            name: z.string().min(1, \"标签名称不能为空\").max(50, \"标签名称不能超过50个字符\"),\r\n            color: z.string().regex(/^#[0-9A-F]{6}$/i, \"颜色格式不正确\").default(\"#6B7280\"),\r\n          })\r\n        ),\r\n      })\r\n    )\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        const results = await ctx.db.$transaction(async (tx) => {\r\n          const createdTags = [];\r\n          const skippedTags = [];\r\n\r\n          for (const tagData of input.tags) {\r\n            // 检查是否已存在\r\n            const existingTag = await tx.tag.findFirst({\r\n              where: {\r\n                name: tagData.name,\r\n                userId: ctx.user.id,\r\n              },\r\n            });\r\n\r\n            if (existingTag) {\r\n              skippedTags.push({ name: tagData.name, reason: \"已存在\" });\r\n              continue;\r\n            }\r\n\r\n            // 创建标签\r\n            const tag = await tx.tag.create({\r\n              data: {\r\n                ...tagData,\r\n                userId: ctx.user.id,\r\n              },\r\n            });\r\n\r\n            createdTags.push(tag);\r\n          }\r\n\r\n          return { createdTags, skippedTags };\r\n        });\r\n\r\n        return {\r\n          success: true,\r\n          createdCount: results.createdTags.length,\r\n          skippedCount: results.skippedTags.length,\r\n          createdTags: results.createdTags,\r\n          skippedTags: results.skippedTags,\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"批量创建标签失败\",\r\n        });\r\n      }\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,kBAAkB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,IAAI;IAC5C,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,OAAO,CAAC;AAChE;AAEA,MAAM,kBAAkB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,IAAI,iBAAiB,QAAQ;IACrE,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,QAAQ;AAChE;AAEO,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,YAAY;IACZ,QAAQ,8HAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QAC7C,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACrC,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,YAAY;wBACd;oBACF;gBACF;gBACA,SAAS;oBACP,WAAW;gBACb;YACF;YAEA,OAAO,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;oBACxB,GAAG,GAAG;oBACN,aAAa,IAAI,MAAM,CAAC,UAAU;gBACpC,CAAC;QACH,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEA,WAAW;IACX,SAAS,8HAAA,CAAA,qBAAkB,CACxB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAa,IAClD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,MAAM,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;gBACrC,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,IAAI,CAAC,KAAK;gBACR,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,GAAG,GAAG;gBACN,aAAa,IAAI,MAAM,CAAC,UAAU;YACpC;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,WAAW;IACX,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,IAAI;QAC9C,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAC3C,IAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACrC,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACnB,MAAM;wBACJ,UAAU,MAAM,KAAK;wBACrB,MAAM;oBACR;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,YAAY;wBACd;oBACF;gBACF;gBACA,MAAM,MAAM,KAAK;gBACjB,SAAS;oBACP,WAAW;gBACb;YACF;YAEA,OAAO,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;oBACxB,GAAG,GAAG;oBACN,aAAa,IAAI,MAAM,CAAC,UAAU;gBACpC,CAAC;QACH,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,iBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,aAAa;YACb,MAAM,cAAc,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;gBAC7C,OAAO;oBACL,MAAM,MAAM,IAAI;oBAChB,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,aAAa;gBACf,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,MAAM,MAAM,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;gBAClC,MAAM;oBACJ,GAAG,KAAK;oBACR,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,iBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;YAE9B,kBAAkB;YAClB,MAAM,cAAc,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;gBAC7C,OAAO;oBACL;oBACA,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,qBAAqB;YACrB,IAAI,WAAW,IAAI,IAAI,WAAW,IAAI,KAAK,YAAY,IAAI,EAAE;gBAC3D,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9C,OAAO;wBACL,MAAM,WAAW,IAAI;wBACrB,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACnB,IAAI;4BAAE,KAAK;wBAAG;oBAChB;gBACF;gBAEA,IAAI,cAAc;oBAChB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;wBAClB,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;YAEA,MAAM,aAAa,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzC,OAAO;oBAAE;gBAAG;gBACZ,MAAM;YACR;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,OAAO;IACP,QAAQ,8HAAA,CAAA,qBAAkB,CACvB,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAa,IAClD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,kBAAkB;YAClB,MAAM,cAAc,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;gBAC7C,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,cAAc;YACd,IAAI,YAAY,MAAM,CAAC,UAAU,GAAG,GAAG;gBACrC,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS,CAAC,OAAO,EAAE,YAAY,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;gBAC9D;YACF;YAEA,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;YACxB;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,qBAAqB;IACrB,aAAa,8HAAA,CAAA,qBAAkB,CAC5B,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAa,IAClD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,kBAAkB;YAClB,MAAM,cAAc,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC;gBAC7C,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,cAAc;YACd,MAAM,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC/B,YAAY;gBACZ,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;oBAC5B,OAAO;wBACL,OAAO,MAAM,EAAE;oBACjB;gBACF;gBAEA,SAAS;gBACT,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;oBAClB,OAAO;wBAAE,IAAI,MAAM,EAAE;oBAAC;gBACxB;YACF;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,YAAY,8HAAA,CAAA,qBAAkB,CAC3B,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAC3C,IAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACrC,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,YAAY;wBACd;oBACF;gBACF;gBACA,SAAS;oBACP,YAAY;wBACV,QAAQ;oBACV;gBACF;gBACA,MAAM,MAAM,KAAK;YACnB;YAEA,OAAO,KACJ,MAAM,CAAC,CAAC,MAAQ,IAAI,MAAM,CAAC,UAAU,GAAG,GACxC,GAAG,CAAC,CAAC,MAAQ,CAAC;oBACb,GAAG,GAAG;oBACN,aAAa,IAAI,MAAM,CAAC,UAAU;gBACpC,CAAC;QACL,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,YAAY,8HAAA,CAAA,qBAAkB,CAC3B,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CACX,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,MAAM,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,IAAI;YAC5C,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB,WAAW,OAAO,CAAC;QAChE;IAEJ,IAED,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC/C,MAAM,cAAc,EAAE;gBACtB,MAAM,cAAc,EAAE;gBAEtB,KAAK,MAAM,WAAW,MAAM,IAAI,CAAE;oBAChC,UAAU;oBACV,MAAM,cAAc,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC;wBACzC,OAAO;4BACL,MAAM,QAAQ,IAAI;4BAClB,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACrB;oBACF;oBAEA,IAAI,aAAa;wBACf,YAAY,IAAI,CAAC;4BAAE,MAAM,QAAQ,IAAI;4BAAE,QAAQ;wBAAM;wBACrD;oBACF;oBAEA,OAAO;oBACP,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;wBAC9B,MAAM;4BACJ,GAAG,OAAO;4BACV,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACrB;oBACF;oBAEA,YAAY,IAAI,CAAC;gBACnB;gBAEA,OAAO;oBAAE;oBAAa;gBAAY;YACpC;YAEA,OAAO;gBACL,SAAS;gBACT,cAAc,QAAQ,WAAW,CAAC,MAAM;gBACxC,cAAc,QAAQ,WAAW,CAAC,MAAM;gBACxC,aAAa,QAAQ,WAAW;gBAChC,aAAa,QAAQ,WAAW;YAClC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;AACJ", "debugId": null}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/api/routers/search.ts"], "sourcesContent": ["import { z } from \"zod\";\r\nimport { createTRPCRouter, protectedProcedure } from \"~/server/api/trpc\";\r\nimport { TRPCError } from \"@trpc/server\";\r\n\r\nconst searchSchema = z.object({\r\n  query: z.string().min(1, \"搜索关键词不能为空\").max(200, \"搜索关键词不能超过200个字符\"),\r\n  categoryId: z.string().uuid(\"分类ID格式不正确\").optional(),\r\n  tags: z.array(z.string()).optional(),\r\n  isFavorite: z.boolean().optional(),\r\n  limit: z.number().min(1).max(100).default(20),\r\n  offset: z.number().min(0).default(0),\r\n});\r\n\r\nconst addSearchHistorySchema = z.object({\r\n  query: z.string().min(1, \"搜索关键词不能为空\").max(200, \"搜索关键词不能超过200个字符\"),\r\n});\r\n\r\nexport const searchRouter = createTRPCRouter({\r\n  // 全文搜索提示词\r\n  searchPrompts: protectedProcedure\r\n    .input(searchSchema)\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const { query, categoryId, tags, isFavorite, limit, offset } = input;\r\n\r\n        // 构建搜索条件\r\n        const where: any = {\r\n          userId: ctx.user.id,\r\n          OR: [\r\n            { title: { contains: query, mode: \"insensitive\" } },\r\n            { content: { contains: query, mode: \"insensitive\" } },\r\n            { description: { contains: query, mode: \"insensitive\" } },\r\n          ],\r\n        };\r\n\r\n        if (categoryId) {\r\n          where.categoryId = categoryId;\r\n        }\r\n\r\n        if (isFavorite !== undefined) {\r\n          where.isFavorite = isFavorite;\r\n        }\r\n\r\n        if (tags && tags.length > 0) {\r\n          where.promptTags = {\r\n            some: {\r\n              tag: {\r\n                name: { in: tags },\r\n                userId: ctx.user.id,\r\n              },\r\n            },\r\n          };\r\n        }\r\n\r\n        // 执行搜索\r\n        const [prompts, total] = await Promise.all([\r\n          ctx.db.prompt.findMany({\r\n            where,\r\n            include: {\r\n              category: true,\r\n              promptTags: {\r\n                include: {\r\n                  tag: true,\r\n                },\r\n              },\r\n            },\r\n            orderBy: [\r\n              // 优先显示标题匹配的结果\r\n              {\r\n                title: {\r\n                  _relevance: {\r\n                    fields: [\"title\"],\r\n                    search: query,\r\n                    sort: \"desc\",\r\n                  },\r\n                },\r\n              },\r\n              // 然后按更新时间排序\r\n              { updatedAt: \"desc\" },\r\n            ],\r\n            skip: offset,\r\n            take: limit,\r\n          }),\r\n          ctx.db.prompt.count({ where }),\r\n        ]);\r\n\r\n        // 生成搜索结果高亮\r\n        const processedPrompts = prompts.map((prompt) => {\r\n          const highlightText = (text: string, query: string) => {\r\n            if (!text) return text;\r\n            const regex = new RegExp(`(${query})`, \"gi\");\r\n            return text.replace(regex, \"<mark>$1</mark>\");\r\n          };\r\n\r\n          return {\r\n            ...prompt,\r\n            title: highlightText(prompt.title, query),\r\n            content: highlightText(prompt.content.slice(0, 200), query),\r\n            description: prompt.description \r\n              ? highlightText(prompt.description, query) \r\n              : null,\r\n            tags: prompt.promptTags.map((pt) => pt.tag),\r\n          };\r\n        });\r\n\r\n        return {\r\n          prompts: processedPrompts,\r\n          total,\r\n          hasMore: offset + limit < total,\r\n          query,\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"搜索提示词失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 智能搜索建议\r\n  getSuggestions: protectedProcedure\r\n    .input(\r\n      z.object({\r\n        query: z.string().min(1).max(200),\r\n        limit: z.number().min(1).max(10).default(5),\r\n      })\r\n    )\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const { query, limit } = input;\r\n\r\n        // 搜索相关的提示词标题\r\n        const titleSuggestions = await ctx.db.prompt.findMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n            title: {\r\n              contains: query,\r\n              mode: \"insensitive\",\r\n            },\r\n          },\r\n          select: {\r\n            title: true,\r\n          },\r\n          take: limit,\r\n          orderBy: {\r\n            usageCount: \"desc\",\r\n          },\r\n        });\r\n\r\n        // 搜索相关的标签\r\n        const tagSuggestions = await ctx.db.tag.findMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n            name: {\r\n              contains: query,\r\n              mode: \"insensitive\",\r\n            },\r\n          },\r\n          select: {\r\n            name: true,\r\n          },\r\n          take: limit,\r\n          orderBy: {\r\n            promptTags: {\r\n              _count: \"desc\",\r\n            },\r\n          },\r\n        });\r\n\r\n        // 搜索相关的分类\r\n        const categorySuggestions = await ctx.db.category.findMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n            name: {\r\n              contains: query,\r\n              mode: \"insensitive\",\r\n            },\r\n          },\r\n          select: {\r\n            name: true,\r\n          },\r\n          take: limit,\r\n          orderBy: {\r\n            prompts: {\r\n              _count: \"desc\",\r\n            },\r\n          },\r\n        });\r\n\r\n        return {\r\n          titles: titleSuggestions.map((p) => p.title),\r\n          tags: tagSuggestions.map((t) => t.name),\r\n          categories: categorySuggestions.map((c) => c.name),\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取搜索建议失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 添加搜索历史\r\n  addSearchHistory: protectedProcedure\r\n    .input(addSearchHistorySchema)\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查是否已存在相同的搜索记录\r\n        const existingRecord = await ctx.db.searchHistory.findFirst({\r\n          where: {\r\n            query: input.query,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (existingRecord) {\r\n          // 如果已存在，更新时间\r\n          await ctx.db.searchHistory.update({\r\n            where: { id: existingRecord.id },\r\n            data: { createdAt: new Date() },\r\n          });\r\n          return existingRecord;\r\n        }\r\n\r\n        // 创建新的搜索记录\r\n        const searchRecord = await ctx.db.searchHistory.create({\r\n          data: {\r\n            query: input.query,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        // 保持搜索历史记录数量（最多100条）\r\n        const historyCount = await ctx.db.searchHistory.count({\r\n          where: { userId: ctx.user.id },\r\n        });\r\n\r\n        if (historyCount > 100) {\r\n          // 删除最老的记录\r\n          const oldestRecords = await ctx.db.searchHistory.findMany({\r\n            where: { userId: ctx.user.id },\r\n            orderBy: { createdAt: \"asc\" },\r\n            take: historyCount - 100,\r\n          });\r\n\r\n          await ctx.db.searchHistory.deleteMany({\r\n            where: {\r\n              id: { in: oldestRecords.map((r) => r.id) },\r\n            },\r\n          });\r\n        }\r\n\r\n        return searchRecord;\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"添加搜索历史失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 获取搜索历史\r\n  getSearchHistory: protectedProcedure\r\n    .input(\r\n      z.object({\r\n        limit: z.number().min(1).max(50).default(20),\r\n      })\r\n    )\r\n    .query(async ({ ctx, input }) => {\r\n      try {\r\n        const history = await ctx.db.searchHistory.findMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n          },\r\n          orderBy: {\r\n            createdAt: \"desc\",\r\n          },\r\n          take: input.limit,\r\n        });\r\n\r\n        return history;\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取搜索历史失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 清空搜索历史\r\n  clearSearchHistory: protectedProcedure\r\n    .mutation(async ({ ctx }) => {\r\n      try {\r\n        await ctx.db.searchHistory.deleteMany({\r\n          where: {\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"清空搜索历史失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 删除单个搜索历史记录\r\n  deleteSearchHistory: protectedProcedure\r\n    .input(z.object({ id: z.string().uuid(\"搜索历史ID格式不正确\") }))\r\n    .mutation(async ({ ctx, input }) => {\r\n      try {\r\n        // 检查记录是否存在且属于当前用户\r\n        const existingRecord = await ctx.db.searchHistory.findFirst({\r\n          where: {\r\n            id: input.id,\r\n            userId: ctx.user.id,\r\n          },\r\n        });\r\n\r\n        if (!existingRecord) {\r\n          throw new TRPCError({\r\n            code: \"NOT_FOUND\",\r\n            message: \"搜索历史记录不存在\",\r\n          });\r\n        }\r\n\r\n        await ctx.db.searchHistory.delete({\r\n          where: { id: input.id },\r\n        });\r\n\r\n        return { success: true };\r\n      } catch (error) {\r\n        if (error instanceof TRPCError) {\r\n          throw error;\r\n        }\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"删除搜索历史失败\",\r\n        });\r\n      }\r\n    }),\r\n\r\n  // 获取搜索统计\r\n  getSearchStats: protectedProcedure\r\n    .query(async ({ ctx }) => {\r\n      try {\r\n        const [\r\n          totalPrompts,\r\n          totalCategories,\r\n          totalTags,\r\n          totalSearches,\r\n          recentSearches,\r\n          popularTags,\r\n        ] = await Promise.all([\r\n          // 总提示词数\r\n          ctx.db.prompt.count({\r\n            where: { userId: ctx.user.id },\r\n          }),\r\n          // 总分类数\r\n          ctx.db.category.count({\r\n            where: { userId: ctx.user.id },\r\n          }),\r\n          // 总标签数\r\n          ctx.db.tag.count({\r\n            where: { userId: ctx.user.id },\r\n          }),\r\n          // 总搜索次数\r\n          ctx.db.searchHistory.count({\r\n            where: { userId: ctx.user.id },\r\n          }),\r\n          // 最近7天的搜索次数\r\n          ctx.db.searchHistory.count({\r\n            where: {\r\n              userId: ctx.user.id,\r\n              createdAt: {\r\n                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\r\n              },\r\n            },\r\n          }),\r\n          // 最受欢迎的标签\r\n          ctx.db.tag.findMany({\r\n            where: { userId: ctx.user.id },\r\n            include: {\r\n              _count: {\r\n                select: {\r\n                  promptTags: true,\r\n                },\r\n              },\r\n            },\r\n            orderBy: {\r\n              promptTags: {\r\n                _count: \"desc\",\r\n              },\r\n            },\r\n            take: 5,\r\n          }),\r\n        ]);\r\n\r\n        return {\r\n          totalPrompts,\r\n          totalCategories,\r\n          totalTags,\r\n          totalSearches,\r\n          recentSearches,\r\n          popularTags: popularTags.map((tag) => ({\r\n            name: tag.name,\r\n            count: tag._count.promptTags,\r\n          })),\r\n        };\r\n      } catch (error) {\r\n        throw new TRPCError({\r\n          code: \"INTERNAL_SERVER_ERROR\",\r\n          message: \"获取搜索统计失败\",\r\n        });\r\n      }\r\n    }),\r\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,eAAe,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,KAAK;IAC/C,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,QAAQ;IACjD,MAAM,oKAAA,CAAA,IAAC,CAAC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IAClC,YAAY,oKAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,OAAO,CAAC;IAC1C,QAAQ,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;AACpC;AAEA,MAAM,yBAAyB,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,KAAK;AACjD;AAEO,MAAM,eAAe,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IAC3C,UAAU;IACV,eAAe,8HAAA,CAAA,qBAAkB,CAC9B,KAAK,CAAC,cACN,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YAE/D,SAAS;YACT,MAAM,QAAa;gBACjB,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACnB,IAAI;oBACF;wBAAE,OAAO;4BAAE,UAAU;4BAAO,MAAM;wBAAc;oBAAE;oBAClD;wBAAE,SAAS;4BAAE,UAAU;4BAAO,MAAM;wBAAc;oBAAE;oBACpD;wBAAE,aAAa;4BAAE,UAAU;4BAAO,MAAM;wBAAc;oBAAE;iBACzD;YACH;YAEA,IAAI,YAAY;gBACd,MAAM,UAAU,GAAG;YACrB;YAEA,IAAI,eAAe,WAAW;gBAC5B,MAAM,UAAU,GAAG;YACrB;YAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,MAAM,UAAU,GAAG;oBACjB,MAAM;wBACJ,KAAK;4BACH,MAAM;gCAAE,IAAI;4BAAK;4BACjB,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACrB;oBACF;gBACF;YACF;YAEA,OAAO;YACP,MAAM,CAAC,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzC,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;oBACrB;oBACA,SAAS;wBACP,UAAU;wBACV,YAAY;4BACV,SAAS;gCACP,KAAK;4BACP;wBACF;oBACF;oBACA,SAAS;wBACP,cAAc;wBACd;4BACE,OAAO;gCACL,YAAY;oCACV,QAAQ;wCAAC;qCAAQ;oCACjB,QAAQ;oCACR,MAAM;gCACR;4BACF;wBACF;wBACA,YAAY;wBACZ;4BAAE,WAAW;wBAAO;qBACrB;oBACD,MAAM;oBACN,MAAM;gBACR;gBACA,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;oBAAE;gBAAM;aAC7B;YAED,WAAW;YACX,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;gBACpC,MAAM,gBAAgB,CAAC,MAAc;oBACnC,IAAI,CAAC,MAAM,OAAO;oBAClB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;oBACvC,OAAO,KAAK,OAAO,CAAC,OAAO;gBAC7B;gBAEA,OAAO;oBACL,GAAG,MAAM;oBACT,OAAO,cAAc,OAAO,KAAK,EAAE;oBACnC,SAAS,cAAc,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM;oBACrD,aAAa,OAAO,WAAW,GAC3B,cAAc,OAAO,WAAW,EAAE,SAClC;oBACJ,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,KAAO,GAAG,GAAG;gBAC5C;YACF;YAEA,OAAO;gBACL,SAAS;gBACT;gBACA,SAAS,SAAS,QAAQ;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,gBAAgB,8HAAA,CAAA,qBAAkB,CAC/B,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAC7B,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAC3C,IAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;YAEzB,aAAa;YACb,MAAM,mBAAmB,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;gBACpD,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACnB,OAAO;wBACL,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,OAAO;gBACT;gBACA,MAAM;gBACN,SAAS;oBACP,YAAY;gBACd;YACF;YAEA,UAAU;YACV,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC/C,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACnB,MAAM;wBACJ,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,MAAM;gBACR;gBACA,MAAM;gBACN,SAAS;oBACP,YAAY;wBACV,QAAQ;oBACV;gBACF;YACF;YAEA,UAAU;YACV,MAAM,sBAAsB,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACzD,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;oBACnB,MAAM;wBACJ,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,MAAM;gBACR;gBACA,MAAM;gBACN,SAAS;oBACP,SAAS;wBACP,QAAQ;oBACV;gBACF;YACF;YAEA,OAAO;gBACL,QAAQ,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK;gBAC3C,MAAM,eAAe,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;gBACtC,YAAY,oBAAoB,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;YACnD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,kBAAkB,8HAAA,CAAA,qBAAkB,CACjC,KAAK,CAAC,wBACN,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,iBAAiB;YACjB,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC1D,OAAO;oBACL,OAAO,MAAM,KAAK;oBAClB,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,gBAAgB;gBAClB,aAAa;gBACb,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;oBAChC,OAAO;wBAAE,IAAI,eAAe,EAAE;oBAAC;oBAC/B,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBACA,OAAO;YACT;YAEA,WAAW;YACX,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;gBACrD,MAAM;oBACJ,OAAO,MAAM,KAAK;oBAClB,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,qBAAqB;YACrB,MAAM,eAAe,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;gBACpD,OAAO;oBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;gBAAC;YAC/B;YAEA,IAAI,eAAe,KAAK;gBACtB,UAAU;gBACV,MAAM,gBAAgB,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC;oBACxD,OAAO;wBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;oBAAC;oBAC7B,SAAS;wBAAE,WAAW;oBAAM;oBAC5B,MAAM,eAAe;gBACvB;gBAEA,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC;oBACpC,OAAO;wBACL,IAAI;4BAAE,IAAI,cAAc,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;wBAAE;oBAC3C;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,kBAAkB,8HAAA,CAAA,qBAAkB,CACjC,KAAK,CACJ,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;IAC3C,IAED,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC;gBAClD,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;gBACA,SAAS;oBACP,WAAW;gBACb;gBACA,MAAM,MAAM,KAAK;YACnB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,oBAAoB,8HAAA,CAAA,qBAAkB,CACnC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACtB,IAAI;YACF,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC;gBACpC,OAAO;oBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,aAAa;IACb,qBAAqB,8HAAA,CAAA,qBAAkB,CACpC,KAAK,CAAC,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAAE,IAAI,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,CAAC;IAAe,IACpD,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;QAC7B,IAAI;YACF,kBAAkB;YAClB,MAAM,iBAAiB,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC1D,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,QAAQ,IAAI,IAAI,CAAC,EAAE;gBACrB;YACF;YAEA,IAAI,CAAC,gBAAgB;gBACnB,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;oBAClB,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,MAAM,IAAI,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;gBAChC,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;YACxB;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,kKAAA,CAAA,YAAS,EAAE;gBAC9B,MAAM;YACR;YACA,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;IAEF,SAAS;IACT,gBAAgB,8HAAA,CAAA,qBAAkB,CAC/B,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE;QACnB,IAAI;YACF,MAAM,CACJ,cACA,iBACA,WACA,eACA,gBACA,YACD,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACpB,QAAQ;gBACR,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;oBAClB,OAAO;wBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;oBAAC;gBAC/B;gBACA,OAAO;gBACP,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;oBACpB,OAAO;wBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;oBAAC;gBAC/B;gBACA,OAAO;gBACP,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC;oBACf,OAAO;wBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;oBAAC;gBAC/B;gBACA,QAAQ;gBACR,IAAI,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;oBACzB,OAAO;wBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;oBAAC;gBAC/B;gBACA,YAAY;gBACZ,IAAI,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;oBACzB,OAAO;wBACL,QAAQ,IAAI,IAAI,CAAC,EAAE;wBACnB,WAAW;4BACT,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;wBAChD;oBACF;gBACF;gBACA,UAAU;gBACV,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAClB,OAAO;wBAAE,QAAQ,IAAI,IAAI,CAAC,EAAE;oBAAC;oBAC7B,SAAS;wBACP,QAAQ;4BACN,QAAQ;gCACN,YAAY;4BACd;wBACF;oBACF;oBACA,SAAS;wBACP,YAAY;4BACV,QAAQ;wBACV;oBACF;oBACA,MAAM;gBACR;aACD;YAED,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA,aAAa,YAAY,GAAG,CAAC,CAAC,MAAQ,CAAC;wBACrC,MAAM,IAAI,IAAI;wBACd,OAAO,IAAI,MAAM,CAAC,UAAU;oBAC9B,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,kKAAA,CAAA,YAAS,CAAC;gBAClB,MAAM;gBACN,SAAS;YACX;QACF;IACF;AACJ", "debugId": null}}, {"offset": {"line": 1990, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/server/api/root.ts"], "sourcesContent": ["import { categoriesRouter } from \"~/server/api/routers/categories\";\nimport { promptsRouter } from \"~/server/api/routers/prompts\";\nimport { tagsRouter } from \"~/server/api/routers/tags\";\nimport { searchRouter } from \"~/server/api/routers/search\";\nimport { createCallerFactory, createTRPCRouter } from \"~/server/api/trpc\";\n\n/**\n * This is the primary router for your server.\n *\n * All routers added in /api/routers should be manually added here.\n */\nexport const appRouter = createTRPCRouter({\n  categories: categoriesRouter,\n  prompts: promptsRouter,\n  tags: tagsRouter,\n  search: searchRouter,\n});\n\n// export type definition of API\nexport type AppRouter = typeof appRouter;\n\n/**\n * Create a server-side caller for the tRPC API.\n * @example\n * const trpc = createCaller(createContext);\n * const res = await trpc.post.all();\n *       ^? Post[]\n */\nexport const createCaller = createCallerFactory(appRouter);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAOO,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,mBAAgB,AAAD,EAAE;IACxC,YAAY,+IAAA,CAAA,mBAAgB;IAC5B,SAAS,4IAAA,CAAA,gBAAa;IACtB,MAAM,yIAAA,CAAA,aAAU;IAChB,QAAQ,2IAAA,CAAA,eAAY;AACtB;AAYO,MAAM,eAAe,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/api/trpc/%5Btrpc%5D/route.ts"], "sourcesContent": ["import { fetchRe<PERSON><PERSON><PERSON><PERSON> } from \"@trpc/server/adapters/fetch\";\nimport { type NextRequest } from \"next/server\";\n\nimport { env } from \"~/env\";\nimport { appRouter } from \"~/server/api/root\";\nimport { createTRPCContext } from \"~/server/api/trpc\";\n\n/**\n * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when\n * handling a HTTP request (e.g. when you make requests from Client Components).\n */\nconst createContext = async (req: NextRequest) => {\n  return createTRPCContext({\n    headers: req.headers,\n  });\n};\n\nconst handler = (req: NextRequest) =>\n  fetchRequestHandler({\n    endpoint: \"/api/trpc\",\n    req,\n    router: appRouter,\n    createContext: () => createContext(req),\n    onError:\n      env.NODE_ENV === \"development\"\n        ? ({ path, error }) => {\n            console.error(\n              `❌ tRPC failed on ${path ?? \"<no-path>\"}: ${error.message}`,\n            );\n          }\n        : undefined,\n  });\n\nexport { handler as GET, handler as <PERSON><PERSON><PERSON> };\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;AACA;;;;;AAEA;;;CAGC,GACD,MAAM,gBAAgB,OAAO;IAC3B,OAAO,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE;QACvB,SAAS,IAAI,OAAO;IACtB;AACF;AAEA,MAAM,UAAU,CAAC,MACf,CAAA,GAAA,yKAAA,CAAA,sBAAmB,AAAD,EAAE;QAClB,UAAU;QACV;QACA,QAAQ,8HAAA,CAAA,YAAS;QACjB,eAAe,IAAM,cAAc;QACnC,SACE,4GAAA,CAAA,MAAG,CAAC,QAAQ,KAAK,gBACb,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;YACd,QAAQ,KAAK,CACX,CAAC,iBAAiB,EAAE,QAAQ,YAAY,EAAE,EAAE,MAAM,OAAO,EAAE;QAE/D,IACA;IACR", "debugId": null}}]}