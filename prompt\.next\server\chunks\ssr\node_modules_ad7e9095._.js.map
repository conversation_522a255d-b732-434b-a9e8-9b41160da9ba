{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40babel/runtime/helpers/extends.js"], "sourcesContent": ["function _extends() {\n  return module.exports = _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _extends.apply(null, arguments);\n}\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS;IACP,OAAO,OAAO,OAAO,GAAG,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,KAAK,yBAMvE,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,SAAS,KAAK,CAAC,MAAM;AACxG;AACA,OAAO,OAAO,GAAG,UAAU,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40babel/runtime/helpers/objectWithoutPropertiesLoose.js"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,8BAA8B,CAAC,EAAE,CAAC;IACzC,IAAI,QAAQ,GAAG,OAAO,CAAC;IACvB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QACjD,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QACzB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACb;IACA,OAAO;AACT;AACA,OAAO,OAAO,GAAG,+BAA+B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/devlop/lib/development.js"], "sourcesContent": ["import {dequal} from 'dequal'\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationE<PERSON>r extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nexport function deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nexport function equal(actual, expected, message) {\n  assert(\n    dequal(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nexport function unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,cAAc,IAAI;AAExB,MAAM,uBAAuB;IAC3B,OAA6B,YAAY;IACzC,OAA6B,gBAAgB;IAE7C;;;;;;;;;;;;;;;GAeC,GACD,sCAAsC;IACtC,YAAY,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAE;QAC1D,KAAK,CAAC;QAEN,IAAI,MAAM,iBAAiB,EAAE;YAC3B,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD;QAEA;;KAEC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;KAEC,GACD,IAAI,CAAC,SAAS,GAAG;QAEjB;;KAEC,GACD,IAAI,CAAC,QAAQ,GAAG;IAClB;AACF;AAEA,MAAM,yBAAyB;IAC7B,OAA6B,qBAAqB;IAElD;;;;;;;;;GASC,GACD,YAAY,OAAO,EAAE,IAAI,CAAE;QACzB,KAAK,CAAC;QAEN;;KAEC,GACD,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAwBO,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI;IACzC,IAAI,SAAS;IAEb,6EAA6E;IAC7E,OAAO,cAAc,CAAC,YAAY;IAElC,8CAA8C;IAC9C,OAAO;;;IAEP;;;;GAIC,GACD,SAAS,WAAW,GAAG,IAAI;QACzB,IAAI,CAAC,QAAQ;YACX,SAAS;YAET,IAAI,OAAO,SAAS,YAAY,YAAY,GAAG,CAAC,OAAO;YACrD,SAAS;YACX,OAAO;gBACL,QAAQ,KAAK,CAAC,IAAI,iBAAiB,SAAS,QAAQ;gBAEpD,IAAI,OAAO,SAAS,UAAU,YAAY,GAAG,CAAC;YAChD;QACF;QAEA,OAAO,aACH,QAAQ,SAAS,CAAC,IAAI,MAAM,cAC5B,QAAQ,KAAK,CAAC,IAAI,IAAI,EAAE;IAC9B;AACF;AAqBO,SAAS,MAAM,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC7C,OACE,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,WACf,QACA,UACA,SACA,sCACA;AAEJ;AAiBO,SAAS,GAAG,KAAK,EAAE,OAAO;IAC/B,OACE,QAAQ,QACR,OACA,MACA,MACA,+BACA;AAEJ;AAeO,SAAS,YAAY,OAAO;IACjC,OAAO,OAAO,OAAO,MAAM,MAAM,eAAe;AAClD;AAEA;;;;;;;;;;;;;;;CAeC,GACD,sCAAsC;AACtC,SAAS,OAAO,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,WAAW;IAC3E,IAAI,CAAC,MAAM;QACT,MAAM,uBAAuB,QACzB,cACA,IAAI,eACF,eAAe,gBACf,QACA,UACA,UACA,CAAC;IAET;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/comma-separated-tokens/index.js"], "sourcesContent": ["/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n/**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */\n\n/**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  /** @type {Array<string>} */\n  const tokens = []\n  const input = String(value || '')\n  let index = input.indexOf(',')\n  let start = 0\n  /** @type {boolean} */\n  let end = false\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    const token = input.slice(start, index).trim()\n\n    if (token || !end) {\n      tokens.push(token)\n    }\n\n    start = index + 1\n    index = input.indexOf(',', start)\n  }\n\n  return tokens\n}\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nexport function stringify(values, options) {\n  const settings = options || {}\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED;;;CAGC,GAED;;;;;;;CAOC;;;;AACM,SAAS,MAAM,KAAK;IACzB,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,MAAM,QAAQ,OAAO,SAAS;IAC9B,IAAI,QAAQ,MAAM,OAAO,CAAC;IAC1B,IAAI,QAAQ;IACZ,oBAAoB,GACpB,IAAI,MAAM;IAEV,MAAO,CAAC,IAAK;QACX,IAAI,UAAU,CAAC,GAAG;YAChB,QAAQ,MAAM,MAAM;YACpB,MAAM;QACR;QAEA,MAAM,QAAQ,MAAM,KAAK,CAAC,OAAO,OAAO,IAAI;QAE5C,IAAI,SAAS,CAAC,KAAK;YACjB,OAAO,IAAI,CAAC;QACd;QAEA,QAAQ,QAAQ;QAChB,QAAQ,MAAM,OAAO,CAAC,KAAK;IAC7B;IAEA,OAAO;AACT;AAYO,SAAS,UAAU,MAAM,EAAE,OAAO;IACvC,MAAM,WAAW,WAAW,CAAC;IAE7B,uCAAuC;IACvC,MAAM,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAAK;WAAI;QAAQ;KAAG,GAAG;IAEnE,OAAO,MACJ,IAAI,CACH,CAAC,SAAS,QAAQ,GAAG,MAAM,EAAE,IAC3B,MACA,CAAC,SAAS,OAAO,KAAK,QAAQ,KAAK,GAAG,GAEzC,IAAI;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-parse-selector/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n */\n\n/**\n * @template {string} SimpleSelector\n *   Selector type.\n * @template {string} DefaultTagName\n *   Default tag name.\n * @typedef {(\n *   SimpleSelector extends ''\n *     ? DefaultTagName\n *     : SimpleSelector extends `${infer TagName}.${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends `${infer TagName}#${infer Rest}`\n *     ? ExtractTagName<TagName, DefaultTagName>\n *     : SimpleSelector extends string\n *     ? SimpleSelector\n *     : DefaultTagName\n * )} ExtractTagName\n *   Extract tag name from a simple selector.\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name (default: `'div'`).\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector (optional).\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nexport function parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: tag name is parsed.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;;;;;CAiBC;;;AAED,MAAM,SAAS;AAoBR,SAAS,cAAc,QAAQ,EAAE,cAAc;IACpD,MAAM,QAAQ,YAAY;IAC1B,uBAAuB,GACvB,MAAM,QAAQ,CAAC;IACf,IAAI,QAAQ;IACZ,+BAA+B,GAC/B,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IAEJ,MAAO,QAAQ,MAAM,MAAM,CAAE;QAC3B,OAAO,SAAS,GAAG;QACnB,MAAM,QAAQ,OAAO,IAAI,CAAC;QAC1B,MAAM,WAAW,MAAM,KAAK,CAAC,OAAO,QAAQ,MAAM,KAAK,GAAG,MAAM,MAAM;QAEtE,IAAI,UAAU;YACZ,IAAI,CAAC,UAAU;gBACb,UAAU;YACZ,OAAO,IAAI,aAAa,KAAK;gBAC3B,MAAM,EAAE,GAAG;YACb,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,SAAS,GAAG;gBACzC,MAAM,SAAS,CAAC,IAAI,CAAC;YACvB,OAAO;gBACL,MAAM,SAAS,GAAG;oBAAC;iBAAS;YAC9B;YAEA,SAAS,SAAS,MAAM;QAC1B;QAEA,IAAI,OAAO;YACT,WAAW,KAAK,CAAC,EAAE;YACnB;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,wCAAwC;QACxC,SAAS,WAAW,kBAAkB;QACtC,YAAY;QACZ,UAAU,EAAE;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/refractor/node_modules/hast-util-parse-selector/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name.\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector.\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: import('./extract.js').ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nexport function parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: fine.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED,MAAM,SAAS;AAoBR,SAAS,cAAc,QAAQ,EAAE,cAAc;IACpD,MAAM,QAAQ,YAAY;IAC1B,uBAAuB,GACvB,MAAM,QAAQ,CAAC;IACf,IAAI,QAAQ;IACZ,+BAA+B,GAC/B,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IAEJ,MAAO,QAAQ,MAAM,MAAM,CAAE;QAC3B,OAAO,SAAS,GAAG;QACnB,MAAM,QAAQ,OAAO,IAAI,CAAC;QAC1B,MAAM,WAAW,MAAM,KAAK,CAAC,OAAO,QAAQ,MAAM,KAAK,GAAG,MAAM,MAAM;QAEtE,IAAI,UAAU;YACZ,IAAI,CAAC,UAAU;gBACb,UAAU;YACZ,OAAO,IAAI,aAAa,KAAK;gBAC3B,MAAM,EAAE,GAAG;YACb,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,SAAS,GAAG;gBACzC,MAAM,SAAS,CAAC,IAAI,CAAC;YACvB,OAAO;gBACL,MAAM,SAAS,GAAG;oBAAC;iBAAS;YAC9B;YAEA,SAAS,SAAS,MAAM;QAC1B;QAEA,IAAI,OAAO;YACT,WAAW,KAAK,CAAC,EAAE;YACnB;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,0BAA0B;QAC1B,SAAS,WAAW,kBAAkB;QACtC,YAAY;QACZ,UAAU,EAAE;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/space-separated-tokens/index.js"], "sourcesContent": ["/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\nexport function parse(value) {\n  const input = String(value || '').trim()\n  return input ? input.split(/[ \\t\\n\\r\\f]+/g) : []\n}\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nexport function stringify(values) {\n  return values.join(' ').trim()\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACM,SAAS,MAAM,KAAK;IACzB,MAAM,QAAQ,OAAO,SAAS,IAAI,IAAI;IACtC,OAAO,QAAQ,MAAM,KAAK,CAAC,mBAAmB,EAAE;AAClD;AAUO,SAAS,UAAU,MAAM;IAC9B,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hastscript/lib/create-h.js"], "sourcesContent": ["/**\n * @import {Element, Nodes, RootContent, Root} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n/**\n * @typedef {Array<Nodes | PrimitiveChild>} ArrayChildNested\n *   List of children (deep).\n */\n\n/**\n * @typedef {Array<ArrayChildNested | Nodes | PrimitiveChild>} ArrayChild\n *   List of children.\n */\n\n/**\n * @typedef {Array<number | string>} ArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n */\n\n/**\n * @typedef {ArrayChild | Nodes | PrimitiveChild} Child\n *   Acceptable child value.\n */\n\n/**\n * @typedef {number | string | null | undefined} PrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n */\n\n/**\n * @typedef {boolean | number | string | null | undefined} PrimitiveValue\n *   Primitive property value.\n */\n\n/**\n * @typedef {Record<string, PropertyValue | Style>} Properties\n *   Acceptable value for element properties.\n */\n\n/**\n * @typedef {ArrayValue | PrimitiveValue} PropertyValue\n *   Primitive value or list value.\n */\n\n/**\n * @typedef {Element | Root} Result\n *   Result from a `h` (or `s`) call.\n */\n\n/**\n * @typedef {number | string} StyleValue\n *   Value for a CSS style field.\n */\n\n/**\n * @typedef {Record<string, StyleValue>} Style\n *   Supported value of a `style` prop.\n */\n\nimport {parse as parseCommas} from 'comma-separated-tokens'\nimport {parseSelector} from 'hast-util-parse-selector'\nimport {find, normalize} from 'property-information'\nimport {parse as parseSpaces} from 'space-separated-tokens'\n\n/**\n * @param {Schema} schema\n *   Schema to use.\n * @param {string} defaultTagName\n *   Default tag name.\n * @param {ReadonlyArray<string> | undefined} [caseSensitive]\n *   Case-sensitive tag names (default: `undefined`).\n * @returns\n *   `h`.\n */\nexport function createH(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : undefined\n\n  /**\n   * Hyperscript compatible DSL for creating virtual hast trees.\n   *\n   * @overload\n   * @param {null | undefined} [selector]\n   * @param {...Child} children\n   * @returns {Root}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {Properties} properties\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @overload\n   * @param {string} selector\n   * @param {...Child} children\n   * @returns {Element}\n   *\n   * @param {string | null | undefined} [selector]\n   *   Selector.\n   * @param {Child | Properties | null | undefined} [properties]\n   *   Properties (or first child) (default: `undefined`).\n   * @param {...Child} children\n   *   Children.\n   * @returns {Result}\n   *   Result.\n   */\n  function h(selector, properties, ...children) {\n    /** @type {Result} */\n    let node\n\n    if (selector === null || selector === undefined) {\n      node = {type: 'root', children: []}\n      // Properties are not supported for roots.\n      const child = /** @type {Child} */ (properties)\n      children.unshift(child)\n    } else {\n      node = parseSelector(selector, defaultTagName)\n      // Normalize the name.\n      const lower = node.tagName.toLowerCase()\n      const adjusted = adjust ? adjust.get(lower) : undefined\n      node.tagName = adjusted || lower\n\n      // Handle properties.\n      if (isChild(properties)) {\n        children.unshift(properties)\n      } else {\n        for (const [key, value] of Object.entries(properties)) {\n          addProperty(schema, node.properties, key, value)\n        }\n      }\n    }\n\n    // Handle children.\n    for (const child of children) {\n      addChild(node.children, child)\n    }\n\n    if (node.type === 'element' && node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  return h\n}\n\n/**\n * Check if something is properties or a child.\n *\n * @param {Child | Properties} value\n *   Value to check.\n * @returns {value is Child}\n *   Whether `value` is definitely a child.\n */\nfunction isChild(value) {\n  // Never properties if not an object.\n  if (value === null || typeof value !== 'object' || Array.isArray(value)) {\n    return true\n  }\n\n  // Never node without `type`; that’s the main discriminator.\n  if (typeof value.type !== 'string') return false\n\n  // Slower check: never property value if object or array with\n  // non-number/strings.\n  const record = /** @type {Record<string, unknown>} */ (value)\n  const keys = Object.keys(value)\n\n  for (const key of keys) {\n    const value = record[key]\n\n    if (value && typeof value === 'object') {\n      if (!Array.isArray(value)) return true\n\n      const list = /** @type {ReadonlyArray<unknown>} */ (value)\n\n      for (const item of list) {\n        if (typeof item !== 'number' && typeof item !== 'string') {\n          return true\n        }\n      }\n    }\n  }\n\n  // Also see empty `children` as a node.\n  if ('children' in value && Array.isArray(value.children)) {\n    return true\n  }\n\n  // Default to properties, someone can always pass an empty object,\n  // put `data: {}` in a node,\n  // or wrap it in an array.\n  return false\n}\n\n/**\n * @param {Schema} schema\n *   Schema.\n * @param {Properties} properties\n *   Properties object.\n * @param {string} key\n *   Property name.\n * @param {PropertyValue | Style} value\n *   Property value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = find(schema, key)\n  /** @type {PropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === null || value === undefined) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = parseSpaces(value)\n    } else if (info.commaSeparated) {\n      result = parseCommas(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = parseSpaces(parseCommas(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = [...value]\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<number | string>} */\n    const finalResult = []\n\n    for (const item of result) {\n      // Assume no booleans in array.\n      finalResult.push(\n        /** @type {number | string} */ (\n          parsePrimitive(info, info.property, item)\n        )\n      )\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // Assume no booleans in `className`.\n    result = properties.className.concat(\n      /** @type {Array<number | string> | number | string} */ (result)\n    )\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<RootContent>} nodes\n *   Children.\n * @param {Child} value\n *   Child.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChild(nodes, value) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'number' || typeof value === 'string') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    for (const child of value) {\n      addChild(nodes, child)\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n *   Property information.\n * @param {string} name\n *   Property name.\n * @param {PrimitiveValue} value\n *   Property value.\n * @returns {PrimitiveValue}\n *   Property value.\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || normalize(value) === normalize(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {Style} styles\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(styles) {\n  /** @type {Array<string>} */\n  const result = []\n\n  for (const [key, value] of Object.entries(styles)) {\n    result.push([key, value].join(': '))\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {ReadonlyArray<string>} values\n *   List of properly cased keys.\n * @returns {Map<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Map<string, string>} */\n  const result = new Map()\n\n  for (const value of values) {\n    result.set(value.toLowerCase(), value)\n  }\n\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC,GAED;;;CAGC;;;AAED;AACA;AACA;AAAA;AACA;;;;;AAYO,SAAS,QAAQ,MAAM,EAAE,cAAc,EAAE,aAAa;IAC3D,MAAM,SAAS,gBAAgB,gBAAgB,iBAAiB;IAEhE;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,QAAQ;QAC1C,mBAAmB,GACnB,IAAI;QAEJ,IAAI,aAAa,QAAQ,aAAa,WAAW;YAC/C,OAAO;gBAAC,MAAM;gBAAQ,UAAU,EAAE;YAAA;YAClC,0CAA0C;YAC1C,MAAM,QAA8B;YACpC,SAAS,OAAO,CAAC;QACnB,OAAO;YACL,OAAO,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;YAC/B,sBAAsB;YACtB,MAAM,QAAQ,KAAK,OAAO,CAAC,WAAW;YACtC,MAAM,WAAW,SAAS,OAAO,GAAG,CAAC,SAAS;YAC9C,KAAK,OAAO,GAAG,YAAY;YAE3B,qBAAqB;YACrB,IAAI,QAAQ,aAAa;gBACvB,SAAS,OAAO,CAAC;YACnB,OAAO;gBACL,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,YAAa;oBACrD,YAAY,QAAQ,KAAK,UAAU,EAAE,KAAK;gBAC5C;YACF;QACF;QAEA,mBAAmB;QACnB,KAAK,MAAM,SAAS,SAAU;YAC5B,SAAS,KAAK,QAAQ,EAAE;QAC1B;QAEA,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;YAC1D,KAAK,OAAO,GAAG;gBAAC,MAAM;gBAAQ,UAAU,KAAK,QAAQ;YAAA;YACrD,KAAK,QAAQ,GAAG,EAAE;QACpB;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK;IACpB,qCAAqC;IACrC,IAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,QAAQ;QACvE,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU,OAAO;IAE3C,6DAA6D;IAC7D,sBAAsB;IACtB,MAAM,SAAiD;IACvD,MAAM,OAAO,OAAO,IAAI,CAAC;IAEzB,KAAK,MAAM,OAAO,KAAM;QACtB,MAAM,QAAQ,MAAM,CAAC,IAAI;QAEzB,IAAI,SAAS,OAAO,UAAU,UAAU;YACtC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO;YAElC,MAAM,OAA8C;YAEpD,KAAK,MAAM,QAAQ,KAAM;gBACvB,IAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;oBACxD,OAAO;gBACT;YACF;QACF;IACF;IAEA,uCAAuC;IACvC,IAAI,cAAc,SAAS,MAAM,OAAO,CAAC,MAAM,QAAQ,GAAG;QACxD,OAAO;IACT;IAEA,kEAAkE;IAClE,4BAA4B;IAC5B,0BAA0B;IAC1B,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,YAAY,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK;IACjD,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;IAC1B,0BAA0B,GAC1B,IAAI;IAEJ,iCAAiC;IACjC,IAAI,UAAU,QAAQ,UAAU,WAAW;IAE3C,IAAI,OAAO,UAAU,UAAU;QAC7B,cAAc;QACd,IAAI,OAAO,KAAK,CAAC,QAAQ;QAEzB,SAAS;IACX,OAEK,IAAI,OAAO,UAAU,WAAW;QACnC,SAAS;IACX,OAEK,IAAI,OAAO,UAAU,UAAU;QAClC,IAAI,KAAK,cAAc,EAAE;YACvB,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAW,AAAD,EAAE;QACvB,OAAO,IAAI,KAAK,cAAc,EAAE;YAC9B,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAW,AAAD,EAAE;QACvB,OAAO,IAAI,KAAK,qBAAqB,EAAE;YACrC,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAW,AAAD,EAAE,CAAA,GAAA,qJAAA,CAAA,QAAW,AAAD,EAAE,OAAO,IAAI,CAAC;QAC/C,OAAO;YACL,SAAS,eAAe,MAAM,KAAK,QAAQ,EAAE;QAC/C;IACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC/B,SAAS;eAAI;SAAM;IACrB,OAAO;QACL,SAAS,KAAK,QAAQ,KAAK,UAAU,MAAM,SAAS,OAAO;IAC7D;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,mCAAmC,GACnC,MAAM,cAAc,EAAE;QAEtB,KAAK,MAAM,QAAQ,OAAQ;YACzB,+BAA+B;YAC/B,YAAY,IAAI,CAEZ,eAAe,MAAM,KAAK,QAAQ,EAAE;QAG1C;QAEA,SAAS;IACX;IAEA,oEAAoE;IACpE,IAAI,KAAK,QAAQ,KAAK,eAAe,MAAM,OAAO,CAAC,WAAW,SAAS,GAAG;QACxE,qCAAqC;QACrC,SAAS,WAAW,SAAS,CAAC,MAAM,CACuB;IAE7D;IAEA,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC9B;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,IAAI,UAAU,QAAQ,UAAU,WAAW;IACzC,SAAS;IACX,OAAO,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;QACjE,MAAM,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO,OAAO;QAAM;IAChD,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC/B,KAAK,MAAM,SAAS,MAAO;YACzB,SAAS,OAAO;QAClB;IACF,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,OAAO;QACvD,IAAI,MAAM,IAAI,KAAK,QAAQ;YACzB,SAAS,OAAO,MAAM,QAAQ;QAChC,OAAO;YACL,MAAM,IAAI,CAAC;QACb;IACF,OAAO;QACL,MAAM,IAAI,MAAM,2CAA2C,QAAQ;IACrE;AACF;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,eAAe,IAAI,EAAE,IAAI,EAAE,KAAK;IACvC,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,KAAK,MAAM,IAAI,SAAS,CAAC,OAAO,KAAK,CAAC,OAAO,SAAS;YACxD,OAAO,OAAO;QAChB;QAEA,IACE,CAAC,KAAK,OAAO,IAAI,KAAK,iBAAiB,KACvC,CAAC,UAAU,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GACrD;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,MAAM,MAAM;IACnB,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IAEjB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACjD,OAAO,IAAI,CAAC;YAAC;YAAK;SAAM,CAAC,IAAI,CAAC;IAChC;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;CAOC,GACD,SAAS,gBAAgB,MAAM;IAC7B,gCAAgC,GAChC,MAAM,SAAS,IAAI;IAEnB,KAAK,MAAM,SAAS,OAAQ;QAC1B,OAAO,GAAG,CAAC,MAAM,WAAW,IAAI;IAClC;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hastscript/lib/svg-case-sensitive-tag-names.js"], "sourcesContent": ["/**\n * List of case-sensitive SVG tag names.\n *\n * @type {ReadonlyArray<string>}\n */\nexport const svgCaseSensitiveTagNames = [\n  'altGlyph',\n  'altGlyphDef',\n  'altGlyphItem',\n  'animateColor',\n  'animateMotion',\n  'animateTransform',\n  'clipPath',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n  'foreignObject',\n  'glyphRef',\n  'linearGradient',\n  'radialGradient',\n  'solidColor',\n  'textArea',\n  'textPath'\n]\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,2BAA2B;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hastscript/lib/index.js"], "sourcesContent": ["// Register the JSX namespace on `h`.\n/**\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n */\n\n// Register the JSX namespace on `s`.\n/**\n * @typedef {import('./jsx-classic.js').Element} s.JSX.Element\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} s.JSX.ElementChildrenAttribute\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} s.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} s.JSX.IntrinsicElements\n */\n\nimport {html, svg} from 'property-information'\nimport {createH} from './create-h.js'\nimport {svgCaseSensitiveTagNames} from './svg-case-sensitive-tag-names.js'\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nexport const h = createH(html, 'div')\n\n// Note: this explicit type is needed, otherwise TS creates broken types.\n/** @type {ReturnType<createH>} */\nexport const s = createH(svg, 'g', svgCaseSensitiveTagNames)\n"], "names": [], "mappings": "AAAA,qCAAqC;AACrC;;;;;CAKC,GAED,qCAAqC;AACrC;;;;;CAKC;;;;AAED;AACA;AACA;;;;AAIO,MAAM,IAAI,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,gKAAA,CAAA,OAAI,EAAE;AAIxB,MAAM,IAAI,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,gKAAA,CAAA,MAAG,EAAE,KAAK,6KAAA,CAAA,2BAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/refractor/node_modules/hastscript/lib/core.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef {Content | Root} Node\n *   Any concrete `hast` node.\n * @typedef {Root | Element} HResult\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {string | number} HStyleValue\n *   Value for a CSS style field.\n * @typedef {Record<string, HStyleValue>} HStyle\n *   Supported value of a `style` prop.\n * @typedef {string | number | boolean | null | undefined} HPrimitiveValue\n *   Primitive property value.\n * @typedef {Array<string | number>} HArrayValue\n *   List of property values for space- or comma separated values (such as `className`).\n * @typedef {HPrimitiveValue | HArrayValue} HPropertyValue\n *   Primitive value or list value.\n * @typedef {{[property: string]: HPropertyValue | HStyle}} HProperties\n *   Acceptable value for element properties.\n *\n * @typedef {string | number | null | undefined} HPrimitiveChild\n *   Primitive children, either ignored (nullish), or turned into text nodes.\n * @typedef {Array<Node | HPrimitiveChild>} HArrayChild\n *   List of children.\n * @typedef {Node | HPrimitiveChild | HArrayChild} HChild\n *   Acceptable child value.\n */\n\nimport {find, normalize} from 'property-information'\nimport {parseSelector} from 'hast-util-parse-selector'\nimport {parse as spaces} from 'space-separated-tokens'\nimport {parse as commas} from 'comma-separated-tokens'\n\nconst buttonTypes = new Set(['menu', 'submit', 'reset', 'button'])\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {Schema} schema\n * @param {string} defaultTagName\n * @param {Array<string>} [caseSensitive]\n */\nexport function core(schema, defaultTagName, caseSensitive) {\n  const adjust = caseSensitive && createAdjustMap(caseSensitive)\n\n  const h =\n    /**\n     * @type {{\n     *   (): Root\n     *   (selector: null | undefined, ...children: Array<HChild>): Root\n     *   (selector: string, properties?: HProperties, ...children: Array<HChild>): Element\n     *   (selector: string, ...children: Array<HChild>): Element\n     * }}\n     */\n    (\n      /**\n       * Hyperscript compatible DSL for creating virtual hast trees.\n       *\n       * @param {string | null} [selector]\n       * @param {HProperties | HChild} [properties]\n       * @param {Array<HChild>} children\n       * @returns {HResult}\n       */\n      function (selector, properties, ...children) {\n        let index = -1\n        /** @type {HResult} */\n        let node\n\n        if (selector === undefined || selector === null) {\n          node = {type: 'root', children: []}\n          // @ts-expect-error Properties are not supported for roots.\n          children.unshift(properties)\n        } else {\n          node = parseSelector(selector, defaultTagName)\n          // Normalize the name.\n          node.tagName = node.tagName.toLowerCase()\n          if (adjust && own.call(adjust, node.tagName)) {\n            node.tagName = adjust[node.tagName]\n          }\n\n          // Handle props.\n          if (isProperties(properties, node.tagName)) {\n            /** @type {string} */\n            let key\n\n            for (key in properties) {\n              if (own.call(properties, key)) {\n                // @ts-expect-error `node.properties` is set.\n                addProperty(schema, node.properties, key, properties[key])\n              }\n            }\n          } else {\n            children.unshift(properties)\n          }\n        }\n\n        // Handle children.\n        while (++index < children.length) {\n          addChild(node.children, children[index])\n        }\n\n        if (node.type === 'element' && node.tagName === 'template') {\n          node.content = {type: 'root', children: node.children}\n          node.children = []\n        }\n\n        return node\n      }\n    )\n\n  return h\n}\n\n/**\n * @param {HProperties | HChild} value\n * @param {string} name\n * @returns {value is HProperties}\n */\nfunction isProperties(value, name) {\n  if (\n    value === null ||\n    value === undefined ||\n    typeof value !== 'object' ||\n    Array.isArray(value)\n  ) {\n    return false\n  }\n\n  if (name === 'input' || !value.type || typeof value.type !== 'string') {\n    return true\n  }\n\n  if ('children' in value && Array.isArray(value.children)) {\n    return false\n  }\n\n  if (name === 'button') {\n    return buttonTypes.has(value.type.toLowerCase())\n  }\n\n  return !('value' in value)\n}\n\n/**\n * @param {Schema} schema\n * @param {Properties} properties\n * @param {string} key\n * @param {HStyle | HPropertyValue} value\n * @returns {void}\n */\nfunction addProperty(schema, properties, key, value) {\n  const info = find(schema, key)\n  let index = -1\n  /** @type {HPropertyValue} */\n  let result\n\n  // Ignore nullish and NaN values.\n  if (value === undefined || value === null) return\n\n  if (typeof value === 'number') {\n    // Ignore NaN.\n    if (Number.isNaN(value)) return\n\n    result = value\n  }\n  // Booleans.\n  else if (typeof value === 'boolean') {\n    result = value\n  }\n  // Handle list values.\n  else if (typeof value === 'string') {\n    if (info.spaceSeparated) {\n      result = spaces(value)\n    } else if (info.commaSeparated) {\n      result = commas(value)\n    } else if (info.commaOrSpaceSeparated) {\n      result = spaces(commas(value).join(' '))\n    } else {\n      result = parsePrimitive(info, info.property, value)\n    }\n  } else if (Array.isArray(value)) {\n    result = value.concat()\n  } else {\n    result = info.property === 'style' ? style(value) : String(value)\n  }\n\n  if (Array.isArray(result)) {\n    /** @type {Array<string | number>} */\n    const finalResult = []\n\n    while (++index < result.length) {\n      // @ts-expect-error Assume no booleans in array.\n      finalResult[index] = parsePrimitive(info, info.property, result[index])\n    }\n\n    result = finalResult\n  }\n\n  // Class names (which can be added both on the `selector` and here).\n  if (info.property === 'className' && Array.isArray(properties.className)) {\n    // @ts-expect-error Assume no booleans in `className`.\n    result = properties.className.concat(result)\n  }\n\n  properties[info.property] = result\n}\n\n/**\n * @param {Array<Content>} nodes\n * @param {HChild} value\n * @returns {void}\n */\nfunction addChild(nodes, value) {\n  let index = -1\n\n  if (value === undefined || value === null) {\n    // Empty.\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n  } else if (Array.isArray(value)) {\n    while (++index < value.length) {\n      addChild(nodes, value[index])\n    }\n  } else if (typeof value === 'object' && 'type' in value) {\n    if (value.type === 'root') {\n      addChild(nodes, value.children)\n    } else {\n      nodes.push(value)\n    }\n  } else {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n}\n\n/**\n * Parse a single primitives.\n *\n * @param {Info} info\n * @param {string} name\n * @param {HPrimitiveValue} value\n * @returns {HPrimitiveValue}\n */\nfunction parsePrimitive(info, name, value) {\n  if (typeof value === 'string') {\n    if (info.number && value && !Number.isNaN(Number(value))) {\n      return Number(value)\n    }\n\n    if (\n      (info.boolean || info.overloadedBoolean) &&\n      (value === '' || normalize(value) === normalize(name))\n    ) {\n      return true\n    }\n  }\n\n  return value\n}\n\n/**\n * Serialize a `style` object as a string.\n *\n * @param {HStyle} value\n *   Style object.\n * @returns {string}\n *   CSS string.\n */\nfunction style(value) {\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      result.push([key, value[key]].join(': '))\n    }\n  }\n\n  return result.join('; ')\n}\n\n/**\n * Create a map to adjust casing.\n *\n * @param {Array<string>} values\n *   List of properly cased keys.\n * @returns {Record<string, string>}\n *   Map of lowercase keys to uppercase keys.\n */\nfunction createAdjustMap(values) {\n  /** @type {Record<string, string>} */\n  const result = {}\n  let index = -1\n\n  while (++index < values.length) {\n    result[values[index].toLowerCase()] = values[index]\n  }\n\n  return result\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC;;;AAED;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,cAAc,IAAI,IAAI;IAAC;IAAQ;IAAU;IAAS;CAAS;AAEjE,MAAM,MAAM,CAAC,EAAE,cAAc;AAOtB,SAAS,KAAK,MAAM,EAAE,cAAc,EAAE,aAAa;IACxD,MAAM,SAAS,iBAAiB,gBAAgB;IAEhD,MAAM,IAUF;;;;;;;OAOC,GACD,SAAU,QAAQ,EAAE,UAAU,EAAE,GAAG,QAAQ;QACzC,IAAI,QAAQ,CAAC;QACb,oBAAoB,GACpB,IAAI;QAEJ,IAAI,aAAa,aAAa,aAAa,MAAM;YAC/C,OAAO;gBAAC,MAAM;gBAAQ,UAAU,EAAE;YAAA;YAClC,2DAA2D;YAC3D,SAAS,OAAO,CAAC;QACnB,OAAO;YACL,OAAO,CAAA,GAAA,8LAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;YAC/B,sBAAsB;YACtB,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,WAAW;YACvC,IAAI,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG;gBAC5C,KAAK,OAAO,GAAG,MAAM,CAAC,KAAK,OAAO,CAAC;YACrC;YAEA,gBAAgB;YAChB,IAAI,aAAa,YAAY,KAAK,OAAO,GAAG;gBAC1C,mBAAmB,GACnB,IAAI;gBAEJ,IAAK,OAAO,WAAY;oBACtB,IAAI,IAAI,IAAI,CAAC,YAAY,MAAM;wBAC7B,6CAA6C;wBAC7C,YAAY,QAAQ,KAAK,UAAU,EAAE,KAAK,UAAU,CAAC,IAAI;oBAC3D;gBACF;YACF,OAAO;gBACL,SAAS,OAAO,CAAC;YACnB;QACF;QAEA,mBAAmB;QACnB,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;YAChC,SAAS,KAAK,QAAQ,EAAE,QAAQ,CAAC,MAAM;QACzC;QAEA,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;YAC1D,KAAK,OAAO,GAAG;gBAAC,MAAM;gBAAQ,UAAU,KAAK,QAAQ;YAAA;YACrD,KAAK,QAAQ,GAAG,EAAE;QACpB;QAEA,OAAO;IACT;IAGJ,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,aAAa,KAAK,EAAE,IAAI;IAC/B,IACE,UAAU,QACV,UAAU,aACV,OAAO,UAAU,YACjB,MAAM,OAAO,CAAC,QACd;QACA,OAAO;IACT;IAEA,IAAI,SAAS,WAAW,CAAC,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;QACrE,OAAO;IACT;IAEA,IAAI,cAAc,SAAS,MAAM,OAAO,CAAC,MAAM,QAAQ,GAAG;QACxD,OAAO;IACT;IAEA,IAAI,SAAS,UAAU;QACrB,OAAO,YAAY,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW;IAC/C;IAEA,OAAO,CAAC,CAAC,WAAW,KAAK;AAC3B;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK;IACjD,MAAM,OAAO,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;IAC1B,IAAI,QAAQ,CAAC;IACb,2BAA2B,GAC3B,IAAI;IAEJ,iCAAiC;IACjC,IAAI,UAAU,aAAa,UAAU,MAAM;IAE3C,IAAI,OAAO,UAAU,UAAU;QAC7B,cAAc;QACd,IAAI,OAAO,KAAK,CAAC,QAAQ;QAEzB,SAAS;IACX,OAEK,IAAI,OAAO,UAAU,WAAW;QACnC,SAAS;IACX,OAEK,IAAI,OAAO,UAAU,UAAU;QAClC,IAAI,KAAK,cAAc,EAAE;YACvB,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAM,AAAD,EAAE;QAClB,OAAO,IAAI,KAAK,cAAc,EAAE;YAC9B,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAM,AAAD,EAAE;QAClB,OAAO,IAAI,KAAK,qBAAqB,EAAE;YACrC,SAAS,CAAA,GAAA,qJAAA,CAAA,QAAM,AAAD,EAAE,CAAA,GAAA,qJAAA,CAAA,QAAM,AAAD,EAAE,OAAO,IAAI,CAAC;QACrC,OAAO;YACL,SAAS,eAAe,MAAM,KAAK,QAAQ,EAAE;QAC/C;IACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC/B,SAAS,MAAM,MAAM;IACvB,OAAO;QACL,SAAS,KAAK,QAAQ,KAAK,UAAU,MAAM,SAAS,OAAO;IAC7D;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,mCAAmC,GACnC,MAAM,cAAc,EAAE;QAEtB,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,gDAAgD;YAChD,WAAW,CAAC,MAAM,GAAG,eAAe,MAAM,KAAK,QAAQ,EAAE,MAAM,CAAC,MAAM;QACxE;QAEA,SAAS;IACX;IAEA,oEAAoE;IACpE,IAAI,KAAK,QAAQ,KAAK,eAAe,MAAM,OAAO,CAAC,WAAW,SAAS,GAAG;QACxE,sDAAsD;QACtD,SAAS,WAAW,SAAS,CAAC,MAAM,CAAC;IACvC;IAEA,UAAU,CAAC,KAAK,QAAQ,CAAC,GAAG;AAC9B;AAEA;;;;CAIC,GACD,SAAS,SAAS,KAAK,EAAE,KAAK;IAC5B,IAAI,QAAQ,CAAC;IAEb,IAAI,UAAU,aAAa,UAAU,MAAM;IACzC,SAAS;IACX,OAAO,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;QACjE,MAAM,IAAI,CAAC;YAAC,MAAM;YAAQ,OAAO,OAAO;QAAM;IAChD,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC/B,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;YAC7B,SAAS,OAAO,KAAK,CAAC,MAAM;QAC9B;IACF,OAAO,IAAI,OAAO,UAAU,YAAY,UAAU,OAAO;QACvD,IAAI,MAAM,IAAI,KAAK,QAAQ;YACzB,SAAS,OAAO,MAAM,QAAQ;QAChC,OAAO;YACL,MAAM,IAAI,CAAC;QACb;IACF,OAAO;QACL,MAAM,IAAI,MAAM,2CAA2C,QAAQ;IACrE;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,eAAe,IAAI,EAAE,IAAI,EAAE,KAAK;IACvC,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,KAAK,MAAM,IAAI,SAAS,CAAC,OAAO,KAAK,CAAC,OAAO,SAAS;YACxD,OAAO,OAAO;QAChB;QAEA,IACE,CAAC,KAAK,OAAO,IAAI,KAAK,iBAAiB,KACvC,CAAC,UAAU,MAAM,CAAA,GAAA,wLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,CAAA,GAAA,wLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GACrD;YACA,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,MAAM,KAAK;IAClB,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,mBAAmB,GACnB,IAAI;IAEJ,IAAK,OAAO,MAAO;QACjB,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM;YACxB,OAAO,IAAI,CAAC;gBAAC;gBAAK,KAAK,CAAC,IAAI;aAAC,CAAC,IAAI,CAAC;QACrC;IACF;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;;;CAOC,GACD,SAAS,gBAAgB,MAAM;IAC7B,mCAAmC,GACnC,MAAM,SAAS,CAAC;IAChB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,MAAM,CAAC,MAAM;IACrD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/refractor/node_modules/hastscript/lib/html.js"], "sourcesContent": ["/**\n * @typedef {import('./core.js').HChild} Child\n *   Acceptable child value.\n * @typedef {import('./core.js').HProperties} Properties\n *   Acceptable value for element properties.\n * @typedef {import('./core.js').HResult} Result\n *   Result from a `h` (or `s`) call.\n *\n * @typedef {import('./jsx-classic.js').Element} h.JSX.Element\n * @typedef {import('./jsx-classic.js').IntrinsicAttributes} h.JSX.IntrinsicAttributes\n * @typedef {import('./jsx-classic.js').IntrinsicElements} h.JSX.IntrinsicElements\n * @typedef {import('./jsx-classic.js').ElementChildrenAttribute} h.JSX.ElementChildrenAttribute\n */\n\nimport {html} from 'property-information'\nimport {core} from './core.js'\n\nexport const h = core(html, 'div')\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC;;;AAED;AACA;;;AAEO,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,OAAI,AAAD,EAAE,6LAAA,CAAA,OAAI,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1081, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/vfile-location/lib/index.js"], "sourcesContent": ["/**\n * @import {VFile, Value} from 'vfile'\n * @import {Location} from 'vfile-location'\n */\n\n/**\n * Create an index of the given document to translate between line/column and\n * offset based positional info.\n *\n * Also implemented in Rust in [`wooorm/markdown-rs`][markdown-rs].\n *\n * [markdown-rs]: https://github.com/wooorm/markdown-rs/blob/main/src/util/location.rs\n *\n * @param {VFile | Value} file\n *   File to index.\n * @returns {Location}\n *   Accessors for index.\n */\nexport function location(file) {\n  const value = String(file)\n  /**\n   * List, where each index is a line number (0-based), and each value is the\n   * byte index *after* where the line ends.\n   *\n   * @type {Array<number>}\n   */\n  const indices = []\n\n  return {toOffset, toPoint}\n\n  /** @type {Location['toPoint']} */\n  function toPoint(offset) {\n    if (typeof offset === 'number' && offset > -1 && offset <= value.length) {\n      let index = 0\n\n      while (true) {\n        let end = indices[index]\n\n        if (end === undefined) {\n          const eol = next(value, indices[index - 1])\n          end = eol === -1 ? value.length + 1 : eol + 1\n          indices[index] = end\n        }\n\n        if (end > offset) {\n          return {\n            line: index + 1,\n            column: offset - (index > 0 ? indices[index - 1] : 0) + 1,\n            offset\n          }\n        }\n\n        index++\n      }\n    }\n  }\n\n  /** @type {Location['toOffset']} */\n  function toOffset(point) {\n    if (\n      point &&\n      typeof point.line === 'number' &&\n      typeof point.column === 'number' &&\n      !Number.isNaN(point.line) &&\n      !Number.isNaN(point.column)\n    ) {\n      while (indices.length < point.line) {\n        const from = indices[indices.length - 1]\n        const eol = next(value, from)\n        const end = eol === -1 ? value.length + 1 : eol + 1\n        if (from === end) break\n        indices.push(end)\n      }\n\n      const offset =\n        (point.line > 1 ? indices[point.line - 2] : 0) + point.column - 1\n      // The given `column` could not exist on this line.\n      if (offset < indices[point.line - 1]) return offset\n    }\n  }\n}\n\n/**\n * @param {string} value\n * @param {number} from\n */\nfunction next(value, from) {\n  const cr = value.indexOf('\\r', from)\n  const lf = value.indexOf('\\n', from)\n  if (lf === -1) return cr\n  if (cr === -1 || cr + 1 === lf) return lf\n  return cr < lf ? cr : lf\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;CAYC;;;AACM,SAAS,SAAS,IAAI;IAC3B,MAAM,QAAQ,OAAO;IACrB;;;;;GAKC,GACD,MAAM,UAAU,EAAE;IAElB,OAAO;QAAC;QAAU;IAAO;;;IAEzB,gCAAgC,GAChC,SAAS,QAAQ,MAAM;QACrB,IAAI,OAAO,WAAW,YAAY,SAAS,CAAC,KAAK,UAAU,MAAM,MAAM,EAAE;YACvE,IAAI,QAAQ;YAEZ,MAAO,KAAM;gBACX,IAAI,MAAM,OAAO,CAAC,MAAM;gBAExB,IAAI,QAAQ,WAAW;oBACrB,MAAM,MAAM,KAAK,OAAO,OAAO,CAAC,QAAQ,EAAE;oBAC1C,MAAM,QAAQ,CAAC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM;oBAC5C,OAAO,CAAC,MAAM,GAAG;gBACnB;gBAEA,IAAI,MAAM,QAAQ;oBAChB,OAAO;wBACL,MAAM,QAAQ;wBACd,QAAQ,SAAS,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI;wBACxD;oBACF;gBACF;gBAEA;YACF;QACF;IACF;IAEA,iCAAiC,GACjC,SAAS,SAAS,KAAK;QACrB,IACE,SACA,OAAO,MAAM,IAAI,KAAK,YACtB,OAAO,MAAM,MAAM,KAAK,YACxB,CAAC,OAAO,KAAK,CAAC,MAAM,IAAI,KACxB,CAAC,OAAO,KAAK,CAAC,MAAM,MAAM,GAC1B;YACA,MAAO,QAAQ,MAAM,GAAG,MAAM,IAAI,CAAE;gBAClC,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;gBACxC,MAAM,MAAM,KAAK,OAAO;gBACxB,MAAM,MAAM,QAAQ,CAAC,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM;gBAClD,IAAI,SAAS,KAAK;gBAClB,QAAQ,IAAI,CAAC;YACf;YAEA,MAAM,SACJ,CAAC,MAAM,IAAI,GAAG,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG;YAClE,mDAAmD;YACnD,IAAI,SAAS,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,EAAE,OAAO;QAC/C;IACF;AACF;AAEA;;;CAGC,GACD,SAAS,KAAK,KAAK,EAAE,IAAI;IACvB,MAAM,KAAK,MAAM,OAAO,CAAC,MAAM;IAC/B,MAAM,KAAK,MAAM,OAAO,CAAC,MAAM;IAC/B,IAAI,OAAO,CAAC,GAAG,OAAO;IACtB,IAAI,OAAO,CAAC,KAAK,KAAK,MAAM,IAAI,OAAO;IACvC,OAAO,KAAK,KAAK,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/web-namespaces/index.js"], "sourcesContent": ["/**\n * Map of web namespaces.\n *\n * @type {Record<string, string>}\n */\nexport const webNamespaces = {\n  html: 'http://www.w3.org/1999/xhtml',\n  mathml: 'http://www.w3.org/1998/Math/MathML',\n  svg: 'http://www.w3.org/2000/svg',\n  xlink: 'http://www.w3.org/1999/xlink',\n  xml: 'http://www.w3.org/XML/1998/namespace',\n  xmlns: 'http://www.w3.org/2000/xmlns/'\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,gBAAgB;IAC3B,MAAM;IACN,QAAQ;IACR,KAAK;IACL,OAAO;IACP,KAAK;IACL,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-from-parse5/lib/index.js"], "sourcesContent": ["/**\n * @import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, No<PERSON>, <PERSON>Content, Root} from 'hast'\n * @import {DefaultTreeAdapterMap, Token} from 'parse5'\n * @import {Schema} from 'property-information'\n * @import {Point, Position} from 'unist'\n * @import {VFile} from 'vfile'\n * @import {Options} from 'hast-util-from-parse5'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean} location\n *   Whether location info was found.\n * @property {Schema} schema\n *   Current schema.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n */\n\nimport {ok as assert} from 'devlop'\nimport {h, s} from 'hastscript'\nimport {find, html, svg} from 'property-information'\nimport {location} from 'vfile-location'\nimport {webNamespaces} from 'web-namespaces'\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {DefaultTreeAdapterMap['node']} tree\n *   `parse5` tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   hast tree.\n */\nexport function fromParse5(tree, options) {\n  const settings = options || {}\n\n  return one(\n    {\n      file: settings.file || undefined,\n      location: false,\n      schema: settings.space === 'svg' ? svg : html,\n      verbose: settings.verbose || false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} node\n *   p5 node.\n * @returns {Nodes}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Nodes} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {DefaultTreeAdapterMap['commentNode']} */ (\n        node\n      )\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference =\n        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */ (\n          node\n        )\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const document = String(state.file)\n        const loc = location(document)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(document.length)\n        // Always defined as we give valid input.\n        assert(start, 'expected `start`')\n        assert(end, 'expected `end`')\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {DefaultTreeAdapterMap['documentType']} */ (\n        node\n      )\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {DefaultTreeAdapterMap['textNode']} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {DefaultTreeAdapterMap['element']} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<DefaultTreeAdapterMap['node']>} nodes\n *   Nodes.\n * @returns {Array<RootContent>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<RootContent>} */\n  const results = []\n\n  while (++index < nodes.length) {\n    // Assume no roots in `nodes`.\n    const result = /** @type {RootContent} */ (one(state, nodes[index]))\n    results.push(result)\n  }\n\n  return results\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['element']} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === webNamespaces.svg ? svg : html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const properties = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      properties[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const x = state.schema.space === 'svg' ? s : h\n  const result = x(node.tagName, properties, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {DefaultTreeAdapterMap['template']} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    // Root in, root out.\n    const content = /** @type {Root} */ (one(state, reference.content))\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} from\n *   p5 node.\n * @param {Nodes} to\n *   hast node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node.\n * @param {Token.ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const properties = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            properties[find(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      assert(location.startTag, 'a start tag should exist')\n      const opening = position(location.startTag)\n      const closing = location.endTag ? position(location.endTag) : undefined\n      /** @type {ElementData['position']} */\n      const data = {opening}\n      if (closing) data.closing = closing\n      data.properties = properties\n\n      node.data = {position: data}\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {Token.Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n\n  // @ts-expect-error: we do use `undefined` for points if one or the other\n  // exists.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC;;;AAED;AACA;AACA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAC7B,oBAAoB,GACpB,iCAAiC;AACjC,MAAM,QAAQ,OAAO,SAAS;AAYvB,SAAS,WAAW,IAAI,EAAE,OAAO;IACtC,MAAM,WAAW,WAAW,CAAC;IAE7B,OAAO,IACL;QACE,MAAM,SAAS,IAAI,IAAI;QACvB,UAAU;QACV,QAAQ,SAAS,KAAK,KAAK,QAAQ,gKAAA,CAAA,MAAG,GAAG,gKAAA,CAAA,OAAI;QAC7C,SAAS,SAAS,OAAO,IAAI;IAC/B,GACA;AAEJ;AAEA;;;;;;;;;CASC,GACD,SAAS,IAAI,KAAK,EAAE,IAAI;IACtB,kBAAkB,GAClB,IAAI;IAEJ,OAAQ,KAAK,QAAQ;QACnB,KAAK;YAAY;gBACf,MAAM,YACJ;gBAEF,SAAS;oBAAC,MAAM;oBAAW,OAAO,UAAU,IAAI;gBAAA;gBAChD,MAAM,OAAO,WAAW;gBACxB,OAAO;YACT;QAEA,KAAK;QACL,KAAK;YAAsB;gBACzB,MAAM,YAEF;gBAEJ,MAAM,aACJ,UAAU,YACN,UAAU,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,mBAClD;gBAEN,SAAS;oBACP,MAAM;oBACN,UAAU,IAAI,OAAO,KAAK,UAAU;oBACpC,MAAM;wBAAC;oBAAU;gBACnB;gBAEA,IAAI,MAAM,IAAI,IAAI,MAAM,QAAQ,EAAE;oBAChC,MAAM,WAAW,OAAO,MAAM,IAAI;oBAClC,MAAM,MAAM,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE;oBACrB,MAAM,QAAQ,IAAI,OAAO,CAAC;oBAC1B,MAAM,MAAM,IAAI,OAAO,CAAC,SAAS,MAAM;oBACvC,yCAAyC;oBACzC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,OAAO;oBACd,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK;oBACZ,OAAO,QAAQ,GAAG;wBAAC;wBAAO;oBAAG;gBAC/B;gBAEA,OAAO;YACT;QAEA,KAAK;YAAiB;gBACpB,MAAM,YACJ;gBAEF,SAAS;oBAAC,MAAM;gBAAS;gBACzB,MAAM,OAAO,WAAW;gBACxB,OAAO;YACT;QAEA,KAAK;YAAS;gBACZ,MAAM,YAA8D;gBACpE,SAAS;oBAAC,MAAM;oBAAQ,OAAO,UAAU,KAAK;gBAAA;gBAC9C,MAAM,OAAO,WAAW;gBACxB,OAAO;YACT;QAEA,WAAW;QACX;YAAS;gBACP,MAAM,YAA6D;gBACnE,SAAS,QAAQ,OAAO;gBACxB,OAAO;YACT;IACF;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,IAAI,KAAK,EAAE,KAAK;IACvB,IAAI,QAAQ,CAAC;IACb,+BAA+B,GAC/B,MAAM,UAAU,EAAE;IAElB,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,8BAA8B;QAC9B,MAAM,SAAqC,IAAI,OAAO,KAAK,CAAC,MAAM;QAClE,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,QAAQ,KAAK,EAAE,IAAI;IAC1B,MAAM,SAAS,MAAM,MAAM;IAE3B,MAAM,MAAM,GAAG,KAAK,YAAY,KAAK,0IAAA,CAAA,gBAAa,CAAC,GAAG,GAAG,gKAAA,CAAA,MAAG,GAAG,gKAAA,CAAA,OAAI;IAEnE,SAAS;IACT,IAAI,QAAQ,CAAC;IACb,mCAAmC,GACnC,MAAM,aAAa,CAAC;IAEpB,MAAO,EAAE,QAAQ,KAAK,KAAK,CAAC,MAAM,CAAE;QAClC,MAAM,YAAY,KAAK,KAAK,CAAC,MAAM;QACnC,MAAM,OACJ,CAAC,UAAU,MAAM,GAAG,UAAU,MAAM,GAAG,MAAM,EAAE,IAAI,UAAU,IAAI;QACnE,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,OAAO;YAC1B,UAAU,CAAC,KAAK,GAAG,UAAU,KAAK;QACpC;IACF;IAEA,SAAS;IACT,MAAM,IAAI,MAAM,MAAM,CAAC,KAAK,KAAK,QAAQ,0IAAA,CAAA,IAAC,GAAG,0IAAA,CAAA,IAAC;IAC9C,MAAM,SAAS,EAAE,KAAK,OAAO,EAAE,YAAY,IAAI,OAAO,KAAK,UAAU;IACrE,MAAM,OAAO,MAAM;IAEnB,kBAAkB;IAClB,IAAI,OAAO,OAAO,KAAK,YAAY;QACjC,MAAM,YAA8D;QACpE,MAAM,MAAM,UAAU,kBAAkB;QACxC,MAAM,WAAW,OAAO,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ;QAC7D,MAAM,SAAS,OAAO,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM;QAEvD,qBAAqB;QACrB,MAAM,UAA+B,IAAI,OAAO,UAAU,OAAO;QAEjE,IAAI,YAAY,UAAU,MAAM,IAAI,EAAE;YACpC,QAAQ,QAAQ,GAAG;gBAAC,OAAO,SAAS,GAAG;gBAAE,KAAK,OAAO,KAAK;YAAA;QAC5D;QAEA,OAAO,OAAO,GAAG;IACnB;IAEA,MAAM,MAAM,GAAG;IAEf,OAAO;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,KAAK,EAAE,IAAI,EAAE,EAAE;IAC5B,IAAI,wBAAwB,QAAQ,KAAK,kBAAkB,IAAI,MAAM,IAAI,EAAE;QACzE,MAAM,WAAW,eAAe,OAAO,IAAI,KAAK,kBAAkB;QAElE,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;YACjB,GAAG,QAAQ,GAAG;QAChB;IACF;AACF;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,eAAe,KAAK,EAAE,IAAI,EAAE,QAAQ;IAC3C,MAAM,SAAS,SAAS;IAExB,IAAI,KAAK,IAAI,KAAK,WAAW;QAC3B,MAAM,OAAO,KAAK,QAAQ,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,EAAE;QAEpD,kCAAkC;QAClC,wDAAwD;QACxD,IACE,UACA,CAAC,SAAS,MAAM,IAChB,QACA,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,GAAG,EACjB;YACA,OAAO,GAAG,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG;QAClD;QAEA,IAAI,MAAM,OAAO,EAAE;YACjB,iDAAiD,GACjD,MAAM,aAAa,CAAC;YACpB,mBAAmB,GACnB,IAAI;YAEJ,IAAI,SAAS,KAAK,EAAE;gBAClB,IAAK,OAAO,SAAS,KAAK,CAAE;oBAC1B,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,MAAM;wBACjC,UAAU,CAAC,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,MAAM,EAAE,KAAK,QAAQ,CAAC,GAAG,SAC7C,SAAS,KAAK,CAAC,IAAI;oBAEvB;gBACF;YACF;YAEA,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,QAAQ,EAAE;YAC1B,MAAM,UAAU,SAAS,SAAS,QAAQ;YAC1C,MAAM,UAAU,SAAS,MAAM,GAAG,SAAS,SAAS,MAAM,IAAI;YAC9D,oCAAoC,GACpC,MAAM,OAAO;gBAAC;YAAO;YACrB,IAAI,SAAS,KAAK,OAAO,GAAG;YAC5B,KAAK,UAAU,GAAG;YAElB,KAAK,IAAI,GAAG;gBAAC,UAAU;YAAI;QAC7B;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,GAAG;IACnB,MAAM,QAAQ,MAAM;QAClB,MAAM,IAAI,SAAS;QACnB,QAAQ,IAAI,QAAQ;QACpB,QAAQ,IAAI,WAAW;IACzB;IACA,MAAM,MAAM,MAAM;QAChB,MAAM,IAAI,OAAO;QACjB,QAAQ,IAAI,MAAM;QAClB,QAAQ,IAAI,SAAS;IACvB;IAEA,yEAAyE;IACzE,UAAU;IACV,OAAO,SAAS,MAAM;QAAC;QAAO;IAAG,IAAI;AACvC;AAEA;;;;;;;CAOC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,GAAG,QAAQ;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/entities/dist/esm/generated/decode-data-html.js", "sourceRoot": "", "sources": ["../../../src/generated/decode-data-html.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,8CAA8C;;;;AAEvC,MAAM,cAAc,GAAgB,aAAA,EAAe,CAAC,IAAI,WAAW,CACtE,kBAAkB;AAClB,aAAA,EAAe,CAAC,268CAA268C,CACt78C,KAAK,CAAC,EAAE,CAAC,CACT,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC", "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/entities/dist/esm/generated/decode-data-xml.js", "sourceRoot": "", "sources": ["../../../src/generated/decode-data-xml.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,8CAA8C;;;;AAEvC,MAAM,aAAa,GAAgB,aAAA,EAAe,CAAC,IAAI,WAAW,CACrE,kBAAkB;AAClB,aAAA,EAAe,CAAC,uFAAuF,CAClG,KAAK,CAAC,EAAE,CAAC,CACT,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC", "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/entities/dist/esm/decode-codepoint.js", "sourceRoot": "", "sources": ["../../src/decode-codepoint.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,qHAAqH;;;;;;;AAErH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC;IACtB;QAAC,CAAC;QAAE,KAAM;KAAC;IACX,sDAAsD;IACtD;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,GAAG;KAAC;CACb,CAAC,CAAC;AAKI,MAAM,aAAa,GACtB,8GAA8G;AAC9G,CAAA,KAAA,MAAM,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KACpB,SAAU,SAAiB;IACvB,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,SAAS,GAAG,KAAO,EAAE,CAAC;QACtB,SAAS,IAAI,KAAS,CAAC;QACvB,MAAM,IAAI,MAAM,CAAC,YAAY,CACzB,AAAE,CAAD,QAAU,KAAK,EAAE,CAAC,EAAG,IAAM,CAAC,EAAG,KAAO,CAC1C,CAAC;QACF,SAAS,GAAG,KAAO,GAAG,AAAC,SAAS,GAAG,IAAM,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACzC,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAOA,SAAU,gBAAgB,CAAC,SAAiB;;IAC9C,IACI,AAAC,SAAS,IAAI,KAAO,IAAI,SAAS,IAAI,KAAO,CAAC,GAC9C,SAAS,GAAG,OAAU,EACxB,CAAC;QACC,OAAO,KAAO,CAAC;IACnB,CAAC;IAED,OAAO,CAAA,KAAA,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,SAAS,CAAC;AACjD,CAAC;AASK,SAAU,eAAe,CAAC,SAAiB;IAC7C,OAAO,aAAa,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;AACtD,CAAC", "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/entities/dist/esm/decode.js", "sourceRoot": "", "sources": ["../../src/decode.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iCAAiC,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAC/D,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;;;;AAExE,IAAW,SAaV;AAbD,CAAA,SAAW,SAAS;IAChB,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,GAAA,GAAA,KAAQ,CAAA;IACR,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,IAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,IAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,IAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;AAChB,CAAC,EAbU,SAAS,IAAA,CAAT,SAAS,GAAA,CAAA,CAAA,GAanB;AAED,oFAAA,EAAsF,CACtF,MAAM,YAAY,GAAG,EAAS,CAAC;AAE/B,IAAY,YAIX;AAJD,CAAA,SAAY,YAAY;IACpB,YAAA,CAAA,YAAA,CAAA,eAAA,GAAA,MAAA,GAAA,cAAoC,CAAA;IACpC,YAAA,CAAA,YAAA,CAAA,gBAAA,GAAA,MAAA,GAAA,eAAqC,CAAA;IACrC,YAAA,CAAA,YAAA,CAAA,aAAA,GAAA,IAAA,GAAA,YAAkC,CAAA;AACtC,CAAC,EAJW,YAAY,IAAA,CAAZ,YAAY,GAAA,CAAA,CAAA,GAIvB;AAED,SAAS,QAAQ,CAAC,IAAY;IAC1B,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC;AAC5D,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAY;IACxC,OAAO,AACH,AAAC,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,GACvD,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,CAC3D,CAAC;AACN,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAY;IACrC,OAAO,AACH,AAAC,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,GACvD,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,GACxD,QAAQ,CAAC,IAAI,CAAC,CACjB,CAAC;AACN,CAAC;AAED;;;;;GAKG,CACH,SAAS,6BAA6B,CAAC,IAAY;IAC/C,OAAO,IAAI,KAAK,SAAS,CAAC,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAClE,CAAC;AAED,IAAW,kBAMV;AAND,CAAA,SAAW,kBAAkB;IACzB,kBAAA,CAAA,kBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAW,CAAA;IACX,kBAAA,CAAA,kBAAA,CAAA,eAAA,GAAA,EAAA,GAAA,cAAY,CAAA;IACZ,kBAAA,CAAA,kBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAc,CAAA;IACd,kBAAA,CAAA,kBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV,kBAAA,CAAA,kBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAW,CAAA;AACf,CAAC,EANU,kBAAkB,IAAA,CAAlB,kBAAkB,GAAA,CAAA,CAAA,GAM5B;AAED,IAAY,YAOX;AAPD,CAAA,SAAY,YAAY;IACpB,4DAAA,EAA8D,CAC9D,YAAA,CAAA,YAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,qDAAA,EAAuD,CACvD,YAAA,CAAA,YAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,kEAAA,EAAoE,CACpE,YAAA,CAAA,YAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAa,CAAA;AACjB,CAAC,EAPW,YAAY,IAAA,CAAZ,YAAY,GAAA,CAAA,CAAA,GAOvB;AAgBK,MAAO,aAAa;IACtB,YACI,sCAAA,EAAwC,CACvB,UAAuB,EACxC;;;;;;;;OAQG,CACc,aAAqD,EACtE,8CAAA,EAAgD,CAC/B,MAAwC,CAAA;QAZxC,IAAA,CAAA,UAAU,GAAV,UAAU,CAAa;QAUvB,IAAA,CAAA,aAAa,GAAb,aAAa,CAAwC;QAErD,IAAA,CAAA,MAAM,GAAN,MAAM,CAAkC;QAG7D,sCAAA,EAAwC,CAChC,IAAA,CAAA,KAAK,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC/C,2DAAA,EAA6D,CACrD,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC;QACrB;;;;;WAKG,CACK,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QAEnB,0CAAA,EAA4C,CACpC,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QACtB,2DAAA,EAA6D,CACrD,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QACnB,gDAAA,EAAkD,CAC1C,IAAA,CAAA,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;IAnBtC,CAAC;IAqBJ,6CAAA,EAA+C,CAC/C,WAAW,CAAC,UAAwB,EAAA;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;IAED;;;;;;;;;;OAUG,CACH,KAAK,CAAC,KAAa,EAAE,MAAc,EAAA;QAC/B,OAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC,CAAC;oBAClC,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE,CAAC;wBAC7C,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,YAAY,CAAC;wBAC7C,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;wBACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;oBACrD,CAAC;oBACD,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,WAAW,CAAC;oBAC5C,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAChD,CAAC;YAED,KAAK,kBAAkB,CAAC,YAAY,CAAC;gBAAC,CAAC;oBACnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACjD,CAAC;YAED,KAAK,kBAAkB,CAAC,cAAc,CAAC;gBAAC,CAAC;oBACrC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACnD,CAAC;YAED,KAAK,kBAAkB,CAAC,UAAU,CAAC;gBAAC,CAAC;oBACjC,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC/C,CAAC;YAED,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC,CAAC;oBAClC,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAChD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG,CACK,iBAAiB,CAAC,KAAa,EAAE,MAAc,EAAA;QACnD,IAAI,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YAClE,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC;YAC3C,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YACnB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAEO,kBAAkB,CACtB,KAAa,EACb,KAAa,EACb,GAAW,EACX,IAAY,EAAA;QAEZ,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAChB,MAAM,UAAU,GAAG,GAAG,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,GACP,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GACxC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,QAAQ,IAAI,UAAU,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG,CACK,eAAe,CAAC,KAAa,EAAE,MAAc,EAAA;QACjD,MAAM,UAAU,GAAG,MAAM,CAAC;QAE1B,MAAO,MAAM,GAAG,KAAK,CAAC,MAAM,CAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjD,MAAM,IAAI,CAAC,CAAC;YAChB,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;gBACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAEvD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG,CACK,mBAAmB,CAAC,KAAa,EAAE,MAAc,EAAA;QACrD,MAAM,UAAU,GAAG,MAAM,CAAC;QAE1B,MAAO,MAAM,GAAG,KAAK,CAAC,MAAM,CAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,CAAC;YAChB,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;gBACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAEvD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;OAYG,CACK,iBAAiB,CAAC,MAAc,EAAE,cAAsB,EAAA;;QAC5D,yCAAyC;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,cAAc,EAAE,CAAC;YAClC,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,0CAA0C,CACnD,IAAI,CAAC,QAAQ,CAChB,CAAC;YACF,OAAO,CAAC,CAAC;QACb,CAAC;QAED,kDAAkD;QAClD,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACvB,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;YACjD,OAAO,CAAC,CAAC;QACb,CAAC;QAED,IAAI,CAAC,aAAa,oKAAC,mBAAA,AAAgB,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,uCAAuC,EAAE,CAAC;YAC1D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG,CACK,gBAAgB,CAAC,KAAa,EAAE,MAAc,EAAA;QAClD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,IAAI,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,4EAA4E;QAC5E,IAAI,WAAW,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAE9D,MAAO,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAE,CAAC;YACpD,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAEtC,IAAI,CAAC,SAAS,GAAG,eAAe,CAC5B,UAAU,EACV,OAAO,EACP,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,EACzC,IAAI,CACP,CAAC;YAEF,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,IAEnB,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,SAAS,IACvC,8DAA8D;gBAC9D,CAAC,WAAW,KAAK,CAAC,IACd,6CAA6C;gBAC7C,6BAA6B,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3C,CAAC,GACD,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC9C,CAAC;YAED,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,WAAW,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YAE1D,kDAAkD;YAClD,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;gBACpB,2DAA2D;gBAC3D,IAAI,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;oBAC1B,OAAO,IAAI,CAAC,mBAAmB,CAC3B,IAAI,CAAC,SAAS,EACd,WAAW,EACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAC9B,CAAC;gBACN,CAAC;gBAED,2FAA2F;gBAC3F,IAAI,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;oBAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;oBAC7B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;oBAC7B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;OAIG,CACK,4BAA4B,GAAA;;QAChC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAEpC,MAAM,WAAW,GACb,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAE3D,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7D,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,uCAAuC,EAAE,CAAC;QAEvD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG,CACK,mBAAmB,CACvB,MAAc,EACd,WAAmB,EACnB,QAAgB,EAAA;QAEhB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,aAAa,CACd,WAAW,KAAK,CAAC,GACX,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,GAC/C,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5B,QAAQ,CACX,CAAC;QACF,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACpB,0DAA0D;YAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;OAMG,CACH,GAAG,GAAA;;QACC,OAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC,CAAC;oBAClC,sCAAsC;oBACtC,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,IACpB,CAAC,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,SAAS,IACvC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,GACjC,IAAI,CAAC,4BAA4B,EAAE,GACnC,CAAC,CAAC;gBACZ,CAAC;YACD,mDAAmD;YACnD,KAAK,kBAAkB,CAAC,cAAc,CAAC;gBAAC,CAAC;oBACrC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxC,CAAC;YACD,KAAK,kBAAkB,CAAC,UAAU,CAAC;gBAAC,CAAC;oBACjC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxC,CAAC;YACD,KAAK,kBAAkB,CAAC,YAAY,CAAC;gBAAC,CAAC;oBACnC,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,0CAA0C,CACnD,IAAI,CAAC,QAAQ,CAChB,CAAC;oBACF,OAAO,CAAC,CAAC;gBACb,CAAC;YACD,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC,CAAC;oBAClC,iCAAiC;oBACjC,OAAO,CAAC,CAAC;gBACb,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAED;;;;;GAKG,CACH,SAAS,UAAU,CAAC,UAAuB;IACvC,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,MAAM,OAAO,GAAG,IAAI,aAAa,CAC7B,UAAU,EACV,CAAC,IAAI,EAAE,CAAI,CAAF,CAAC,SAAY,uKAAI,gBAAA,AAAa,EAAC,IAAI,CAAC,CAAC,CACjD,CAAC;IAEF,OAAO,SAAS,cAAc,CAC1B,KAAa,EACb,UAAwB;QAExB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,MAAO,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAE,CAAC;YAChD,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE9C,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CACxB,KAAK,EACL,eAAe;YACf,MAAM,GAAG,CAAC,CACb,CAAC;YAEF,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;gBACb,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;gBACnC,MAAM;YACV,CAAC;YAED,SAAS,GAAG,MAAM,GAAG,MAAM,CAAC;YAC5B,uDAAuD;YACvD,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACtD,CAAC;QAED,MAAM,MAAM,GAAG,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEpD,2DAA2D;QAC3D,WAAW,GAAG,EAAE,CAAC;QAEjB,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACN,CAAC;AAYK,SAAU,eAAe,CAC3B,UAAuB,EACvB,OAAe,EACf,SAAiB,EACjB,IAAY;IAEZ,MAAM,WAAW,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChE,MAAM,UAAU,GAAG,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC;IAErD,+CAA+C;IAC/C,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;QACpB,OAAO,UAAU,KAAK,CAAC,IAAI,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,kDAAkD;IAClD,IAAI,UAAU,EAAE,CAAC;QACb,MAAM,KAAK,GAAG,IAAI,GAAG,UAAU,CAAC;QAEhC,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,WAAW,GAClC,CAAC,CAAC,GACF,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,kDAAkD;IAElD,mCAAmC;IACnC,IAAI,EAAE,GAAG,SAAS,CAAC;IACnB,IAAI,EAAE,GAAG,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;IAE9B,MAAO,EAAE,IAAI,EAAE,CAAE,CAAC;QACd,MAAM,GAAG,GAAI,AAAD,EAAG,GAAG,EAAE,CAAC,IAAK,CAAC,CAAC;QAC5B,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAEjC,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YAClB,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;QACjB,CAAC,MAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;YACzB,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;QACjB,CAAC,MAAM,CAAC;YACJ,OAAO,UAAU,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,CAAC,CAAC;AACd,CAAC;AAED,MAAM,WAAW,GAAG,aAAA,EAAe,CAAC,UAAU,gLAAC,iBAAc,CAAC,CAAC;AAC/D,MAAM,UAAU,GAAG,aAAA,EAAe,CAAC,UAAU,+KAAC,gBAAa,CAAC,CAAC;AASvD,SAAU,UAAU,CACtB,UAAkB,EAClB,OAAqB,YAAY,CAAC,MAAM;IAExC,OAAO,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACzC,CAAC;AAQK,SAAU,mBAAmB,CAAC,aAAqB;IACrD,OAAO,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;AAC9D,CAAC;AAQK,SAAU,gBAAgB,CAAC,UAAkB;IAC/C,OAAO,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACxD,CAAC;AAQK,SAAU,SAAS,CAAC,SAAiB;IACvC,OAAO,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACtD,CAAC", "debugId": null}}, {"offset": {"line": 2079, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/entities/dist/esm/escape.js", "sourceRoot": "", "sources": ["../../src/escape.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;AAAO,MAAM,WAAW,GAAW,wBAAwB,CAAC;AAE5D,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;IACvB;QAAC,EAAE;QAAE,QAAQ;KAAC;IACd;QAAC,EAAE;QAAE,OAAO;KAAC;IACb;QAAC,EAAE;QAAE,QAAQ;KAAC;IACd;QAAC,EAAE;QAAE,MAAM;KAAC;IACZ;QAAC,EAAE;QAAE,MAAM;KAAC;CACf,CAAC,CAAC;AAGI,MAAM,YAAY,GACrB,uEAAuE;AACvE,MAAM,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI,GAC9B,CAAC,CAAS,EAAE,KAAa,EAAU,CAC/B,CAAC,AADgC,CAC/B,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAO,CAAC,KAAK,KAAO,GACrC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAO,CAAC,GAAG,IAAM,GACxC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GACvB,KAAO,GACP,KAAS,GACT,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAE7B,CAAC,KAAa,EAAE,KAAa,EAAU,CAAG,CAAD,IAAM,CAAC,WAAW,CAAC,KAAK,CAAE,CAAC;AASxE,SAAU,SAAS,CAAC,KAAa;IACnC,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,CAAC;IAEV,MAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,CAAE,CAAC;QAChD,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QACxB,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACrB,WAAW,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA,GAAA,EAAM,YAAY,CACjE,KAAK,EACL,KAAK,CACR,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA,CAAA,CAAG,CAAC;YAClB,4CAA4C;YAC5C,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,MAAM,CACvC,CAAC,IAAI,GAAG,KAAO,CAAC,KAAK,KAAO,CAC/B,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;YACxD,SAAS,GAAG,KAAK,GAAG,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED,OAAO,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjD,CAAC;AAWM,MAAM,MAAM,GAAqB,SAAS,CAAC;AAElD;;;;;;;;;GASG,CACH,SAAS,UAAU,CACf,KAAa,EACb,GAAwB;IAExB,OAAO,SAAS,MAAM,CAAC,IAAY;QAC/B,IAAI,KAAK,CAAC;QACV,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,MAAQ,CAAD,IAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,AAAE,CAAC;YAChC,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC;YAED,kDAAkD;YAClD,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC;YAE3C,kCAAkC;YAClC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC,CAAC;AACN,CAAC;AASM,MAAM,UAAU,GAA6B,aAAA,EAAe,CAAC,UAAU,CAC1E,UAAU,EACV,UAAU,CACb,CAAC;AAQK,MAAM,eAAe,GACxB,aAAA,EAAe,CAAC,UAAU,CACtB,aAAa,EACb,IAAI,GAAG,CAAC;IACJ;QAAC,EAAE;QAAE,QAAQ;KAAC;IACd;QAAC,EAAE;QAAE,OAAO;KAAC;IACb;QAAC,GAAG;QAAE,QAAQ;KAAC;CAClB,CAAC,CACL,CAAC;AAQC,MAAM,UAAU,GAA6B,aAAA,EAAe,CAAC,UAAU,CAC1E,cAAc,EACd,IAAI,GAAG,CAAC;IACJ;QAAC,EAAE;QAAE,OAAO;KAAC;IACb;QAAC,EAAE;QAAE,MAAM;KAAC;IACZ;QAAC,EAAE;QAAE,MAAM;KAAC;IACZ;QAAC,GAAG;QAAE,QAAQ;KAAC;CAClB,CAAC,CACL,CAAC", "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unist-util-stringify-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;;;;;;CAcC;;;AACM,SAAS,kBAAkB,KAAK;IACrC,WAAW;IACX,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO;IACT;IAEA,QAAQ;IACR,IAAI,cAAc,SAAS,UAAU,OAAO;QAC1C,OAAO,SAAS,MAAM,QAAQ;IAChC;IAEA,YAAY;IACZ,IAAI,WAAW,SAAS,SAAS,OAAO;QACtC,OAAO,SAAS;IAClB;IAEA,SAAS;IACT,IAAI,UAAU,SAAS,YAAY,OAAO;QACxC,OAAO,MAAM;IACf;IAEA,IAAI;IACJ,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,SAAS,MAAM,IAAI,IAAI,MAAM,MAAM,SAAS,MAAM,MAAM;AACvE;AAEA;;;CAGC,GACD,SAAS,SAAS,GAAG;IACnB,OAAO,MAAM,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7D;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2272, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/vfile-message/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef Options\n *   Configuration.\n * @property {Array<Node> | null | undefined} [ancestors]\n *   Stack of (inclusive) ancestor nodes surrounding the message (optional).\n * @property {Error | null | undefined} [cause]\n *   Original error cause of the message (optional).\n * @property {Point | Position | null | undefined} [place]\n *   Place of message (optional).\n * @property {string | null | undefined} [ruleId]\n *   Category of message (optional, example: `'my-rule'`).\n * @property {string | null | undefined} [source]\n *   Namespace of who sent the message (optional, example: `'my-package'`).\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Options | null | undefined} [options]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | Options | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // eslint-disable-next-line complexity\n  constructor(causeOrReason, optionsOrParentOrPlace, origin) {\n    super()\n\n    if (typeof optionsOrParentOrPlace === 'string') {\n      origin = optionsOrParentOrPlace\n      optionsOrParentOrPlace = undefined\n    }\n\n    /** @type {string} */\n    let reason = ''\n    /** @type {Options} */\n    let options = {}\n    let legacyCause = false\n\n    if (optionsOrParentOrPlace) {\n      // Point.\n      if (\n        'line' in optionsOrParentOrPlace &&\n        'column' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Position.\n      else if (\n        'start' in optionsOrParentOrPlace &&\n        'end' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Node.\n      else if ('type' in optionsOrParentOrPlace) {\n        options = {\n          ancestors: [optionsOrParentOrPlace],\n          place: optionsOrParentOrPlace.position\n        }\n      }\n      // Options.\n      else {\n        options = {...optionsOrParentOrPlace}\n      }\n    }\n\n    if (typeof causeOrReason === 'string') {\n      reason = causeOrReason\n    }\n    // Error.\n    else if (!options.cause && causeOrReason) {\n      legacyCause = true\n      reason = causeOrReason.message\n      options.cause = causeOrReason\n    }\n\n    if (!options.ruleId && !options.source && typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        options.ruleId = origin\n      } else {\n        options.source = origin.slice(0, index)\n        options.ruleId = origin.slice(index + 1)\n      }\n    }\n\n    if (!options.place && options.ancestors && options.ancestors) {\n      const parent = options.ancestors[options.ancestors.length - 1]\n\n      if (parent) {\n        options.place = parent.position\n      }\n    }\n\n    const start =\n      options.place && 'start' in options.place\n        ? options.place.start\n        : options.place\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Stack of ancestor nodes surrounding the message.\n     *\n     * @type {Array<Node> | undefined}\n     */\n    this.ancestors = options.ancestors || undefined\n\n    /**\n     * Original error cause of the message.\n     *\n     * @type {Error | undefined}\n     */\n    this.cause = options.cause || undefined\n\n    /**\n     * Starting column of message.\n     *\n     * @type {number | undefined}\n     */\n    this.column = start ? start.column : undefined\n\n    /**\n     * State of problem.\n     *\n     * * `true` — error, file not usable\n     * * `false` — warning, change may be needed\n     * * `undefined` — change likely not needed\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal = undefined\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | undefined}\n     */\n    this.file\n\n    // Field from `Error`.\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = reason\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | undefined}\n     */\n    this.line = start ? start.line : undefined\n\n    // Field from `Error`.\n    /**\n     * Serialized positional info of message.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(options.place) || '1:1'\n\n    /**\n     * Place of message.\n     *\n     * @type {Point | Position | undefined}\n     */\n    this.place = options.place || undefined\n\n    /**\n     * Reason for message, should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | undefined}\n     */\n    this.ruleId = options.ruleId || undefined\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | undefined}\n     */\n    this.source = options.source || undefined\n\n    // Field from `Error`.\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack =\n      legacyCause && options.cause && typeof options.cause.stack === 'string'\n        ? options.cause.stack\n        : ''\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | undefined}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | undefined}\n     */\n    this.expected\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | undefined}\n     */\n    this.note\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | undefined}\n     */\n    this.url\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.column = undefined\nVFileMessage.prototype.line = undefined\nVFileMessage.prototype.ancestors = undefined\nVFileMessage.prototype.cause = undefined\nVFileMessage.prototype.fatal = undefined\nVFileMessage.prototype.place = undefined\nVFileMessage.prototype.ruleId = undefined\nVFileMessage.prototype.source = undefined\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;;;CAeC;;;AAED;;AAKO,MAAM,qBAAqB;IAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqDC,GACD,sCAAsC;IACtC,YAAY,aAAa,EAAE,sBAAsB,EAAE,MAAM,CAAE;QACzD,KAAK;QAEL,IAAI,OAAO,2BAA2B,UAAU;YAC9C,SAAS;YACT,yBAAyB;QAC3B;QAEA,mBAAmB,GACnB,IAAI,SAAS;QACb,oBAAoB,GACpB,IAAI,UAAU,CAAC;QACf,IAAI,cAAc;QAElB,IAAI,wBAAwB;YAC1B,SAAS;YACT,IACE,UAAU,0BACV,YAAY,wBACZ;gBACA,UAAU;oBAAC,OAAO;gBAAsB;YAC1C,OAEK,IACH,WAAW,0BACX,SAAS,wBACT;gBACA,UAAU;oBAAC,OAAO;gBAAsB;YAC1C,OAEK,IAAI,UAAU,wBAAwB;gBACzC,UAAU;oBACR,WAAW;wBAAC;qBAAuB;oBACnC,OAAO,uBAAuB,QAAQ;gBACxC;YACF,OAEK;gBACH,UAAU;oBAAC,GAAG,sBAAsB;gBAAA;YACtC;QACF;QAEA,IAAI,OAAO,kBAAkB,UAAU;YACrC,SAAS;QACX,OAEK,IAAI,CAAC,QAAQ,KAAK,IAAI,eAAe;YACxC,cAAc;YACd,SAAS,cAAc,OAAO;YAC9B,QAAQ,KAAK,GAAG;QAClB;QAEA,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,IAAI,OAAO,WAAW,UAAU;YACpE,MAAM,QAAQ,OAAO,OAAO,CAAC;YAE7B,IAAI,UAAU,CAAC,GAAG;gBAChB,QAAQ,MAAM,GAAG;YACnB,OAAO;gBACL,QAAQ,MAAM,GAAG,OAAO,KAAK,CAAC,GAAG;gBACjC,QAAQ,MAAM,GAAG,OAAO,KAAK,CAAC,QAAQ;YACxC;QACF;QAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,EAAE;YAC5D,MAAM,SAAS,QAAQ,SAAS,CAAC,QAAQ,SAAS,CAAC,MAAM,GAAG,EAAE;YAE9D,IAAI,QAAQ;gBACV,QAAQ,KAAK,GAAG,OAAO,QAAQ;YACjC;QACF;QAEA,MAAM,QACJ,QAAQ,KAAK,IAAI,WAAW,QAAQ,KAAK,GACrC,QAAQ,KAAK,CAAC,KAAK,GACnB,QAAQ,KAAK;QAEnB,wCAAwC,GACxC;;;;KAIC,GACD,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,IAAI;QAEtC;;;;KAIC,GACD,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,IAAI;QAE9B;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,MAAM,GAAG;QAErC;;;;;;;;KAQC,GACD,IAAI,CAAC,KAAK,GAAG;QAEb;;;;KAIC,GACD,IAAI,CAAC,IAAI;QAET,sBAAsB;QACtB;;;;KAIC,GACD,IAAI,CAAC,OAAO,GAAG;QAEf;;;;KAIC,GACD,IAAI,CAAC,IAAI,GAAG,QAAQ,MAAM,IAAI,GAAG;QAEjC,sBAAsB;QACtB;;;;;KAKC,GACD,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,sKAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,KAAK,KAAK;QAEhD;;;;KAIC,GACD,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,IAAI;QAE9B;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;QAE1B;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,IAAI;QAEhC;;;;KAIC,GACD,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM,IAAI;QAEhC,sBAAsB;QACtB;;;;;;;KAOC,GACD,IAAI,CAAC,KAAK,GACR,eAAe,QAAQ,KAAK,IAAI,OAAO,QAAQ,KAAK,CAAC,KAAK,KAAK,WAC3D,QAAQ,KAAK,CAAC,KAAK,GACnB;QAEN,yCAAyC;QACzC,gBAAgB;QAChB,+DAA+D;QAE/D;;;;;KAKC,GACD,IAAI,CAAC,MAAM;QAEX;;;;KAIC,GACD,IAAI,CAAC,QAAQ;QAEb;;;;KAIC,GACD,IAAI,CAAC,IAAI;QAET;;;;;;;KAOC,GACD,IAAI,CAAC,GAAG;IACR,uCAAuC,GACzC;AACF;AAEA,aAAa,SAAS,CAAC,IAAI,GAAG;AAC9B,aAAa,SAAS,CAAC,IAAI,GAAG;AAC9B,aAAa,SAAS,CAAC,MAAM,GAAG;AAChC,aAAa,SAAS,CAAC,OAAO,GAAG;AACjC,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,MAAM,GAAG;AAChC,aAAa,SAAS,CAAC,IAAI,GAAG;AAC9B,aAAa,SAAS,CAAC,SAAS,GAAG;AACnC,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,KAAK,GAAG;AAC/B,aAAa,SAAS,CAAC,MAAM,GAAG;AAChC,aAAa,SAAS,CAAC,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/vfile/lib/minurl.shared.js"], "sourcesContent": ["/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nexport function isUrl(fileUrlOrPath) {\n  return Boolean(\n    fileUrlOrPath !== null &&\n      typeof fileUrlOrPath === 'object' &&\n      'href' in fileUrlOrPath &&\n      fileUrlOrPath.href &&\n      'protocol' in fileUrlOrPath &&\n      fileUrlOrPath.protocol &&\n      // @ts-expect-error: indexing is fine.\n      fileUrlOrPath.auth === undefined\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;CAiBC,GACD,+EAA+E;;;;AACxE,SAAS,MAAM,aAAa;IACjC,OAAO,QACL,kBAAkB,QAChB,OAAO,kBAAkB,YACzB,UAAU,iBACV,cAAc,IAAI,IAClB,cAAc,iBACd,cAAc,QAAQ,IACtB,sCAAsC;IACtC,cAAc,IAAI,KAAK;AAE7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/vfile/lib/index.js"], "sourcesContent": ["/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {VFileMessage} from 'vfile-message'\nimport {minpath} from '#minpath'\nimport {minproc} from '#minproc'\nimport {urlToPath, isUrl} from '#minurl'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : minproc.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = minpath.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? minpath.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = minpath.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? minpath.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = minpath.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = minpath.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(minpath.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + minpath.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;CAEC;;;AAED;AACA;AACA;AACA;AAAA;;;;;AAEA;;;;CAIC,GACD,MAAM,QAA8B;IAClC;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM;IACX;;;;;;;;;;;;;;;;;;;;;GAqBC,GACD,YAAY,KAAK,CAAE;QACjB,4BAA4B,GAC5B,IAAI;QAEJ,IAAI,CAAC,OAAO;YACV,UAAU,CAAC;QACb,OAAO,IAAI,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,QAAQ;YACvB,UAAU;gBAAC,MAAM;YAAK;QACxB,OAAO,IAAI,OAAO,UAAU,YAAY,aAAa,QAAQ;YAC3D,UAAU;gBAAC;YAAK;QAClB,OAAO;YACL,UAAU;QACZ;QAEA,wCAAwC,GAExC;;;;KAIC,GACD,uEAAuE;QACvE,yDAAyD;QACzD,IAAI,CAAC,GAAG,GAAG,SAAS,UAAU,KAAK,6JAAA,CAAA,UAAO,CAAC,GAAG;QAE9C;;;;;;;KAOC,GACD,IAAI,CAAC,IAAI,GAAG,CAAC;QAEb;;;;;;KAMC,GACD,IAAI,CAAC,OAAO,GAAG,EAAE;QAEjB;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG,EAAE;QAElB;;;;KAIC,GACD,IAAI,CAAC,KAAK;QAEV,qDAAqD;QACrD,gCAAgC;QAChC;;;;;;;KAOC,GACD,IAAI,CAAC,GAAG;QAER;;;;;;;KAOC,GACD,IAAI,CAAC,MAAM;QAEX;;;;;;KAMC,GACD,IAAI,CAAC,MAAM;QACX,uCAAuC,GAEvC,oDAAoD;QACpD,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;YAC7B,MAAM,QAAQ,KAAK,CAAC,MAAM;YAE1B,uEAAuE;YACvE,gBAAgB;YAChB,IACE,SAAS,WACT,OAAO,CAAC,MAAM,KAAK,aACnB,OAAO,CAAC,MAAM,KAAK,MACnB;gBACA,yDAAyD;gBACzD,IAAI,CAAC,MAAM,GAAG,UAAU,YAAY;uBAAI,OAAO,CAAC,MAAM;iBAAC,GAAG,OAAO,CAAC,MAAM;YAC1E;QACF;QAEA,mBAAmB,GACnB,IAAI;QAEJ,mCAAmC;QACnC,IAAK,SAAS,QAAS;YACrB,8CAA8C;YAC9C,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ;gBAC1B,8CAA8C;gBAC9C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,IAAI,WAAW;QACb,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAC1B;IACN;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,SAAS,QAAQ,EAAE;QACrB,eAAe,UAAU;QACzB,WAAW,UAAU;QACrB,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI;IAC/C;IAEA;;;;;GAKC,GACD,IAAI,UAAU;QACZ,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IACzB;IACN;IAEA;;;;;;;;;GASC,GACD,IAAI,QAAQ,OAAO,EAAE;QACnB,WAAW,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ;IACvD;IAEA;;;;;GAKC,GACD,IAAI,UAAU;QACZ,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IACzB;IACN;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,QAAQ,OAAO,EAAE;QACnB,WAAW,SAAS;QACpB,WAAW,IAAI,CAAC,OAAO,EAAE;QAEzB,IAAI,SAAS;YACX,IAAI,QAAQ,WAAW,CAAC,OAAO,GAAG,OAAO,KAAI;gBAC3C,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,QAAQ,QAAQ,CAAC,KAAK,IAAI;gBAC5B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE;IACnE;IAEA;;;;;GAKC,GACD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;IAC9C;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,KAAK,IAAI,EAAE;QACb,IAAI,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,OAAO;YACf,OAAO,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;QACnB;QAEA,eAAe,MAAM;QAErB,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACpB;IACF;IAEA;;;;;GAKC,GACD,IAAI,OAAO;QACT,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK,WACxB,uJAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,IACxC;IACN;IAEA;;;;;;;;;;;GAWC,GACD,IAAI,KAAK,IAAI,EAAE;QACb,eAAe,MAAM;QACrB,WAAW,MAAM;QACjB,IAAI,CAAC,IAAI,GAAG,uJAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE;IACzE;IAEA,6BAA6B;IAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2DC,GACD,KAAK,aAAa,EAAE,sBAAsB,EAAE,MAAM,EAAE;QAClD,4CAA4C;QAC5C,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,eAAe,wBAAwB;QAEpE,QAAQ,KAAK,GAAG;QAEhB,MAAM;IACR;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDC,GACD,KAAK,aAAa,EAAE,sBAAsB,EAAE,MAAM,EAAE;QAClD,4CAA4C;QAC5C,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,eAAe,wBAAwB;QAEpE,QAAQ,KAAK,GAAG;QAEhB,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyDC,GACD,QAAQ,aAAa,EAAE,sBAAsB,EAAE,MAAM,EAAE;QACrD,MAAM,UAAU,IAAI,gJAAA,CAAA,eAAY,CAC9B,4CAA4C;QAC5C,eACA,wBACA;QAGF,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,QAAQ,IAAI;YAC7C,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI;QAC1B;QAEA,QAAQ,KAAK,GAAG;QAEhB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEnB,OAAO;IACT;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,QAAQ,EAAE;QACjB,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC5B,OAAO;QACT;QAEA,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,UAAU;YAClC,OAAO,IAAI,CAAC,KAAK;QACnB;QAEA,MAAM,UAAU,IAAI,YAAY,YAAY;QAC5C,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK;IAClC;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,IAAI,EAAE,IAAI;IAC5B,IAAI,QAAQ,KAAK,QAAQ,CAAC,uJAAA,CAAA,UAAO,CAAC,GAAG,GAAG;QACtC,MAAM,IAAI,MACR,MAAM,OAAO,yCAAyC,uJAAA,CAAA,UAAO,CAAC,GAAG,GAAG;IAExE;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,eAAe,IAAI,EAAE,IAAI;IAChC,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,WAAW,IAAI,EAAE,IAAI;IAC5B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM,cAAc,OAAO;IACvC;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,QACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAEtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-from-html/lib/errors.js"], "sourcesContent": ["/**\n * @typedef ErrorInfo\n *   Info on a `parse5` error.\n * @property {string} reason\n *   Reason of error.\n * @property {string} description\n *   More info on error.\n * @property {false} [url]\n *   Turn off if this is not documented in the html5 spec (optional).\n */\n\nexport const errors = {\n  /** @type {ErrorInfo} */\n  abandonedHeadElementChild: {\n    reason: 'Unexpected metadata element after head',\n    description:\n      'Unexpected element after head. Expected the element before `</head>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  abruptClosingOfEmptyComment: {\n    reason: 'Unexpected abruptly closed empty comment',\n    description: 'Unexpected `>` or `->`. Expected `-->` to close comments'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypePublicIdentifier: {\n    reason: 'Unexpected abruptly closed public identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the public identifier'\n  },\n  /** @type {ErrorInfo} */\n  abruptDoctypeSystemIdentifier: {\n    reason: 'Unexpected abruptly closed system identifier',\n    description:\n      'Unexpected `>`. Expected a closing `\"` or `\\'` after the identifier identifier'\n  },\n  /** @type {ErrorInfo} */\n  absenceOfDigitsInNumericCharacterReference: {\n    reason: 'Unexpected non-digit at start of numeric character reference',\n    description:\n      'Unexpected `%c`. Expected `[0-9]` for decimal references or `[0-9a-fA-F]` for hexadecimal references'\n  },\n  /** @type {ErrorInfo} */\n  cdataInHtmlContent: {\n    reason: 'Unexpected CDATA section in HTML',\n    description:\n      'Unexpected `<![CDATA[` in HTML. Remove it, use a comment, or encode special characters instead'\n  },\n  /** @type {ErrorInfo} */\n  characterReferenceOutsideUnicodeRange: {\n    reason: 'Unexpected too big numeric character reference',\n    description:\n      'Unexpectedly high character reference. Expected character references to be at most hexadecimal 10ffff (or decimal 1114111)'\n  },\n  /** @type {ErrorInfo} */\n  closingOfElementWithOpenChildElements: {\n    reason: 'Unexpected closing tag with open child elements',\n    description:\n      'Unexpectedly closing tag. Expected other tags to be closed first',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterInInputStream: {\n    reason: 'Unexpected control character',\n    description:\n      'Unexpected control character `%x`. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  controlCharacterReference: {\n    reason: 'Unexpected control character reference',\n    description:\n      'Unexpectedly control character in reference. Expected a non-control code point, 0x00, or ASCII whitespace'\n  },\n  /** @type {ErrorInfo} */\n  disallowedContentInNoscriptInHead: {\n    reason: 'Disallowed content inside `<noscript>` in `<head>`',\n    description:\n      'Unexpected text character `%c`. Only use text in `<noscript>`s in `<body>`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  duplicateAttribute: {\n    reason: 'Unexpected duplicate attribute',\n    description:\n      'Unexpectedly double attribute. Expected attributes to occur only once'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithAttributes: {\n    reason: 'Unexpected attribute on closing tag',\n    description: 'Unexpected attribute. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithTrailingSolidus: {\n    reason: 'Unexpected slash at end of closing tag',\n    description: 'Unexpected `%c-1`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  endTagWithoutMatchingOpenElement: {\n    reason: 'Unexpected unopened end tag',\n    description: 'Unexpected end tag. Expected no end tag or another end tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofBeforeTagName: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected tag name instead'\n  },\n  /** @type {ErrorInfo} */\n  eofInCdata: {\n    reason: 'Unexpected end of file in CDATA',\n    description: 'Unexpected end of file. Expected `]]>` to close the CDATA'\n  },\n  /** @type {ErrorInfo} */\n  eofInComment: {\n    reason: 'Unexpected end of file in comment',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInDoctype: {\n    reason: 'Unexpected end of file in doctype',\n    description:\n      'Unexpected end of file. Expected a valid doctype (such as `<!doctype html>`)'\n  },\n  /** @type {ErrorInfo} */\n  eofInElementThatCanContainOnlyText: {\n    reason: 'Unexpected end of file in element that can only contain text',\n    description: 'Unexpected end of file. Expected text or a closing tag',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  eofInScriptHtmlCommentLikeText: {\n    reason: 'Unexpected end of file in comment inside script',\n    description: 'Unexpected end of file. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  eofInTag: {\n    reason: 'Unexpected end of file in tag',\n    description: 'Unexpected end of file. Expected `>` to close the tag'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyClosedComment: {\n    reason: 'Incorrectly closed comment',\n    description: 'Unexpected `%c-1`. Expected `-->` to close the comment'\n  },\n  /** @type {ErrorInfo} */\n  incorrectlyOpenedComment: {\n    reason: 'Incorrectly opened comment',\n    description: 'Unexpected `%c`. Expected `<!--` to open the comment'\n  },\n  /** @type {ErrorInfo} */\n  invalidCharacterSequenceAfterDoctypeName: {\n    reason: 'Invalid sequence after doctype name',\n    description: 'Unexpected sequence at `%c`. Expected `public` or `system`'\n  },\n  /** @type {ErrorInfo} */\n  invalidFirstCharacterOfTagName: {\n    reason: 'Invalid first character in tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  misplacedDoctype: {\n    reason: 'Misplaced doctype',\n    description: 'Unexpected doctype. Expected doctype before head',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  misplacedStartTagForHeadElement: {\n    reason: 'Misplaced `<head>` start tag',\n    description:\n      'Unexpected start tag `<head>`. Expected `<head>` directly after doctype',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingAttributeValue: {\n    reason: 'Missing attribute value',\n    description:\n      'Unexpected `%c-1`. Expected an attribute value or no `%c-1` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctype: {\n    reason: 'Missing doctype before other content',\n    description: 'Expected a `<!doctype html>` before anything else',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeName: {\n    reason: 'Missing doctype name',\n    description: 'Unexpected doctype end at `%c`. Expected `html` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypePublicIdentifier: {\n    reason: 'Missing public identifier in doctype',\n    description: 'Unexpected `%c`. Expected identifier for `public` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingDoctypeSystemIdentifier: {\n    reason: 'Missing system identifier in doctype',\n    description:\n      'Unexpected `%c`. Expected identifier for `system` instead (suggested: `\"about:legacy-compat\"`)'\n  },\n  /** @type {ErrorInfo} */\n  missingEndTagName: {\n    reason: 'Missing name in end tag',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypePublicIdentifier: {\n    reason: 'Missing quote before public identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingQuoteBeforeDoctypeSystemIdentifier: {\n    reason: 'Missing quote before system identifier in doctype',\n    description: 'Unexpected `%c`. Expected `\"` or `\\'` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingSemicolonAfterCharacterReference: {\n    reason: 'Missing semicolon after character reference',\n    description: 'Unexpected `%c`. Expected `;` instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypePublicKeyword: {\n    reason: 'Missing whitespace after public identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceAfterDoctypeSystemKeyword: {\n    reason: 'Missing whitespace after system identifier in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBeforeDoctypeName: {\n    reason: 'Missing whitespace before doctype name',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenAttributes: {\n    reason: 'Missing whitespace between attributes',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: {\n    reason:\n      'Missing whitespace between public and system identifiers in doctype',\n    description: 'Unexpected `%c`. Expected ASCII whitespace instead'\n  },\n  /** @type {ErrorInfo} */\n  nestedComment: {\n    reason: 'Unexpected nested comment',\n    description: 'Unexpected `<!--`. Expected `-->`'\n  },\n  /** @type {ErrorInfo} */\n  nestedNoscriptInHead: {\n    reason: 'Unexpected nested `<noscript>` in `<head>`',\n    description:\n      'Unexpected `<noscript>`. Expected a closing tag or a meta element',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonConformingDoctype: {\n    reason: 'Unexpected non-conforming doctype declaration',\n    description:\n      'Expected `<!doctype html>` or `<!doctype html system \"about:legacy-compat\">`',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  nonVoidHtmlElementStartTagWithTrailingSolidus: {\n    reason: 'Unexpected trailing slash on start tag of non-void element',\n    description: 'Unexpected `/`. Expected `>` instead'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterCharacterReference: {\n    reason:\n      'Unexpected noncharacter code point referenced by character reference',\n    description: 'Unexpected code point. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  noncharacterInInputStream: {\n    reason: 'Unexpected noncharacter character',\n    description: 'Unexpected code point `%x`. Do not use noncharacters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  nullCharacterReference: {\n    reason: 'Unexpected NULL character referenced by character reference',\n    description: 'Unexpected code point. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  openElementsLeftAfterEof: {\n    reason: 'Unexpected end of file',\n    description: 'Unexpected end of file. Expected closing tag instead',\n    url: false\n  },\n  /** @type {ErrorInfo} */\n  surrogateCharacterReference: {\n    reason: 'Unexpected surrogate character referenced by character reference',\n    description:\n      'Unexpected code point. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  surrogateInInputStream: {\n    reason: 'Unexpected surrogate character',\n    description:\n      'Unexpected code point `%x`. Do not use lone surrogate characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterAfterDoctypeSystemIdentifier: {\n    reason: 'Invalid character after system identifier in doctype',\n    description: 'Unexpected character at `%c`. Expected `>`'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInAttributeName: {\n    reason: 'Unexpected character in attribute name',\n    description:\n      'Unexpected `%c`. Expected whitespace, `/`, `>`, `=`, or probably an ASCII letter'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedCharacterInUnquotedAttributeValue: {\n    reason: 'Unexpected character in unquoted attribute value',\n    description: 'Unexpected `%c`. Quote the attribute value to include it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedEqualsSignBeforeAttributeName: {\n    reason: 'Unexpected equals sign before attribute name',\n    description: 'Unexpected `%c`. Add an attribute name before it'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedNullCharacter: {\n    reason: 'Unexpected NULL character',\n    description:\n      'Unexpected code point `%x`. Do not use NULL characters in HTML'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedQuestionMarkInsteadOfTagName: {\n    reason: 'Unexpected question mark instead of tag name',\n    description: 'Unexpected `%c`. Expected an ASCII letter instead'\n  },\n  /** @type {ErrorInfo} */\n  unexpectedSolidusInTag: {\n    reason: 'Unexpected slash in tag',\n    description:\n      'Unexpected `%c-1`. Expected it followed by `>` or in a quoted attribute value'\n  },\n  /** @type {ErrorInfo} */\n  unknownNamedCharacterReference: {\n    reason: 'Unexpected unknown named character reference',\n    description:\n      'Unexpected character reference. Expected known named character references'\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAEM,MAAM,SAAS;IACpB,sBAAsB,GACtB,2BAA2B;QACzB,QAAQ;QACR,aACE;QACF,KAAK;IACP;IACA,sBAAsB,GACtB,6BAA6B;QAC3B,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,+BAA+B;QAC7B,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,+BAA+B;QAC7B,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,4CAA4C;QAC1C,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,oBAAoB;QAClB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,uCAAuC;QACrC,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,uCAAuC;QACrC,QAAQ;QACR,aACE;QACF,KAAK;IACP;IACA,sBAAsB,GACtB,+BAA+B;QAC7B,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,2BAA2B;QACzB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,mCAAmC;QACjC,QAAQ;QACR,aACE;QACF,KAAK;IACP;IACA,sBAAsB,GACtB,oBAAoB;QAClB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,sBAAsB;QACpB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,2BAA2B;QACzB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,kCAAkC;QAChC,QAAQ;QACR,aAAa;QACb,KAAK;IACP;IACA,sBAAsB,GACtB,kBAAkB;QAChB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,YAAY;QACV,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,cAAc;QACZ,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,cAAc;QACZ,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,oCAAoC;QAClC,QAAQ;QACR,aAAa;QACb,KAAK;IACP;IACA,sBAAsB,GACtB,gCAAgC;QAC9B,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,UAAU;QACR,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,0BAA0B;QACxB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,0BAA0B;QACxB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,0CAA0C;QACxC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,gCAAgC;QAC9B,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,kBAAkB;QAChB,QAAQ;QACR,aAAa;QACb,KAAK;IACP;IACA,sBAAsB,GACtB,iCAAiC;QAC/B,QAAQ;QACR,aACE;QACF,KAAK;IACP;IACA,sBAAsB,GACtB,uBAAuB;QACrB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,gBAAgB;QACd,QAAQ;QACR,aAAa;QACb,KAAK;IACP;IACA,sBAAsB,GACtB,oBAAoB;QAClB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,gCAAgC;QAC9B,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,gCAAgC;QAC9B,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,mBAAmB;QACjB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,2CAA2C;QACzC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,2CAA2C;QACzC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,yCAAyC;QACvC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,4CAA4C;QAC1C,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,4CAA4C;QAC1C,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,oCAAoC;QAClC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,oCAAoC;QAClC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,2DAA2D;QACzD,QACE;QACF,aAAa;IACf;IACA,sBAAsB,GACtB,eAAe;QACb,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,sBAAsB;QACpB,QAAQ;QACR,aACE;QACF,KAAK;IACP;IACA,sBAAsB,GACtB,sBAAsB;QACpB,QAAQ;QACR,aACE;QACF,KAAK;IACP;IACA,sBAAsB,GACtB,+CAA+C;QAC7C,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,gCAAgC;QAC9B,QACE;QACF,aAAa;IACf;IACA,sBAAsB,GACtB,2BAA2B;QACzB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,wBAAwB;QACtB,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,0BAA0B;QACxB,QAAQ;QACR,aAAa;QACb,KAAK;IACP;IACA,sBAAsB,GACtB,6BAA6B;QAC3B,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,wBAAwB;QACtB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,iDAAiD;QAC/C,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,oCAAoC;QAClC,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,6CAA6C;QAC3C,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,yCAAyC;QACvC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,yBAAyB;QACvB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,wCAAwC;QACtC,QAAQ;QACR,aAAa;IACf;IACA,sBAAsB,GACtB,wBAAwB;QACtB,QAAQ;QACR,aACE;IACJ;IACA,sBAAsB,GACtB,gCAAgC;QAC9B,QAAQ;QACR,aACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-from-html/lib/index.js"], "sourcesContent": ["/**\n * @import {Root} from 'hast'\n * @import {ParserError} from 'parse5'\n * @import {Value} from 'vfile'\n * @import {ErrorCode, Options} from './types.js'\n */\n\nimport {ok as assert} from 'devlop'\nimport {fromParse5} from 'hast-util-from-parse5'\nimport {parse, parseFragment} from 'parse5'\nimport {VFile} from 'vfile'\nimport {VFileMessage} from 'vfile-message'\nimport {errors} from './errors.js'\n\nconst base = 'https://html.spec.whatwg.org/multipage/parsing.html#parse-error-'\n\nconst dashToCamelRe = /-[a-z]/g\nconst formatCRe = /%c(?:([-+])(\\d+))?/g\nconst formatXRe = /%x/g\n\nconst fatalities = {2: true, 1: false, 0: null}\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Turn serialized HTML into a hast tree.\n *\n * @param {VFile | Value} value\n *   Serialized HTML to parse.\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Root}\n *   Tree.\n */\nexport function fromHtml(value, options) {\n  const settings = options || emptyOptions\n  const onerror = settings.onerror\n  const file = value instanceof VFile ? value : new VFile(value)\n  const parseFunction = settings.fragment ? parseFragment : parse\n  const document = String(file)\n  const p5Document = parseFunction(document, {\n    sourceCodeLocationInfo: true,\n    // Note `parse5` types currently do not allow `undefined`.\n    onParseError: settings.onerror ? internalOnerror : null,\n    scriptingEnabled: false\n  })\n\n  // `parse5` returns document which are always mapped to roots.\n  return /** @type {Root} */ (\n    fromParse5(p5Document, {\n      file,\n      space: settings.space,\n      verbose: settings.verbose\n    })\n  )\n\n  /**\n   * Handle a parse error.\n   *\n   * @param {ParserError} error\n   *   Parse5 error.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function internalOnerror(error) {\n    const code = error.code\n    const name = camelcase(code)\n    const setting = settings[name]\n    const config = setting === null || setting === undefined ? true : setting\n    const level = typeof config === 'number' ? config : config ? 1 : 0\n\n    if (level) {\n      const info = errors[name]\n      assert(info, 'expected known error from `parse5`')\n\n      const message = new VFileMessage(format(info.reason), {\n        place: {\n          start: {\n            line: error.startLine,\n            column: error.startCol,\n            offset: error.startOffset\n          },\n          end: {\n            line: error.endLine,\n            column: error.endCol,\n            offset: error.endOffset\n          }\n        },\n        ruleId: code,\n        source: 'hast-util-from-html'\n      })\n\n      if (file.path) {\n        message.file = file.path\n        message.name = file.path + ':' + message.name\n      }\n\n      message.fatal = fatalities[level]\n      message.note = format(info.description)\n      message.url = info.url === false ? undefined : base + code\n\n      assert(onerror, '`internalOnerror` is not passed if `onerror` is not set')\n      onerror(message)\n    }\n\n    /**\n     * Format a human readable string about an error.\n     *\n     * @param {string} value\n     *   Value to format.\n     * @returns {string}\n     *   Formatted.\n     */\n    function format(value) {\n      return value.replace(formatCRe, formatC).replace(formatXRe, formatX)\n\n      /**\n       * Format the character.\n       *\n       * @param {string} _\n       *   Match.\n       * @param {string} $1\n       *   Sign (`-` or `+`, optional).\n       * @param {string} $2\n       *   Offset.\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatC(_, $1, $2) {\n        const offset =\n          ($2 ? Number.parseInt($2, 10) : 0) * ($1 === '-' ? -1 : 1)\n        const char = document.charAt(error.startOffset + offset)\n        return visualizeCharacter(char)\n      }\n\n      /**\n       * Format the character code.\n       *\n       * @returns {string}\n       *   Formatted.\n       */\n      function formatX() {\n        return visualizeCharacterCode(document.charCodeAt(error.startOffset))\n      }\n    }\n  }\n}\n\n/**\n * @param {string} value\n *   Error code in dash case.\n * @returns {ErrorCode}\n *   Error code in camelcase.\n */\nfunction camelcase(value) {\n  // This should match an error code.\n  return /** @type {ErrorCode} */ (value.replace(dashToCamelRe, dashToCamel))\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @returns {string}\n *   Camelcased.\n */\nfunction dashToCamel($0) {\n  return $0.charAt(1).toUpperCase()\n}\n\n/**\n * @param {string} char\n *   Character.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacter(char) {\n  return char === '`' ? '` ` `' : char\n}\n\n/**\n * @param {number} charCode\n *   Character code.\n * @returns {string}\n *   Formatted.\n */\nfunction visualizeCharacterCode(charCode) {\n  return '0x' + charCode.toString(16).toUpperCase()\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,OAAO;AAEb,MAAM,gBAAgB;AACtB,MAAM,YAAY;AAClB,MAAM,YAAY;AAElB,MAAM,aAAa;IAAC,GAAG;IAAM,GAAG;IAAO,GAAG;AAAI;AAE9C,8BAA8B,GAC9B,MAAM,eAAe,CAAC;AAYf,SAAS,SAAS,KAAK,EAAE,OAAO;IACrC,MAAM,WAAW,WAAW;IAC5B,MAAM,UAAU,SAAS,OAAO;IAChC,MAAM,OAAO,iBAAiB,qIAAA,CAAA,QAAK,GAAG,QAAQ,IAAI,qIAAA,CAAA,QAAK,CAAC;IACxD,MAAM,gBAAgB,SAAS,QAAQ,GAAG,uJAAA,CAAA,gBAAa,GAAG,uJAAA,CAAA,QAAK;IAC/D,MAAM,WAAW,OAAO;IACxB,MAAM,aAAa,cAAc,UAAU;QACzC,wBAAwB;QACxB,0DAA0D;QAC1D,cAAc,SAAS,OAAO,GAAG,kBAAkB;QACnD,kBAAkB;IACpB;IAEA,8DAA8D;IAC9D,OACE,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,YAAY;QACrB;QACA,OAAO,SAAS,KAAK;QACrB,SAAS,SAAS,OAAO;IAC3B;;;IAGF;;;;;;;GAOC,GACD,SAAS,gBAAgB,KAAK;QAC5B,MAAM,OAAO,MAAM,IAAI;QACvB,MAAM,OAAO,UAAU;QACvB,MAAM,UAAU,QAAQ,CAAC,KAAK;QAC9B,MAAM,SAAS,YAAY,QAAQ,YAAY,YAAY,OAAO;QAClE,MAAM,QAAQ,OAAO,WAAW,WAAW,SAAS,SAAS,IAAI;QAEjE,IAAI,OAAO;YACT,MAAM,OAAO,6JAAA,CAAA,SAAM,CAAC,KAAK;YACzB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;YAEb,MAAM,UAAU,IAAI,gJAAA,CAAA,eAAY,CAAC,OAAO,KAAK,MAAM,GAAG;gBACpD,OAAO;oBACL,OAAO;wBACL,MAAM,MAAM,SAAS;wBACrB,QAAQ,MAAM,QAAQ;wBACtB,QAAQ,MAAM,WAAW;oBAC3B;oBACA,KAAK;wBACH,MAAM,MAAM,OAAO;wBACnB,QAAQ,MAAM,MAAM;wBACpB,QAAQ,MAAM,SAAS;oBACzB;gBACF;gBACA,QAAQ;gBACR,QAAQ;YACV;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,IAAI,GAAG,KAAK,IAAI;gBACxB,QAAQ,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,QAAQ,IAAI;YAC/C;YAEA,QAAQ,KAAK,GAAG,UAAU,CAAC,MAAM;YACjC,QAAQ,IAAI,GAAG,OAAO,KAAK,WAAW;YACtC,QAAQ,GAAG,GAAG,KAAK,GAAG,KAAK,QAAQ,YAAY,OAAO;YAEtD,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS;YAChB,QAAQ;QACV;QAEA;;;;;;;KAOC,GACD,SAAS,OAAO,KAAK;YACnB,OAAO,MAAM,OAAO,CAAC,WAAW,SAAS,OAAO,CAAC,WAAW;;;YAE5D;;;;;;;;;;;OAWC,GACD,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE;gBACxB,MAAM,SACJ,CAAC,KAAK,OAAO,QAAQ,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC;gBAC3D,MAAM,OAAO,SAAS,MAAM,CAAC,MAAM,WAAW,GAAG;gBACjD,OAAO,mBAAmB;YAC5B;YAEA;;;;;OAKC,GACD,SAAS;gBACP,OAAO,uBAAuB,SAAS,UAAU,CAAC,MAAM,WAAW;YACrE;QACF;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,UAAU,KAAK;IACtB,mCAAmC;IACnC,OAAiC,MAAM,OAAO,CAAC,eAAe;AAChE;AAEA;;;;;CAKC,GACD,SAAS,YAAY,EAAE;IACrB,OAAO,GAAG,MAAM,CAAC,GAAG,WAAW;AACjC;AAEA;;;;;CAKC,GACD,SAAS,mBAAmB,IAAI;IAC9B,OAAO,SAAS,MAAM,UAAU;AAClC;AAEA;;;;;CAKC,GACD,SAAS,uBAAuB,QAAQ;IACtC,OAAO,OAAO,SAAS,QAAQ,CAAC,IAAI,WAAW;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3546, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/rehype-parse/lib/index.js"], "sourcesContent": ["/**\n * @import {Root} from 'hast'\n * @import {Options as FromHtmlOptions} from 'hast-util-from-html'\n * @import {Parser, Processor} from 'unified'\n */\n\n/**\n * @typedef {Omit<FromHtmlOptions, 'onerror'> & RehypeParseFields} Options\n *   Configuration.\n *\n * @typedef RehypeParseFields\n *   Extra fields.\n * @property {boolean | null | undefined} [emitParseErrors=false]\n *   Whether to emit parse errors while parsing (default: `false`).\n *\n *   > 👉 **Note**: parse errors are currently being added to HTML.\n *   > Not all errors emitted by parse5 (or us) are specced yet.\n *   > Some documentation may still be missing.\n */\n\nimport {fromHtml} from 'hast-util-from-html'\n\n/**\n * Plugin to add support for parsing from HTML.\n *\n * > 👉 **Note**: this is not an XML parser.\n * > It supports SVG as embedded in HTML.\n * > It does not support the features available in XML.\n * > Passing SVG files might break but fragments of modern SVG should be fine.\n * > Use [`xast-util-from-xml`][xast-util-from-xml] to parse XML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function rehypeParse(options) {\n  /** @type {Processor<Root>} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n  const {emitParseErrors, ...settings} = {...self.data('settings'), ...options}\n\n  self.parser = parser\n\n  /**\n   * @type {Parser<Root>}\n   */\n  function parser(document, file) {\n    return fromHtml(document, {\n      ...settings,\n      onerror: emitParseErrors\n        ? function (message) {\n            if (file.path) {\n              message.name = file.path + ':' + message.name\n              message.file = file.path\n            }\n\n            file.messages.push(message)\n          }\n        : undefined\n    })\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;CAYC;;;AAED;;AAgBe,SAAS,YAAY,OAAO;IACzC,4BAA4B,GAC5B,oFAAoF;IACpF,MAAM,OAAO,IAAI;IACjB,MAAM,EAAC,eAAe,EAAE,GAAG,UAAS,GAAG;QAAC,GAAG,KAAK,IAAI,CAAC,WAAW;QAAE,GAAG,OAAO;IAAA;IAE5E,KAAK,MAAM,GAAG;IAEd;;GAEC,GACD,SAAS,OAAO,QAAQ,EAAE,IAAI;QAC5B,OAAO,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;YACxB,GAAG,QAAQ;YACX,SAAS,kBACL,SAAU,OAAO;gBACf,IAAI,KAAK,IAAI,EAAE;oBACb,QAAQ,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,QAAQ,IAAI;oBAC7C,QAAQ,IAAI,GAAG,KAAK,IAAI;gBAC1B;gBAEA,KAAK,QAAQ,CAAC,IAAI,CAAC;YACrB,IACA;QACN;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3594, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/html-void-elements/index.js"], "sourcesContent": ["/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nexport const htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'keygen',\n  'link',\n  'meta',\n  'param',\n  'source',\n  'track',\n  'wbr'\n]\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3627, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/zwitch/index.js"], "sourcesContent": ["/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nexport function zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;;;;;;;;;CAUC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;CAYC;;;AAED,MAAM,MAAM,CAAC,EAAE,cAAc;AActB,SAAS,OAAO,GAAG,EAAE,OAAO;IACjC,MAAM,WAAW,WAAW,CAAC;IAE7B;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,SAAS,IAAI,KAAK,EAAE,GAAG,UAAU;QAC/B,8BAA8B,GAC9B,IAAI,KAAK,IAAI,OAAO;QACpB,MAAM,WAAW,IAAI,QAAQ;QAE7B,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,MAAM;YACjC,8BAA8B;YAC9B,MAAM,KAAK,OAAO,KAAK,CAAC,IAAI;YAC5B,8BAA8B;YAC9B,KAAK,IAAI,IAAI,CAAC,UAAU,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,OAAO;QAC1D;QAEA,IAAI,IAAI;YACN,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU;QACjC;IACF;IAEA,IAAI,QAAQ,GAAG,SAAS,QAAQ,IAAI,CAAC;IACrC,IAAI,OAAO,GAAG,SAAS,OAAO;IAC9B,IAAI,OAAO,GAAG,SAAS,OAAO;IAE9B,6BAA6B;IAC7B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/core.js"], "sourcesContent": ["/**\n * @typedef CoreOptions\n * @property {ReadonlyArray<string>} [subset=[]]\n *   Whether to only escape the given subset of characters.\n * @property {boolean} [escapeOnly=false]\n *   Whether to only escape possibly dangerous characters.\n *   Those characters are `\"`, `&`, `'`, `<`, `>`, and `` ` ``.\n *\n * @typedef FormatOptions\n * @property {(code: number, next: number, options: CoreWithFormatOptions) => string} format\n *   Format strategy.\n *\n * @typedef {CoreOptions & FormatOptions & import('./util/format-smart.js').FormatSmartOptions} CoreWithFormatOptions\n */\n\nconst defaultSubsetRegex = /[\"&'<>`]/g\nconst surrogatePairsRegex = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g\nconst controlCharactersRegex =\n  // eslint-disable-next-line no-control-regex, unicorn/no-hex-escape\n  /[\\x01-\\t\\v\\f\\x0E-\\x1F\\x7F\\x81\\x8D\\x8F\\x90\\x9D\\xA0-\\uFFFF]/g\nconst regexEscapeRegex = /[|\\\\{}()[\\]^$+*?.]/g\n\n/** @type {WeakMap<ReadonlyArray<string>, RegExp>} */\nconst subsetToRegexCache = new WeakMap()\n\n/**\n * Encode certain characters in `value`.\n *\n * @param {string} value\n * @param {CoreWithFormatOptions} options\n * @returns {string}\n */\nexport function core(value, options) {\n  value = value.replace(\n    options.subset\n      ? charactersToExpressionCached(options.subset)\n      : defaultSubsetRegex,\n    basic\n  )\n\n  if (options.subset || options.escapeOnly) {\n    return value\n  }\n\n  return (\n    value\n      // Surrogate pairs.\n      .replace(surrogatePairsRegex, surrogate)\n      // BMP control characters (C0 except for LF, CR, SP; DEL; and some more\n      // non-ASCII ones).\n      .replace(controlCharactersRegex, basic)\n  )\n\n  /**\n   * @param {string} pair\n   * @param {number} index\n   * @param {string} all\n   */\n  function surrogate(pair, index, all) {\n    return options.format(\n      (pair.charCodeAt(0) - 0xd800) * 0x400 +\n        pair.charCodeAt(1) -\n        0xdc00 +\n        0x10000,\n      all.charCodeAt(index + 2),\n      options\n    )\n  }\n\n  /**\n   * @param {string} character\n   * @param {number} index\n   * @param {string} all\n   */\n  function basic(character, index, all) {\n    return options.format(\n      character.charCodeAt(0),\n      all.charCodeAt(index + 1),\n      options\n    )\n  }\n}\n\n/**\n * A wrapper function that caches the result of `charactersToExpression` with a WeakMap.\n * This can improve performance when tooling calls `charactersToExpression` repeatedly\n * with the same subset.\n *\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpressionCached(subset) {\n  let cached = subsetToRegexCache.get(subset)\n\n  if (!cached) {\n    cached = charactersToExpression(subset)\n    subsetToRegexCache.set(subset, cached)\n  }\n\n  return cached\n}\n\n/**\n * @param {ReadonlyArray<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpression(subset) {\n  /** @type {Array<string>} */\n  const groups = []\n  let index = -1\n\n  while (++index < subset.length) {\n    groups.push(subset[index].replace(regexEscapeRegex, '\\\\$&'))\n  }\n\n  return new RegExp('(?:' + groups.join('|') + ')', 'g')\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;CAaC;;;AAED,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB;AAC5B,MAAM,yBACJ,mEAAmE;AACnE;AACF,MAAM,mBAAmB;AAEzB,mDAAmD,GACnD,MAAM,qBAAqB,IAAI;AASxB,SAAS,KAAK,KAAK,EAAE,OAAO;IACjC,QAAQ,MAAM,OAAO,CACnB,QAAQ,MAAM,GACV,6BAA6B,QAAQ,MAAM,IAC3C,oBACJ;IAGF,IAAI,QAAQ,MAAM,IAAI,QAAQ,UAAU,EAAE;QACxC,OAAO;IACT;IAEA,OACE,KACE,mBAAmB;KAClB,OAAO,CAAC,qBAAqB,UAC9B,uEAAuE;IACvE,mBAAmB;KAClB,OAAO,CAAC,wBAAwB;;;IAGrC;;;;GAIC,GACD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,GAAG;QACjC,OAAO,QAAQ,MAAM,CACnB,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM,IAAI,QAC9B,KAAK,UAAU,CAAC,KAChB,SACA,SACF,IAAI,UAAU,CAAC,QAAQ,IACvB;IAEJ;IAEA;;;;GAIC,GACD,SAAS,MAAM,SAAS,EAAE,KAAK,EAAE,GAAG;QAClC,OAAO,QAAQ,MAAM,CACnB,UAAU,UAAU,CAAC,IACrB,IAAI,UAAU,CAAC,QAAQ,IACvB;IAEJ;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,6BAA6B,MAAM;IAC1C,IAAI,SAAS,mBAAmB,GAAG,CAAC;IAEpC,IAAI,CAAC,QAAQ;QACX,SAAS,uBAAuB;QAChC,mBAAmB,GAAG,CAAC,QAAQ;IACjC;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,uBAAuB,MAAM;IACpC,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB;IACtD;IAEA,OAAO,IAAI,OAAO,QAAQ,OAAO,IAAI,CAAC,OAAO,KAAK;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3802, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/util/to-hexadecimal.js"], "sourcesContent": ["const hexadecimalRegex = /[\\dA-Fa-f]/\n\n/**\n * Configurable ways to encode characters as hexadecimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nexport function toHexadecimal(code, next, omit) {\n  const value = '&#x' + code.toString(16).toUpperCase()\n  return omit && next && !hexadecimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB;AAUlB,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,IAAI;IAC5C,MAAM,QAAQ,QAAQ,KAAK,QAAQ,CAAC,IAAI,WAAW;IACnD,OAAO,QAAQ,QAAQ,CAAC,iBAAiB,IAAI,CAAC,OAAO,YAAY,CAAC,SAC9D,QACA,QAAQ;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3814, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/util/to-decimal.js"], "sourcesContent": ["const decimalRegex = /\\d/\n\n/**\n * Configurable ways to encode characters as decimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nexport function toDecimal(code, next, omit) {\n  const value = '&#' + String(code)\n  return omit && next && !decimalRegex.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n"], "names": [], "mappings": ";;;AAAA,MAAM,eAAe;AAUd,SAAS,UAAU,IAAI,EAAE,IAAI,EAAE,IAAI;IACxC,MAAM,QAAQ,OAAO,OAAO;IAC5B,OAAO,QAAQ,QAAQ,CAAC,aAAa,IAAI,CAAC,OAAO,YAAY,CAAC,SAC1D,QACA,QAAQ;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/constant/dangerous.js"], "sourcesContent": ["/**\n * List of legacy (that don’t need a trailing `;`) named references which could,\n * depending on what follows them, turn into a different meaning\n *\n * @type {Array<string>}\n */\nexport const dangerous = [\n  'cent',\n  'copy',\n  'divide',\n  'gt',\n  'lt',\n  'not',\n  'para',\n  'times'\n]\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACM,MAAM,YAAY;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3848, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/util/to-named.js"], "sourcesContent": ["import {characterEntitiesLegacy} from 'character-entities-legacy'\nimport {characterEntitiesHtml4} from 'character-entities-html4'\nimport {dangerous} from '../constant/dangerous.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * `characterEntitiesHtml4` but inverted.\n *\n * @type {Record<string, string>}\n */\nconst characters = {}\n\n/** @type {string} */\nlet key\n\nfor (key in characterEntitiesHtml4) {\n  if (own.call(characterEntitiesHtml4, key)) {\n    characters[characterEntitiesHtml4[key]] = key\n  }\n}\n\nconst notAlphanumericRegex = /[^\\dA-Za-z]/\n\n/**\n * Configurable ways to encode characters as named references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @param {boolean|undefined} attribute\n * @returns {string}\n */\nexport function toNamed(code, next, omit, attribute) {\n  const character = String.fromCharCode(code)\n\n  if (own.call(characters, character)) {\n    const name = characters[character]\n    const value = '&' + name\n\n    if (\n      omit &&\n      characterEntitiesLegacy.includes(name) &&\n      !dangerous.includes(name) &&\n      (!attribute ||\n        (next &&\n          next !== 61 /* `=` */ &&\n          notAlphanumericRegex.test(String.fromCharCode(next))))\n    ) {\n      return value\n    }\n\n    return value + ';'\n  }\n\n  return ''\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAE7B;;;;CAIC,GACD,MAAM,aAAa,CAAC;AAEpB,mBAAmB,GACnB,IAAI;AAEJ,IAAK,OAAO,uJAAA,CAAA,yBAAsB,CAAE;IAClC,IAAI,IAAI,IAAI,CAAC,uJAAA,CAAA,yBAAsB,EAAE,MAAM;QACzC,UAAU,CAAC,uJAAA,CAAA,yBAAsB,CAAC,IAAI,CAAC,GAAG;IAC5C;AACF;AAEA,MAAM,uBAAuB;AAWtB,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS;IACjD,MAAM,YAAY,OAAO,YAAY,CAAC;IAEtC,IAAI,IAAI,IAAI,CAAC,YAAY,YAAY;QACnC,MAAM,OAAO,UAAU,CAAC,UAAU;QAClC,MAAM,QAAQ,MAAM;QAEpB,IACE,QACA,wJAAA,CAAA,0BAAuB,CAAC,QAAQ,CAAC,SACjC,CAAC,qKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,SACpB,CAAC,CAAC,aACC,QACC,SAAS,GAAG,OAAO,OACnB,qBAAqB,IAAI,CAAC,OAAO,YAAY,CAAC,MAAO,GACzD;YACA,OAAO;QACT;QAEA,OAAO,QAAQ;IACjB;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3886, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/util/format-smart.js"], "sourcesContent": ["/**\n * @typedef FormatSmartOptions\n * @property {boolean} [useNamedReferences=false]\n *   Prefer named character references (`&amp;`) where possible.\n * @property {boolean} [useShortestReferences=false]\n *   Prefer the shortest possible reference, if that results in less bytes.\n *   **Note**: `useNamedReferences` can be omitted when using `useShortestReferences`.\n * @property {boolean} [omitOptionalSemicolons=false]\n *   Whether to omit semicolons when possible.\n *   **Note**: This creates what HTML calls “parse errors” but is otherwise still valid HTML — don’t use this except when building a minifier.\n *   Omitting semicolons is possible for certain named and numeric references in some cases.\n * @property {boolean} [attribute=false]\n *   Create character references which don’t fail in attributes.\n *   **Note**: `attribute` only applies when operating dangerously with\n *   `omitOptionalSemicolons: true`.\n */\n\nimport {toHexadecimal} from './to-hexadecimal.js'\nimport {toDecimal} from './to-decimal.js'\nimport {toNamed} from './to-named.js'\n\n/**\n * Configurable ways to encode a character yielding pretty or small results.\n *\n * @param {number} code\n * @param {number} next\n * @param {FormatSmartOptions} options\n * @returns {string}\n */\nexport function formatSmart(code, next, options) {\n  let numeric = toHexadecimal(code, next, options.omitOptionalSemicolons)\n  /** @type {string|undefined} */\n  let named\n\n  if (options.useNamedReferences || options.useShortestReferences) {\n    named = toNamed(\n      code,\n      next,\n      options.omitOptionalSemicolons,\n      options.attribute\n    )\n  }\n\n  // Use the shortest numeric reference when requested.\n  // A simple algorithm would use decimal for all code points under 100, as\n  // those are shorter than hexadecimal:\n  //\n  // * `&#99;` vs `&#x63;` (decimal shorter)\n  // * `&#100;` vs `&#x64;` (equal)\n  //\n  // However, because we take `next` into consideration when `omit` is used,\n  // And it would be possible that decimals are shorter on bigger values as\n  // well if `next` is hexadecimal but not decimal, we instead compare both.\n  if (\n    (options.useShortestReferences || !named) &&\n    options.useShortestReferences\n  ) {\n    const decimal = toDecimal(code, next, options.omitOptionalSemicolons)\n\n    if (decimal.length < numeric.length) {\n      numeric = decimal\n    }\n  }\n\n  return named &&\n    (!options.useShortestReferences || named.length < numeric.length)\n    ? named\n    : numeric\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;CAeC;;;AAED;AACA;AACA;;;;AAUO,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,OAAO;IAC7C,IAAI,UAAU,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,MAAM,QAAQ,sBAAsB;IACtE,6BAA6B,GAC7B,IAAI;IAEJ,IAAI,QAAQ,kBAAkB,IAAI,QAAQ,qBAAqB,EAAE;QAC/D,QAAQ,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EACZ,MACA,MACA,QAAQ,sBAAsB,EAC9B,QAAQ,SAAS;IAErB;IAEA,qDAAqD;IACrD,yEAAyE;IACzE,sCAAsC;IACtC,EAAE;IACF,0CAA0C;IAC1C,iCAAiC;IACjC,EAAE;IACF,0EAA0E;IAC1E,yEAAyE;IACzE,0EAA0E;IAC1E,IACE,CAAC,QAAQ,qBAAqB,IAAI,CAAC,KAAK,KACxC,QAAQ,qBAAqB,EAC7B;QACA,MAAM,UAAU,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,MAAM,QAAQ,sBAAsB;QAEpE,IAAI,QAAQ,MAAM,GAAG,QAAQ,MAAM,EAAE;YACnC,UAAU;QACZ;IACF;IAEA,OAAO,SACL,CAAC,CAAC,QAAQ,qBAAqB,IAAI,MAAM,MAAM,GAAG,QAAQ,MAAM,IAC9D,QACA;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3938, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/util/format-basic.js"], "sourcesContent": ["/**\n * The smallest way to encode a character.\n *\n * @param {number} code\n * @returns {string}\n */\nexport function formatBasic(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACM,SAAS,YAAY,IAAI;IAC9B,OAAO,QAAQ,KAAK,QAAQ,CAAC,IAAI,WAAW,KAAK;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/stringify-entities/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('./core.js').CoreOptions & import('./util/format-smart.js').FormatSmartOptions} Options\n * @typedef {import('./core.js').CoreOptions} LightOptions\n */\n\nimport {core} from './core.js'\nimport {formatSmart} from './util/format-smart.js'\nimport {formatBasic} from './util/format-basic.js'\n\n/**\n * Encode special characters in `value`.\n *\n * @param {string} value\n *   Value to encode.\n * @param {Options} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nexport function stringifyEntities(value, options) {\n  return core(value, Object.assign({format: formatSmart}, options))\n}\n\n/**\n * Encode special characters in `value` as hexadecimals.\n *\n * @param {string} value\n *   Value to encode.\n * @param {LightOptions} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nexport function stringifyEntitiesLight(value, options) {\n  return core(value, Object.assign({format: formatBasic}, options))\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;;;;AAYO,SAAS,kBAAkB,KAAK,EAAE,OAAO;IAC9C,OAAO,CAAA,GAAA,oJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAAC,QAAQ,uKAAA,CAAA,cAAW;IAAA,GAAG;AAC1D;AAYO,SAAS,uBAAuB,KAAK,EAAE,OAAO;IACnD,OAAO,CAAA,GAAA,oJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAAC,QAAQ,uKAAA,CAAA,cAAW;IAAA,GAAG;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3980, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/character-entities-legacy/index.js"], "sourcesContent": ["/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nexport const characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  '<PERSON>grave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'C<PERSON><PERSON>',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4099, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/character-entities-html4/index.js"], "sourcesContent": ["/**\n * Map of named character references from HTML 4.\n *\n * @type {Record<string, string>}\n */\nexport const characterEntitiesHtml4 = {\n  nbsp: ' ',\n  iexcl: '¡',\n  cent: '¢',\n  pound: '£',\n  curren: '¤',\n  yen: '¥',\n  brvbar: '¦',\n  sect: '§',\n  uml: '¨',\n  copy: '©',\n  ordf: 'ª',\n  laquo: '«',\n  not: '¬',\n  shy: '­',\n  reg: '®',\n  macr: '¯',\n  deg: '°',\n  plusmn: '±',\n  sup2: '²',\n  sup3: '³',\n  acute: '´',\n  micro: 'µ',\n  para: '¶',\n  middot: '·',\n  cedil: '¸',\n  sup1: '¹',\n  ordm: 'º',\n  raquo: '»',\n  frac14: '¼',\n  frac12: '½',\n  frac34: '¾',\n  iquest: '¿',\n  Agrave: 'À',\n  Aacute: 'Á',\n  Acirc: 'Â',\n  Atilde: 'Ã',\n  Auml: 'Ä',\n  Aring: 'Å',\n  AElig: 'Æ',\n  Ccedil: 'Ç',\n  Egrave: 'È',\n  Eacute: 'É',\n  Ecirc: 'Ê',\n  Euml: 'Ë',\n  Igrave: 'Ì',\n  Iacute: 'Í',\n  Icirc: 'Î',\n  Iuml: 'Ï',\n  ETH: 'Ð',\n  Ntilde: 'Ñ',\n  Ograve: 'Ò',\n  Oacute: 'Ó',\n  Ocirc: 'Ô',\n  Otilde: 'Õ',\n  Ouml: 'Ö',\n  times: '×',\n  Oslash: 'Ø',\n  Ugrave: 'Ù',\n  Uacute: 'Ú',\n  Ucirc: 'Û',\n  Uuml: 'Ü',\n  Yacute: 'Ý',\n  THORN: 'Þ',\n  szlig: 'ß',\n  agrave: 'à',\n  aacute: 'á',\n  acirc: 'â',\n  atilde: 'ã',\n  auml: 'ä',\n  aring: 'å',\n  aelig: 'æ',\n  ccedil: 'ç',\n  egrave: 'è',\n  eacute: 'é',\n  ecirc: 'ê',\n  euml: 'ë',\n  igrave: 'ì',\n  iacute: 'í',\n  icirc: 'î',\n  iuml: 'ï',\n  eth: 'ð',\n  ntilde: 'ñ',\n  ograve: 'ò',\n  oacute: 'ó',\n  ocirc: 'ô',\n  otilde: 'õ',\n  ouml: 'ö',\n  divide: '÷',\n  oslash: 'ø',\n  ugrave: 'ù',\n  uacute: 'ú',\n  ucirc: 'û',\n  uuml: 'ü',\n  yacute: 'ý',\n  thorn: 'þ',\n  yuml: 'ÿ',\n  fnof: 'ƒ',\n  Alpha: 'Α',\n  Beta: 'Β',\n  Gamma: 'Γ',\n  Delta: 'Δ',\n  Epsilon: 'Ε',\n  Zeta: 'Ζ',\n  Eta: 'Η',\n  Theta: 'Θ',\n  Iota: 'Ι',\n  Kappa: 'Κ',\n  Lambda: 'Λ',\n  Mu: 'Μ',\n  Nu: 'Ν',\n  Xi: 'Ξ',\n  Omicron: 'Ο',\n  Pi: 'Π',\n  Rho: 'Ρ',\n  Sigma: 'Σ',\n  Tau: 'Τ',\n  Upsilon: 'Υ',\n  Phi: 'Φ',\n  Chi: 'Χ',\n  Psi: 'Ψ',\n  Omega: 'Ω',\n  alpha: 'α',\n  beta: 'β',\n  gamma: 'γ',\n  delta: 'δ',\n  epsilon: 'ε',\n  zeta: 'ζ',\n  eta: 'η',\n  theta: 'θ',\n  iota: 'ι',\n  kappa: 'κ',\n  lambda: 'λ',\n  mu: 'μ',\n  nu: 'ν',\n  xi: 'ξ',\n  omicron: 'ο',\n  pi: 'π',\n  rho: 'ρ',\n  sigmaf: 'ς',\n  sigma: 'σ',\n  tau: 'τ',\n  upsilon: 'υ',\n  phi: 'φ',\n  chi: 'χ',\n  psi: 'ψ',\n  omega: 'ω',\n  thetasym: 'ϑ',\n  upsih: 'ϒ',\n  piv: 'ϖ',\n  bull: '•',\n  hellip: '…',\n  prime: '′',\n  Prime: '″',\n  oline: '‾',\n  frasl: '⁄',\n  weierp: '℘',\n  image: 'ℑ',\n  real: 'ℜ',\n  trade: '™',\n  alefsym: 'ℵ',\n  larr: '←',\n  uarr: '↑',\n  rarr: '→',\n  darr: '↓',\n  harr: '↔',\n  crarr: '↵',\n  lArr: '⇐',\n  uArr: '⇑',\n  rArr: '⇒',\n  dArr: '⇓',\n  hArr: '⇔',\n  forall: '∀',\n  part: '∂',\n  exist: '∃',\n  empty: '∅',\n  nabla: '∇',\n  isin: '∈',\n  notin: '∉',\n  ni: '∋',\n  prod: '∏',\n  sum: '∑',\n  minus: '−',\n  lowast: '∗',\n  radic: '√',\n  prop: '∝',\n  infin: '∞',\n  ang: '∠',\n  and: '∧',\n  or: '∨',\n  cap: '∩',\n  cup: '∪',\n  int: '∫',\n  there4: '∴',\n  sim: '∼',\n  cong: '≅',\n  asymp: '≈',\n  ne: '≠',\n  equiv: '≡',\n  le: '≤',\n  ge: '≥',\n  sub: '⊂',\n  sup: '⊃',\n  nsub: '⊄',\n  sube: '⊆',\n  supe: '⊇',\n  oplus: '⊕',\n  otimes: '⊗',\n  perp: '⊥',\n  sdot: '⋅',\n  lceil: '⌈',\n  rceil: '⌉',\n  lfloor: '⌊',\n  rfloor: '⌋',\n  lang: '〈',\n  rang: '〉',\n  loz: '◊',\n  spades: '♠',\n  clubs: '♣',\n  hearts: '♥',\n  diams: '♦',\n  quot: '\"',\n  amp: '&',\n  lt: '<',\n  gt: '>',\n  OElig: 'Œ',\n  oelig: 'œ',\n  Scaron: 'Š',\n  scaron: 'š',\n  Yuml: 'Ÿ',\n  circ: 'ˆ',\n  tilde: '˜',\n  ensp: ' ',\n  emsp: ' ',\n  thinsp: ' ',\n  zwnj: '‌',\n  zwj: '‍',\n  lrm: '‎',\n  rlm: '‏',\n  ndash: '–',\n  mdash: '—',\n  lsquo: '‘',\n  rsquo: '’',\n  sbquo: '‚',\n  ldquo: '“',\n  rdquo: '”',\n  bdquo: '„',\n  dagger: '†',\n  Dagger: '‡',\n  permil: '‰',\n  lsaquo: '‹',\n  rsaquo: '›',\n  euro: '€'\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,yBAAyB;IACpC,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,OAAO;IACP,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,UAAU;IACV,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,IAAI;IACJ,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,QAAQ;IACR,KAAK;IACL,MAAM;IACN,OAAO;IACP,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4364, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/comment.js"], "sourcesContent": ["/**\n * @import {Comment, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\nimport {stringifyEntities} from 'stringify-entities'\n\nconst htmlCommentRegex = /^>|^->|<!--|-->|--!>|<!-$/g\n\n// Declare arrays as variables so it can be cached by `stringifyEntities`\nconst bogusCommentEntitySubset = ['>']\nconst commentEntitySubset = ['<', '>']\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        stringifyEntities(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {\n            subset: bogusCommentEntitySubset\n          })\n        ) +\n        '>'\n    : '<!--' + node.value.replace(htmlCommentRegex, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return stringifyEntities(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: commentEntitySubset\n      })\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAEA,MAAM,mBAAmB;AAEzB,yEAAyE;AACzE,MAAM,2BAA2B;IAAC;CAAI;AACtC,MAAM,sBAAsB;IAAC;IAAK;CAAI;AAgB/B,SAAS,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;IACzC,qEAAqE;IACrE,OAAO,MAAM,QAAQ,CAAC,aAAa,GAC/B,OACE,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACd,KAAK,KAAK,EACV,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QACpD,QAAQ;IACV,MAEF,MACF,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,kBAAkB,UAAU;;;IAE5D;;GAEC,GACD,SAAS,OAAO,EAAE;QAChB,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACrB,IACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;YACpD,QAAQ;QACV;IAEJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4400, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/doctype.js"], "sourcesContent": ["/**\n * @import {Doctype, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a doctype.\n *\n * @param {Doctype} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;CAaC;;;AACM,SAAS,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;IACvC,OACE,OACA,CAAC,MAAM,QAAQ,CAAC,YAAY,GAAG,YAAY,SAAS,IACpD,CAAC,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,IACvC;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4426, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/omission/util/siblings.js"], "sourcesContent": ["/**\n * @import {Parents, RootContent} from 'hast'\n */\n\nimport {whitespace} from 'hast-util-whitespace'\n\nexport const siblingAfter = siblings(1)\nexport const siblingBefore = siblings(-1)\n\n/** @type {Array<RootContent>} */\nconst emptyChildren = []\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @template {Parents} Parent\n   *   Parent type.\n   * @param {Parent | undefined} parent\n   *   Parent.\n   * @param {number | undefined} index\n   *   Index of child in `parent`.\n   * @param {boolean | undefined} [includeWhitespace=false]\n   *   Whether to include whitespace (default: `false`).\n   * @returns {Parent extends {children: Array<infer Child>} ? Child | undefined : never}\n   *   Child of parent.\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : emptyChildren\n    let offset = (index || 0) + increment\n    let next = siblings[offset]\n\n    if (!includeWhitespace) {\n      while (next && whitespace(next)) {\n        offset += increment\n        next = siblings[offset]\n      }\n    }\n\n    // @ts-expect-error: it’s a correct child.\n    return next\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;;AAEO,MAAM,eAAe,SAAS;AAC9B,MAAM,gBAAgB,SAAS,CAAC;AAEvC,+BAA+B,GAC/B,MAAM,gBAAgB,EAAE;AAExB;;;;CAIC,GACD,SAAS,SAAS,SAAS;IACzB,OAAO;;;IAEP;;;;;;;;;;;;;GAaC,GACD,SAAS,QAAQ,MAAM,EAAE,KAAK,EAAE,iBAAiB;QAC/C,MAAM,WAAW,SAAS,OAAO,QAAQ,GAAG;QAC5C,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI;QAC5B,IAAI,OAAO,QAAQ,CAAC,OAAO;QAE3B,IAAI,CAAC,mBAAmB;YACtB,MAAO,QAAQ,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,MAAO;gBAC/B,UAAU;gBACV,OAAO,QAAQ,CAAC,OAAO;YACzB;QACF;QAEA,0CAA0C;QAC1C,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4476, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/omission/omission.js"], "sourcesContent": ["/**\n * @import {Element, Parents} from 'hast'\n */\n\n/**\n * @callback OmitHandle\n *   Check if a tag can be omitted.\n * @param {Element} element\n *   Element to check.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether to omit a tag.\n *\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nexport function omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;;;;;;CAYC;;;AAED,MAAM,MAAM,CAAC,EAAE,cAAc;AAWtB,SAAS,SAAS,QAAQ;IAC/B,OAAO;;;IAEP;;;;GAIC,GACD,SAAS,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM;QAC/B,OACE,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,KAC/B,QAAQ,CAAC,KAAK,OAAO,CAAC,CAAC,MAAM,OAAO;IAExC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/omission/closing.js"], "sourcesContent": ["/**\n * @import {Element, Parents} from 'hast'\n */\n\nimport {whitespace} from 'hast-util-whitespace'\nimport {siblingAfter} from './util/siblings.js'\nimport {omission} from './omission.js'\n\nexport const closing = omission({\n  body,\n  caption: headOrColgroupOrCaption,\n  colgroup: headOrColgroupOrCaption,\n  dd,\n  dt,\n  head: headOrColgroupOrCaption,\n  html,\n  li,\n  optgroup,\n  option,\n  p,\n  rp: rubyElement,\n  rt: rubyElement,\n  tbody,\n  td: cells,\n  tfoot,\n  th: cells,\n  thead,\n  tr\n})\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = siblingAfter(parent, index, true)\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && whitespace(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction p(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !siblingAfter(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = siblingAfter(parent, index)\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;;;;AAEO,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE;IAC9B;IACA,SAAS;IACT,UAAU;IACV;IACA;IACA,MAAM;IACN;IACA;IACA;IACA;IACA;IACA,IAAI;IACJ,IAAI;IACJ;IACA,IAAI;IACJ;IACA,IAAI;IACJ;IACA;AACF;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,wBAAwB,CAAC,EAAE,KAAK,EAAE,MAAM;IAC/C,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;IACzC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,CAAC,KAAK,IAAI,KAAK,UAAU,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG;AAEhE;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM;IAC5B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAQ,KAAK,IAAI,KAAK;AAChC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM;IAC5B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAQ,KAAK,IAAI,KAAK;AAChC;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IACzB,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,OACH,KAAK,IAAI,KAAK,aACZ,CAAC,KAAK,OAAO,KAAK,aAChB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,WACjB,KAAK,OAAO,KAAK,gBACjB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,cACjB,KAAK,OAAO,KAAK,gBACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,QACjB,KAAK,OAAO,KAAK,OACjB,KAAK,OAAO,KAAK,SACjB,KAAK,OAAO,KAAK,aACjB,KAAK,OAAO,KAAK,WACjB,KAAK,OAAO,KAAK,IAAI,IACzB,CAAC,UACC,oBAAoB;IACpB,CAAC,CACC,OAAO,IAAI,KAAK,aAChB,CAAC,OAAO,OAAO,KAAK,OAClB,OAAO,OAAO,KAAK,WACnB,OAAO,OAAO,KAAK,SACnB,OAAO,OAAO,KAAK,SACnB,OAAO,OAAO,KAAK,SACnB,OAAO,OAAO,KAAK,cACnB,OAAO,OAAO,KAAK,OAAO,CAC9B;AACR;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AAC/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,QACL,QACE,KAAK,IAAI,KAAK,aACd,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,YAAY,CAAC,EAAE,KAAK,EAAE,MAAM;IACnC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,SAAS,CAAC,EAAE,KAAK,EAAE,MAAM;IAChC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AAC/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,OAAO,CAAC,EAAE,KAAK,EAAE,MAAM;IAC9B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,YAAY,KAAK,OAAO,KAAK,UAAU;AAE/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,QACL,QACE,KAAK,IAAI,KAAK,aACd,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,OAAO;AAE3D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,KAAK,OAAO;AAE3D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,OAAO,CAAC,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;AAC/B;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM;IAC1B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OAAO,CAAC,QAAS,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AAC/D;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM;IAC7B,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;IAClC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,IAAI;AAErD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4771, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/omission/opening.js"], "sourcesContent": ["/**\n * @import {Element, Parents} from 'hast'\n */\n\nimport {whitespace} from 'hast-util-whitespace'\nimport {siblingAfter, siblingBefore} from './util/siblings.js'\nimport {closing} from './closing.js'\nimport {omission} from './omission.js'\n\nexport const opening = omission({\n  body,\n  colgroup,\n  head,\n  html,\n  tbody\n})\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = siblingAfter(node, -1)\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  /** @type {Set<string>} */\n  const seen = new Set()\n\n  // Whether `srcdoc` or not,\n  // make sure the content model at least doesn’t have too many `base`s/`title`s.\n  for (const child of node.children) {\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'base' || child.tagName === 'title')\n    ) {\n      if (seen.has(child.tagName)) return false\n      seen.add(child.tagName)\n    }\n  }\n\n  // “May be omitted if the element is empty,\n  // or if the first thing inside the head element is an element.”\n  const child = node.children[0]\n  return !child || child.type === 'element'\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = siblingAfter(node, -1, true)\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && whitespace(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = siblingBefore(parent, index)\n  const head = siblingAfter(node, -1, true)\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    closing(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'col')\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = siblingBefore(parent, index)\n  const head = siblingAfter(node, -1)\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    closing(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'tr')\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;AACA;AACA;AACA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE;IAC9B;IACA;IACA;IACA;IACA;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI;IAChB,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC;IACjC,OAAO,CAAC,QAAQ,KAAK,IAAI,KAAK;AAChC;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI;IAChB,wBAAwB,GACxB,MAAM,OAAO,IAAI;IAEjB,2BAA2B;IAC3B,+EAA+E;IAC/E,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAE;QACjC,IACE,MAAM,IAAI,KAAK,aACf,CAAC,MAAM,OAAO,KAAK,UAAU,MAAM,OAAO,KAAK,OAAO,GACtD;YACA,IAAI,KAAK,GAAG,CAAC,MAAM,OAAO,GAAG,OAAO;YACpC,KAAK,GAAG,CAAC,MAAM,OAAO;QACxB;IACF;IAEA,2CAA2C;IAC3C,gEAAgE;IAChE,MAAM,QAAQ,KAAK,QAAQ,CAAC,EAAE;IAC9B,OAAO,CAAC,SAAS,MAAM,IAAI,KAAK;AAClC;AAEA;;;;;;;CAOC,GACD,SAAS,KAAK,IAAI;IAChB,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC,GAAG;IAEpC,OACE,CAAC,QACA,KAAK,IAAI,KAAK,aACb,CAAC,CAAC,KAAK,IAAI,KAAK,UAAU,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD,EAAE,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,KAC1D,CAAC,CACC,KAAK,IAAI,KAAK,aACd,CAAC,KAAK,OAAO,KAAK,UAChB,KAAK,OAAO,KAAK,UACjB,KAAK,OAAO,KAAK,YACjB,KAAK,OAAO,KAAK,WACjB,KAAK,OAAO,KAAK,UAAU,CAC/B;AAEN;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;IACnC,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;IACvC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC,GAAG;IAEpC,yCAAyC;IACzC,IACE,UACA,YACA,SAAS,IAAI,KAAK,aAClB,SAAS,OAAO,KAAK,cACrB,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,SACrD;QACA,OAAO;IACT;IAEA,OAAO,QAAQ,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AACrE;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,MAAM,IAAI,EAAE,KAAK,EAAE,MAAM;IAChC,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;IACvC,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC;IAEjC,8CAA8C;IAC9C,IACE,UACA,YACA,SAAS,IAAI,KAAK,aAClB,CAAC,SAAS,OAAO,KAAK,WAAW,SAAS,OAAO,KAAK,OAAO,KAC7D,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,SACrD;QACA,OAAO;IACT;IAEA,OAAO,QAAQ,QAAQ,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/element.js"], "sourcesContent": ["/**\n * @import {Element, Parents, Properties} from 'hast'\n * @import {State} from '../index.js'\n */\n\nimport {ccount} from 'ccount'\nimport {stringify as commas} from 'comma-separated-tokens'\nimport {find, svg} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport {stringifyEntities} from 'stringify-entities'\nimport {closing} from '../omission/closing.js'\nimport {opening} from '../omission/opening.js'\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'double' | 'name' | 'single' | 'unquoted', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n}\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function element(node, index, parent, state) {\n  const schema = state.schema\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase())\n  /** @type {Array<string>} */\n  const parts = []\n  /** @type {string} */\n  let last\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = svg\n  }\n\n  const attributes = serializeAttributes(state, node.properties)\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  )\n\n  state.schema = schema\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  // Note: `menuitem` has since been removed from the HTML spec, and so is no\n  // longer void.\n  if (content) selfClosing = false\n\n  if (attributes || !omit || !opening(node, index, parent)) {\n    parts.push('<', node.tagName, attributes ? ' ' + attributes : '')\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attributes.charAt(attributes.length - 1)\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ')\n      }\n\n      parts.push('/')\n    }\n\n    parts.push('>')\n  }\n\n  parts.push(content)\n\n  if (!selfClosing && (!omit || !closing(node, index, parent))) {\n    parts.push('</' + node.tagName + '>')\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} properties\n * @returns {string}\n */\nfunction serializeAttributes(state, properties) {\n  /** @type {Array<string>} */\n  const values = []\n  let index = -1\n  /** @type {string} */\n  let key\n\n  if (properties) {\n    for (key in properties) {\n      if (properties[key] !== null && properties[key] !== undefined) {\n        const value = serializeAttribute(state, key, properties[key])\n        if (value) values.push(value)\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : undefined\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' '\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {Properties[keyof Properties]} value\n * @returns {string}\n */\nfunction serializeAttribute(state, key, value) {\n  const info = find(state.schema, key)\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1\n  const y = state.settings.allowDangerousCharacters ? 0 : 1\n  let quote = state.quote\n  /** @type {string | undefined} */\n  let result\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true\n  } else if (\n    (info.boolean || info.overloadedBoolean) &&\n    (typeof value !== 'string' || value === info.attribute || value === '')\n  ) {\n    value = Boolean(value)\n  }\n\n  if (\n    value === null ||\n    value === undefined ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = stringifyEntities(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  )\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? commas : spaces)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value)\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = stringifyEntities(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        attribute: true,\n        subset: constants.unquoted[x][y]\n      })\n    )\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      ccount(value, quote) > ccount(value, state.alternative)\n    ) {\n      quote = state.alternative\n    }\n\n    result =\n      quote +\n      stringifyEntities(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA;;;;;;;;CAQC,GACD,MAAM,YAAY;IAChB,6DAA6D;IAC7D,MAAM;QACJ;YAAC,gBAAgB,KAAK,CAAC;YAAK,oBAAoB,KAAK,CAAC;SAAI;QAC1D;YAAC,sBAAsB,KAAK,CAAC;YAAK,uBAAuB,KAAK,CAAC;SAAI;KACpE;IACD,yEAAyE;IACzE,UAAU;QACR;YAAC,cAAc,KAAK,CAAC;YAAK,sBAAsB,KAAK,CAAC;SAAI;QAC1D;YAAC,sBAAsB,KAAK,CAAC;YAAK,sBAAsB,KAAK,CAAC;SAAI;KACnE;IACD,8EAA8E;IAC9E,QAAQ;QACN;YAAC,KAAK,KAAK,CAAC;YAAK,QAAQ,KAAK,CAAC;SAAI;QACnC;YAAC,OAAO,KAAK,CAAC;YAAK,UAAU,KAAK,CAAC;SAAI;KACxC;IACD,8EAA8E;IAC9E,QAAQ;QACN;YAAC,KAAK,KAAK,CAAC;YAAK,QAAQ,KAAK,CAAC;SAAI;QACnC;YAAC,OAAO,KAAK,CAAC;YAAK,UAAU,KAAK,CAAC;SAAI;KACxC;AACH;AAgBO,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;IAChD,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,OAAO,OAAO,KAAK,KAAK,QAAQ,QAAQ,MAAM,QAAQ,CAAC,gBAAgB;IAC7E,IAAI,cACF,OAAO,KAAK,KAAK,QACb,MAAM,QAAQ,CAAC,kBAAkB,GACjC,MAAM,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,OAAO,CAAC,WAAW;IAC5D,0BAA0B,GAC1B,MAAM,QAAQ,EAAE;IAChB,mBAAmB,GACnB,IAAI;IAEJ,IAAI,OAAO,KAAK,KAAK,UAAU,KAAK,OAAO,KAAK,OAAO;QACrD,MAAM,MAAM,GAAG,gKAAA,CAAA,MAAG;IACpB;IAEA,MAAM,aAAa,oBAAoB,OAAO,KAAK,UAAU;IAE7D,MAAM,UAAU,MAAM,GAAG,CACvB,OAAO,KAAK,KAAK,UAAU,KAAK,OAAO,KAAK,aAAa,KAAK,OAAO,GAAG;IAG1E,MAAM,MAAM,GAAG;IAEf,sEAAsE;IACtE,kBAAkB;IAClB,2EAA2E;IAC3E,mDAAmD;IACnD,2EAA2E;IAC3E,eAAe;IACf,IAAI,SAAS,cAAc;IAE3B,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,SAAS;QACxD,MAAM,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE,aAAa,MAAM,aAAa;QAE9D,IACE,eACA,CAAC,OAAO,KAAK,KAAK,SAAS,MAAM,QAAQ,CAAC,gBAAgB,GAC1D;YACA,OAAO,WAAW,MAAM,CAAC,WAAW,MAAM,GAAG;YAC7C,IACE,CAAC,MAAM,QAAQ,CAAC,gBAAgB,IAChC,SAAS,OACR,QAAQ,SAAS,OAAO,SAAS,KAClC;gBACA,MAAM,IAAI,CAAC;YACb;YAEA,MAAM,IAAI,CAAC;QACb;QAEA,MAAM,IAAI,CAAC;IACb;IAEA,MAAM,IAAI,CAAC;IAEX,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAA,GAAA,wKAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,OAAO,GAAG;QAC5D,MAAM,IAAI,CAAC,OAAO,KAAK,OAAO,GAAG;IACnC;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA;;;;CAIC,GACD,SAAS,oBAAoB,KAAK,EAAE,UAAU;IAC5C,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IACb,mBAAmB,GACnB,IAAI;IAEJ,IAAI,YAAY;QACd,IAAK,OAAO,WAAY;YACtB,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,UAAU,CAAC,IAAI,KAAK,WAAW;gBAC7D,MAAM,QAAQ,mBAAmB,OAAO,KAAK,UAAU,CAAC,IAAI;gBAC5D,IAAI,OAAO,OAAO,IAAI,CAAC;YACzB;QACF;IACF;IAEA,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,OAAO,MAAM,QAAQ,CAAC,eAAe,GACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,KAC5C;QAEJ,4DAA4D;QAC5D,IAAI,UAAU,OAAO,MAAM,GAAG,KAAK,SAAS,OAAO,SAAS,KAAK;YAC/D,MAAM,CAAC,MAAM,IAAI;QACnB;IACF;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA;;;;;CAKC,GACD,SAAS,mBAAmB,KAAK,EAAE,GAAG,EAAE,KAAK;IAC3C,MAAM,OAAO,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,MAAM,EAAE;IAChC,MAAM,IACJ,MAAM,QAAQ,CAAC,gBAAgB,IAAI,MAAM,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI;IACzE,MAAM,IAAI,MAAM,QAAQ,CAAC,wBAAwB,GAAG,IAAI;IACxD,IAAI,QAAQ,MAAM,KAAK;IACvB,+BAA+B,GAC/B,IAAI;IAEJ,IAAI,KAAK,iBAAiB,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE,GAAG;QACxE,QAAQ;IACV,OAAO,IACL,CAAC,KAAK,OAAO,IAAI,KAAK,iBAAiB,KACvC,CAAC,OAAO,UAAU,YAAY,UAAU,KAAK,SAAS,IAAI,UAAU,EAAE,GACtE;QACA,QAAQ,QAAQ;IAClB;IAEA,IACE,UAAU,QACV,UAAU,aACV,UAAU,SACT,OAAO,UAAU,YAAY,OAAO,KAAK,CAAC,QAC3C;QACA,OAAO;IACT;IAEA,MAAM,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAC3B,KAAK,SAAS,EACd,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QACpD,kDAAkD;QAClD,QAAQ,UAAU,IAAI,CAAC,EAAE,CAAC,EAAE;IAC9B;IAGF,YAAY;IACZ,uEAAuE;IACvE,SAAS;IACT,6EAA6E;IAC7E,+CAA+C;IAC/C,+BAA+B;IAC/B,EAAE;IACF,UAAU;IACV,kBAAkB;IAClB,8BAA8B;IAC9B,0CAA0C;IAC1C,kCAAkC;IAClC,SAAS;IACT,SAAS;IACT,MAAM;IACN,EAAE;IACF,6EAA6E;IAC7E,IAAI,UAAU,MAAM,OAAO;IAE3B,yEAAyE;IACzE,yBAAyB;IACzB,QAAQ,MAAM,OAAO,CAAC,SAClB,CAAC,KAAK,cAAc,GAAG,qJAAA,CAAA,YAAM,GAAG,qJAAA,CAAA,YAAM,EAAE,OAAO;QAC7C,SAAS,CAAC,MAAM,QAAQ,CAAC,wBAAwB;IACnD,KACA,OAAO;IAEX,IAAI,MAAM,QAAQ,CAAC,uBAAuB,IAAI,CAAC,OAAO,OAAO;IAE7D,wBAAwB;IACxB,IAAI,MAAM,QAAQ,CAAC,cAAc,EAAE;QACjC,SAAS,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACvB,OACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;YACpD,WAAW;YACX,QAAQ,UAAU,QAAQ,CAAC,EAAE,CAAC,EAAE;QAClC;IAEJ;IAEA,8EAA8E;IAC9E,YAAY;IACZ,IAAI,WAAW,OAAO;QACpB,0DAA0D;QAC1D,IACE,MAAM,QAAQ,CAAC,UAAU,IACzB,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,WAAW,GACtD;YACA,QAAQ,MAAM,WAAW;QAC3B;QAEA,SACE,QACA,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACd,OACA,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;YACpD,kDAAkD;YAClD,QAAQ,CAAC,UAAU,MAAM,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;YACnE,WAAW;QACb,MAEF;IACJ;IAEA,wCAAwC;IACxC,OAAO,OAAO,CAAC,SAAS,MAAM,SAAS,MAAM;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5092, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/text.js"], "sourcesContent": ["/**\n * @import {Parents, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\nimport {stringifyEntities} from 'stringify-entities'\n\n// Declare array as variable so it can be cached by `stringifyEntities`\nconst textEntitySubset = ['<', '&']\n\n/**\n * Serialize a text node.\n *\n * @param {Raw | Text} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : stringifyEntities(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: textEntitySubset\n        })\n      )\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAEA,uEAAuE;AACvE,MAAM,mBAAmB;IAAC;IAAK;CAAI;AAgB5B,SAAS,KAAK,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK;IACzC,gDAAgD;IAChD,OAAO,UACL,OAAO,IAAI,KAAK,aAChB,CAAC,OAAO,OAAO,KAAK,YAAY,OAAO,OAAO,KAAK,OAAO,IACxD,KAAK,KAAK,GACV,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EACd,KAAK,KAAK,EACV,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QACpD,QAAQ;IACV;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5116, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/raw.js"], "sourcesContent": ["/**\n * @import {Parents} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {State} from '../index.js'\n */\n\nimport {text} from './text.js'\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : text(node, index, parent, state)\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAgBO,SAAS,IAAI,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;IAC5C,OAAO,MAAM,QAAQ,CAAC,kBAAkB,GACpC,KAAK,KAAK,GACV,CAAA,GAAA,mKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,QAAQ;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5132, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/root.js"], "sourcesContent": ["/**\n * @import {Parents, Root} from 'hast'\n * @import {State} from '../index.js'\n */\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nexport function root(node, _1, _2, state) {\n  return state.all(node)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;CAaC;;;AACM,SAAS,KAAK,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;IACtC,OAAO,MAAM,GAAG,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/handle/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents} from 'hast'\n * @import {State} from '../index.js'\n */\n\nimport {zwitch} from 'zwitch'\nimport {comment} from './comment.js'\nimport {doctype} from './doctype.js'\nimport {element} from './element.js'\nimport {raw} from './raw.js'\nimport {root} from './root.js'\nimport {text} from './text.js'\n\n/**\n * @type {(node: Nodes, index: number | undefined, parent: Parents | undefined, state: State) => string}\n */\nexport const handle = zwitch('type', {\n  invalid,\n  unknown,\n  handlers: {comment, doctype, element, raw, root, text}\n})\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node_\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node_) {\n  // `type` is guaranteed by runtime JS.\n  const node = /** @type {Nodes} */ (node_)\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAKO,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IACnC;IACA;IACA,UAAU;QAAC,SAAA,sKAAA,CAAA,UAAO;QAAE,SAAA,sKAAA,CAAA,UAAO;QAAE,SAAA,sKAAA,CAAA,UAAO;QAAE,KAAA,kKAAA,CAAA,MAAG;QAAE,MAAA,mKAAA,CAAA,OAAI;QAAE,MAAA,mKAAA,CAAA,OAAI;IAAA;AACvD;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,IAAI;IACnB,MAAM,IAAI,MAAM,yBAAyB,OAAO;AAClD;AAEA;;;;;;;CAOC,GACD,SAAS,QAAQ,KAAK;IACpB,sCAAsC;IACtC,MAAM,OAA6B;IACnC,MAAM,IAAI,MAAM,kCAAkC,KAAK,IAAI,GAAG;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5216, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-html/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents, RootContent} from 'hast'\n * @import {Schema} from 'property-information'\n * @import {Options as StringifyEntitiesOptions} from 'stringify-entities'\n */\n\n/**\n * @typedef {Omit<StringifyEntitiesOptions, 'attribute' | 'escapeOnly' | 'subset'>} CharacterReferences\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [allowDangerousCharacters=false]\n *   Do not encode some characters which cause XSS vulnerabilities in older\n *   browsers (default: `false`).\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Allow `raw` nodes and insert them as raw HTML (default: `false`).\n *\n *   When `false`, `Raw` nodes are encoded.\n *\n *   > ⚠️ **Danger**: only set this if you completely trust the content.\n * @property {boolean | null | undefined} [allowParseErrors=false]\n *   Do not encode characters which cause parse errors (even though they work),\n *   to save bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [bogusComments=false]\n *   Use “bogus comments” instead of comments to save byes: `<?charlie>`\n *   instead of `<!--charlie-->` (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {CharacterReferences | null | undefined} [characterReferences]\n *   Configure how to serialize character references (optional).\n * @property {boolean | null | undefined} [closeEmptyElements=false]\n *   Close SVG elements without any content with slash (`/`) on the opening tag\n *   instead of an end tag: `<circle />` instead of `<circle></circle>`\n *   (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the HTML space.\n * @property {boolean | null | undefined} [closeSelfClosing=false]\n *   Close self-closing nodes with an extra slash (`/`): `<img />` instead of\n *   `<img>` (default: `false`).\n *\n *   See `tightSelfClosing` to control whether a space is used before the\n *   slash.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [collapseEmptyAttributes=false]\n *   Collapse empty attributes: get `class` instead of `class=\"\"` (default:\n *   `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: boolean attributes (such as `hidden`) are always collapsed.\n * @property {boolean | null | undefined} [omitOptionalTags=false]\n *   Omit optional opening and closing tags (default: `false`).\n *\n *   For example, in `<ol><li>one</li><li>two</li></ol>`, both `</li>` closing\n *   tags can be omitted.\n *   The first because it’s followed by another `li`, the last because it’s\n *   followed by nothing.\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [preferUnquoted=false]\n *   Leave attributes unquoted if that results in less bytes (default: `false`).\n *\n *   Not used in the SVG space.\n * @property {boolean | null | undefined} [quoteSmart=false]\n *   Use the other quote if that results in less bytes (default: `false`).\n * @property {Quote | null | undefined} [quote='\"']\n *   Preferred quote to use (default: `'\"'`).\n * @property {Space | null | undefined} [space='html']\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it (default: `'html'`).\n *\n *   > 👉 **Note**: hast is not XML.\n *   > It supports SVG as embedded in HTML.\n *   > It does not support the features available in XML.\n *   > Passing SVG might break but fragments of modern SVG should be fine.\n *   > Use [`xast`][xast] if you need to support SVG as XML.\n * @property {boolean | null | undefined} [tightAttributes=false]\n *   Join attributes together, without whitespace, if possible: get\n *   `class=\"a b\"title=\"c d\"` instead of `class=\"a b\" title=\"c d\"` to save\n *   bytes (default: `false`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightCommaSeparatedLists=false]\n *   Join known comma-separated attribute values with just a comma (`,`),\n *   instead of padding them on the right as well (`,␠`, where `␠` represents a\n *   space) (default: `false`).\n * @property {boolean | null | undefined} [tightDoctype=false]\n *   Drop unneeded spaces in doctypes: `<!doctypehtml>` instead of\n *   `<!doctype html>` to save bytes (default: `false`).\n *\n *   > 👉 **Note**: intentionally creates parse errors in markup (how parse\n *   > errors are handled is well defined, so this works but isn’t pretty).\n * @property {boolean | null | undefined} [tightSelfClosing=false]\n *   Do not use an extra space when closing self-closing elements: `<img/>`\n *   instead of `<img />` (default: `false`).\n *\n *   > 👉 **Note**: only used if `closeSelfClosing: true` or\n *   > `closeEmptyElements: true`.\n * @property {boolean | null | undefined} [upperDoctype=false]\n *   Use a `<!DOCTYPE…` instead of `<!doctype…` (default: `false`).\n *\n *   Useless except for XHTML.\n * @property {ReadonlyArray<string> | null | undefined} [voids]\n *   Tag names of elements to serialize without closing tag (default: `html-void-elements`).\n *\n *   Not used in the SVG space.\n *\n *   > 👉 **Note**: It’s highly unlikely that you want to pass this, because\n *   > hast is not for XML, and HTML will not add more void elements.\n *\n * @typedef {'\"' | \"'\"} Quote\n *   HTML quotes for attribute values.\n *\n * @typedef {Omit<Required<{[key in keyof Options]: Exclude<Options[key], null | undefined>}>, 'space' | 'quote'>} Settings\n *\n * @typedef {'html' | 'svg'} Space\n *   Namespace.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Parents | undefined) => string} all\n *   Serialize the children of a parent node.\n * @property {Quote} alternative\n *   Alternative quote.\n * @property {(node: Nodes, index: number | undefined, parent: Parents | undefined) => string} one\n *   Serialize one node.\n * @property {Quote} quote\n *   Preferred quote.\n * @property {Schema} schema\n *   Current schema.\n * @property {Settings} settings\n *   User configuration.\n */\n\nimport {htmlVoidElements} from 'html-void-elements'\nimport {html, svg} from 'property-information'\nimport {handle} from './handle/index.js'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/** @type {CharacterReferences} */\nconst emptyCharacterReferences = {}\n\n/** @type {Array<never>} */\nconst emptyChildren = []\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Array<RootContent> | Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized HTML.\n */\nexport function toHtml(tree, options) {\n  const options_ = options || emptyOptions\n  const quote = options_.quote || '\"'\n  const alternative = quote === '\"' ? \"'\" : '\"'\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || emptyCharacterReferences,\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? svg : html,\n    quote,\n    alternative\n  }\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return handle(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nexport function all(parent) {\n  /** @type {Array<string>} */\n  const results = []\n  const children = (parent && parent.children) || emptyChildren\n  let index = -1\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent)\n  }\n\n  return results.join('')\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8IC;;;;AAED;AACA;AACA;;;;AAEA,oBAAoB,GACpB,MAAM,eAAe,CAAC;AAEtB,gCAAgC,GAChC,MAAM,2BAA2B,CAAC;AAElC,yBAAyB,GACzB,MAAM,gBAAgB,EAAE;AAYjB,SAAS,OAAO,IAAI,EAAE,OAAO;IAClC,MAAM,WAAW,WAAW;IAC5B,MAAM,QAAQ,SAAS,KAAK,IAAI;IAChC,MAAM,cAAc,UAAU,MAAM,MAAM;IAE1C,IAAI,UAAU,OAAO,UAAU,KAAK;QAClC,MAAM,IAAI,MAAM,oBAAoB,QAAQ;IAC9C;IAEA,kBAAkB,GAClB,MAAM,QAAQ;QACZ;QACA;QACA,UAAU;YACR,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,0BAA0B,SAAS,wBAAwB,IAAI;YAC/D,YAAY,SAAS,UAAU,IAAI;YACnC,gBAAgB,SAAS,cAAc,IAAI;YAC3C,iBAAiB,SAAS,eAAe,IAAI;YAC7C,cAAc,SAAS,YAAY,IAAI;YACvC,cAAc,SAAS,YAAY,IAAI;YACvC,eAAe,SAAS,aAAa,IAAI;YACzC,0BAA0B,SAAS,wBAAwB,IAAI;YAC/D,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,yBAAyB,SAAS,uBAAuB,IAAI;YAC7D,oBAAoB,SAAS,kBAAkB,IAAI;YACnD,OAAO,SAAS,KAAK,IAAI,iJAAA,CAAA,mBAAgB;YACzC,qBACE,SAAS,mBAAmB,IAAI;YAClC,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,oBAAoB,SAAS,kBAAkB,IAAI;QACrD;QACA,QAAQ,SAAS,KAAK,KAAK,QAAQ,gKAAA,CAAA,MAAG,GAAG,gKAAA,CAAA,OAAI;QAC7C;QACA;IACF;IAEA,OAAO,MAAM,GAAG,CACd,MAAM,OAAO,CAAC,QAAQ;QAAC,MAAM;QAAQ,UAAU;IAAI,IAAI,MACvD,WACA;AAEJ;AAEA;;;;;;;;;;;;;CAaC,GACD,SAAS,IAAI,IAAI,EAAE,KAAK,EAAE,MAAM;IAC9B,OAAO,CAAA,GAAA,oKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO,QAAQ,IAAI;AACzC;AAWO,SAAS,IAAI,MAAM;IACxB,0BAA0B,GAC1B,MAAM,UAAU,EAAE;IAClB,MAAM,WAAW,AAAC,UAAU,OAAO,QAAQ,IAAK;IAChD,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,SAAS,MAAM,CAAE;QAChC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO;IACpD;IAEA,OAAO,QAAQ,IAAI,CAAC;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5442, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/ccount/index.js"], "sourcesContent": ["/**\n * Count how often a character (or substring) is used in a string.\n *\n * @param {string} value\n *   Value to search in.\n * @param {string} character\n *   Character (or substring) to look for.\n * @return {number}\n *   Number of times `character` occurred in `value`.\n */\nexport function ccount(value, character) {\n  const source = String(value)\n\n  if (typeof character !== 'string') {\n    throw new TypeError('Expected character')\n  }\n\n  let count = 0\n  let index = source.indexOf(character)\n\n  while (index !== -1) {\n    count++\n    index = source.indexOf(character, index + character.length)\n  }\n\n  return count\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AACM,SAAS,OAAO,KAAK,EAAE,SAAS;IACrC,MAAM,SAAS,OAAO;IAEtB,IAAI,OAAO,cAAc,UAAU;QACjC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,QAAQ;IACZ,IAAI,QAAQ,OAAO,OAAO,CAAC;IAE3B,MAAO,UAAU,CAAC,EAAG;QACnB;QACA,QAAQ,OAAO,OAAO,CAAC,WAAW,QAAQ,UAAU,MAAM;IAC5D;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5471, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,8BAA8B;AAC9B,yDAAyD;;;;AACzD,MAAM,KAAK;AAaJ,SAAS,WAAW,KAAK;IAC9B,OAAO,OAAO,UAAU,WACpB,MAAM,IAAI,KAAK,SACb,MAAM,MAAM,KAAK,IACjB,QACF,MAAM;AACZ;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,MAAM,OAAO,CAAC,IAAI,QAAQ;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5492, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/rehype-stringify/lib/index.js"], "sourcesContent": ["/**\n * @import {Root} from 'hast'\n * @import {Options} from 'hast-util-to-html'\n * @import {Compiler, Processor} from 'unified'\n */\n\nimport {toHtml} from 'hast-util-to-html'\n\n/**\n * Plugin to add support for serializing as HTML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function rehypeStringify(options) {\n  /** @type {Processor<undefined, undefined, undefined, Root, string>} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n  const settings = {...self.data('settings'), ...options}\n\n  self.compiler = compiler\n\n  /**\n   * @type {Compiler<Root, string>}\n   */\n  function compiler(tree) {\n    return toHtml(tree, settings)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAUe,SAAS,gBAAgB,OAAO;IAC7C,qEAAqE,GACrE,oFAAoF;IACpF,MAAM,OAAO,IAAI;IACjB,MAAM,WAAW;QAAC,GAAG,KAAK,IAAI,CAAC,WAAW;QAAE,GAAG,OAAO;IAAA;IAEtD,KAAK,QAAQ,GAAG;IAEhB;;GAEC,GACD,SAAS,SAAS,IAAI;QACpB,OAAO,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5519, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/bail/index.js"], "sourcesContent": ["/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACM,SAAS,KAAK,KAAK;IACxB,IAAI,OAAO;QACT,MAAM;IACR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5539, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/extend/index.js"], "sourcesContent": ["'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nvar isArray = function isArray(arr) {\n\tif (typeof Array.isArray === 'function') {\n\t\treturn Array.isArray(arr);\n\t}\n\n\treturn toStr.call(arr) === '[object Array]';\n};\n\nvar isPlainObject = function isPlainObject(obj) {\n\tif (!obj || toStr.call(obj) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tvar hasOwnConstructor = hasOwn.call(obj, 'constructor');\n\tvar hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n\t// Not own constructor property must be Object\n\tif (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n\t\treturn false;\n\t}\n\n\t// Own properties are enumerated firstly, so to speed up,\n\t// if last one is own, then all properties are own.\n\tvar key;\n\tfor (key in obj) { /**/ }\n\n\treturn typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n\tif (defineProperty && options.name === '__proto__') {\n\t\tdefineProperty(target, options.name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t\tvalue: options.newValue,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\ttarget[options.name] = options.newValue;\n\t}\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n\tif (name === '__proto__') {\n\t\tif (!hasOwn.call(obj, name)) {\n\t\t\treturn void 0;\n\t\t} else if (gOPD) {\n\t\t\t// In early versions of node, obj['__proto__'] is buggy when obj has\n\t\t\t// __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n\t\t\treturn gOPD(obj, name).value;\n\t\t}\n\t}\n\n\treturn obj[name];\n};\n\nmodule.exports = function extend() {\n\tvar options, name, src, copy, copyIsArray, clone;\n\tvar target = arguments[0];\n\tvar i = 1;\n\tvar length = arguments.length;\n\tvar deep = false;\n\n\t// Handle a deep copy situation\n\tif (typeof target === 'boolean') {\n\t\tdeep = target;\n\t\ttarget = arguments[1] || {};\n\t\t// skip the boolean and the target\n\t\ti = 2;\n\t}\n\tif (target == null || (typeof target !== 'object' && typeof target !== 'function')) {\n\t\ttarget = {};\n\t}\n\n\tfor (; i < length; ++i) {\n\t\toptions = arguments[i];\n\t\t// Only deal with non-null/undefined values\n\t\tif (options != null) {\n\t\t\t// Extend the base object\n\t\t\tfor (name in options) {\n\t\t\t\tsrc = getProperty(target, name);\n\t\t\t\tcopy = getProperty(options, name);\n\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif (target !== copy) {\n\t\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\t\tif (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n\t\t\t\t\t\tif (copyIsArray) {\n\t\t\t\t\t\t\tcopyIsArray = false;\n\t\t\t\t\t\t\tclone = src && isArray(src) ? src : [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tclone = src && isPlainObject(src) ? src : {};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: extend(deep, clone, copy) });\n\n\t\t\t\t\t// Don't bring in undefined values\n\t\t\t\t\t} else if (typeof copy !== 'undefined') {\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: copy });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n"], "names": [], "mappings": "AAEA,IAAI,SAAS,OAAO,SAAS,CAAC,cAAc;AAC5C,IAAI,QAAQ,OAAO,SAAS,CAAC,QAAQ;AACrC,IAAI,iBAAiB,OAAO,cAAc;AAC1C,IAAI,OAAO,OAAO,wBAAwB;AAE1C,IAAI,UAAU,SAAS,QAAQ,GAAG;IACjC,IAAI,OAAO,MAAM,OAAO,KAAK,YAAY;QACxC,OAAO,MAAM,OAAO,CAAC;IACtB;IAEA,OAAO,MAAM,IAAI,CAAC,SAAS;AAC5B;AAEA,IAAI,gBAAgB,SAAS,cAAc,GAAG;IAC7C,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,SAAS,mBAAmB;QAClD,OAAO;IACR;IAEA,IAAI,oBAAoB,OAAO,IAAI,CAAC,KAAK;IACzC,IAAI,mBAAmB,IAAI,WAAW,IAAI,IAAI,WAAW,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,IAAI,WAAW,CAAC,SAAS,EAAE;IAC9G,8CAA8C;IAC9C,IAAI,IAAI,WAAW,IAAI,CAAC,qBAAqB,CAAC,kBAAkB;QAC/D,OAAO;IACR;IAEA,yDAAyD;IACzD,mDAAmD;IACnD,IAAI;IACJ,IAAK,OAAO,IAAK,CAAO;IAExB,OAAO,OAAO,QAAQ,eAAe,OAAO,IAAI,CAAC,KAAK;AACvD;AAEA,gHAAgH;AAChH,IAAI,cAAc,SAAS,YAAY,MAAM,EAAE,OAAO;IACrD,IAAI,kBAAkB,QAAQ,IAAI,KAAK,aAAa;QACnD,eAAe,QAAQ,QAAQ,IAAI,EAAE;YACpC,YAAY;YACZ,cAAc;YACd,OAAO,QAAQ,QAAQ;YACvB,UAAU;QACX;IACD,OAAO;QACN,MAAM,CAAC,QAAQ,IAAI,CAAC,GAAG,QAAQ,QAAQ;IACxC;AACD;AAEA,8EAA8E;AAC9E,IAAI,cAAc,SAAS,YAAY,GAAG,EAAE,IAAI;IAC/C,IAAI,SAAS,aAAa;QACzB,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,OAAO;YAC5B,OAAO,KAAK;QACb,OAAO,IAAI,MAAM;YAChB,oEAAoE;YACpE,yEAAyE;YACzE,OAAO,KAAK,KAAK,MAAM,KAAK;QAC7B;IACD;IAEA,OAAO,GAAG,CAAC,KAAK;AACjB;AAEA,OAAO,OAAO,GAAG,SAAS;IACzB,IAAI,SAAS,MAAM,KAAK,MAAM,aAAa;IAC3C,IAAI,SAAS,SAAS,CAAC,EAAE;IACzB,IAAI,IAAI;IACR,IAAI,SAAS,UAAU,MAAM;IAC7B,IAAI,OAAO;IAEX,+BAA+B;IAC/B,IAAI,OAAO,WAAW,WAAW;QAChC,OAAO;QACP,SAAS,SAAS,CAAC,EAAE,IAAI,CAAC;QAC1B,kCAAkC;QAClC,IAAI;IACL;IACA,IAAI,UAAU,QAAS,OAAO,WAAW,YAAY,OAAO,WAAW,YAAa;QACnF,SAAS,CAAC;IACX;IAEA,MAAO,IAAI,QAAQ,EAAE,EAAG;QACvB,UAAU,SAAS,CAAC,EAAE;QACtB,2CAA2C;QAC3C,IAAI,WAAW,MAAM;YACpB,yBAAyB;YACzB,IAAK,QAAQ,QAAS;gBACrB,MAAM,YAAY,QAAQ;gBAC1B,OAAO,YAAY,SAAS;gBAE5B,4BAA4B;gBAC5B,IAAI,WAAW,MAAM;oBACpB,mDAAmD;oBACnD,IAAI,QAAQ,QAAQ,CAAC,cAAc,SAAS,CAAC,cAAc,QAAQ,KAAK,CAAC,GAAG;wBAC3E,IAAI,aAAa;4BAChB,cAAc;4BACd,QAAQ,OAAO,QAAQ,OAAO,MAAM,EAAE;wBACvC,OAAO;4BACN,QAAQ,OAAO,cAAc,OAAO,MAAM,CAAC;wBAC5C;wBAEA,0CAA0C;wBAC1C,YAAY,QAAQ;4BAAE,MAAM;4BAAM,UAAU,OAAO,MAAM,OAAO;wBAAM;oBAEvE,kCAAkC;oBAClC,OAAO,IAAI,OAAO,SAAS,aAAa;wBACvC,YAAY,QAAQ;4BAAE,MAAM;4BAAM,UAAU;wBAAK;oBAClD;gBACD;YACD;QACD;IACD;IAEA,6BAA6B;IAC7B,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/is-plain-obj/index.js"], "sourcesContent": ["export default function isPlainObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,cAAc,KAAK;IAC1C,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;QAChD,OAAO;IACR;IAEA,MAAM,YAAY,OAAO,cAAc,CAAC;IACxC,OAAO,CAAC,cAAc,QAAQ,cAAc,OAAO,SAAS,IAAI,OAAO,cAAc,CAAC,eAAe,IAAI,KAAK,CAAC,CAAC,OAAO,WAAW,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,QAAQ,IAAI,KAAK;AAC3K", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5662, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/trough/lib/index.js"], "sourcesContent": ["// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n"], "names": [], "mappings": "AAAA,wBAAwB;AACxB,uEAAuE;AAEvE;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GAED;;;;;CAKC;;;;AACM,SAAS;IACd,8BAA8B,GAC9B,MAAM,MAAM,EAAE;IACd,qBAAqB,GACrB,MAAM,WAAW;QAAC;QAAK;IAAG;IAE1B,OAAO;;;IAEP,gBAAgB,GAChB,SAAS,IAAI,GAAG,MAAM;QACpB,IAAI,kBAAkB,CAAC;QACvB,qBAAqB,GACrB,MAAM,WAAW,OAAO,GAAG;QAE3B,IAAI,OAAO,aAAa,YAAY;YAClC,MAAM,IAAI,UAAU,6CAA6C;QACnE;QAEA,KAAK,SAAS;QAEd;;;;;KAKC,GACD,SAAS,KAAK,KAAK,EAAE,GAAG,MAAM;YAC5B,MAAM,KAAK,GAAG,CAAC,EAAE,gBAAgB;YACjC,IAAI,QAAQ,CAAC;YAEb,IAAI,OAAO;gBACT,SAAS;gBACT;YACF;YAEA,sCAAsC;YACtC,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;gBAC9B,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,KAAK,WAAW;oBACzD,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;gBAC/B;YACF;YAEA,qDAAqD;YACrD,SAAS;YAET,gBAAgB;YAChB,IAAI,IAAI;gBACN,KAAK,IAAI,SAAS;YACpB,OAAO;gBACL,SAAS,SAAS;YACpB;QACF;IACF;IAEA,gBAAgB,GAChB,SAAS,IAAI,UAAU;QACrB,IAAI,OAAO,eAAe,YAAY;YACpC,MAAM,IAAI,UACR,iDAAiD;QAErD;QAEA,IAAI,IAAI,CAAC;QACT,OAAO;IACT;AACF;AAkCO,SAAS,KAAK,UAAU,EAAE,QAAQ;IACvC,oBAAoB,GACpB,IAAI;IAEJ,OAAO;;;IAEP;;;;;GAKC,GACD,SAAS,QAAQ,GAAG,UAAU;QAC5B,MAAM,oBAAoB,WAAW,MAAM,GAAG,WAAW,MAAM;QAC/D,gBAAgB,GAChB,IAAI;QAEJ,IAAI,mBAAmB;YACrB,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI;YACF,SAAS,WAAW,KAAK,CAAC,IAAI,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,MAAM,YAAkC;YAExC,kCAAkC;YAClC,yEAAyE;YACzE,kBAAkB;YAClB,2DAA2D;YAC3D,IAAI,qBAAqB,QAAQ;gBAC/B,MAAM;YACR;YAEA,OAAO,KAAK;QACd;QAEA,IAAI,CAAC,mBAAmB;YACtB,IAAI,UAAU,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY;gBAC9D,OAAO,IAAI,CAAC,MAAM;YACpB,OAAO,IAAI,kBAAkB,OAAO;gBAClC,KAAK;YACP,OAAO;gBACL,KAAK;YACP;QACF;IACF;IAEA;;;;GAIC,GACD,SAAS,KAAK,KAAK,EAAE,GAAG,MAAM;QAC5B,IAAI,CAAC,QAAQ;YACX,SAAS;YACT,SAAS,UAAU;QACrB;IACF;IAEA;;;;GAIC,GACD,SAAS,KAAK,KAAK;QACjB,KAAK,MAAM;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5813, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unified/lib/callable-instance.js"], "sourcesContent": ["export const CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n"], "names": [], "mappings": ";;;AAAO,MAAM,mBAOP;;;;OAIC,GACD,SAAU,QAAQ;IAChB,MAAM,OAAO,IAAI;IACjB,MAAM,SAAS,KAAK,WAAW;IAC/B,MAAM,QACJ,uBAAuB;IACvB,iCAAiC;IACjC,OAAO,SAAS;IAElB,MAAM,QAAQ,KAAK,CAAC,SAAS;IAC7B,uDAAuD,GACvD,MAAM,QAAQ;QACZ,OAAO,MAAM,KAAK,CAAC,OAAO;IAC5B;IAEA,OAAO,cAAc,CAAC,OAAO;IAE7B,kEAAkE;IAClE,YAAY;IACZ,yDAAyD;IACzD,QAAQ;IACR,oBAAoB;IACpB,kDAAkD;IAClD,EAAE;IACF,2BAA2B;IAC3B,iEAAiE;IACjE,gEAAgE;IAChE,IAAI;IAEJ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5848, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unified/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\nimport {bail} from 'bail'\nimport extend from 'extend'\nimport {ok as assert} from 'devlop'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\nimport {CallableInstance} from './callable-instance.js'\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nexport class Processor extends CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = trough()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      bail(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      assert(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if (isPlainObj(currentPrimary) && isPlainObj(primary)) {\n          primary = extend(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nexport const unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC,GAED;;;;;;CAMC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCC,GAED;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;CAOC,GAED;;;CAGC,GAED,4DAA4D;AAC5D,2DAA2D;AAC3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8CC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;;;;;;;;;CAeC,GAED;;;;;;;;;;;;;;;;CAgBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0CC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwDC,GAED;;;;;;;;;;;;CAYC;;;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,kEAAkE;AAElE,4EAA4E;AAC5E,qBAAqB;AACrB,6EAA6E;AAE7E,MAAM,MAAM,CAAC,EAAE,cAAc;AAetB,MAAM,kBAAkB,sJAAA,CAAA,mBAAgB;IAC7C;;GAEC,GACD,aAAc;QACZ,kEAAkE;QAClE,KAAK,CAAC;QAEN;;;;;;;;;;;;KAYC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;;;;;KASC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd,qDAAqD;QACrD,mEAAmE;QACnE,mEAAmE;QACnE,0EAA0E;QAC1E;;;;;;KAMC,GACD,IAAI,CAAC,SAAS,GAAG,EAAE;QAEnB;;;;;;;;;;KAUC,GACD,IAAI,CAAC,QAAQ,GAAG;QAEhB;;;;;;KAMC,GACD,IAAI,CAAC,WAAW,GAAG,CAAC;QAEpB;;;;;;KAMC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;;;;;KAMC,GACD,IAAI,CAAC,SAAS,GAAG,CAAC;QAElB;;;;;;;KAOC,GACD,IAAI,CAAC,MAAM,GAAG;QAEd;;;;;;KAMC,GACD,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD;IAC3B;IAEA;;;;;;;;;;GAUC,GACD,OAAO;QACL,gEAAgE;QAChE,MAAM,cAEF,IAAI;QAER,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE;YACtC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM;YACtC,YAAY,GAAG,IAAI;QACrB;QAEA,YAAY,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,UAAM,AAAD,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS;QAEhD,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0DC,GACD,KAAK,GAAG,EAAE,KAAK,EAAE;QACf,IAAI,OAAO,QAAQ,UAAU;YAC3B,aAAa;YACb,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,eAAe,QAAQ,IAAI,CAAC,MAAM;gBAClC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;gBACtB,OAAO,IAAI;YACb;YAEA,aAAa;YACb,OAAO,AAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,IAAK;QACnE;QAEA,aAAa;QACb,IAAI,KAAK;YACP,eAAe,QAAQ,IAAI,CAAC,MAAM;YAClC,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO,IAAI;QACb;QAEA,aAAa;QACb,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,SAAS;QACP,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI;QACb;QAEA,2CAA2C;QAC3C,yEAAyE;QACzE,wBAAwB;QACxB,MAAM,OAAyD,IAAI;QAEnE,MAAO,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE;YACjD,MAAM,CAAC,UAAU,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;YAE/D,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO;gBACxB;YACF;YAEA,IAAI,OAAO,CAAC,EAAE,KAAK,MAAM;gBACvB,OAAO,CAAC,EAAE,GAAG;YACf;YAEA,MAAM,cAAc,SAAS,IAAI,CAAC,SAAS;YAE3C,IAAI,OAAO,gBAAgB,YAAY;gBACrC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG,OAAO,iBAAiB;QAE3C,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;GAaC,GACD,MAAM,IAAI,EAAE;QACV,IAAI,CAAC,MAAM;QACX,MAAM,WAAW,MAAM;QACvB,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QACzC,aAAa,SAAS;QACtB,OAAO,OAAO,OAAO,WAAW;IAClC;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCC,GACD,QAAQ,IAAI,EAAE,IAAI,EAAE;QAClB,MAAM,OAAO,IAAI;QAEjB,IAAI,CAAC,MAAM;QACX,aAAa,WAAW,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QAClD,eAAe,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAExD,OAAO,OAAO,SAAS,WAAW,QAAQ,IAAI,QAAQ;;;QAEtD,+BAA+B;QAC/B;;;;KAIC,GACD,SAAS,SAAS,OAAO,EAAE,MAAM;YAC/B,MAAM,WAAW,MAAM;YACvB,wEAAwE;YACxE,iCAAiC;YACjC,MAAM,YAEsB,KAAK,KAAK,CAAC;YAGvC,KAAK,GAAG,CAAC,WAAW,UAAU,SAAU,KAAK,EAAE,IAAI,EAAE,IAAI;gBACvD,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM;oBAC3B,OAAO,SAAS;gBAClB;gBAEA,+DAA+D;gBAC/D,6CAA6C;gBAC7C,MAAM,cAEsB;gBAG5B,MAAM,gBAAgB,KAAK,SAAS,CAAC,aAAa;gBAElD,IAAI,gBAAgB,gBAAgB;oBAClC,KAAK,KAAK,GAAG;gBACf,OAAO;oBACL,KAAK,MAAM,GAAG;gBAChB;gBAEA,SAAS,OAAsD;YACjE;YAEA;;;;OAIC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;gBAC3B,IAAI,SAAS,CAAC,MAAM;oBAClB,OAAO;gBACT,OAAO,IAAI,SAAS;oBAClB,QAAQ;gBACV,OAAO;oBACL,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;oBACb,KAAK,WAAW;gBAClB;YACF;QACF;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BC,GACD,YAAY,IAAI,EAAE;QAChB,oBAAoB,GACpB,IAAI,WAAW;QACf,uDAAuD,GACvD,IAAI;QAEJ,IAAI,CAAC,MAAM;QACX,aAAa,eAAe,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;QACtD,eAAe,eAAe,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAE5D,IAAI,CAAC,OAAO,CAAC,MAAM;QACnB,WAAW,eAAe,WAAW;QACrC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,QAAQ;QAEf,OAAO;;;QAEP;;KAEC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;YAC3B,WAAW;YACX,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD,EAAE;YACL,SAAS;QACX;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCC,GACD,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;QACpB,WAAW;QACX,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe,IAAI,CAAC,YAAY;QAEtC,IAAI,CAAC,QAAQ,OAAO,SAAS,YAAY;YACvC,OAAO;YACP,OAAO;QACT;QAEA,OAAO,OAAO,SAAS,WAAW,QAAQ,IAAI,QAAQ;;;QAEtD,+BAA+B;QAC/B;;;;;;;KAOC,GACD,SAAS,SAAS,OAAO,EAAE,MAAM;YAC/B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,OAAO,SAAS,YAChB;YAEF,MAAM,WAAW,MAAM;YACvB,aAAa,GAAG,CAAC,MAAM,UAAU;YAEjC;;;;;OAKC,GACD,SAAS,SAAS,KAAK,EAAE,UAAU,EAAE,IAAI;gBACvC,MAAM,gBAEF,cAAc;gBAGlB,IAAI,OAAO;oBACT,OAAO;gBACT,OAAO,IAAI,SAAS;oBAClB,QAAQ;gBACV,OAAO;oBACL,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,MAAM;oBACb,KAAK,WAAW,eAAe;gBACjC;YACF;QACF;IACF;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,QAAQ,IAAI,EAAE,IAAI,EAAE;QAClB,oBAAoB,GACpB,IAAI,WAAW;QACf,uEAAuE,GACvE,IAAI;QAEJ,IAAI,CAAC,GAAG,CAAC,MAAM,MAAM;QAErB,WAAW,WAAW,OAAO;QAC7B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,QAAQ;QACf,OAAO;;;QAEP;;KAEC,GACD,SAAS,SAAS,KAAK,EAAE,IAAI;YAC3B,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD,EAAE;YACL,SAAS;YACT,WAAW;QACb;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BC,GACD,UAAU,IAAI,EAAE,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM;QACX,MAAM,WAAW,MAAM;QACvB,MAAM,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC/C,eAAe,aAAa;QAC5B,WAAW;QAEX,OAAO,SAAS,MAAM;IACxB;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwDC,GACD,IAAI,KAAK,EAAE,GAAG,UAAU,EAAE;QACxB,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,YAAY,IAAI,CAAC,SAAS;QAEhC,eAAe,OAAO,IAAI,CAAC,MAAM;QAEjC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,SAAS;QACX,OAAO,IAAI,OAAO,UAAU,YAAY;YACtC,UAAU,OAAO;QACnB,OAAO,IAAI,OAAO,UAAU,UAAU;YACpC,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,QAAQ;YACV,OAAO;gBACL,UAAU;YACZ;QACF,OAAO;YACL,MAAM,IAAI,UAAU,iCAAiC,QAAQ;QAC/D;QAEA,OAAO,IAAI;;;QAEX;;;KAGC,GACD,SAAS,IAAI,KAAK;YAChB,IAAI,OAAO,UAAU,YAAY;gBAC/B,UAAU,OAAO,EAAE;YACrB,OAAO,IAAI,OAAO,UAAU,UAAU;gBACpC,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,CAAC,QAAQ,GAAG,WAAW,GACiB;oBAC9C,UAAU,QAAQ;gBACpB,OAAO;oBACL,UAAU;gBACZ;YACF,OAAO;gBACL,MAAM,IAAI,UAAU,iCAAiC,QAAQ;YAC/D;QACF;QAEA;;;KAGC,GACD,SAAS,UAAU,MAAM;YACvB,IAAI,CAAC,CAAC,aAAa,MAAM,KAAK,CAAC,CAAC,cAAc,MAAM,GAAG;gBACrD,MAAM,IAAI,MACR;YAEJ;YAEA,QAAQ,OAAO,OAAO;YAEtB,IAAI,OAAO,QAAQ,EAAE;gBACnB,UAAU,QAAQ,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAM,AAAD,EAAE,MAAM,UAAU,QAAQ,EAAE,OAAO,QAAQ;YACvE;QACF;QAEA;;;KAGC,GACD,SAAS,QAAQ,OAAO;YACtB,IAAI,QAAQ,CAAC;YAEb,IAAI,YAAY,QAAQ,YAAY,WAAW;YAC7C,SAAS;YACX,OAAO,IAAI,MAAM,OAAO,CAAC,UAAU;gBACjC,MAAO,EAAE,QAAQ,QAAQ,MAAM,CAAE;oBAC/B,MAAM,QAAQ,OAAO,CAAC,MAAM;oBAC5B,IAAI;gBACN;YACF,OAAO;gBACL,MAAM,IAAI,UAAU,sCAAsC,UAAU;YACtE;QACF;QAEA;;;;KAIC,GACD,SAAS,UAAU,MAAM,EAAE,UAAU;YACnC,IAAI,QAAQ,CAAC;YACb,IAAI,aAAa,CAAC;YAElB,MAAO,EAAE,QAAQ,UAAU,MAAM,CAAE;gBACjC,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,QAAQ;oBAClC,aAAa;oBACb;gBACF;YACF;YAEA,IAAI,eAAe,CAAC,GAAG;gBACrB,UAAU,IAAI,CAAC;oBAAC;uBAAW;iBAAW;YACxC,OAGK,IAAI,WAAW,MAAM,GAAG,GAAG;gBAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;gBACzB,MAAM,iBAAiB,SAAS,CAAC,WAAW,CAAC,EAAE;gBAC/C,IAAI,CAAA,GAAA,2IAAA,CAAA,UAAU,AAAD,EAAE,mBAAmB,CAAA,GAAA,2IAAA,CAAA,UAAU,AAAD,EAAE,UAAU;oBACrD,UAAU,CAAA,GAAA,+HAAA,CAAA,UAAM,AAAD,EAAE,MAAM,gBAAgB;gBACzC;gBAEA,SAAS,CAAC,WAAW,GAAG;oBAAC;oBAAQ;uBAAY;iBAAK;YACpD;QACF;IACF;AACF;AA8BO,MAAM,UAAU,IAAI,YAAY,MAAM;AAE7C;;;;;;CAMC,GACD,SAAS,aAAa,IAAI,EAAE,KAAK;IAC/B,IAAI,OAAO,UAAU,YAAY;QAC/B,MAAM,IAAI,UAAU,aAAa,OAAO;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,IAAI,EAAE,KAAK;IACjC,IAAI,OAAO,UAAU,YAAY;QAC/B,MAAM,IAAI,UAAU,aAAa,OAAO;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,SAAS,eAAe,IAAI,EAAE,MAAM;IAClC,IAAI,QAAQ;QACV,MAAM,IAAI,MACR,kBACE,OACA;IAEN;AACF;AAEA;;;;;CAKC,GACD,SAAS,WAAW,IAAI;IACtB,8DAA8D;IAC9D,iCAAiC;IACjC,IAAI,CAAC,CAAA,GAAA,2IAAA,CAAA,UAAU,AAAD,EAAE,SAAS,OAAO,KAAK,IAAI,KAAK,UAAU;QACtD,MAAM,IAAI,UAAU,yBAAyB,OAAO;IACpD,QAAQ;IACV;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,WAAW,IAAI,EAAE,SAAS,EAAE,QAAQ;IAC3C,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MACR,MAAM,OAAO,4BAA4B,YAAY;IAEzD;AACF;AAEA;;;CAGC,GACD,SAAS,MAAM,KAAK;IAClB,OAAO,gBAAgB,SAAS,QAAQ,IAAI,qIAAA,CAAA,QAAK,CAAC;AACpD;AAEA;;;CAGC,GACD,SAAS,gBAAgB,KAAK;IAC5B,OAAO,QACL,SACE,OAAO,UAAU,YACjB,aAAa,SACb,cAAc;AAEpB;AAEA;;;CAGC,GACD,SAAS,gBAAgB,KAAK;IAC5B,OAAO,OAAO,UAAU,YAAY,aAAa;AACnD;AAEA;;;;;;;CAOC,GACD,SAAS,aAAa,KAAK;IACzB,OAAO,QACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAEtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6969, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/rehype/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`\nimport rehypeParse from 'rehype-parse'\nimport rehypeStringify from 'rehype-stringify'\nimport {unified} from 'unified'\n\n/**\n * Create a new unified processor that already uses `rehype-parse` and\n * `rehype-stringify`.\n */\nexport const rehype = unified().use(rehypeParse).use(rehypeStringify).freeze()\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AACxC;AACA;AACA;;;;AAMO,MAAM,SAAS,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,IAAI,GAAG,CAAC,+IAAA,CAAA,UAAW,EAAE,GAAG,CAAC,mJAAA,CAAA,UAAe,EAAE,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6984, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40uiw/react-textarea-code-editor/esm/utils.js"], "sourcesContent": ["import { rehype } from 'rehype';\nexport var processHtml = function processHtml(html, plugins) {\n  if (plugins === void 0) {\n    plugins = [];\n  }\n  return rehype().data('settings', {\n    fragment: true\n  }).use([...plugins]).processSync(\"\" + html).toString();\n};\nexport function htmlEncode(sHtml) {\n  return sHtml.replace(/```(tsx?|jsx?|html|xml)(.*)\\s+([\\s\\S]*?)(\\s.+)?```/g, str => {\n    return str.replace(/[<&\"]/g, c => ({\n      '<': '&lt;',\n      '>': '&gt;',\n      '&': '&amp;',\n      '\"': '&quot;'\n    })[c]);\n  }).replace(/[<&\"]/g, c => ({\n    '<': '&lt;',\n    '>': '&gt;',\n    '&': '&amp;',\n    '\"': '&quot;'\n  })[c]);\n}\nexport function stopPropagation(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}"], "names": [], "mappings": ";;;;;AAAA;;AACO,IAAI,cAAc,SAAS,YAAY,IAAI,EAAE,OAAO;IACzD,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,EAAE;IACd;IACA,OAAO,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,IAAI,IAAI,CAAC,YAAY;QAC/B,UAAU;IACZ,GAAG,GAAG,CAAC;WAAI;KAAQ,EAAE,WAAW,CAAC,KAAK,MAAM,QAAQ;AACtD;AACO,SAAS,WAAW,KAAK;IAC9B,OAAO,MAAM,OAAO,CAAC,uDAAuD,CAAA;QAC1E,OAAO,IAAI,OAAO,CAAC,UAAU,CAAA,IAAK,CAAC;gBACjC,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;YACP,CAAC,CAAC,CAAC,EAAE;IACP,GAAG,OAAO,CAAC,UAAU,CAAA,IAAK,CAAC;YACzB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,CAAC,CAAC,CAAC,EAAE;AACP;AACO,SAAS,gBAAgB,CAAC;IAC/B,EAAE,eAAe;IACjB,EAAE,cAAc;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7024, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40uiw/react-textarea-code-editor/esm/SelectionText.js"], "sourcesContent": ["export class SelectionText {\n  constructor(elm) {\n    this.elm = void 0;\n    this.start = void 0;\n    this.end = void 0;\n    this.value = void 0;\n    var {\n      selectionStart,\n      selectionEnd\n    } = elm;\n    this.elm = elm;\n    this.start = selectionStart;\n    this.end = selectionEnd;\n    this.value = this.elm.value;\n  }\n  position(start, end) {\n    var {\n      selectionStart,\n      selectionEnd\n    } = this.elm;\n    this.start = typeof start === 'number' && !isNaN(start) ? start : selectionStart;\n    this.end = typeof end === 'number' && !isNaN(end) ? end : selectionEnd;\n    this.elm.selectionStart = this.start;\n    this.elm.selectionEnd = this.end;\n    return this;\n  }\n  insertText(text) {\n    // Most of the used APIs only work with the field selected\n    this.elm.focus();\n    this.elm.setRangeText(text);\n    this.value = this.elm.value;\n    this.position();\n    return this;\n  }\n  getSelectedValue(start, end) {\n    var {\n      selectionStart,\n      selectionEnd\n    } = this.elm;\n    return this.value.slice(typeof start === 'number' && !isNaN(start) ? start : selectionStart, typeof end === 'number' && !isNaN(end) ? start : selectionEnd);\n  }\n  getLineStartNumber() {\n    var start = this.start;\n    while (start > 0) {\n      start--;\n      if (this.value.charAt(start) === '\\n') {\n        start++;\n        break;\n      }\n    }\n    return start;\n  }\n  /** Indent on new lines */\n  getIndentText() {\n    var start = this.getLineStartNumber();\n    var str = this.getSelectedValue(start);\n    var indent = '';\n    str.replace(/(^(\\s)+)/, (str, old) => indent = old);\n    return indent;\n  }\n  lineStarInstert(text) {\n    if (text) {\n      var oldStart = this.start;\n      var start = this.getLineStartNumber();\n      var str = this.getSelectedValue(start);\n      this.position(start, this.end).insertText(str.split('\\n').map(txt => text + txt).join('\\n')).position(oldStart + text.length, this.end);\n    }\n    return this;\n  }\n  lineStarRemove(text) {\n    if (text) {\n      var oldStart = this.start;\n      var start = this.getLineStartNumber();\n      var str = this.getSelectedValue(start);\n      var reg = new RegExp(\"^\" + text, 'g');\n      var newStart = oldStart - text.length;\n      if (!reg.test(str)) {\n        newStart = oldStart;\n      }\n      this.position(start, this.end).insertText(str.split('\\n').map(txt => txt.replace(reg, '')).join('\\n')).position(newStart, this.end);\n    }\n  }\n  /** Notify any possible listeners of the change */\n  notifyChange() {\n    var event = new Event('input', {\n      bubbles: true,\n      cancelable: false\n    });\n    this.elm.dispatchEvent(event);\n  }\n}"], "names": [], "mappings": ";;;AAAO,MAAM;IACX,YAAY,GAAG,CAAE;QACf,IAAI,CAAC,GAAG,GAAG,KAAK;QAChB,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,GAAG,GAAG,KAAK;QAChB,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,EACF,cAAc,EACd,YAAY,EACb,GAAG;QACJ,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK;IAC7B;IACA,SAAS,KAAK,EAAE,GAAG,EAAE;QACnB,IAAI,EACF,cAAc,EACd,YAAY,EACb,GAAG,IAAI,CAAC,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,OAAO,UAAU,YAAY,CAAC,MAAM,SAAS,QAAQ;QAClE,IAAI,CAAC,GAAG,GAAG,OAAO,QAAQ,YAAY,CAAC,MAAM,OAAO,MAAM;QAC1D,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK;QACpC,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG;QAChC,OAAO,IAAI;IACb;IACA,WAAW,IAAI,EAAE;QACf,0DAA0D;QAC1D,IAAI,CAAC,GAAG,CAAC,KAAK;QACd,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK;QAC3B,IAAI,CAAC,QAAQ;QACb,OAAO,IAAI;IACb;IACA,iBAAiB,KAAK,EAAE,GAAG,EAAE;QAC3B,IAAI,EACF,cAAc,EACd,YAAY,EACb,GAAG,IAAI,CAAC,GAAG;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,UAAU,YAAY,CAAC,MAAM,SAAS,QAAQ,gBAAgB,OAAO,QAAQ,YAAY,CAAC,MAAM,OAAO,QAAQ;IAChJ;IACA,qBAAqB;QACnB,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,MAAO,QAAQ,EAAG;YAChB;YACA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,MAAM;gBACrC;gBACA;YACF;QACF;QACA,OAAO;IACT;IACA,wBAAwB,GACxB,gBAAgB;QACd,IAAI,QAAQ,IAAI,CAAC,kBAAkB;QACnC,IAAI,MAAM,IAAI,CAAC,gBAAgB,CAAC;QAChC,IAAI,SAAS;QACb,IAAI,OAAO,CAAC,YAAY,CAAC,KAAK,MAAQ,SAAS;QAC/C,OAAO;IACT;IACA,gBAAgB,IAAI,EAAE;QACpB,IAAI,MAAM;YACR,IAAI,WAAW,IAAI,CAAC,KAAK;YACzB,IAAI,QAAQ,IAAI,CAAC,kBAAkB;YACnC,IAAI,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,MAAO,OAAO,KAAK,IAAI,CAAC,OAAO,QAAQ,CAAC,WAAW,KAAK,MAAM,EAAE,IAAI,CAAC,GAAG;QACxI;QACA,OAAO,IAAI;IACb;IACA,eAAe,IAAI,EAAE;QACnB,IAAI,MAAM;YACR,IAAI,WAAW,IAAI,CAAC,KAAK;YACzB,IAAI,QAAQ,IAAI,CAAC,kBAAkB;YACnC,IAAI,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAChC,IAAI,MAAM,IAAI,OAAO,MAAM,MAAM;YACjC,IAAI,WAAW,WAAW,KAAK,MAAM;YACrC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM;gBAClB,WAAW;YACb;YACA,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,MAAO,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI,CAAC,OAAO,QAAQ,CAAC,UAAU,IAAI,CAAC,GAAG;QACpI;IACF;IACA,gDAAgD,GAChD,eAAe;QACb,IAAI,QAAQ,IAAI,MAAM,SAAS;YAC7B,SAAS;YACT,YAAY;QACd;QACA,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40uiw/react-textarea-code-editor/esm/shortcuts.js"], "sourcesContent": ["import { stopPropagation } from \"./utils.js\";\nimport { SelectionText } from \"./SelectionText.js\";\nexport default function shortcuts(e, indentWidth) {\n  if (indentWidth === void 0) {\n    indentWidth = 2;\n  }\n  var api = new SelectionText(e.target);\n  /**\n   * Support of shortcuts for React v16\n   * https://github.com/uiwjs/react-textarea-code-editor/issues/128\n   * https://blog.saeloun.com/2021/04/23/react-keyboard-event-code.html\n   */\n  var code = (e.code || e.nativeEvent.code).toLocaleLowerCase();\n  var indent = ' '.repeat(indentWidth);\n  if (code === 'tab') {\n    stopPropagation(e);\n    if (api.start === api.end) {\n      if (e.shiftKey) {\n        api.lineStarRemove(indent);\n      } else {\n        api.insertText(indent).position(api.start + indentWidth, api.end + indentWidth);\n      }\n    } else if (api.getSelectedValue().indexOf('\\n') > -1 && e.shiftKey) {\n      api.lineStarRemove(indent);\n    } else if (api.getSelectedValue().indexOf('\\n') > -1) {\n      api.lineStarInstert(indent);\n    } else {\n      api.insertText(indent).position(api.start + indentWidth, api.end);\n    }\n    api.notifyChange();\n  } else if (code === 'enter') {\n    stopPropagation(e);\n    var _indent = \"\\n\" + api.getIndentText();\n    api.insertText(_indent).position(api.start + _indent.length, api.start + _indent.length);\n    api.notifyChange();\n  } else if (code && /^(quote|backquote|bracketleft|digit9|comma)$/.test(code) && api.getSelectedValue()) {\n    stopPropagation(e);\n    var val = api.getSelectedValue();\n    var txt = '';\n    switch (code) {\n      case 'quote':\n        txt = \"'\" + val + \"'\";\n        if (e.shiftKey) {\n          txt = \"\\\"\" + val + \"\\\"\";\n        }\n        break;\n      case 'backquote':\n        txt = \"`\" + val + \"`\";\n        break;\n      case 'bracketleft':\n        txt = \"[\" + val + \"]\";\n        if (e.shiftKey) {\n          txt = \"{\" + val + \"}\";\n        }\n        break;\n      case 'digit9':\n        txt = \"(\" + val + \")\";\n        break;\n      case 'comma':\n        txt = \"<\" + val + \">\";\n        break;\n    }\n    api.insertText(txt);\n    api.notifyChange();\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS,UAAU,CAAC,EAAE,WAAW;IAC9C,IAAI,gBAAgB,KAAK,GAAG;QAC1B,cAAc;IAChB;IACA,IAAI,MAAM,IAAI,oLAAA,CAAA,gBAAa,CAAC,EAAE,MAAM;IACpC;;;;GAIC,GACD,IAAI,OAAO,CAAC,EAAE,IAAI,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,iBAAiB;IAC3D,IAAI,SAAS,IAAI,MAAM,CAAC;IACxB,IAAI,SAAS,OAAO;QAClB,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,IAAI,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;YACzB,IAAI,EAAE,QAAQ,EAAE;gBACd,IAAI,cAAc,CAAC;YACrB,OAAO;gBACL,IAAI,UAAU,CAAC,QAAQ,QAAQ,CAAC,IAAI,KAAK,GAAG,aAAa,IAAI,GAAG,GAAG;YACrE;QACF,OAAO,IAAI,IAAI,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,EAAE;YAClE,IAAI,cAAc,CAAC;QACrB,OAAO,IAAI,IAAI,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG;YACpD,IAAI,eAAe,CAAC;QACtB,OAAO;YACL,IAAI,UAAU,CAAC,QAAQ,QAAQ,CAAC,IAAI,KAAK,GAAG,aAAa,IAAI,GAAG;QAClE;QACA,IAAI,YAAY;IAClB,OAAO,IAAI,SAAS,SAAS;QAC3B,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,IAAI,UAAU,OAAO,IAAI,aAAa;QACtC,IAAI,UAAU,CAAC,SAAS,QAAQ,CAAC,IAAI,KAAK,GAAG,QAAQ,MAAM,EAAE,IAAI,KAAK,GAAG,QAAQ,MAAM;QACvF,IAAI,YAAY;IAClB,OAAO,IAAI,QAAQ,+CAA+C,IAAI,CAAC,SAAS,IAAI,gBAAgB,IAAI;QACtG,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,IAAI,MAAM,IAAI,gBAAgB;QAC9B,IAAI,MAAM;QACV,OAAQ;YACN,KAAK;gBACH,MAAM,MAAM,MAAM;gBAClB,IAAI,EAAE,QAAQ,EAAE;oBACd,MAAM,OAAO,MAAM;gBACrB;gBACA;YACF,KAAK;gBACH,MAAM,MAAM,MAAM;gBAClB;YACF,KAAK;gBACH,MAAM,MAAM,MAAM;gBAClB,IAAI,EAAE,QAAQ,EAAE;oBACd,MAAM,MAAM,MAAM;gBACpB;gBACA;YACF,KAAK;gBACH,MAAM,MAAM,MAAM;gBAClB;YACF,KAAK;gBACH,MAAM,MAAM,MAAM;gBAClB;QACJ;QACA,IAAI,UAAU,CAAC;QACf,IAAI,YAAY;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7185, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40uiw/react-textarea-code-editor/esm/styles.js"], "sourcesContent": ["export var container = {\n  position: 'relative',\n  textAlign: 'left',\n  boxSizing: 'border-box',\n  padding: 0,\n  overflow: 'hidden'\n};\nexport var textarea = {\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  height: '100%',\n  width: '100%',\n  resize: 'none',\n  color: 'inherit',\n  opacity: 0.8,\n  overflow: 'hidden',\n  MozOsxFontSmoothing: 'grayscale',\n  WebkitFontSmoothing: 'antialiased',\n  WebkitTextFillColor: 'transparent'\n};\nexport var editor = {\n  margin: 0,\n  border: 0,\n  background: 'none',\n  boxSizing: 'inherit',\n  display: 'inherit',\n  fontFamily: 'inherit',\n  fontSize: 'inherit',\n  fontStyle: 'inherit',\n  fontVariantLigatures: 'inherit',\n  fontWeight: 'inherit',\n  letterSpacing: 'inherit',\n  lineHeight: 'inherit',\n  tabSize: 'inherit',\n  textIndent: 'inherit',\n  textRendering: 'inherit',\n  textTransform: 'inherit',\n  whiteSpace: 'pre-wrap',\n  wordBreak: 'keep-all',\n  overflowWrap: 'break-word',\n  outline: 0\n};"], "names": [], "mappings": ";;;;;AAAO,IAAI,YAAY;IACrB,UAAU;IACV,WAAW;IACX,WAAW;IACX,SAAS;IACT,UAAU;AACZ;AACO,IAAI,WAAW;IACpB,UAAU;IACV,KAAK;IACL,MAAM;IACN,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,UAAU;IACV,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;AACvB;AACO,IAAI,SAAS;IAClB,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,SAAS;IACT,YAAY;IACZ,UAAU;IACV,WAAW;IACX,sBAAsB;IACtB,YAAY;IACZ,eAAe;IACf,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,eAAe;IACf,eAAe;IACf,YAAY;IACZ,WAAW;IACX,cAAc;IACd,SAAS;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7237, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40uiw/react-textarea-code-editor/esm/Editor.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"prefixCls\", \"value\", \"padding\", \"minHeight\", \"placeholder\", \"language\", \"data-color-mode\", \"className\", \"style\", \"rehypePlugins\", \"onChange\", \"indentWidth\"];\nimport React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport { processHtml, htmlEncode } from \"./utils.js\";\nimport shortcuts from \"./shortcuts.js\";\nimport * as styles from \"./styles.js\";\nimport \"./style/index.css\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport * from \"./SelectionText.js\";\nexport default /*#__PURE__*/React.forwardRef((props, ref) => {\n  var {\n      prefixCls = 'w-tc-editor',\n      padding = 10,\n      minHeight = 16,\n      placeholder,\n      language,\n      'data-color-mode': dataColorMode,\n      className,\n      style,\n      rehypePlugins,\n      onChange,\n      indentWidth = 2\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var [value, setValue] = useState(props.value || '');\n  useEffect(() => setValue(props.value || ''), [props.value]);\n  var textRef = useRef(null);\n  useImperativeHandle(ref, () => textRef.current, [textRef]);\n  var contentStyle = {\n    paddingTop: padding,\n    paddingRight: padding,\n    paddingBottom: padding,\n    paddingLeft: padding\n  };\n  var htmlStr = useMemo(() => processHtml(\"<pre aria-hidden=true><code \" + (language && value ? \"class=\\\"language-\" + language + \"\\\"\" : '') + \" >\" + htmlEncode(String(value || '')) + \"</code><br /></pre>\", rehypePlugins), [value, language, rehypePlugins]);\n  var preView = useMemo(() => /*#__PURE__*/_jsx(\"div\", {\n    style: _extends({}, styles.editor, contentStyle, {\n      minHeight\n    }),\n    className: prefixCls + \"-preview \" + (language ? \"language-\" + language : ''),\n    dangerouslySetInnerHTML: {\n      __html: htmlStr\n    }\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [prefixCls, language, htmlStr]);\n  var change = evn => {\n    setValue(evn.target.value);\n    onChange && onChange(evn);\n  };\n  var keyDown = evn => {\n    if (other.readOnly) return;\n    if (!other.onKeyDown || other.onKeyDown(evn) !== false) {\n      shortcuts(evn, indentWidth);\n    }\n  };\n  var textareaProps = _extends({\n    autoComplete: 'off',\n    autoCorrect: 'off',\n    spellCheck: 'false',\n    autoCapitalize: 'off'\n  }, other, {\n    placeholder,\n    onKeyDown: keyDown,\n    style: _extends({}, styles.editor, styles.textarea, contentStyle, {\n      minHeight\n    }, placeholder && !value ? {\n      WebkitTextFillColor: 'inherit'\n    } : {}),\n    onChange: change,\n    className: prefixCls + \"-text\",\n    value: value\n  });\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: _extends({}, styles.container, style),\n    className: prefixCls + \" \" + (className || ''),\n    \"data-color-mode\": dataColorMode,\n    children: [/*#__PURE__*/_jsx(\"textarea\", _extends({}, textareaProps, {\n      ref: textRef\n    })), preView]\n  });\n});"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;;;AAPA,IAAI,YAAY;IAAC;IAAa;IAAS;IAAW;IAAa;IAAe;IAAY;IAAmB;IAAa;IAAS;IAAiB;IAAY;CAAc;;;;;;;;uCAQ/J,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC,CAAC,OAAO;IACnD,IAAI,EACA,YAAY,aAAa,EACzB,UAAU,EAAE,EACZ,YAAY,EAAE,EACd,WAAW,EACX,QAAQ,EACR,mBAAmB,aAAa,EAChC,SAAS,EACT,KAAK,EACL,aAAa,EACb,QAAQ,EACR,cAAc,CAAC,EAChB,GAAG,OACJ,QAAQ,CAAA,GAAA,6KAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;IAC/C,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,KAAK,IAAI;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,IAAM,SAAS,MAAM,KAAK,IAAI,KAAK;QAAC,MAAM,KAAK;KAAC;IAC1D,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACrB,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,QAAQ,OAAO,EAAE;QAAC;KAAQ;IACzD,IAAI,eAAe;QACjB,YAAY;QACZ,cAAc;QACd,eAAe;QACf,aAAa;IACf;IACA,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAE,iCAAiC,CAAC,YAAY,QAAQ,sBAAsB,WAAW,OAAO,EAAE,IAAI,OAAO,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,OAAO,uBAAuB,gBAAgB;QAAC;QAAO;QAAU;KAAc;IAC5P,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YACnD,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,6KAAA,CAAA,SAAa,EAAE,cAAc;gBAC/C;YACF;YACA,WAAW,YAAY,cAAc,CAAC,WAAW,cAAc,WAAW,EAAE;YAC5E,yBAAyB;gBACvB,QAAQ;YACV;QACF,IACA,uDAAuD;IACvD;QAAC;QAAW;QAAU;KAAQ;IAC9B,IAAI,SAAS,CAAA;QACX,SAAS,IAAI,MAAM,CAAC,KAAK;QACzB,YAAY,SAAS;IACvB;IACA,IAAI,UAAU,CAAA;QACZ,IAAI,MAAM,QAAQ,EAAE;QACpB,IAAI,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,SAAS,OAAO;YACtD,CAAA,GAAA,gLAAA,CAAA,UAAS,AAAD,EAAE,KAAK;QACjB;IACF;IACA,IAAI,gBAAgB,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;QAC3B,cAAc;QACd,aAAa;QACb,YAAY;QACZ,gBAAgB;IAClB,GAAG,OAAO;QACR;QACA,WAAW;QACX,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,6KAAA,CAAA,SAAa,EAAE,6KAAA,CAAA,WAAe,EAAE,cAAc;YAChE;QACF,GAAG,eAAe,CAAC,QAAQ;YACzB,qBAAqB;QACvB,IAAI,CAAC;QACL,UAAU;QACV,WAAW,YAAY;QACvB,OAAO;IACT;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC/B,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,6KAAA,CAAA,YAAgB,EAAE;QACtC,WAAW,YAAY,MAAM,CAAC,aAAa,EAAE;QAC7C,mBAAmB;QACnB,UAAU;YAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,eAAe;gBACnE,KAAK;YACP;YAAK;SAAQ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/%40uiw/react-textarea-code-editor/esm/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"rehypePlugins\"];\nimport React from 'react';\nimport Editor from \"./Editor.js\";\nimport rehypePrism from 'rehype-prism-plus';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport * from \"./Editor.js\";\nexport default /*#__PURE__*/React.forwardRef((props, ref) => {\n  var {\n      rehypePlugins = [[rehypePrism, {\n        ignoreMissing: true\n      }]]\n    } = props,\n    reset = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(Editor, _extends({}, reset, {\n    rehypePlugins: rehypePlugins,\n    ref: ref\n  }));\n});"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AAAA;AACA;AACA;;;AAJA,IAAI,YAAY;IAAC;CAAgB;;;;;;uCAMlB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC,CAAC,OAAO;IACnD,IAAI,EACA,gBAAgB;QAAC;YAAC,8JAAA,CAAA,UAAW;YAAE;gBAC7B,eAAe;YACjB;SAAE;KAAC,EACJ,GAAG,OACJ,QAAQ,CAAA,GAAA,6KAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;IAC/C,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,6LAAA,CAAA,UAAM,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnD,eAAe;QACf,KAAK;IACP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7413, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;CAIC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC,GAED;;;;;;;;;;;;;;;CAeC;;;;AACM,MAAM,KAaT;;;;;;;KAOC,GACD,sCAAsC;AACtC,SAAU,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO;IAC1C,MAAM,QAAQ,QAAQ;IAEtB,IACE,UAAU,aACV,UAAU,QACV,CAAC,OAAO,UAAU,YAChB,QAAQ,KACR,UAAU,OAAO,iBAAiB,GACpC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,WAAW,aACX,WAAW,QACX,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,QAAQ,GAChC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,IACE,CAAC,WAAW,aAAa,WAAW,IAAI,MACxC,CAAC,UAAU,aAAa,UAAU,IAAI,GACtC;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,eAAe,QAClB,MAAM,IAAI,CAAC,SAAS,MAAM,OAAO,UACjC;AACN;AAqBG,MAAM,UAYT;;;KAGC,GACD,SAAU,IAAI;IACZ,IAAI,SAAS,QAAQ,SAAS,WAAW;QACvC,OAAO;IACT;IAEA,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO,YAAY;IACrB;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,MAAM,OAAO,CAAC,QAAQ,WAAW,QAAQ,aAAa;IAC/D;IAEA,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,YAAY;IACrB;IAEA,MAAM,IAAI,MAAM;AAClB;AAGJ;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,yBAAyB,GACzB,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,CAAC;IAEb,MAAO,EAAE,QAAQ,MAAM,MAAM,CAAE;QAC7B,MAAM,CAAC,MAAM,GAAG,QAAQ,KAAK,CAAC,MAAM;IACtC;IAEA,OAAO,YAAY;;;IAEnB;;;GAGC,GACD,SAAS,IAAI,GAAG,UAAU;QACxB,IAAI,QAAQ,CAAC;QAEb,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,OAAO;QACpD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,aAAa,KAAK;IACzB,MAAM,gBAAwD;IAE9D,OAAO,YAAY;;;IAEnB;;;GAGC,GACD,SAAS,IAAI,IAAI;QACf,MAAM,eACoB;QAG1B,mBAAmB,GACnB,IAAI;QAEJ,IAAK,OAAO,MAAO;YACjB,IAAI,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,OAAO;QACvD;QAEA,OAAO;IACT;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,KAAK;IACxB,OAAO,YAAY;;;IAEnB;;GAEC,GACD,SAAS,KAAK,IAAI;QAChB,OAAO,QAAQ,KAAK,IAAI,KAAK;IAC/B;AACF;AAEA;;;;;CAKC,GACD,SAAS,YAAY,YAAY;IAC/B,OAAO;;;IAEP;;;GAGC,GACD,SAAS,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM;QACjC,OAAO,QACL,eAAe,UACb,aAAa,IAAI,CACf,IAAI,EACJ,OACA,OAAO,UAAU,WAAW,QAAQ,WACpC,UAAU;IAGlB;AACF;AAEA,SAAS;IACP,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,eAAe,KAAK;IAC3B,OAAO,UAAU,QAAQ,OAAO,UAAU,YAAY,UAAU;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7603, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unist-util-visit-parents/lib/color.node.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return '\\u001B[33m' + d + '\\u001B[39m'\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,MAAM,CAAC;IACrB,OAAO,eAAe,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7616, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unist-util-visit-parents/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {InternalAncestor<InclusiveDescendant<Tree>, Child>} Ancestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {'skip' | boolean} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<VisitedParents>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [VisitedParents=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Matches<InclusiveDescendant<Tree>, Check>, Ancestor<Tree, Matches<InclusiveDescendant<Tree>, Check>>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Tree type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from 'unist-util-visit-parents/do-not-use-color'\n\n/** @type {Readonly<ActionTuple>} */\nconst empty = []\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} test\n *   `unist-util-is`-compatible test\n * @param {Visitor | boolean | null | undefined} [visitor]\n *   Handle each node.\n * @param {boolean | null | undefined} [reverse]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visitParents(tree, test, visitor, reverse) {\n  /** @type {Test} */\n  let check\n\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test\n  } else {\n    // @ts-expect-error visitor given, so `test` isn’t a visitor.\n    check = test\n  }\n\n  const is = convert(check)\n  const step = reverse ? -1 : 1\n\n  factory(tree, undefined, [])()\n\n  /**\n   * @param {UnistNode} node\n   * @param {number | undefined} index\n   * @param {Array<UnistParent>} parents\n   */\n  function factory(node, index, parents) {\n    const value = /** @type {Record<string, unknown>} */ (\n      node && typeof node === 'object' ? node : {}\n    )\n\n    if (typeof value.type === 'string') {\n      const name =\n        // `hast`\n        typeof value.tagName === 'string'\n          ? value.tagName\n          : // `xast`\n          typeof value.name === 'string'\n          ? value.name\n          : undefined\n\n      Object.defineProperty(visit, 'name', {\n        value:\n          'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n      })\n    }\n\n    return visit\n\n    function visit() {\n      /** @type {Readonly<ActionTuple>} */\n      let result = empty\n      /** @type {Readonly<ActionTuple>} */\n      let subresult\n      /** @type {number} */\n      let offset\n      /** @type {Array<UnistParent>} */\n      let grandparents\n\n      if (!test || is(node, index, parents[parents.length - 1] || undefined)) {\n        // @ts-expect-error: `visitor` is now a visitor.\n        result = toResult(visitor(node, parents))\n\n        if (result[0] === EXIT) {\n          return result\n        }\n      }\n\n      if ('children' in node && node.children) {\n        const nodeAsParent = /** @type {UnistParent} */ (node)\n\n        if (nodeAsParent.children && result[0] !== SKIP) {\n          offset = (reverse ? nodeAsParent.children.length : -1) + step\n          grandparents = parents.concat(nodeAsParent)\n\n          while (offset > -1 && offset < nodeAsParent.children.length) {\n            const child = nodeAsParent.children[offset]\n\n            subresult = factory(child, offset, grandparents)()\n\n            if (subresult[0] === EXIT) {\n              return subresult\n            }\n\n            offset =\n              typeof subresult[1] === 'number' ? subresult[1] : offset + step\n          }\n        }\n      }\n\n      return result\n    }\n  }\n}\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {Readonly<ActionTuple>}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return value === null || value === undefined ? empty : [value]\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;CAkBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCC,GAED;;;;;;;;;CASC;;;;;;AAED;AACA;;;AAEA,kCAAkC,GAClC,MAAM,QAAQ,EAAE;AAKT,MAAM,WAAW;AAKjB,MAAM,OAAO;AAKb,MAAM,OAAO;AAiDb,SAAS,aAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;IACvD,iBAAiB,GACjB,IAAI;IAEJ,IAAI,OAAO,SAAS,cAAc,OAAO,YAAY,YAAY;QAC/D,UAAU;QACV,2DAA2D;QAC3D,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,QAAQ;IACV;IAEA,MAAM,KAAK,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE;IACnB,MAAM,OAAO,UAAU,CAAC,IAAI;IAE5B,QAAQ,MAAM,WAAW,EAAE;IAE3B;;;;GAIC,GACD,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,OAAO;QACnC,MAAM,QACJ,QAAQ,OAAO,SAAS,WAAW,OAAO,CAAC;QAG7C,IAAI,OAAO,MAAM,IAAI,KAAK,UAAU;YAClC,MAAM,OACJ,SAAS;YACT,OAAO,MAAM,OAAO,KAAK,WACrB,MAAM,OAAO,GAEf,OAAO,MAAM,IAAI,KAAK,WACpB,MAAM,IAAI,GACV;YAEN,OAAO,cAAc,CAAC,OAAO,QAAQ;gBACnC,OACE,WAAW,CAAA,GAAA,yKAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,GAAG,CAAC,OAAO,MAAM,OAAO,MAAM,EAAE,KAAK;YACnE;QACF;QAEA,OAAO;;;QAEP,SAAS;YACP,kCAAkC,GAClC,IAAI,SAAS;YACb,kCAAkC,GAClC,IAAI;YACJ,mBAAmB,GACnB,IAAI;YACJ,+BAA+B,GAC/B,IAAI;YAEJ,IAAI,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,IAAI,YAAY;gBACtE,gDAAgD;gBAChD,SAAS,SAAS,QAAQ,MAAM;gBAEhC,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBACtB,OAAO;gBACT;YACF;YAEA,IAAI,cAAc,QAAQ,KAAK,QAAQ,EAAE;gBACvC,MAAM,eAA2C;gBAEjD,IAAI,aAAa,QAAQ,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM;oBAC/C,SAAS,CAAC,UAAU,aAAa,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI;oBACzD,eAAe,QAAQ,MAAM,CAAC;oBAE9B,MAAO,SAAS,CAAC,KAAK,SAAS,aAAa,QAAQ,CAAC,MAAM,CAAE;wBAC3D,MAAM,QAAQ,aAAa,QAAQ,CAAC,OAAO;wBAE3C,YAAY,QAAQ,OAAO,QAAQ;wBAEnC,IAAI,SAAS,CAAC,EAAE,KAAK,MAAM;4BACzB,OAAO;wBACT;wBAEA,SACE,OAAO,SAAS,CAAC,EAAE,KAAK,WAAW,SAAS,CAAC,EAAE,GAAG,SAAS;oBAC/D;gBACF;YACF;YAEA,OAAO;QACT;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,SAAS,KAAK;IACrB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;YAAC;YAAU;SAAM;IAC1B;IAEA,OAAO,UAAU,QAAQ,UAAU,YAAY,QAAQ;QAAC;KAAM;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7904, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unist-util-visit/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist-util-visit-parents').VisitorResult} VisitorResult\n */\n\n/**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n// To do: use types from `unist-util-visit-parents` when it’s released.\n\n/**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */\n\n/**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */\n\n/**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */\n\n/**\n * @typedef {0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10} Uint\n *   Number; capped reasonably.\n */\n\n/**\n * @typedef {I extends 0 ? 1 : I extends 1 ? 2 : I extends 2 ? 3 : I extends 3 ? 4 : I extends 4 ? 5 : I extends 5 ? 6 : I extends 6 ? 7 : I extends 7 ? 8 : I extends 8 ? 9 : 10} Increment\n *   Increment a number in the type system.\n * @template {Uint} [I=0]\n *   Index.\n */\n\n/**\n * @typedef {(\n *   Node extends UnistParent\n *   ? Node extends {children: Array<infer Children>}\n *     ? Child extends Children ? Node : never\n *     : never\n *   : never\n * )} InternalParent\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {InternalParent<InclusiveDescendant<Tree>, Child>} Parent\n *   Collect nodes in `Tree` that can be parents of `Child`.\n * @template {UnistNode} Tree\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n */\n\n/**\n * @typedef {(\n *   Depth extends Max\n *   ? never\n *   :\n *     | InternalParent<Node, Child>\n *     | InternalAncestor<Node, InternalParent<Node, Child>, Max, Increment<Depth>>\n * )} InternalAncestor\n *   Collect nodes in `Tree` that can be ancestors of `Child`.\n * @template {UnistNode} Node\n *   All node types in a tree.\n * @template {UnistNode} Child\n *   Node to search for.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @typedef {(\n *   Tree extends UnistParent\n *     ? Depth extends Max\n *       ? Tree\n *       : Tree | InclusiveDescendant<Tree['children'][number], Max, Increment<Depth>>\n *     : Tree\n * )} InclusiveDescendant\n *   Collect all (inclusive) descendants of `Tree`.\n *\n *   > 👉 **Note**: for performance reasons, this seems to be the fastest way to\n *   > recurse without actually running into an infinite loop, which the\n *   > previous version did.\n *   >\n *   > Practically, a max of `2` is typically enough assuming a `Root` is\n *   > passed, but it doesn’t improve performance.\n *   > It gets higher with `List > ListItem > Table > TableRow > TableCell`.\n *   > Using up to `10` doesn’t hurt or help either.\n * @template {UnistNode} Tree\n *   Tree type.\n * @template {Uint} [Max=10]\n *   Max; searches up to this depth.\n * @template {Uint} [Depth=0]\n *   Current depth.\n */\n\n/**\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform `parent`.\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of `parent` still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Visited extends UnistNode ? number | undefined : never} index\n *   Index of `node` in `parent`.\n * @param {Ancestor extends UnistParent ? Ancestor | undefined : never} parent\n *   Parent of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n * @template {UnistNode} [Visited=UnistNode]\n *   Visited node type.\n * @template {UnistParent} [Ancestor=UnistParent]\n *   Ancestor type.\n */\n\n/**\n * @typedef {Visitor<Visited, Parent<Ancestor, Visited>>} BuildVisitorFromMatch\n *   Build a typed `Visitor` function from a node and all possible parents.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Visited\n *   Node type.\n * @template {UnistParent} Ancestor\n *   Parent type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromMatch<\n *     Matches<Descendant, Check>,\n *     Extract<Descendant, UnistParent>\n *   >\n * )} BuildVisitorFromDescendants\n *   Build a typed `Visitor` function from a list of descendants and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} Descendant\n *   Node type.\n * @template {Test} Check\n *   Test type.\n */\n\n/**\n * @typedef {(\n *   BuildVisitorFromDescendants<\n *     InclusiveDescendant<Tree>,\n *     Check\n *   >\n * )} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parent`.\n * @template {UnistNode} [Tree=UnistNode]\n *   Node type.\n * @template {Test} [Check=Test]\n *   Test type.\n */\n\nimport {visitParents} from 'unist-util-visit-parents'\n\nexport {CONTINUE, EXIT, SKIP} from 'unist-util-visit-parents'\n\n/**\n * Visit nodes.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} check\n * @param {BuildVisitor<Tree, Check>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @overload\n * @param {Tree} tree\n * @param {BuildVisitor<Tree>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {undefined}\n *\n * @param {UnistNode} tree\n *   Tree to traverse.\n * @param {Visitor | Test} testOrVisitor\n *   `unist-util-is`-compatible test (optional, omit to pass a visitor).\n * @param {Visitor | boolean | null | undefined} [visitorOrReverse]\n *   Handle each node (when test is omitted, pass `reverse`).\n * @param {boolean | null | undefined} [maybeReverse=false]\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns {undefined}\n *   Nothing.\n *\n * @template {UnistNode} Tree\n *   Node type.\n * @template {Test} Check\n *   `unist-util-is`-compatible test.\n */\nexport function visit(tree, testOrVisitor, visitorOrReverse, maybeReverse) {\n  /** @type {boolean | null | undefined} */\n  let reverse\n  /** @type {Test} */\n  let test\n  /** @type {Visitor} */\n  let visitor\n\n  if (\n    typeof testOrVisitor === 'function' &&\n    typeof visitorOrReverse !== 'function'\n  ) {\n    test = undefined\n    visitor = testOrVisitor\n    reverse = visitorOrReverse\n  } else {\n    // @ts-expect-error: assume the overload with test was given.\n    test = testOrVisitor\n    // @ts-expect-error: assume the overload with test was given.\n    visitor = visitorOrReverse\n    reverse = maybeReverse\n  }\n\n  visitParents(tree, test, overload, reverse)\n\n  /**\n   * @param {UnistNode} node\n   * @param {Array<UnistParent>} parents\n   */\n  function overload(node, parents) {\n    const parent = parents[parents.length - 1]\n    const index = parent ? parent.children.indexOf(node) : undefined\n    return visitor(node, index, parent)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;CAOC,GAED,uEAAuE;AAEvE;;;;;;;;;;;CAWC,GAED;;;;;;;;;;;;;;;;;;;CAmBC,GAED;;;;;;;;;;;CAWC,GAED;;;CAGC,GAED;;;;;CAKC,GAED;;;;;;;;;;;;;CAaC,GAED;;;;;;;CAOC,GAED;;;;;;;;;;;;;;;;;CAiBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;CAwBC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqCC,GAED;;;;;;;;;CASC,GAED;;;;;;;;;;;;;;CAcC,GAED;;;;;;;;;;;;;;CAcC;;;AAED;;;AAmDO,SAAS,MAAM,IAAI,EAAE,aAAa,EAAE,gBAAgB,EAAE,YAAY;IACvE,uCAAuC,GACvC,IAAI;IACJ,iBAAiB,GACjB,IAAI;IACJ,oBAAoB,GACpB,IAAI;IAEJ,IACE,OAAO,kBAAkB,cACzB,OAAO,qBAAqB,YAC5B;QACA,OAAO;QACP,UAAU;QACV,UAAU;IACZ,OAAO;QACL,6DAA6D;QAC7D,OAAO;QACP,6DAA6D;QAC7D,UAAU;QACV,UAAU;IACZ;IAEA,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM,UAAU;IAEnC;;;GAGC,GACD,SAAS,SAAS,IAAI,EAAE,OAAO;QAC7B,MAAM,SAAS,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;QAC1C,MAAM,QAAQ,SAAS,OAAO,QAAQ,CAAC,OAAO,CAAC,QAAQ;QACvD,OAAO,QAAQ,MAAM,OAAO;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/hast-util-to-string/lib/index.js"], "sourcesContent": ["/**\n * @import {Nodes, Parents} from 'hast'\n */\n\n/**\n * Get the plain-text value of a hast node.\n *\n * @param {Nodes} node\n *   Node to serialize.\n * @returns {string}\n *   Serialized node.\n */\nexport function toString(node) {\n  // “The concatenation of data of all the Text node descendants of the context\n  // object, in tree order.”\n  if ('children' in node) {\n    return all(node)\n  }\n\n  // “Context object’s data.”\n  return 'value' in node ? node.value : ''\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node) {\n  if (node.type === 'text') {\n    return node.value\n  }\n\n  return 'children' in node ? all(node) : ''\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {string}\n *   Serialized node.\n */\nfunction all(node) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n\n  while (++index < node.children.length) {\n    result[index] = one(node.children[index])\n  }\n\n  return result.join('')\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;;;;CAOC;;;AACM,SAAS,SAAS,IAAI;IAC3B,6EAA6E;IAC7E,0BAA0B;IAC1B,IAAI,cAAc,MAAM;QACtB,OAAO,IAAI;IACb;IAEA,2BAA2B;IAC3B,OAAO,WAAW,OAAO,KAAK,KAAK,GAAG;AACxC;AAEA;;;;;CAKC,GACD,SAAS,IAAI,IAAI;IACf,IAAI,KAAK,IAAI,KAAK,QAAQ;QACxB,OAAO,KAAK,KAAK;IACnB;IAEA,OAAO,cAAc,OAAO,IAAI,QAAQ;AAC1C;AAEA;;;;;CAKC,GACD,SAAS,IAAI,IAAI;IACf,IAAI,QAAQ,CAAC;IACb,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IAEjB,MAAO,EAAE,QAAQ,KAAK,QAAQ,CAAC,MAAM,CAAE;QACrC,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM;IAC1C;IAEA,OAAO,OAAO,IAAI,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8184, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/unist-util-filter/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n *\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [cascade=true]\n *   Whether to drop parent nodes if they had children, but all their children\n *   were filtered out (default: `true`).\n */\n\nimport {convert} from 'unist-util-is'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a new `tree` of copies of all nodes that pass `test`.\n *\n * The tree is walked in *preorder* (NLR), visiting the node itself, then its\n * head, etc.\n *\n * @template {Node} Tree\n * @template {Test} Check\n *\n * @overload\n * @param {Tree} tree\n * @param {Options | null | undefined} options\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {null | undefined} [options]\n * @returns {Tree}\n *\n * @param {Node} tree\n *   Tree to filter.\n * @param {Options | Test} [options]\n *   Configuration (optional).\n * @param {Test} [test]\n *   `unist-util-is` compatible test.\n * @returns {Node | undefined}\n *   New filtered tree.\n *\n *   `undefined` is returned if `tree` itself didn’t pass the test, or is\n *   cascaded away.\n */\nexport function filter(tree, options, test) {\n  const is = convert(test || options)\n  const cascadeRaw =\n    options && typeof options === 'object' && 'cascade' in options\n      ? /** @type {boolean | null | undefined} */ (options.cascade)\n      : undefined\n  const cascade =\n    cascadeRaw === undefined || cascadeRaw === null ? true : cascadeRaw\n\n  return preorder(tree)\n\n  /**\n   * @param {Node} node\n   *   Current node.\n   * @param {number | undefined} [index]\n   *   Index of `node` in `parent`.\n   * @param {Parent | undefined} [parentNode]\n   *   Parent node.\n   * @returns {Node | undefined}\n   *   Shallow copy of `node`.\n   */\n  function preorder(node, index, parentNode) {\n    /** @type {Array<Node>} */\n    const children = []\n\n    if (!is(node, index, parentNode)) return undefined\n\n    if (parent(node)) {\n      let childIndex = -1\n\n      while (++childIndex < node.children.length) {\n        const result = preorder(node.children[childIndex], childIndex, node)\n\n        if (result) {\n          children.push(result)\n        }\n      }\n\n      if (cascade && node.children.length > 0 && children.length === 0) {\n        return undefined\n      }\n    }\n\n    // Create a shallow clone, using the new children.\n    /** @type {typeof node} */\n    // @ts-expect-error all the fields will be copied over.\n    const next = {}\n    /** @type {string} */\n    let key\n\n    for (key in node) {\n      if (own.call(node, key)) {\n        // @ts-expect-error: Looks like a record.\n        next[key] = key === 'children' ? children : node[key]\n      }\n    }\n\n    return next\n  }\n}\n\n/**\n * @param {Node} node\n * @returns {node is Parent}\n */\nfunction parent(node) {\n  return 'children' in node && node.children !== undefined\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED;;;;;;CAMC;;;AAED;;AAEA,MAAM,MAAM,CAAC,EAAE,cAAc;AAuCtB,SAAS,OAAO,IAAI,EAAE,OAAO,EAAE,IAAI;IACxC,MAAM,KAAK,CAAA,GAAA,mJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAC3B,MAAM,aACJ,WAAW,OAAO,YAAY,YAAY,aAAa,UACR,QAAQ,OAAO,GAC1D;IACN,MAAM,UACJ,eAAe,aAAa,eAAe,OAAO,OAAO;IAE3D,OAAO,SAAS;;;IAEhB;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,UAAU;QACvC,wBAAwB,GACxB,MAAM,WAAW,EAAE;QAEnB,IAAI,CAAC,GAAG,MAAM,OAAO,aAAa,OAAO;QAEzC,IAAI,OAAO,OAAO;YAChB,IAAI,aAAa,CAAC;YAElB,MAAO,EAAE,aAAa,KAAK,QAAQ,CAAC,MAAM,CAAE;gBAC1C,MAAM,SAAS,SAAS,KAAK,QAAQ,CAAC,WAAW,EAAE,YAAY;gBAE/D,IAAI,QAAQ;oBACV,SAAS,IAAI,CAAC;gBAChB;YACF;YAEA,IAAI,WAAW,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,SAAS,MAAM,KAAK,GAAG;gBAChE,OAAO;YACT;QACF;QAEA,kDAAkD;QAClD,wBAAwB,GACxB,uDAAuD;QACvD,MAAM,OAAO,CAAC;QACd,mBAAmB,GACnB,IAAI;QAEJ,IAAK,OAAO,KAAM;YAChB,IAAI,IAAI,IAAI,CAAC,MAAM,MAAM;gBACvB,yCAAyC;gBACzC,IAAI,CAAC,IAAI,GAAG,QAAQ,aAAa,WAAW,IAAI,CAAC,IAAI;YACvD;QACF;QAEA,OAAO;IACT;AACF;AAEA;;;CAGC,GACD,SAAS,OAAO,IAAI;IAClB,OAAO,cAAc,QAAQ,KAAK,QAAQ,KAAK;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/parse-numeric-range/index.js"], "sourcesContent": ["/**\n * @param {string} string    The string to parse\n * @returns {Array<number>}  Returns an energetic array.\n */\nfunction parsePart(string) {\n  let res = [];\n  let m;\n\n  for (let str of string.split(\",\").map((str) => str.trim())) {\n    // just a number\n    if (/^-?\\d+$/.test(str)) {\n      res.push(parseInt(str, 10));\n    } else if (\n      (m = str.match(/^(-?\\d+)(-|\\.\\.\\.?|\\u2025|\\u2026|\\u22EF)(-?\\d+)$/))\n    ) {\n      // 1-5 or 1..5 (equivalent) or 1...5 (doesn't include 5)\n      let [_, lhs, sep, rhs] = m;\n\n      if (lhs && rhs) {\n        lhs = parseInt(lhs);\n        rhs = parseInt(rhs);\n        const incr = lhs < rhs ? 1 : -1;\n\n        // Make it inclusive by moving the right 'stop-point' away by one.\n        if (sep === \"-\" || sep === \"..\" || sep === \"\\u2025\") rhs += incr;\n\n        for (let i = lhs; i !== rhs; i += incr) res.push(i);\n      }\n    }\n  }\n\n  return res;\n}\n\nexports.default = parsePart;\nmodule.exports = parsePart;\n"], "names": [], "mappings": "AAAA;;;CAGC,GACD,SAAS,UAAU,MAAM;IACvB,IAAI,MAAM,EAAE;IACZ,IAAI;IAEJ,KAAK,IAAI,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,IAAK;QAC1D,gBAAgB;QAChB,IAAI,UAAU,IAAI,CAAC,MAAM;YACvB,IAAI,IAAI,CAAC,SAAS,KAAK;QACzB,OAAO,IACJ,IAAI,IAAI,KAAK,CAAC,qDACf;YACA,wDAAwD;YACxD,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;YAEzB,IAAI,OAAO,KAAK;gBACd,MAAM,SAAS;gBACf,MAAM,SAAS;gBACf,MAAM,OAAO,MAAM,MAAM,IAAI,CAAC;gBAE9B,kEAAkE;gBAClE,IAAI,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,UAAU,OAAO;gBAE5D,IAAK,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,KAAM,IAAI,IAAI,CAAC;YACnD;QACF;IACF;IAEA,OAAO;AACT;AAEA,QAAQ,OAAO,GAAG;AAClB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8292, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/character-reference-invalid/index.js"], "sourcesContent": ["/**\n * Map of invalid numeric character references to their replacements, according to HTML.\n *\n * @type {Record<number, string>}\n */\nexport const characterReferenceInvalid = {\n  0: '�',\n  128: '€',\n  130: '‚',\n  131: 'ƒ',\n  132: '„',\n  133: '…',\n  134: '†',\n  135: '‡',\n  136: 'ˆ',\n  137: '‰',\n  138: 'Š',\n  139: '‹',\n  140: 'Œ',\n  142: 'Ž',\n  145: '‘',\n  146: '’',\n  147: '“',\n  148: '”',\n  149: '•',\n  150: '–',\n  151: '—',\n  152: '˜',\n  153: '™',\n  154: 'š',\n  155: '›',\n  156: 'œ',\n  158: 'ž',\n  159: 'Ÿ'\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,4BAA4B;IACvC,GAAG;IACH,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8333, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/is-decimal/index.js"], "sourcesContent": ["/**\n * Check if the given character code, or the character code at the first\n * character, is decimal.\n *\n * @param {string|number} character\n * @returns {boolean} Whether `character` is a decimal\n */\nexport function isDecimal(character) {\n  const code =\n    typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return code >= 48 && code <= 57 /* 0-9 */\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACM,SAAS,UAAU,SAAS;IACjC,MAAM,OACJ,OAAO,cAAc,WAAW,UAAU,UAAU,CAAC,KAAK;IAE5D,OAAO,QAAQ,MAAM,QAAQ,GAAG,OAAO;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8350, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/is-hexadecimal/index.js"], "sourcesContent": ["/**\n * Check if the given character code, or the character code at the first\n * character, is hexadecimal.\n *\n * @param {string|number} character\n * @returns {boolean} Whether `character` is hexadecimal\n */\nexport function isHexadecimal(character) {\n  const code =\n    typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return (\n    (code >= 97 /* a */ && code <= 102) /* z */ ||\n    (code >= 65 /* A */ && code <= 70) /* Z */ ||\n    (code >= 48 /* A */ && code <= 57) /* Z */\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACM,SAAS,cAAc,SAAS;IACrC,MAAM,OACJ,OAAO,cAAc,WAAW,UAAU,UAAU,CAAC,KAAK;IAE5D,OACE,AAAC,QAAQ,GAAG,KAAK,OAAM,QAAQ,OAC9B,QAAQ,GAAG,KAAK,OAAM,QAAQ,MAC9B,QAAQ,GAAG,KAAK,OAAM,QAAQ;AAEnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8367, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/is-alphabetical/index.js"], "sourcesContent": ["/**\n * Check if the given character code, or the character code at the first\n * character, is alphabetical.\n *\n * @param {string|number} character\n * @returns {boolean} Whether `character` is alphabetical.\n */\nexport function isAlphabetical(character) {\n  const code =\n    typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return (\n    (code >= 97 && code <= 122) /* a-z */ ||\n    (code >= 65 && code <= 90) /* A-Z */\n  )\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACM,SAAS,eAAe,SAAS;IACtC,MAAM,OACJ,OAAO,cAAc,WAAW,UAAU,UAAU,CAAC,KAAK;IAE5D,OACE,AAAC,QAAQ,MAAM,QAAQ,OACtB,QAAQ,MAAM,QAAQ;AAE3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8384, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/is-alphanumerical/index.js"], "sourcesContent": ["import {isAlphabetical} from 'is-alphabetical'\nimport {isDecimal} from 'is-decimal'\n\n/**\n * Check if the given character code, or the character code at the first\n * character, is alphanumerical.\n *\n * @param {string|number} character\n * @returns {boolean} Whether `character` is alphanumerical.\n */\nexport function isAlphanumerical(character) {\n  return isAlphabetical(character) || isDecimal(character)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,SAAS,iBAAiB,SAAS;IACxC,OAAO,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,CAAA,GAAA,sIAAA,CAAA,YAAS,AAAD,EAAE;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8398, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/character-entities/index.js"], "sourcesContent": ["/**\n * Map of named character references.\n *\n * @type {Record<string, string>}\n */\nexport const characterEntities = {\n  AElig: 'Æ',\n  AMP: '&',\n  Aacute: 'Á',\n  Abreve: 'Ă',\n  Acirc: 'Â',\n  Acy: 'А',\n  Afr: '𝔄',\n  Agrave: 'À',\n  Alpha: 'Α',\n  Amacr: 'Ā',\n  And: '⩓',\n  Aogon: 'Ą',\n  Aopf: '𝔸',\n  ApplyFunction: '⁡',\n  Aring: 'Å',\n  Ascr: '𝒜',\n  Assign: '≔',\n  Atilde: 'Ã',\n  Auml: 'Ä',\n  Backslash: '∖',\n  Barv: '⫧',\n  Barwed: '⌆',\n  Bcy: 'Б',\n  Because: '∵',\n  Bernoullis: 'ℬ',\n  Beta: 'Β',\n  Bfr: '𝔅',\n  Bopf: '𝔹',\n  Breve: '˘',\n  Bscr: 'ℬ',\n  Bumpeq: '≎',\n  CHcy: 'Ч',\n  COPY: '©',\n  Cacute: 'Ć',\n  Cap: '⋒',\n  CapitalDifferentialD: 'ⅅ',\n  Cayleys: 'ℭ',\n  Ccaron: 'Č',\n  Ccedil: 'Ç',\n  Ccirc: 'Ĉ',\n  Cconint: '∰',\n  Cdot: 'Ċ',\n  Cedilla: '¸',\n  CenterDot: '·',\n  Cfr: 'ℭ',\n  Chi: 'Χ',\n  CircleDot: '⊙',\n  CircleMinus: '⊖',\n  CirclePlus: '⊕',\n  CircleTimes: '⊗',\n  ClockwiseContourIntegral: '∲',\n  CloseCurlyDoubleQuote: '”',\n  CloseCurlyQuote: '’',\n  Colon: '∷',\n  Colone: '⩴',\n  Congruent: '≡',\n  Conint: '∯',\n  ContourIntegral: '∮',\n  Copf: 'ℂ',\n  Coproduct: '∐',\n  CounterClockwiseContourIntegral: '∳',\n  Cross: '⨯',\n  Cscr: '𝒞',\n  Cup: '⋓',\n  CupCap: '≍',\n  DD: 'ⅅ',\n  DDotrahd: '⤑',\n  DJcy: 'Ђ',\n  DScy: 'Ѕ',\n  DZcy: 'Џ',\n  Dagger: '‡',\n  Darr: '↡',\n  Dashv: '⫤',\n  Dcaron: 'Ď',\n  Dcy: 'Д',\n  Del: '∇',\n  Delta: 'Δ',\n  Dfr: '𝔇',\n  DiacriticalAcute: '´',\n  DiacriticalDot: '˙',\n  DiacriticalDoubleAcute: '˝',\n  DiacriticalGrave: '`',\n  DiacriticalTilde: '˜',\n  Diamond: '⋄',\n  DifferentialD: 'ⅆ',\n  Dopf: '𝔻',\n  Dot: '¨',\n  DotDot: '⃜',\n  DotEqual: '≐',\n  DoubleContourIntegral: '∯',\n  DoubleDot: '¨',\n  DoubleDownArrow: '⇓',\n  DoubleLeftArrow: '⇐',\n  DoubleLeftRightArrow: '⇔',\n  DoubleLeftTee: '⫤',\n  DoubleLongLeftArrow: '⟸',\n  DoubleLongLeftRightArrow: '⟺',\n  DoubleLongRightArrow: '⟹',\n  DoubleRightArrow: '⇒',\n  DoubleRightTee: '⊨',\n  DoubleUpArrow: '⇑',\n  DoubleUpDownArrow: '⇕',\n  DoubleVerticalBar: '∥',\n  DownArrow: '↓',\n  DownArrowBar: '⤓',\n  DownArrowUpArrow: '⇵',\n  DownBreve: '̑',\n  DownLeftRightVector: '⥐',\n  DownLeftTeeVector: '⥞',\n  DownLeftVector: '↽',\n  DownLeftVectorBar: '⥖',\n  DownRightTeeVector: '⥟',\n  DownRightVector: '⇁',\n  DownRightVectorBar: '⥗',\n  DownTee: '⊤',\n  DownTeeArrow: '↧',\n  Downarrow: '⇓',\n  Dscr: '𝒟',\n  Dstrok: 'Đ',\n  ENG: 'Ŋ',\n  ETH: 'Ð',\n  Eacute: 'É',\n  Ecaron: 'Ě',\n  Ecirc: 'Ê',\n  Ecy: 'Э',\n  Edot: 'Ė',\n  Efr: '𝔈',\n  Egrave: 'È',\n  Element: '∈',\n  Emacr: 'Ē',\n  EmptySmallSquare: '◻',\n  EmptyVerySmallSquare: '▫',\n  Eogon: 'Ę',\n  Eopf: '𝔼',\n  Epsilon: 'Ε',\n  Equal: '⩵',\n  EqualTilde: '≂',\n  Equilibrium: '⇌',\n  Escr: 'ℰ',\n  Esim: '⩳',\n  Eta: 'Η',\n  Euml: 'Ë',\n  Exists: '∃',\n  ExponentialE: 'ⅇ',\n  Fcy: 'Ф',\n  Ffr: '𝔉',\n  FilledSmallSquare: '◼',\n  FilledVerySmallSquare: '▪',\n  Fopf: '𝔽',\n  ForAll: '∀',\n  Fouriertrf: 'ℱ',\n  Fscr: 'ℱ',\n  GJcy: 'Ѓ',\n  GT: '>',\n  Gamma: 'Γ',\n  Gammad: 'Ϝ',\n  Gbreve: 'Ğ',\n  Gcedil: 'Ģ',\n  Gcirc: 'Ĝ',\n  Gcy: 'Г',\n  Gdot: 'Ġ',\n  Gfr: '𝔊',\n  Gg: '⋙',\n  Gopf: '𝔾',\n  GreaterEqual: '≥',\n  GreaterEqualLess: '⋛',\n  GreaterFullEqual: '≧',\n  GreaterGreater: '⪢',\n  GreaterLess: '≷',\n  GreaterSlantEqual: '⩾',\n  GreaterTilde: '≳',\n  Gscr: '𝒢',\n  Gt: '≫',\n  HARDcy: 'Ъ',\n  Hacek: 'ˇ',\n  Hat: '^',\n  Hcirc: 'Ĥ',\n  Hfr: 'ℌ',\n  HilbertSpace: 'ℋ',\n  Hopf: 'ℍ',\n  HorizontalLine: '─',\n  Hscr: 'ℋ',\n  Hstrok: 'Ħ',\n  HumpDownHump: '≎',\n  HumpEqual: '≏',\n  IEcy: 'Е',\n  IJlig: 'Ĳ',\n  IOcy: 'Ё',\n  Iacute: 'Í',\n  Icirc: 'Î',\n  Icy: 'И',\n  Idot: 'İ',\n  Ifr: 'ℑ',\n  Igrave: 'Ì',\n  Im: 'ℑ',\n  Imacr: 'Ī',\n  ImaginaryI: 'ⅈ',\n  Implies: '⇒',\n  Int: '∬',\n  Integral: '∫',\n  Intersection: '⋂',\n  InvisibleComma: '⁣',\n  InvisibleTimes: '⁢',\n  Iogon: 'Į',\n  Iopf: '𝕀',\n  Iota: 'Ι',\n  Iscr: 'ℐ',\n  Itilde: 'Ĩ',\n  Iukcy: 'І',\n  Iuml: 'Ï',\n  Jcirc: 'Ĵ',\n  Jcy: 'Й',\n  Jfr: '𝔍',\n  Jopf: '𝕁',\n  Jscr: '𝒥',\n  Jsercy: 'Ј',\n  Jukcy: 'Є',\n  KHcy: 'Х',\n  KJcy: 'Ќ',\n  Kappa: 'Κ',\n  Kcedil: 'Ķ',\n  Kcy: 'К',\n  Kfr: '𝔎',\n  Kopf: '𝕂',\n  Kscr: '𝒦',\n  LJcy: 'Љ',\n  LT: '<',\n  Lacute: 'Ĺ',\n  Lambda: 'Λ',\n  Lang: '⟪',\n  Laplacetrf: 'ℒ',\n  Larr: '↞',\n  Lcaron: 'Ľ',\n  Lcedil: 'Ļ',\n  Lcy: 'Л',\n  LeftAngleBracket: '⟨',\n  LeftArrow: '←',\n  LeftArrowBar: '⇤',\n  LeftArrowRightArrow: '⇆',\n  LeftCeiling: '⌈',\n  LeftDoubleBracket: '⟦',\n  LeftDownTeeVector: '⥡',\n  LeftDownVector: '⇃',\n  LeftDownVectorBar: '⥙',\n  LeftFloor: '⌊',\n  LeftRightArrow: '↔',\n  LeftRightVector: '⥎',\n  LeftTee: '⊣',\n  LeftTeeArrow: '↤',\n  LeftTeeVector: '⥚',\n  LeftTriangle: '⊲',\n  LeftTriangleBar: '⧏',\n  LeftTriangleEqual: '⊴',\n  LeftUpDownVector: '⥑',\n  LeftUpTeeVector: '⥠',\n  LeftUpVector: '↿',\n  LeftUpVectorBar: '⥘',\n  LeftVector: '↼',\n  LeftVectorBar: '⥒',\n  Leftarrow: '⇐',\n  Leftrightarrow: '⇔',\n  LessEqualGreater: '⋚',\n  LessFullEqual: '≦',\n  LessGreater: '≶',\n  LessLess: '⪡',\n  LessSlantEqual: '⩽',\n  LessTilde: '≲',\n  Lfr: '𝔏',\n  Ll: '⋘',\n  Lleftarrow: '⇚',\n  Lmidot: 'Ŀ',\n  LongLeftArrow: '⟵',\n  LongLeftRightArrow: '⟷',\n  LongRightArrow: '⟶',\n  Longleftarrow: '⟸',\n  Longleftrightarrow: '⟺',\n  Longrightarrow: '⟹',\n  Lopf: '𝕃',\n  LowerLeftArrow: '↙',\n  LowerRightArrow: '↘',\n  Lscr: 'ℒ',\n  Lsh: '↰',\n  Lstrok: 'Ł',\n  Lt: '≪',\n  Map: '⤅',\n  Mcy: 'М',\n  MediumSpace: ' ',\n  Mellintrf: 'ℳ',\n  Mfr: '𝔐',\n  MinusPlus: '∓',\n  Mopf: '𝕄',\n  Mscr: 'ℳ',\n  Mu: 'Μ',\n  NJcy: 'Њ',\n  Nacute: 'Ń',\n  Ncaron: 'Ň',\n  Ncedil: 'Ņ',\n  Ncy: 'Н',\n  NegativeMediumSpace: '​',\n  NegativeThickSpace: '​',\n  NegativeThinSpace: '​',\n  NegativeVeryThinSpace: '​',\n  NestedGreaterGreater: '≫',\n  NestedLessLess: '≪',\n  NewLine: '\\n',\n  Nfr: '𝔑',\n  NoBreak: '⁠',\n  NonBreakingSpace: ' ',\n  Nopf: 'ℕ',\n  Not: '⫬',\n  NotCongruent: '≢',\n  NotCupCap: '≭',\n  NotDoubleVerticalBar: '∦',\n  NotElement: '∉',\n  NotEqual: '≠',\n  NotEqualTilde: '≂̸',\n  NotExists: '∄',\n  NotGreater: '≯',\n  NotGreaterEqual: '≱',\n  NotGreaterFullEqual: '≧̸',\n  NotGreaterGreater: '≫̸',\n  NotGreaterLess: '≹',\n  NotGreaterSlantEqual: '⩾̸',\n  NotGreaterTilde: '≵',\n  NotHumpDownHump: '≎̸',\n  NotHumpEqual: '≏̸',\n  NotLeftTriangle: '⋪',\n  NotLeftTriangleBar: '⧏̸',\n  NotLeftTriangleEqual: '⋬',\n  NotLess: '≮',\n  NotLessEqual: '≰',\n  NotLessGreater: '≸',\n  NotLessLess: '≪̸',\n  NotLessSlantEqual: '⩽̸',\n  NotLessTilde: '≴',\n  NotNestedGreaterGreater: '⪢̸',\n  NotNestedLessLess: '⪡̸',\n  NotPrecedes: '⊀',\n  NotPrecedesEqual: '⪯̸',\n  NotPrecedesSlantEqual: '⋠',\n  NotReverseElement: '∌',\n  NotRightTriangle: '⋫',\n  NotRightTriangleBar: '⧐̸',\n  NotRightTriangleEqual: '⋭',\n  NotSquareSubset: '⊏̸',\n  NotSquareSubsetEqual: '⋢',\n  NotSquareSuperset: '⊐̸',\n  NotSquareSupersetEqual: '⋣',\n  NotSubset: '⊂⃒',\n  NotSubsetEqual: '⊈',\n  NotSucceeds: '⊁',\n  NotSucceedsEqual: '⪰̸',\n  NotSucceedsSlantEqual: '⋡',\n  NotSucceedsTilde: '≿̸',\n  NotSuperset: '⊃⃒',\n  NotSupersetEqual: '⊉',\n  NotTilde: '≁',\n  NotTildeEqual: '≄',\n  NotTildeFullEqual: '≇',\n  NotTildeTilde: '≉',\n  NotVerticalBar: '∤',\n  Nscr: '𝒩',\n  Ntilde: 'Ñ',\n  Nu: 'Ν',\n  OElig: 'Œ',\n  Oacute: 'Ó',\n  Ocirc: 'Ô',\n  Ocy: 'О',\n  Odblac: 'Ő',\n  Ofr: '𝔒',\n  Ograve: 'Ò',\n  Omacr: 'Ō',\n  Omega: 'Ω',\n  Omicron: 'Ο',\n  Oopf: '𝕆',\n  OpenCurlyDoubleQuote: '“',\n  OpenCurlyQuote: '‘',\n  Or: '⩔',\n  Oscr: '𝒪',\n  Oslash: 'Ø',\n  Otilde: 'Õ',\n  Otimes: '⨷',\n  Ouml: 'Ö',\n  OverBar: '‾',\n  OverBrace: '⏞',\n  OverBracket: '⎴',\n  OverParenthesis: '⏜',\n  PartialD: '∂',\n  Pcy: 'П',\n  Pfr: '𝔓',\n  Phi: 'Φ',\n  Pi: 'Π',\n  PlusMinus: '±',\n  Poincareplane: 'ℌ',\n  Popf: 'ℙ',\n  Pr: '⪻',\n  Precedes: '≺',\n  PrecedesEqual: '⪯',\n  PrecedesSlantEqual: '≼',\n  PrecedesTilde: '≾',\n  Prime: '″',\n  Product: '∏',\n  Proportion: '∷',\n  Proportional: '∝',\n  Pscr: '𝒫',\n  Psi: 'Ψ',\n  QUOT: '\"',\n  Qfr: '𝔔',\n  Qopf: 'ℚ',\n  Qscr: '𝒬',\n  RBarr: '⤐',\n  REG: '®',\n  Racute: 'Ŕ',\n  Rang: '⟫',\n  Rarr: '↠',\n  Rarrtl: '⤖',\n  Rcaron: 'Ř',\n  Rcedil: 'Ŗ',\n  Rcy: 'Р',\n  Re: 'ℜ',\n  ReverseElement: '∋',\n  ReverseEquilibrium: '⇋',\n  ReverseUpEquilibrium: '⥯',\n  Rfr: 'ℜ',\n  Rho: 'Ρ',\n  RightAngleBracket: '⟩',\n  RightArrow: '→',\n  RightArrowBar: '⇥',\n  RightArrowLeftArrow: '⇄',\n  RightCeiling: '⌉',\n  RightDoubleBracket: '⟧',\n  RightDownTeeVector: '⥝',\n  RightDownVector: '⇂',\n  RightDownVectorBar: '⥕',\n  RightFloor: '⌋',\n  RightTee: '⊢',\n  RightTeeArrow: '↦',\n  RightTeeVector: '⥛',\n  RightTriangle: '⊳',\n  RightTriangleBar: '⧐',\n  RightTriangleEqual: '⊵',\n  RightUpDownVector: '⥏',\n  RightUpTeeVector: '⥜',\n  RightUpVector: '↾',\n  RightUpVectorBar: '⥔',\n  RightVector: '⇀',\n  RightVectorBar: '⥓',\n  Rightarrow: '⇒',\n  Ropf: 'ℝ',\n  RoundImplies: '⥰',\n  Rrightarrow: '⇛',\n  Rscr: 'ℛ',\n  Rsh: '↱',\n  RuleDelayed: '⧴',\n  SHCHcy: 'Щ',\n  SHcy: 'Ш',\n  SOFTcy: 'Ь',\n  Sacute: 'Ś',\n  Sc: '⪼',\n  Scaron: 'Š',\n  Scedil: 'Ş',\n  Scirc: 'Ŝ',\n  Scy: 'С',\n  Sfr: '𝔖',\n  ShortDownArrow: '↓',\n  ShortLeftArrow: '←',\n  ShortRightArrow: '→',\n  ShortUpArrow: '↑',\n  Sigma: 'Σ',\n  SmallCircle: '∘',\n  Sopf: '𝕊',\n  Sqrt: '√',\n  Square: '□',\n  SquareIntersection: '⊓',\n  SquareSubset: '⊏',\n  SquareSubsetEqual: '⊑',\n  SquareSuperset: '⊐',\n  SquareSupersetEqual: '⊒',\n  SquareUnion: '⊔',\n  Sscr: '𝒮',\n  Star: '⋆',\n  Sub: '⋐',\n  Subset: '⋐',\n  SubsetEqual: '⊆',\n  Succeeds: '≻',\n  SucceedsEqual: '⪰',\n  SucceedsSlantEqual: '≽',\n  SucceedsTilde: '≿',\n  SuchThat: '∋',\n  Sum: '∑',\n  Sup: '⋑',\n  Superset: '⊃',\n  SupersetEqual: '⊇',\n  Supset: '⋑',\n  THORN: 'Þ',\n  TRADE: '™',\n  TSHcy: 'Ћ',\n  TScy: 'Ц',\n  Tab: '\\t',\n  Tau: 'Τ',\n  Tcaron: 'Ť',\n  Tcedil: 'Ţ',\n  Tcy: 'Т',\n  Tfr: '𝔗',\n  Therefore: '∴',\n  Theta: 'Θ',\n  ThickSpace: '  ',\n  ThinSpace: ' ',\n  Tilde: '∼',\n  TildeEqual: '≃',\n  TildeFullEqual: '≅',\n  TildeTilde: '≈',\n  Topf: '𝕋',\n  TripleDot: '⃛',\n  Tscr: '𝒯',\n  Tstrok: 'Ŧ',\n  Uacute: 'Ú',\n  Uarr: '↟',\n  Uarrocir: '⥉',\n  Ubrcy: 'Ў',\n  Ubreve: 'Ŭ',\n  Ucirc: 'Û',\n  Ucy: 'У',\n  Udblac: 'Ű',\n  Ufr: '𝔘',\n  Ugrave: 'Ù',\n  Umacr: 'Ū',\n  UnderBar: '_',\n  UnderBrace: '⏟',\n  UnderBracket: '⎵',\n  UnderParenthesis: '⏝',\n  Union: '⋃',\n  UnionPlus: '⊎',\n  Uogon: 'Ų',\n  Uopf: '𝕌',\n  UpArrow: '↑',\n  UpArrowBar: '⤒',\n  UpArrowDownArrow: '⇅',\n  UpDownArrow: '↕',\n  UpEquilibrium: '⥮',\n  UpTee: '⊥',\n  UpTeeArrow: '↥',\n  Uparrow: '⇑',\n  Updownarrow: '⇕',\n  UpperLeftArrow: '↖',\n  UpperRightArrow: '↗',\n  Upsi: 'ϒ',\n  Upsilon: 'Υ',\n  Uring: 'Ů',\n  Uscr: '𝒰',\n  Utilde: 'Ũ',\n  Uuml: 'Ü',\n  VDash: '⊫',\n  Vbar: '⫫',\n  Vcy: 'В',\n  Vdash: '⊩',\n  Vdashl: '⫦',\n  Vee: '⋁',\n  Verbar: '‖',\n  Vert: '‖',\n  VerticalBar: '∣',\n  VerticalLine: '|',\n  VerticalSeparator: '❘',\n  VerticalTilde: '≀',\n  VeryThinSpace: ' ',\n  Vfr: '𝔙',\n  Vopf: '𝕍',\n  Vscr: '𝒱',\n  Vvdash: '⊪',\n  Wcirc: 'Ŵ',\n  Wedge: '⋀',\n  Wfr: '𝔚',\n  Wopf: '𝕎',\n  Wscr: '𝒲',\n  Xfr: '𝔛',\n  Xi: 'Ξ',\n  Xopf: '𝕏',\n  Xscr: '𝒳',\n  YAcy: 'Я',\n  YIcy: 'Ї',\n  YUcy: 'Ю',\n  Yacute: 'Ý',\n  Ycirc: 'Ŷ',\n  Ycy: 'Ы',\n  Yfr: '𝔜',\n  Yopf: '𝕐',\n  Yscr: '𝒴',\n  Yuml: 'Ÿ',\n  ZHcy: 'Ж',\n  Zacute: 'Ź',\n  Zcaron: 'Ž',\n  Zcy: 'З',\n  Zdot: 'Ż',\n  ZeroWidthSpace: '​',\n  Zeta: 'Ζ',\n  Zfr: 'ℨ',\n  Zopf: 'ℤ',\n  Zscr: '𝒵',\n  aacute: 'á',\n  abreve: 'ă',\n  ac: '∾',\n  acE: '∾̳',\n  acd: '∿',\n  acirc: 'â',\n  acute: '´',\n  acy: 'а',\n  aelig: 'æ',\n  af: '⁡',\n  afr: '𝔞',\n  agrave: 'à',\n  alefsym: 'ℵ',\n  aleph: 'ℵ',\n  alpha: 'α',\n  amacr: 'ā',\n  amalg: '⨿',\n  amp: '&',\n  and: '∧',\n  andand: '⩕',\n  andd: '⩜',\n  andslope: '⩘',\n  andv: '⩚',\n  ang: '∠',\n  ange: '⦤',\n  angle: '∠',\n  angmsd: '∡',\n  angmsdaa: '⦨',\n  angmsdab: '⦩',\n  angmsdac: '⦪',\n  angmsdad: '⦫',\n  angmsdae: '⦬',\n  angmsdaf: '⦭',\n  angmsdag: '⦮',\n  angmsdah: '⦯',\n  angrt: '∟',\n  angrtvb: '⊾',\n  angrtvbd: '⦝',\n  angsph: '∢',\n  angst: 'Å',\n  angzarr: '⍼',\n  aogon: 'ą',\n  aopf: '𝕒',\n  ap: '≈',\n  apE: '⩰',\n  apacir: '⩯',\n  ape: '≊',\n  apid: '≋',\n  apos: \"'\",\n  approx: '≈',\n  approxeq: '≊',\n  aring: 'å',\n  ascr: '𝒶',\n  ast: '*',\n  asymp: '≈',\n  asympeq: '≍',\n  atilde: 'ã',\n  auml: 'ä',\n  awconint: '∳',\n  awint: '⨑',\n  bNot: '⫭',\n  backcong: '≌',\n  backepsilon: '϶',\n  backprime: '‵',\n  backsim: '∽',\n  backsimeq: '⋍',\n  barvee: '⊽',\n  barwed: '⌅',\n  barwedge: '⌅',\n  bbrk: '⎵',\n  bbrktbrk: '⎶',\n  bcong: '≌',\n  bcy: 'б',\n  bdquo: '„',\n  becaus: '∵',\n  because: '∵',\n  bemptyv: '⦰',\n  bepsi: '϶',\n  bernou: 'ℬ',\n  beta: 'β',\n  beth: 'ℶ',\n  between: '≬',\n  bfr: '𝔟',\n  bigcap: '⋂',\n  bigcirc: '◯',\n  bigcup: '⋃',\n  bigodot: '⨀',\n  bigoplus: '⨁',\n  bigotimes: '⨂',\n  bigsqcup: '⨆',\n  bigstar: '★',\n  bigtriangledown: '▽',\n  bigtriangleup: '△',\n  biguplus: '⨄',\n  bigvee: '⋁',\n  bigwedge: '⋀',\n  bkarow: '⤍',\n  blacklozenge: '⧫',\n  blacksquare: '▪',\n  blacktriangle: '▴',\n  blacktriangledown: '▾',\n  blacktriangleleft: '◂',\n  blacktriangleright: '▸',\n  blank: '␣',\n  blk12: '▒',\n  blk14: '░',\n  blk34: '▓',\n  block: '█',\n  bne: '=⃥',\n  bnequiv: '≡⃥',\n  bnot: '⌐',\n  bopf: '𝕓',\n  bot: '⊥',\n  bottom: '⊥',\n  bowtie: '⋈',\n  boxDL: '╗',\n  boxDR: '╔',\n  boxDl: '╖',\n  boxDr: '╓',\n  boxH: '═',\n  boxHD: '╦',\n  boxHU: '╩',\n  boxHd: '╤',\n  boxHu: '╧',\n  boxUL: '╝',\n  boxUR: '╚',\n  boxUl: '╜',\n  boxUr: '╙',\n  boxV: '║',\n  boxVH: '╬',\n  boxVL: '╣',\n  boxVR: '╠',\n  boxVh: '╫',\n  boxVl: '╢',\n  boxVr: '╟',\n  boxbox: '⧉',\n  boxdL: '╕',\n  boxdR: '╒',\n  boxdl: '┐',\n  boxdr: '┌',\n  boxh: '─',\n  boxhD: '╥',\n  boxhU: '╨',\n  boxhd: '┬',\n  boxhu: '┴',\n  boxminus: '⊟',\n  boxplus: '⊞',\n  boxtimes: '⊠',\n  boxuL: '╛',\n  boxuR: '╘',\n  boxul: '┘',\n  boxur: '└',\n  boxv: '│',\n  boxvH: '╪',\n  boxvL: '╡',\n  boxvR: '╞',\n  boxvh: '┼',\n  boxvl: '┤',\n  boxvr: '├',\n  bprime: '‵',\n  breve: '˘',\n  brvbar: '¦',\n  bscr: '𝒷',\n  bsemi: '⁏',\n  bsim: '∽',\n  bsime: '⋍',\n  bsol: '\\\\',\n  bsolb: '⧅',\n  bsolhsub: '⟈',\n  bull: '•',\n  bullet: '•',\n  bump: '≎',\n  bumpE: '⪮',\n  bumpe: '≏',\n  bumpeq: '≏',\n  cacute: 'ć',\n  cap: '∩',\n  capand: '⩄',\n  capbrcup: '⩉',\n  capcap: '⩋',\n  capcup: '⩇',\n  capdot: '⩀',\n  caps: '∩︀',\n  caret: '⁁',\n  caron: 'ˇ',\n  ccaps: '⩍',\n  ccaron: 'č',\n  ccedil: 'ç',\n  ccirc: 'ĉ',\n  ccups: '⩌',\n  ccupssm: '⩐',\n  cdot: 'ċ',\n  cedil: '¸',\n  cemptyv: '⦲',\n  cent: '¢',\n  centerdot: '·',\n  cfr: '𝔠',\n  chcy: 'ч',\n  check: '✓',\n  checkmark: '✓',\n  chi: 'χ',\n  cir: '○',\n  cirE: '⧃',\n  circ: 'ˆ',\n  circeq: '≗',\n  circlearrowleft: '↺',\n  circlearrowright: '↻',\n  circledR: '®',\n  circledS: 'Ⓢ',\n  circledast: '⊛',\n  circledcirc: '⊚',\n  circleddash: '⊝',\n  cire: '≗',\n  cirfnint: '⨐',\n  cirmid: '⫯',\n  cirscir: '⧂',\n  clubs: '♣',\n  clubsuit: '♣',\n  colon: ':',\n  colone: '≔',\n  coloneq: '≔',\n  comma: ',',\n  commat: '@',\n  comp: '∁',\n  compfn: '∘',\n  complement: '∁',\n  complexes: 'ℂ',\n  cong: '≅',\n  congdot: '⩭',\n  conint: '∮',\n  copf: '𝕔',\n  coprod: '∐',\n  copy: '©',\n  copysr: '℗',\n  crarr: '↵',\n  cross: '✗',\n  cscr: '𝒸',\n  csub: '⫏',\n  csube: '⫑',\n  csup: '⫐',\n  csupe: '⫒',\n  ctdot: '⋯',\n  cudarrl: '⤸',\n  cudarrr: '⤵',\n  cuepr: '⋞',\n  cuesc: '⋟',\n  cularr: '↶',\n  cularrp: '⤽',\n  cup: '∪',\n  cupbrcap: '⩈',\n  cupcap: '⩆',\n  cupcup: '⩊',\n  cupdot: '⊍',\n  cupor: '⩅',\n  cups: '∪︀',\n  curarr: '↷',\n  curarrm: '⤼',\n  curlyeqprec: '⋞',\n  curlyeqsucc: '⋟',\n  curlyvee: '⋎',\n  curlywedge: '⋏',\n  curren: '¤',\n  curvearrowleft: '↶',\n  curvearrowright: '↷',\n  cuvee: '⋎',\n  cuwed: '⋏',\n  cwconint: '∲',\n  cwint: '∱',\n  cylcty: '⌭',\n  dArr: '⇓',\n  dHar: '⥥',\n  dagger: '†',\n  daleth: 'ℸ',\n  darr: '↓',\n  dash: '‐',\n  dashv: '⊣',\n  dbkarow: '⤏',\n  dblac: '˝',\n  dcaron: 'ď',\n  dcy: 'д',\n  dd: 'ⅆ',\n  ddagger: '‡',\n  ddarr: '⇊',\n  ddotseq: '⩷',\n  deg: '°',\n  delta: 'δ',\n  demptyv: '⦱',\n  dfisht: '⥿',\n  dfr: '𝔡',\n  dharl: '⇃',\n  dharr: '⇂',\n  diam: '⋄',\n  diamond: '⋄',\n  diamondsuit: '♦',\n  diams: '♦',\n  die: '¨',\n  digamma: 'ϝ',\n  disin: '⋲',\n  div: '÷',\n  divide: '÷',\n  divideontimes: '⋇',\n  divonx: '⋇',\n  djcy: 'ђ',\n  dlcorn: '⌞',\n  dlcrop: '⌍',\n  dollar: '$',\n  dopf: '𝕕',\n  dot: '˙',\n  doteq: '≐',\n  doteqdot: '≑',\n  dotminus: '∸',\n  dotplus: '∔',\n  dotsquare: '⊡',\n  doublebarwedge: '⌆',\n  downarrow: '↓',\n  downdownarrows: '⇊',\n  downharpoonleft: '⇃',\n  downharpoonright: '⇂',\n  drbkarow: '⤐',\n  drcorn: '⌟',\n  drcrop: '⌌',\n  dscr: '𝒹',\n  dscy: 'ѕ',\n  dsol: '⧶',\n  dstrok: 'đ',\n  dtdot: '⋱',\n  dtri: '▿',\n  dtrif: '▾',\n  duarr: '⇵',\n  duhar: '⥯',\n  dwangle: '⦦',\n  dzcy: 'џ',\n  dzigrarr: '⟿',\n  eDDot: '⩷',\n  eDot: '≑',\n  eacute: 'é',\n  easter: '⩮',\n  ecaron: 'ě',\n  ecir: '≖',\n  ecirc: 'ê',\n  ecolon: '≕',\n  ecy: 'э',\n  edot: 'ė',\n  ee: 'ⅇ',\n  efDot: '≒',\n  efr: '𝔢',\n  eg: '⪚',\n  egrave: 'è',\n  egs: '⪖',\n  egsdot: '⪘',\n  el: '⪙',\n  elinters: '⏧',\n  ell: 'ℓ',\n  els: '⪕',\n  elsdot: '⪗',\n  emacr: 'ē',\n  empty: '∅',\n  emptyset: '∅',\n  emptyv: '∅',\n  emsp13: ' ',\n  emsp14: ' ',\n  emsp: ' ',\n  eng: 'ŋ',\n  ensp: ' ',\n  eogon: 'ę',\n  eopf: '𝕖',\n  epar: '⋕',\n  eparsl: '⧣',\n  eplus: '⩱',\n  epsi: 'ε',\n  epsilon: 'ε',\n  epsiv: 'ϵ',\n  eqcirc: '≖',\n  eqcolon: '≕',\n  eqsim: '≂',\n  eqslantgtr: '⪖',\n  eqslantless: '⪕',\n  equals: '=',\n  equest: '≟',\n  equiv: '≡',\n  equivDD: '⩸',\n  eqvparsl: '⧥',\n  erDot: '≓',\n  erarr: '⥱',\n  escr: 'ℯ',\n  esdot: '≐',\n  esim: '≂',\n  eta: 'η',\n  eth: 'ð',\n  euml: 'ë',\n  euro: '€',\n  excl: '!',\n  exist: '∃',\n  expectation: 'ℰ',\n  exponentiale: 'ⅇ',\n  fallingdotseq: '≒',\n  fcy: 'ф',\n  female: '♀',\n  ffilig: 'ﬃ',\n  fflig: 'ﬀ',\n  ffllig: 'ﬄ',\n  ffr: '𝔣',\n  filig: 'ﬁ',\n  fjlig: 'fj',\n  flat: '♭',\n  fllig: 'ﬂ',\n  fltns: '▱',\n  fnof: 'ƒ',\n  fopf: '𝕗',\n  forall: '∀',\n  fork: '⋔',\n  forkv: '⫙',\n  fpartint: '⨍',\n  frac12: '½',\n  frac13: '⅓',\n  frac14: '¼',\n  frac15: '⅕',\n  frac16: '⅙',\n  frac18: '⅛',\n  frac23: '⅔',\n  frac25: '⅖',\n  frac34: '¾',\n  frac35: '⅗',\n  frac38: '⅜',\n  frac45: '⅘',\n  frac56: '⅚',\n  frac58: '⅝',\n  frac78: '⅞',\n  frasl: '⁄',\n  frown: '⌢',\n  fscr: '𝒻',\n  gE: '≧',\n  gEl: '⪌',\n  gacute: 'ǵ',\n  gamma: 'γ',\n  gammad: 'ϝ',\n  gap: '⪆',\n  gbreve: 'ğ',\n  gcirc: 'ĝ',\n  gcy: 'г',\n  gdot: 'ġ',\n  ge: '≥',\n  gel: '⋛',\n  geq: '≥',\n  geqq: '≧',\n  geqslant: '⩾',\n  ges: '⩾',\n  gescc: '⪩',\n  gesdot: '⪀',\n  gesdoto: '⪂',\n  gesdotol: '⪄',\n  gesl: '⋛︀',\n  gesles: '⪔',\n  gfr: '𝔤',\n  gg: '≫',\n  ggg: '⋙',\n  gimel: 'ℷ',\n  gjcy: 'ѓ',\n  gl: '≷',\n  glE: '⪒',\n  gla: '⪥',\n  glj: '⪤',\n  gnE: '≩',\n  gnap: '⪊',\n  gnapprox: '⪊',\n  gne: '⪈',\n  gneq: '⪈',\n  gneqq: '≩',\n  gnsim: '⋧',\n  gopf: '𝕘',\n  grave: '`',\n  gscr: 'ℊ',\n  gsim: '≳',\n  gsime: '⪎',\n  gsiml: '⪐',\n  gt: '>',\n  gtcc: '⪧',\n  gtcir: '⩺',\n  gtdot: '⋗',\n  gtlPar: '⦕',\n  gtquest: '⩼',\n  gtrapprox: '⪆',\n  gtrarr: '⥸',\n  gtrdot: '⋗',\n  gtreqless: '⋛',\n  gtreqqless: '⪌',\n  gtrless: '≷',\n  gtrsim: '≳',\n  gvertneqq: '≩︀',\n  gvnE: '≩︀',\n  hArr: '⇔',\n  hairsp: ' ',\n  half: '½',\n  hamilt: 'ℋ',\n  hardcy: 'ъ',\n  harr: '↔',\n  harrcir: '⥈',\n  harrw: '↭',\n  hbar: 'ℏ',\n  hcirc: 'ĥ',\n  hearts: '♥',\n  heartsuit: '♥',\n  hellip: '…',\n  hercon: '⊹',\n  hfr: '𝔥',\n  hksearow: '⤥',\n  hkswarow: '⤦',\n  hoarr: '⇿',\n  homtht: '∻',\n  hookleftarrow: '↩',\n  hookrightarrow: '↪',\n  hopf: '𝕙',\n  horbar: '―',\n  hscr: '𝒽',\n  hslash: 'ℏ',\n  hstrok: 'ħ',\n  hybull: '⁃',\n  hyphen: '‐',\n  iacute: 'í',\n  ic: '⁣',\n  icirc: 'î',\n  icy: 'и',\n  iecy: 'е',\n  iexcl: '¡',\n  iff: '⇔',\n  ifr: '𝔦',\n  igrave: 'ì',\n  ii: 'ⅈ',\n  iiiint: '⨌',\n  iiint: '∭',\n  iinfin: '⧜',\n  iiota: '℩',\n  ijlig: 'ĳ',\n  imacr: 'ī',\n  image: 'ℑ',\n  imagline: 'ℐ',\n  imagpart: 'ℑ',\n  imath: 'ı',\n  imof: '⊷',\n  imped: 'Ƶ',\n  in: '∈',\n  incare: '℅',\n  infin: '∞',\n  infintie: '⧝',\n  inodot: 'ı',\n  int: '∫',\n  intcal: '⊺',\n  integers: 'ℤ',\n  intercal: '⊺',\n  intlarhk: '⨗',\n  intprod: '⨼',\n  iocy: 'ё',\n  iogon: 'į',\n  iopf: '𝕚',\n  iota: 'ι',\n  iprod: '⨼',\n  iquest: '¿',\n  iscr: '𝒾',\n  isin: '∈',\n  isinE: '⋹',\n  isindot: '⋵',\n  isins: '⋴',\n  isinsv: '⋳',\n  isinv: '∈',\n  it: '⁢',\n  itilde: 'ĩ',\n  iukcy: 'і',\n  iuml: 'ï',\n  jcirc: 'ĵ',\n  jcy: 'й',\n  jfr: '𝔧',\n  jmath: 'ȷ',\n  jopf: '𝕛',\n  jscr: '𝒿',\n  jsercy: 'ј',\n  jukcy: 'є',\n  kappa: 'κ',\n  kappav: 'ϰ',\n  kcedil: 'ķ',\n  kcy: 'к',\n  kfr: '𝔨',\n  kgreen: 'ĸ',\n  khcy: 'х',\n  kjcy: 'ќ',\n  kopf: '𝕜',\n  kscr: '𝓀',\n  lAarr: '⇚',\n  lArr: '⇐',\n  lAtail: '⤛',\n  lBarr: '⤎',\n  lE: '≦',\n  lEg: '⪋',\n  lHar: '⥢',\n  lacute: 'ĺ',\n  laemptyv: '⦴',\n  lagran: 'ℒ',\n  lambda: 'λ',\n  lang: '⟨',\n  langd: '⦑',\n  langle: '⟨',\n  lap: '⪅',\n  laquo: '«',\n  larr: '←',\n  larrb: '⇤',\n  larrbfs: '⤟',\n  larrfs: '⤝',\n  larrhk: '↩',\n  larrlp: '↫',\n  larrpl: '⤹',\n  larrsim: '⥳',\n  larrtl: '↢',\n  lat: '⪫',\n  latail: '⤙',\n  late: '⪭',\n  lates: '⪭︀',\n  lbarr: '⤌',\n  lbbrk: '❲',\n  lbrace: '{',\n  lbrack: '[',\n  lbrke: '⦋',\n  lbrksld: '⦏',\n  lbrkslu: '⦍',\n  lcaron: 'ľ',\n  lcedil: 'ļ',\n  lceil: '⌈',\n  lcub: '{',\n  lcy: 'л',\n  ldca: '⤶',\n  ldquo: '“',\n  ldquor: '„',\n  ldrdhar: '⥧',\n  ldrushar: '⥋',\n  ldsh: '↲',\n  le: '≤',\n  leftarrow: '←',\n  leftarrowtail: '↢',\n  leftharpoondown: '↽',\n  leftharpoonup: '↼',\n  leftleftarrows: '⇇',\n  leftrightarrow: '↔',\n  leftrightarrows: '⇆',\n  leftrightharpoons: '⇋',\n  leftrightsquigarrow: '↭',\n  leftthreetimes: '⋋',\n  leg: '⋚',\n  leq: '≤',\n  leqq: '≦',\n  leqslant: '⩽',\n  les: '⩽',\n  lescc: '⪨',\n  lesdot: '⩿',\n  lesdoto: '⪁',\n  lesdotor: '⪃',\n  lesg: '⋚︀',\n  lesges: '⪓',\n  lessapprox: '⪅',\n  lessdot: '⋖',\n  lesseqgtr: '⋚',\n  lesseqqgtr: '⪋',\n  lessgtr: '≶',\n  lesssim: '≲',\n  lfisht: '⥼',\n  lfloor: '⌊',\n  lfr: '𝔩',\n  lg: '≶',\n  lgE: '⪑',\n  lhard: '↽',\n  lharu: '↼',\n  lharul: '⥪',\n  lhblk: '▄',\n  ljcy: 'љ',\n  ll: '≪',\n  llarr: '⇇',\n  llcorner: '⌞',\n  llhard: '⥫',\n  lltri: '◺',\n  lmidot: 'ŀ',\n  lmoust: '⎰',\n  lmoustache: '⎰',\n  lnE: '≨',\n  lnap: '⪉',\n  lnapprox: '⪉',\n  lne: '⪇',\n  lneq: '⪇',\n  lneqq: '≨',\n  lnsim: '⋦',\n  loang: '⟬',\n  loarr: '⇽',\n  lobrk: '⟦',\n  longleftarrow: '⟵',\n  longleftrightarrow: '⟷',\n  longmapsto: '⟼',\n  longrightarrow: '⟶',\n  looparrowleft: '↫',\n  looparrowright: '↬',\n  lopar: '⦅',\n  lopf: '𝕝',\n  loplus: '⨭',\n  lotimes: '⨴',\n  lowast: '∗',\n  lowbar: '_',\n  loz: '◊',\n  lozenge: '◊',\n  lozf: '⧫',\n  lpar: '(',\n  lparlt: '⦓',\n  lrarr: '⇆',\n  lrcorner: '⌟',\n  lrhar: '⇋',\n  lrhard: '⥭',\n  lrm: '‎',\n  lrtri: '⊿',\n  lsaquo: '‹',\n  lscr: '𝓁',\n  lsh: '↰',\n  lsim: '≲',\n  lsime: '⪍',\n  lsimg: '⪏',\n  lsqb: '[',\n  lsquo: '‘',\n  lsquor: '‚',\n  lstrok: 'ł',\n  lt: '<',\n  ltcc: '⪦',\n  ltcir: '⩹',\n  ltdot: '⋖',\n  lthree: '⋋',\n  ltimes: '⋉',\n  ltlarr: '⥶',\n  ltquest: '⩻',\n  ltrPar: '⦖',\n  ltri: '◃',\n  ltrie: '⊴',\n  ltrif: '◂',\n  lurdshar: '⥊',\n  luruhar: '⥦',\n  lvertneqq: '≨︀',\n  lvnE: '≨︀',\n  mDDot: '∺',\n  macr: '¯',\n  male: '♂',\n  malt: '✠',\n  maltese: '✠',\n  map: '↦',\n  mapsto: '↦',\n  mapstodown: '↧',\n  mapstoleft: '↤',\n  mapstoup: '↥',\n  marker: '▮',\n  mcomma: '⨩',\n  mcy: 'м',\n  mdash: '—',\n  measuredangle: '∡',\n  mfr: '𝔪',\n  mho: '℧',\n  micro: 'µ',\n  mid: '∣',\n  midast: '*',\n  midcir: '⫰',\n  middot: '·',\n  minus: '−',\n  minusb: '⊟',\n  minusd: '∸',\n  minusdu: '⨪',\n  mlcp: '⫛',\n  mldr: '…',\n  mnplus: '∓',\n  models: '⊧',\n  mopf: '𝕞',\n  mp: '∓',\n  mscr: '𝓂',\n  mstpos: '∾',\n  mu: 'μ',\n  multimap: '⊸',\n  mumap: '⊸',\n  nGg: '⋙̸',\n  nGt: '≫⃒',\n  nGtv: '≫̸',\n  nLeftarrow: '⇍',\n  nLeftrightarrow: '⇎',\n  nLl: '⋘̸',\n  nLt: '≪⃒',\n  nLtv: '≪̸',\n  nRightarrow: '⇏',\n  nVDash: '⊯',\n  nVdash: '⊮',\n  nabla: '∇',\n  nacute: 'ń',\n  nang: '∠⃒',\n  nap: '≉',\n  napE: '⩰̸',\n  napid: '≋̸',\n  napos: 'ŉ',\n  napprox: '≉',\n  natur: '♮',\n  natural: '♮',\n  naturals: 'ℕ',\n  nbsp: ' ',\n  nbump: '≎̸',\n  nbumpe: '≏̸',\n  ncap: '⩃',\n  ncaron: 'ň',\n  ncedil: 'ņ',\n  ncong: '≇',\n  ncongdot: '⩭̸',\n  ncup: '⩂',\n  ncy: 'н',\n  ndash: '–',\n  ne: '≠',\n  neArr: '⇗',\n  nearhk: '⤤',\n  nearr: '↗',\n  nearrow: '↗',\n  nedot: '≐̸',\n  nequiv: '≢',\n  nesear: '⤨',\n  nesim: '≂̸',\n  nexist: '∄',\n  nexists: '∄',\n  nfr: '𝔫',\n  ngE: '≧̸',\n  nge: '≱',\n  ngeq: '≱',\n  ngeqq: '≧̸',\n  ngeqslant: '⩾̸',\n  nges: '⩾̸',\n  ngsim: '≵',\n  ngt: '≯',\n  ngtr: '≯',\n  nhArr: '⇎',\n  nharr: '↮',\n  nhpar: '⫲',\n  ni: '∋',\n  nis: '⋼',\n  nisd: '⋺',\n  niv: '∋',\n  njcy: 'њ',\n  nlArr: '⇍',\n  nlE: '≦̸',\n  nlarr: '↚',\n  nldr: '‥',\n  nle: '≰',\n  nleftarrow: '↚',\n  nleftrightarrow: '↮',\n  nleq: '≰',\n  nleqq: '≦̸',\n  nleqslant: '⩽̸',\n  nles: '⩽̸',\n  nless: '≮',\n  nlsim: '≴',\n  nlt: '≮',\n  nltri: '⋪',\n  nltrie: '⋬',\n  nmid: '∤',\n  nopf: '𝕟',\n  not: '¬',\n  notin: '∉',\n  notinE: '⋹̸',\n  notindot: '⋵̸',\n  notinva: '∉',\n  notinvb: '⋷',\n  notinvc: '⋶',\n  notni: '∌',\n  notniva: '∌',\n  notnivb: '⋾',\n  notnivc: '⋽',\n  npar: '∦',\n  nparallel: '∦',\n  nparsl: '⫽⃥',\n  npart: '∂̸',\n  npolint: '⨔',\n  npr: '⊀',\n  nprcue: '⋠',\n  npre: '⪯̸',\n  nprec: '⊀',\n  npreceq: '⪯̸',\n  nrArr: '⇏',\n  nrarr: '↛',\n  nrarrc: '⤳̸',\n  nrarrw: '↝̸',\n  nrightarrow: '↛',\n  nrtri: '⋫',\n  nrtrie: '⋭',\n  nsc: '⊁',\n  nsccue: '⋡',\n  nsce: '⪰̸',\n  nscr: '𝓃',\n  nshortmid: '∤',\n  nshortparallel: '∦',\n  nsim: '≁',\n  nsime: '≄',\n  nsimeq: '≄',\n  nsmid: '∤',\n  nspar: '∦',\n  nsqsube: '⋢',\n  nsqsupe: '⋣',\n  nsub: '⊄',\n  nsubE: '⫅̸',\n  nsube: '⊈',\n  nsubset: '⊂⃒',\n  nsubseteq: '⊈',\n  nsubseteqq: '⫅̸',\n  nsucc: '⊁',\n  nsucceq: '⪰̸',\n  nsup: '⊅',\n  nsupE: '⫆̸',\n  nsupe: '⊉',\n  nsupset: '⊃⃒',\n  nsupseteq: '⊉',\n  nsupseteqq: '⫆̸',\n  ntgl: '≹',\n  ntilde: 'ñ',\n  ntlg: '≸',\n  ntriangleleft: '⋪',\n  ntrianglelefteq: '⋬',\n  ntriangleright: '⋫',\n  ntrianglerighteq: '⋭',\n  nu: 'ν',\n  num: '#',\n  numero: '№',\n  numsp: ' ',\n  nvDash: '⊭',\n  nvHarr: '⤄',\n  nvap: '≍⃒',\n  nvdash: '⊬',\n  nvge: '≥⃒',\n  nvgt: '>⃒',\n  nvinfin: '⧞',\n  nvlArr: '⤂',\n  nvle: '≤⃒',\n  nvlt: '<⃒',\n  nvltrie: '⊴⃒',\n  nvrArr: '⤃',\n  nvrtrie: '⊵⃒',\n  nvsim: '∼⃒',\n  nwArr: '⇖',\n  nwarhk: '⤣',\n  nwarr: '↖',\n  nwarrow: '↖',\n  nwnear: '⤧',\n  oS: 'Ⓢ',\n  oacute: 'ó',\n  oast: '⊛',\n  ocir: '⊚',\n  ocirc: 'ô',\n  ocy: 'о',\n  odash: '⊝',\n  odblac: 'ő',\n  odiv: '⨸',\n  odot: '⊙',\n  odsold: '⦼',\n  oelig: 'œ',\n  ofcir: '⦿',\n  ofr: '𝔬',\n  ogon: '˛',\n  ograve: 'ò',\n  ogt: '⧁',\n  ohbar: '⦵',\n  ohm: 'Ω',\n  oint: '∮',\n  olarr: '↺',\n  olcir: '⦾',\n  olcross: '⦻',\n  oline: '‾',\n  olt: '⧀',\n  omacr: 'ō',\n  omega: 'ω',\n  omicron: 'ο',\n  omid: '⦶',\n  ominus: '⊖',\n  oopf: '𝕠',\n  opar: '⦷',\n  operp: '⦹',\n  oplus: '⊕',\n  or: '∨',\n  orarr: '↻',\n  ord: '⩝',\n  order: 'ℴ',\n  orderof: 'ℴ',\n  ordf: 'ª',\n  ordm: 'º',\n  origof: '⊶',\n  oror: '⩖',\n  orslope: '⩗',\n  orv: '⩛',\n  oscr: 'ℴ',\n  oslash: 'ø',\n  osol: '⊘',\n  otilde: 'õ',\n  otimes: '⊗',\n  otimesas: '⨶',\n  ouml: 'ö',\n  ovbar: '⌽',\n  par: '∥',\n  para: '¶',\n  parallel: '∥',\n  parsim: '⫳',\n  parsl: '⫽',\n  part: '∂',\n  pcy: 'п',\n  percnt: '%',\n  period: '.',\n  permil: '‰',\n  perp: '⊥',\n  pertenk: '‱',\n  pfr: '𝔭',\n  phi: 'φ',\n  phiv: 'ϕ',\n  phmmat: 'ℳ',\n  phone: '☎',\n  pi: 'π',\n  pitchfork: '⋔',\n  piv: 'ϖ',\n  planck: 'ℏ',\n  planckh: 'ℎ',\n  plankv: 'ℏ',\n  plus: '+',\n  plusacir: '⨣',\n  plusb: '⊞',\n  pluscir: '⨢',\n  plusdo: '∔',\n  plusdu: '⨥',\n  pluse: '⩲',\n  plusmn: '±',\n  plussim: '⨦',\n  plustwo: '⨧',\n  pm: '±',\n  pointint: '⨕',\n  popf: '𝕡',\n  pound: '£',\n  pr: '≺',\n  prE: '⪳',\n  prap: '⪷',\n  prcue: '≼',\n  pre: '⪯',\n  prec: '≺',\n  precapprox: '⪷',\n  preccurlyeq: '≼',\n  preceq: '⪯',\n  precnapprox: '⪹',\n  precneqq: '⪵',\n  precnsim: '⋨',\n  precsim: '≾',\n  prime: '′',\n  primes: 'ℙ',\n  prnE: '⪵',\n  prnap: '⪹',\n  prnsim: '⋨',\n  prod: '∏',\n  profalar: '⌮',\n  profline: '⌒',\n  profsurf: '⌓',\n  prop: '∝',\n  propto: '∝',\n  prsim: '≾',\n  prurel: '⊰',\n  pscr: '𝓅',\n  psi: 'ψ',\n  puncsp: ' ',\n  qfr: '𝔮',\n  qint: '⨌',\n  qopf: '𝕢',\n  qprime: '⁗',\n  qscr: '𝓆',\n  quaternions: 'ℍ',\n  quatint: '⨖',\n  quest: '?',\n  questeq: '≟',\n  quot: '\"',\n  rAarr: '⇛',\n  rArr: '⇒',\n  rAtail: '⤜',\n  rBarr: '⤏',\n  rHar: '⥤',\n  race: '∽̱',\n  racute: 'ŕ',\n  radic: '√',\n  raemptyv: '⦳',\n  rang: '⟩',\n  rangd: '⦒',\n  range: '⦥',\n  rangle: '⟩',\n  raquo: '»',\n  rarr: '→',\n  rarrap: '⥵',\n  rarrb: '⇥',\n  rarrbfs: '⤠',\n  rarrc: '⤳',\n  rarrfs: '⤞',\n  rarrhk: '↪',\n  rarrlp: '↬',\n  rarrpl: '⥅',\n  rarrsim: '⥴',\n  rarrtl: '↣',\n  rarrw: '↝',\n  ratail: '⤚',\n  ratio: '∶',\n  rationals: 'ℚ',\n  rbarr: '⤍',\n  rbbrk: '❳',\n  rbrace: '}',\n  rbrack: ']',\n  rbrke: '⦌',\n  rbrksld: '⦎',\n  rbrkslu: '⦐',\n  rcaron: 'ř',\n  rcedil: 'ŗ',\n  rceil: '⌉',\n  rcub: '}',\n  rcy: 'р',\n  rdca: '⤷',\n  rdldhar: '⥩',\n  rdquo: '”',\n  rdquor: '”',\n  rdsh: '↳',\n  real: 'ℜ',\n  realine: 'ℛ',\n  realpart: 'ℜ',\n  reals: 'ℝ',\n  rect: '▭',\n  reg: '®',\n  rfisht: '⥽',\n  rfloor: '⌋',\n  rfr: '𝔯',\n  rhard: '⇁',\n  rharu: '⇀',\n  rharul: '⥬',\n  rho: 'ρ',\n  rhov: 'ϱ',\n  rightarrow: '→',\n  rightarrowtail: '↣',\n  rightharpoondown: '⇁',\n  rightharpoonup: '⇀',\n  rightleftarrows: '⇄',\n  rightleftharpoons: '⇌',\n  rightrightarrows: '⇉',\n  rightsquigarrow: '↝',\n  rightthreetimes: '⋌',\n  ring: '˚',\n  risingdotseq: '≓',\n  rlarr: '⇄',\n  rlhar: '⇌',\n  rlm: '‏',\n  rmoust: '⎱',\n  rmoustache: '⎱',\n  rnmid: '⫮',\n  roang: '⟭',\n  roarr: '⇾',\n  robrk: '⟧',\n  ropar: '⦆',\n  ropf: '𝕣',\n  roplus: '⨮',\n  rotimes: '⨵',\n  rpar: ')',\n  rpargt: '⦔',\n  rppolint: '⨒',\n  rrarr: '⇉',\n  rsaquo: '›',\n  rscr: '𝓇',\n  rsh: '↱',\n  rsqb: ']',\n  rsquo: '’',\n  rsquor: '’',\n  rthree: '⋌',\n  rtimes: '⋊',\n  rtri: '▹',\n  rtrie: '⊵',\n  rtrif: '▸',\n  rtriltri: '⧎',\n  ruluhar: '⥨',\n  rx: '℞',\n  sacute: 'ś',\n  sbquo: '‚',\n  sc: '≻',\n  scE: '⪴',\n  scap: '⪸',\n  scaron: 'š',\n  sccue: '≽',\n  sce: '⪰',\n  scedil: 'ş',\n  scirc: 'ŝ',\n  scnE: '⪶',\n  scnap: '⪺',\n  scnsim: '⋩',\n  scpolint: '⨓',\n  scsim: '≿',\n  scy: 'с',\n  sdot: '⋅',\n  sdotb: '⊡',\n  sdote: '⩦',\n  seArr: '⇘',\n  searhk: '⤥',\n  searr: '↘',\n  searrow: '↘',\n  sect: '§',\n  semi: ';',\n  seswar: '⤩',\n  setminus: '∖',\n  setmn: '∖',\n  sext: '✶',\n  sfr: '𝔰',\n  sfrown: '⌢',\n  sharp: '♯',\n  shchcy: 'щ',\n  shcy: 'ш',\n  shortmid: '∣',\n  shortparallel: '∥',\n  shy: '­',\n  sigma: 'σ',\n  sigmaf: 'ς',\n  sigmav: 'ς',\n  sim: '∼',\n  simdot: '⩪',\n  sime: '≃',\n  simeq: '≃',\n  simg: '⪞',\n  simgE: '⪠',\n  siml: '⪝',\n  simlE: '⪟',\n  simne: '≆',\n  simplus: '⨤',\n  simrarr: '⥲',\n  slarr: '←',\n  smallsetminus: '∖',\n  smashp: '⨳',\n  smeparsl: '⧤',\n  smid: '∣',\n  smile: '⌣',\n  smt: '⪪',\n  smte: '⪬',\n  smtes: '⪬︀',\n  softcy: 'ь',\n  sol: '/',\n  solb: '⧄',\n  solbar: '⌿',\n  sopf: '𝕤',\n  spades: '♠',\n  spadesuit: '♠',\n  spar: '∥',\n  sqcap: '⊓',\n  sqcaps: '⊓︀',\n  sqcup: '⊔',\n  sqcups: '⊔︀',\n  sqsub: '⊏',\n  sqsube: '⊑',\n  sqsubset: '⊏',\n  sqsubseteq: '⊑',\n  sqsup: '⊐',\n  sqsupe: '⊒',\n  sqsupset: '⊐',\n  sqsupseteq: '⊒',\n  squ: '□',\n  square: '□',\n  squarf: '▪',\n  squf: '▪',\n  srarr: '→',\n  sscr: '𝓈',\n  ssetmn: '∖',\n  ssmile: '⌣',\n  sstarf: '⋆',\n  star: '☆',\n  starf: '★',\n  straightepsilon: 'ϵ',\n  straightphi: 'ϕ',\n  strns: '¯',\n  sub: '⊂',\n  subE: '⫅',\n  subdot: '⪽',\n  sube: '⊆',\n  subedot: '⫃',\n  submult: '⫁',\n  subnE: '⫋',\n  subne: '⊊',\n  subplus: '⪿',\n  subrarr: '⥹',\n  subset: '⊂',\n  subseteq: '⊆',\n  subseteqq: '⫅',\n  subsetneq: '⊊',\n  subsetneqq: '⫋',\n  subsim: '⫇',\n  subsub: '⫕',\n  subsup: '⫓',\n  succ: '≻',\n  succapprox: '⪸',\n  succcurlyeq: '≽',\n  succeq: '⪰',\n  succnapprox: '⪺',\n  succneqq: '⪶',\n  succnsim: '⋩',\n  succsim: '≿',\n  sum: '∑',\n  sung: '♪',\n  sup1: '¹',\n  sup2: '²',\n  sup3: '³',\n  sup: '⊃',\n  supE: '⫆',\n  supdot: '⪾',\n  supdsub: '⫘',\n  supe: '⊇',\n  supedot: '⫄',\n  suphsol: '⟉',\n  suphsub: '⫗',\n  suplarr: '⥻',\n  supmult: '⫂',\n  supnE: '⫌',\n  supne: '⊋',\n  supplus: '⫀',\n  supset: '⊃',\n  supseteq: '⊇',\n  supseteqq: '⫆',\n  supsetneq: '⊋',\n  supsetneqq: '⫌',\n  supsim: '⫈',\n  supsub: '⫔',\n  supsup: '⫖',\n  swArr: '⇙',\n  swarhk: '⤦',\n  swarr: '↙',\n  swarrow: '↙',\n  swnwar: '⤪',\n  szlig: 'ß',\n  target: '⌖',\n  tau: 'τ',\n  tbrk: '⎴',\n  tcaron: 'ť',\n  tcedil: 'ţ',\n  tcy: 'т',\n  tdot: '⃛',\n  telrec: '⌕',\n  tfr: '𝔱',\n  there4: '∴',\n  therefore: '∴',\n  theta: 'θ',\n  thetasym: 'ϑ',\n  thetav: 'ϑ',\n  thickapprox: '≈',\n  thicksim: '∼',\n  thinsp: ' ',\n  thkap: '≈',\n  thksim: '∼',\n  thorn: 'þ',\n  tilde: '˜',\n  times: '×',\n  timesb: '⊠',\n  timesbar: '⨱',\n  timesd: '⨰',\n  tint: '∭',\n  toea: '⤨',\n  top: '⊤',\n  topbot: '⌶',\n  topcir: '⫱',\n  topf: '𝕥',\n  topfork: '⫚',\n  tosa: '⤩',\n  tprime: '‴',\n  trade: '™',\n  triangle: '▵',\n  triangledown: '▿',\n  triangleleft: '◃',\n  trianglelefteq: '⊴',\n  triangleq: '≜',\n  triangleright: '▹',\n  trianglerighteq: '⊵',\n  tridot: '◬',\n  trie: '≜',\n  triminus: '⨺',\n  triplus: '⨹',\n  trisb: '⧍',\n  tritime: '⨻',\n  trpezium: '⏢',\n  tscr: '𝓉',\n  tscy: 'ц',\n  tshcy: 'ћ',\n  tstrok: 'ŧ',\n  twixt: '≬',\n  twoheadleftarrow: '↞',\n  twoheadrightarrow: '↠',\n  uArr: '⇑',\n  uHar: '⥣',\n  uacute: 'ú',\n  uarr: '↑',\n  ubrcy: 'ў',\n  ubreve: 'ŭ',\n  ucirc: 'û',\n  ucy: 'у',\n  udarr: '⇅',\n  udblac: 'ű',\n  udhar: '⥮',\n  ufisht: '⥾',\n  ufr: '𝔲',\n  ugrave: 'ù',\n  uharl: '↿',\n  uharr: '↾',\n  uhblk: '▀',\n  ulcorn: '⌜',\n  ulcorner: '⌜',\n  ulcrop: '⌏',\n  ultri: '◸',\n  umacr: 'ū',\n  uml: '¨',\n  uogon: 'ų',\n  uopf: '𝕦',\n  uparrow: '↑',\n  updownarrow: '↕',\n  upharpoonleft: '↿',\n  upharpoonright: '↾',\n  uplus: '⊎',\n  upsi: 'υ',\n  upsih: 'ϒ',\n  upsilon: 'υ',\n  upuparrows: '⇈',\n  urcorn: '⌝',\n  urcorner: '⌝',\n  urcrop: '⌎',\n  uring: 'ů',\n  urtri: '◹',\n  uscr: '𝓊',\n  utdot: '⋰',\n  utilde: 'ũ',\n  utri: '▵',\n  utrif: '▴',\n  uuarr: '⇈',\n  uuml: 'ü',\n  uwangle: '⦧',\n  vArr: '⇕',\n  vBar: '⫨',\n  vBarv: '⫩',\n  vDash: '⊨',\n  vangrt: '⦜',\n  varepsilon: 'ϵ',\n  varkappa: 'ϰ',\n  varnothing: '∅',\n  varphi: 'ϕ',\n  varpi: 'ϖ',\n  varpropto: '∝',\n  varr: '↕',\n  varrho: 'ϱ',\n  varsigma: 'ς',\n  varsubsetneq: '⊊︀',\n  varsubsetneqq: '⫋︀',\n  varsupsetneq: '⊋︀',\n  varsupsetneqq: '⫌︀',\n  vartheta: 'ϑ',\n  vartriangleleft: '⊲',\n  vartriangleright: '⊳',\n  vcy: 'в',\n  vdash: '⊢',\n  vee: '∨',\n  veebar: '⊻',\n  veeeq: '≚',\n  vellip: '⋮',\n  verbar: '|',\n  vert: '|',\n  vfr: '𝔳',\n  vltri: '⊲',\n  vnsub: '⊂⃒',\n  vnsup: '⊃⃒',\n  vopf: '𝕧',\n  vprop: '∝',\n  vrtri: '⊳',\n  vscr: '𝓋',\n  vsubnE: '⫋︀',\n  vsubne: '⊊︀',\n  vsupnE: '⫌︀',\n  vsupne: '⊋︀',\n  vzigzag: '⦚',\n  wcirc: 'ŵ',\n  wedbar: '⩟',\n  wedge: '∧',\n  wedgeq: '≙',\n  weierp: '℘',\n  wfr: '𝔴',\n  wopf: '𝕨',\n  wp: '℘',\n  wr: '≀',\n  wreath: '≀',\n  wscr: '𝓌',\n  xcap: '⋂',\n  xcirc: '◯',\n  xcup: '⋃',\n  xdtri: '▽',\n  xfr: '𝔵',\n  xhArr: '⟺',\n  xharr: '⟷',\n  xi: 'ξ',\n  xlArr: '⟸',\n  xlarr: '⟵',\n  xmap: '⟼',\n  xnis: '⋻',\n  xodot: '⨀',\n  xopf: '𝕩',\n  xoplus: '⨁',\n  xotime: '⨂',\n  xrArr: '⟹',\n  xrarr: '⟶',\n  xscr: '𝓍',\n  xsqcup: '⨆',\n  xuplus: '⨄',\n  xutri: '△',\n  xvee: '⋁',\n  xwedge: '⋀',\n  yacute: 'ý',\n  yacy: 'я',\n  ycirc: 'ŷ',\n  ycy: 'ы',\n  yen: '¥',\n  yfr: '𝔶',\n  yicy: 'ї',\n  yopf: '𝕪',\n  yscr: '𝓎',\n  yucy: 'ю',\n  yuml: 'ÿ',\n  zacute: 'ź',\n  zcaron: 'ž',\n  zcy: 'з',\n  zdot: 'ż',\n  zeetrf: 'ℨ',\n  zeta: 'ζ',\n  zfr: '𝔷',\n  zhcy: 'ж',\n  zigrarr: '⇝',\n  zopf: '𝕫',\n  zscr: '𝓏',\n  zwj: '‍',\n  zwnj: '‌'\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,MAAM,oBAAoB;IAC/B,OAAO;IACP,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,KAAK;IACL,QAAQ;IACR,OAAO;IACP,OAAO;IACP,KAAK;IACL,OAAO;IACP,MAAM;IACN,eAAe;IACf,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,WAAW;IACX,MAAM;IACN,QAAQ;IACR,KAAK;IACL,SAAS;IACT,YAAY;IACZ,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,KAAK;IACL,sBAAsB;IACtB,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,MAAM;IACN,SAAS;IACT,WAAW;IACX,KAAK;IACL,KAAK;IACL,WAAW;IACX,aAAa;IACb,YAAY;IACZ,aAAa;IACb,0BAA0B;IAC1B,uBAAuB;IACvB,iBAAiB;IACjB,OAAO;IACP,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,iBAAiB;IACjB,MAAM;IACN,WAAW;IACX,iCAAiC;IACjC,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,IAAI;IACJ,UAAU;IACV,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,OAAO;IACP,KAAK;IACL,kBAAkB;IAClB,gBAAgB;IAChB,wBAAwB;IACxB,kBAAkB;IAClB,kBAAkB;IAClB,SAAS;IACT,eAAe;IACf,MAAM;IACN,KAAK;IACL,QAAQ;IACR,UAAU;IACV,uBAAuB;IACvB,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,qBAAqB;IACrB,0BAA0B;IAC1B,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,mBAAmB;IACnB,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,WAAW;IACX,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,iBAAiB;IACjB,oBAAoB;IACpB,SAAS;IACT,cAAc;IACd,WAAW;IACX,MAAM;IACN,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,MAAM;IACN,KAAK;IACL,QAAQ;IACR,SAAS;IACT,OAAO;IACP,kBAAkB;IAClB,sBAAsB;IACtB,OAAO;IACP,MAAM;IACN,SAAS;IACT,OAAO;IACP,YAAY;IACZ,aAAa;IACb,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,QAAQ;IACR,cAAc;IACd,KAAK;IACL,KAAK;IACL,mBAAmB;IACnB,uBAAuB;IACvB,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,MAAM;IACN,MAAM;IACN,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,MAAM;IACN,KAAK;IACL,IAAI;IACJ,MAAM;IACN,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,aAAa;IACb,mBAAmB;IACnB,cAAc;IACd,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,KAAK;IACL,OAAO;IACP,KAAK;IACL,cAAc;IACd,MAAM;IACN,gBAAgB;IAChB,MAAM;IACN,QAAQ;IACR,cAAc;IACd,WAAW;IACX,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,KAAK;IACL,MAAM;IACN,KAAK;IACL,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,YAAY;IACZ,SAAS;IACT,KAAK;IACL,UAAU;IACV,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,YAAY;IACZ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,kBAAkB;IAClB,WAAW;IACX,cAAc;IACd,qBAAqB;IACrB,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,SAAS;IACT,cAAc;IACd,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;IACd,iBAAiB;IACjB,YAAY;IACZ,eAAe;IACf,WAAW;IACX,gBAAgB;IAChB,kBAAkB;IAClB,eAAe;IACf,aAAa;IACb,UAAU;IACV,gBAAgB;IAChB,WAAW;IACX,KAAK;IACL,IAAI;IACJ,YAAY;IACZ,QAAQ;IACR,eAAe;IACf,oBAAoB;IACpB,gBAAgB;IAChB,eAAe;IACf,oBAAoB;IACpB,gBAAgB;IAChB,MAAM;IACN,gBAAgB;IAChB,iBAAiB;IACjB,MAAM;IACN,KAAK;IACL,QAAQ;IACR,IAAI;IACJ,KAAK;IACL,KAAK;IACL,aAAa;IACb,WAAW;IACX,KAAK;IACL,WAAW;IACX,MAAM;IACN,MAAM;IACN,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,qBAAqB;IACrB,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,sBAAsB;IACtB,gBAAgB;IAChB,SAAS;IACT,KAAK;IACL,SAAS;IACT,kBAAkB;IAClB,MAAM;IACN,KAAK;IACL,cAAc;IACd,WAAW;IACX,sBAAsB;IACtB,YAAY;IACZ,UAAU;IACV,eAAe;IACf,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,sBAAsB;IACtB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,iBAAiB;IACjB,oBAAoB;IACpB,sBAAsB;IACtB,SAAS;IACT,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,mBAAmB;IACnB,cAAc;IACd,yBAAyB;IACzB,mBAAmB;IACnB,aAAa;IACb,kBAAkB;IAClB,uBAAuB;IACvB,mBAAmB;IACnB,kBAAkB;IAClB,qBAAqB;IACrB,uBAAuB;IACvB,iBAAiB;IACjB,sBAAsB;IACtB,mBAAmB;IACnB,wBAAwB;IACxB,WAAW;IACX,gBAAgB;IAChB,aAAa;IACb,kBAAkB;IAClB,uBAAuB;IACvB,kBAAkB;IAClB,aAAa;IACb,kBAAkB;IAClB,UAAU;IACV,eAAe;IACf,mBAAmB;IACnB,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,OAAO;IACP,KAAK;IACL,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,sBAAsB;IACtB,gBAAgB;IAChB,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,SAAS;IACT,WAAW;IACX,aAAa;IACb,iBAAiB;IACjB,UAAU;IACV,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,WAAW;IACX,eAAe;IACf,MAAM;IACN,IAAI;IACJ,UAAU;IACV,eAAe;IACf,oBAAoB;IACpB,eAAe;IACf,OAAO;IACP,SAAS;IACT,YAAY;IACZ,cAAc;IACd,MAAM;IACN,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,IAAI;IACJ,gBAAgB;IAChB,oBAAoB;IACpB,sBAAsB;IACtB,KAAK;IACL,KAAK;IACL,mBAAmB;IACnB,YAAY;IACZ,eAAe;IACf,qBAAqB;IACrB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB;IACjB,oBAAoB;IACpB,YAAY;IACZ,UAAU;IACV,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,kBAAkB;IAClB,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,gBAAgB;IAChB,YAAY;IACZ,MAAM;IACN,cAAc;IACd,aAAa;IACb,MAAM;IACN,KAAK;IACL,aAAa;IACb,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,KAAK;IACL,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,OAAO;IACP,aAAa;IACb,MAAM;IACN,MAAM;IACN,QAAQ;IACR,oBAAoB;IACpB,cAAc;IACd,mBAAmB;IACnB,gBAAgB;IAChB,qBAAqB;IACrB,aAAa;IACb,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,aAAa;IACb,UAAU;IACV,eAAe;IACf,oBAAoB;IACpB,eAAe;IACf,UAAU;IACV,KAAK;IACL,KAAK;IACL,UAAU;IACV,eAAe;IACf,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,WAAW;IACX,OAAO;IACP,YAAY;IACZ,WAAW;IACX,OAAO;IACP,YAAY;IACZ,gBAAgB;IAChB,YAAY;IACZ,MAAM;IACN,WAAW;IACX,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,QAAQ;IACR,OAAO;IACP,KAAK;IACL,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,OAAO;IACP,UAAU;IACV,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,OAAO;IACP,WAAW;IACX,OAAO;IACP,MAAM;IACN,SAAS;IACT,YAAY;IACZ,kBAAkB;IAClB,aAAa;IACb,eAAe;IACf,OAAO;IACP,YAAY;IACZ,SAAS;IACT,aAAa;IACb,gBAAgB;IAChB,iBAAiB;IACjB,MAAM;IACN,SAAS;IACT,OAAO;IACP,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,aAAa;IACb,cAAc;IACd,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,IAAI;IACJ,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,gBAAgB;IAChB,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,KAAK;IACL,OAAO;IACP,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,SAAS;IACT,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,QAAQ;IACR,MAAM;IACN,UAAU;IACV,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,OAAO;IACP,MAAM;IACN,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,OAAO;IACP,SAAS;IACT,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,UAAU;IACV,aAAa;IACb,WAAW;IACX,SAAS;IACT,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,MAAM;IACN,UAAU;IACV,OAAO;IACP,KAAK;IACL,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,KAAK;IACL,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,UAAU;IACV,WAAW;IACX,UAAU;IACV,SAAS;IACT,iBAAiB;IACjB,eAAe;IACf,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,cAAc;IACd,aAAa;IACb,eAAe;IACf,mBAAmB;IACnB,mBAAmB;IACnB,oBAAoB;IACpB,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;IACT,UAAU;IACV,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,UAAU;IACV,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;IACP,SAAS;IACT,MAAM;IACN,WAAW;IACX,KAAK;IACL,MAAM;IACN,OAAO;IACP,WAAW;IACX,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,UAAU;IACV,YAAY;IACZ,aAAa;IACb,aAAa;IACb,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,OAAO;IACP,UAAU;IACV,OAAO;IACP,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,MAAM;IACN,SAAS;IACT,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,KAAK;IACL,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,SAAS;IACT,aAAa;IACb,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,gBAAgB;IAChB,iBAAiB;IACjB,OAAO;IACP,OAAO;IACP,UAAU;IACV,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,KAAK;IACL,IAAI;IACJ,SAAS;IACT,OAAO;IACP,SAAS;IACT,KAAK;IACL,OAAO;IACP,SAAS;IACT,QAAQ;IACR,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,aAAa;IACb,OAAO;IACP,KAAK;IACL,SAAS;IACT,OAAO;IACP,KAAK;IACL,QAAQ;IACR,eAAe;IACf,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;IACP,UAAU;IACV,UAAU;IACV,SAAS;IACT,WAAW;IACX,gBAAgB;IAChB,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,UAAU;IACV,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;IACN,IAAI;IACJ,OAAO;IACP,KAAK;IACL,IAAI;IACJ,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,IAAI;IACJ,UAAU;IACV,KAAK;IACL,KAAK;IACL,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,OAAO;IACP,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,aAAa;IACb,cAAc;IACd,eAAe;IACf,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,OAAO;IACP,KAAK;IACL,MAAM;IACN,IAAI;IACJ,KAAK;IACL,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,QAAQ;IACR,KAAK;IACL,IAAI;IACJ,KAAK;IACL,OAAO;IACP,MAAM;IACN,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,IAAI;IACJ,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,SAAS;IACT,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,UAAU;IACV,UAAU;IACV,OAAO;IACP,QAAQ;IACR,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,QAAQ;IACR,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,UAAU;IACV,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,KAAK;IACL,MAAM;IACN,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,IAAI;IACJ,WAAW;IACX,eAAe;IACf,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,qBAAqB;IACrB,gBAAgB;IAChB,KAAK;IACL,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,SAAS;IACT,WAAW;IACX,YAAY;IACZ,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,IAAI;IACJ,KAAK;IACL,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,IAAI;IACJ,OAAO;IACP,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,eAAe;IACf,oBAAoB;IACpB,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,UAAU;IACV,OAAO;IACP,QAAQ;IACR,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;IACT,WAAW;IACX,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,SAAS;IACT,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,OAAO;IACP,eAAe;IACf,KAAK;IACL,KAAK;IACL,OAAO;IACP,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,IAAI;IACJ,UAAU;IACV,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,YAAY;IACZ,iBAAiB;IACjB,KAAK;IACL,KAAK;IACL,MAAM;IACN,aAAa;IACb,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,SAAS;IACT,UAAU;IACV,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,KAAK;IACL,OAAO;IACP,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,WAAW;IACX,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,IAAI;IACJ,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,KAAK;IACL,OAAO;IACP,MAAM;IACN,KAAK;IACL,YAAY;IACZ,iBAAiB;IACjB,MAAM;IACN,OAAO;IACP,WAAW;IACX,MAAM;IACN,OAAO;IACP,OAAO;IACP,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;IACR,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,SAAS;IACT,SAAS;IACT,SAAS;IACT,MAAM;IACN,WAAW;IACX,QAAQ;IACR,OAAO;IACP,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,WAAW;IACX,gBAAgB;IAChB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,WAAW;IACX,YAAY;IACZ,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,QAAQ;IACR,MAAM;IACN,eAAe;IACf,iBAAiB;IACjB,gBAAgB;IAChB,kBAAkB;IAClB,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,QAAQ;IACR,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,KAAK;IACL,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,KAAK;IACL,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,IAAI;IACJ,OAAO;IACP,KAAK;IACL,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,SAAS;IACT,KAAK;IACL,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,UAAU;IACV,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,SAAS;IACT,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,WAAW;IACX,KAAK;IACL,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;IACT,IAAI;IACJ,UAAU;IACV,MAAM;IACN,OAAO;IACP,IAAI;IACJ,KAAK;IACL,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,UAAU;IACV,UAAU;IACV,UAAU;IACV,MAAM;IACN,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,QAAQ;IACR,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,aAAa;IACb,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,WAAW;IACX,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,OAAO;IACP,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;IACN,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,iBAAiB;IACjB,MAAM;IACN,cAAc;IACd,OAAO;IACP,OAAO;IACP,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,SAAS;IACT,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;IACT,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,KAAK;IACL,MAAM;IACN,QAAQ;IACR,OAAO;IACP,KAAK;IACL,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,UAAU;IACV,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,UAAU;IACV,eAAe;IACf,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,eAAe;IACf,QAAQ;IACR,UAAU;IACV,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,WAAW;IACX,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,iBAAiB;IACjB,aAAa;IACb,OAAO;IACP,KAAK;IACL,MAAM;IACN,QAAQ;IACR,MAAM;IACN,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,UAAU;IACV,WAAW;IACX,WAAW;IACX,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;IACN,QAAQ;IACR,SAAS;IACT,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,SAAS;IACT,QAAQ;IACR,UAAU;IACV,WAAW;IACX,WAAW;IACX,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,SAAS;IACT,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,WAAW;IACX,OAAO;IACP,UAAU;IACV,QAAQ;IACR,aAAa;IACb,UAAU;IACV,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;IACP,UAAU;IACV,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,WAAW;IACX,eAAe;IACf,iBAAiB;IACjB,QAAQ;IACR,MAAM;IACN,UAAU;IACV,SAAS;IACT,OAAO;IACP,SAAS;IACT,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,kBAAkB;IAClB,mBAAmB;IACnB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,KAAK;IACL,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,KAAK;IACL,OAAO;IACP,MAAM;IACN,SAAS;IACT,aAAa;IACb,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,WAAW;IACX,MAAM;IACN,QAAQ;IACR,UAAU;IACV,cAAc;IACd,eAAe;IACf,cAAc;IACd,eAAe;IACf,UAAU;IACV,iBAAiB;IACjB,kBAAkB;IAClB,KAAK;IACL,OAAO;IACP,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,KAAK;IACL,OAAO;IACP,OAAO;IACP,IAAI;IACJ,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10536, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/decode-named-character-reference/index.js"], "sourcesContent": ["import {characterEntities} from 'character-entities'\n\n// To do: next major: use `Object.hasOwn`.\nconst own = {}.hasOwnProperty\n\n/**\n * Decode a single character reference (without the `&` or `;`).\n * You probably only need this when you’re building parsers yourself that follow\n * different rules compared to HTML.\n * This is optimized to be tiny in browsers.\n *\n * @param {string} value\n *   `notin` (named), `#123` (deci), `#x123` (hexa).\n * @returns {string|false}\n *   Decoded reference.\n */\nexport function decodeNamedCharacterReference(value) {\n  return own.call(characterEntities, value) ? characterEntities[value] : false\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,0CAA0C;AAC1C,MAAM,MAAM,CAAC,EAAE,cAAc;AAatB,SAAS,8BAA8B,KAAK;IACjD,OAAO,IAAI,IAAI,CAAC,8IAAA,CAAA,oBAAiB,EAAE,SAAS,8IAAA,CAAA,oBAAiB,CAAC,MAAM,GAAG;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10550, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/parse-entities/lib/index.js"], "sourcesContent": ["/**\n * @import {Point} from 'unist'\n * @import {Options} from '../index.js'\n */\n\nimport {characterEntitiesLegacy} from 'character-entities-legacy'\nimport {characterReferenceInvalid} from 'character-reference-invalid'\nimport {isDecimal} from 'is-decimal'\nimport {isHexadecimal} from 'is-hexadecimal'\nimport {isAlphanumerical} from 'is-alphanumerical'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\n\n// Warning messages.\nconst messages = [\n  '',\n  /* 1: Non terminated (named) */\n  'Named character references must be terminated by a semicolon',\n  /* 2: Non terminated (numeric) */\n  'Numeric character references must be terminated by a semicolon',\n  /* 3: Empty (named) */\n  'Named character references cannot be empty',\n  /* 4: Empty (numeric) */\n  'Numeric character references cannot be empty',\n  /* 5: Unknown (named) */\n  'Named character references must be known',\n  /* 6: Disallowed (numeric) */\n  'Numeric character references cannot be disallowed',\n  /* 7: Prohibited (numeric) */\n  'Numeric character references cannot be outside the permissible Unicode range'\n]\n\n/**\n * Parse HTML character references.\n *\n * @param {string} value\n * @param {Readonly<Options> | null | undefined} [options]\n */\nexport function parseEntities(value, options) {\n  const settings = options || {}\n  const additional =\n    typeof settings.additional === 'string'\n      ? settings.additional.charCodeAt(0)\n      : settings.additional\n  /** @type {Array<string>} */\n  const result = []\n  let index = 0\n  let lines = -1\n  let queue = ''\n  /** @type {Point | undefined} */\n  let point\n  /** @type {Array<number>|undefined} */\n  let indent\n\n  if (settings.position) {\n    if ('start' in settings.position || 'indent' in settings.position) {\n      // @ts-expect-error: points don’t have indent.\n      indent = settings.position.indent\n      // @ts-expect-error: points don’t have indent.\n      point = settings.position.start\n    } else {\n      point = settings.position\n    }\n  }\n\n  let line = (point ? point.line : 0) || 1\n  let column = (point ? point.column : 0) || 1\n\n  // Cache the current point.\n  let previous = now()\n  /** @type {number|undefined} */\n  let character\n\n  // Ensure the algorithm walks over the first character (inclusive).\n  index--\n\n  while (++index <= value.length) {\n    // If the previous character was a newline.\n    if (character === 10 /* `\\n` */) {\n      column = (indent ? indent[lines] : 0) || 1\n    }\n\n    character = value.charCodeAt(index)\n\n    if (character === 38 /* `&` */) {\n      const following = value.charCodeAt(index + 1)\n\n      // The behavior depends on the identity of the next character.\n      if (\n        following === 9 /* `\\t` */ ||\n        following === 10 /* `\\n` */ ||\n        following === 12 /* `\\f` */ ||\n        following === 32 /* ` ` */ ||\n        following === 38 /* `&` */ ||\n        following === 60 /* `<` */ ||\n        Number.isNaN(following) ||\n        (additional && following === additional)\n      ) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += String.fromCharCode(character)\n        column++\n        continue\n      }\n\n      const start = index + 1\n      let begin = start\n      let end = start\n      /** @type {string} */\n      let type\n\n      if (following === 35 /* `#` */) {\n        // Numerical reference.\n        end = ++begin\n\n        // The behavior further depends on the next character.\n        const following = value.charCodeAt(end)\n\n        if (following === 88 /* `X` */ || following === 120 /* `x` */) {\n          // ASCII hexadecimal digits.\n          type = 'hexadecimal'\n          end = ++begin\n        } else {\n          // ASCII decimal digits.\n          type = 'decimal'\n        }\n      } else {\n        // Named reference.\n        type = 'named'\n      }\n\n      let characterReferenceCharacters = ''\n      let characterReference = ''\n      let characters = ''\n      // Each type of character reference accepts different characters.\n      // This test is used to detect whether a reference has ended (as the semicolon\n      // is not strictly needed).\n      const test =\n        type === 'named'\n          ? isAlphanumerical\n          : type === 'decimal'\n            ? isDecimal\n            : isHexadecimal\n\n      end--\n\n      while (++end <= value.length) {\n        const following = value.charCodeAt(end)\n\n        if (!test(following)) {\n          break\n        }\n\n        characters += String.fromCharCode(following)\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === 'named' && characterEntitiesLegacy.includes(characters)) {\n          characterReferenceCharacters = characters\n          // @ts-expect-error: always able to decode.\n          characterReference = decodeNamedCharacterReference(characters)\n        }\n      }\n\n      let terminated = value.charCodeAt(end) === 59 /* `;` */\n\n      if (terminated) {\n        end++\n\n        const namedReference =\n          type === 'named' ? decodeNamedCharacterReference(characters) : false\n\n        if (namedReference) {\n          characterReferenceCharacters = characters\n          characterReference = namedReference\n        }\n      }\n\n      let diff = 1 + end - start\n      let reference = ''\n\n      if (!terminated && settings.nonTerminated === false) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) reference is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== 'named') {\n          warning(4 /* Empty (numeric) */, diff)\n        }\n      } else if (type === 'named') {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !characterReference) {\n          warning(5 /* Unknown (named) */, 1)\n        } else {\n          // If there’s something after an named reference which is not known,\n          // cap the reference.\n          if (characterReferenceCharacters !== characters) {\n            end = begin + characterReferenceCharacters.length\n            diff = 1 + end - begin\n            terminated = false\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            const reason = characterReferenceCharacters\n              ? 1 /* Non terminated (named) */\n              : 3 /* Empty (named) */\n\n            if (settings.attribute) {\n              const following = value.charCodeAt(end)\n\n              if (following === 61 /* `=` */) {\n                warning(reason, diff)\n                characterReference = ''\n              } else if (isAlphanumerical(following)) {\n                characterReference = ''\n              } else {\n                warning(reason, diff)\n              }\n            } else {\n              warning(reason, diff)\n            }\n          }\n        }\n\n        reference = characterReference\n      } else {\n        if (!terminated) {\n          // All nonterminated numeric references are not rendered, and emit a\n          // warning.\n          warning(2 /* Non terminated (numeric) */, diff)\n        }\n\n        // When terminated and numerical, parse as either hexadecimal or\n        // decimal.\n        let referenceCode = Number.parseInt(\n          characters,\n          type === 'hexadecimal' ? 16 : 10\n        )\n\n        // Emit a warning when the parsed number is prohibited, and replace with\n        // replacement character.\n        if (prohibited(referenceCode)) {\n          warning(7 /* Prohibited (numeric) */, diff)\n          reference = String.fromCharCode(65533 /* `�` */)\n        } else if (referenceCode in characterReferenceInvalid) {\n          // Emit a warning when the parsed number is disallowed, and replace by\n          // an alternative.\n          warning(6 /* Disallowed (numeric) */, diff)\n          reference = characterReferenceInvalid[referenceCode]\n        } else {\n          // Parse the number.\n          let output = ''\n\n          // Emit a warning when the parsed number should not be used.\n          if (disallowed(referenceCode)) {\n            warning(6 /* Disallowed (numeric) */, diff)\n          }\n\n          // Serialize the number.\n          if (referenceCode > 0xffff) {\n            referenceCode -= 0x10000\n            output += String.fromCharCode(\n              (referenceCode >>> (10 & 0x3ff)) | 0xd800\n            )\n            referenceCode = 0xdc00 | (referenceCode & 0x3ff)\n          }\n\n          reference = output + String.fromCharCode(referenceCode)\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat a reference.\n      if (reference) {\n        flush()\n\n        previous = now()\n        index = end - 1\n        column += end - start + 1\n        result.push(reference)\n        const next = now()\n        next.offset++\n\n        if (settings.reference) {\n          settings.reference.call(\n            settings.referenceContext || undefined,\n            reference,\n            {start: previous, end: next},\n            value.slice(start - 1, end)\n          )\n        }\n\n        previous = next\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end)\n        queue += characters\n        column += characters.length\n        index = end - 1\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (character === 10 /* `\\n` */) {\n        line++\n        lines++\n        column = 0\n      }\n\n      if (Number.isNaN(character)) {\n        flush()\n      } else {\n        queue += String.fromCharCode(character)\n        column++\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('')\n\n  // Get current position.\n  function now() {\n    return {\n      line,\n      column,\n      offset: index + ((point ? point.offset : 0) || 0)\n    }\n  }\n\n  /**\n   * Handle the warning.\n   *\n   * @param {1|2|3|4|5|6|7} code\n   * @param {number} offset\n   */\n  function warning(code, offset) {\n    /** @type {ReturnType<now>} */\n    let position\n\n    if (settings.warning) {\n      position = now()\n      position.column += offset\n      position.offset += offset\n\n      settings.warning.call(\n        settings.warningContext || undefined,\n        messages[code],\n        position,\n        code\n      )\n    }\n  }\n\n  /**\n   * Flush `queue` (normal text).\n   * Macro invoked before each reference and at the end of `value`.\n   * Does nothing when `queue` is empty.\n   */\n  function flush() {\n    if (queue) {\n      result.push(queue)\n\n      if (settings.text) {\n        settings.text.call(settings.textContext || undefined, queue, {\n          start: previous,\n          end: now()\n        })\n      }\n\n      queue = ''\n    }\n  }\n}\n\n/**\n * Check if `character` is outside the permissible unicode range.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction prohibited(code) {\n  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff\n}\n\n/**\n * Check if `character` is disallowed.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction disallowed(code) {\n  return (\n    (code >= 0x0001 && code <= 0x0008) ||\n    code === 0x000b ||\n    (code >= 0x000d && code <= 0x001f) ||\n    (code >= 0x007f && code <= 0x009f) ||\n    (code >= 0xfdd0 && code <= 0xfdef) ||\n    (code & 0xffff) === 0xffff ||\n    (code & 0xffff) === 0xfffe\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,oBAAoB;AACpB,MAAM,WAAW;IACf;IACA,6BAA6B,GAC7B;IACA,+BAA+B,GAC/B;IACA,oBAAoB,GACpB;IACA,sBAAsB,GACtB;IACA,sBAAsB,GACtB;IACA,2BAA2B,GAC3B;IACA,2BAA2B,GAC3B;CACD;AAQM,SAAS,cAAc,KAAK,EAAE,OAAO;IAC1C,MAAM,WAAW,WAAW,CAAC;IAC7B,MAAM,aACJ,OAAO,SAAS,UAAU,KAAK,WAC3B,SAAS,UAAU,CAAC,UAAU,CAAC,KAC/B,SAAS,UAAU;IACzB,0BAA0B,GAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ;IACZ,IAAI,QAAQ,CAAC;IACb,IAAI,QAAQ;IACZ,8BAA8B,GAC9B,IAAI;IACJ,oCAAoC,GACpC,IAAI;IAEJ,IAAI,SAAS,QAAQ,EAAE;QACrB,IAAI,WAAW,SAAS,QAAQ,IAAI,YAAY,SAAS,QAAQ,EAAE;YACjE,8CAA8C;YAC9C,SAAS,SAAS,QAAQ,CAAC,MAAM;YACjC,8CAA8C;YAC9C,QAAQ,SAAS,QAAQ,CAAC,KAAK;QACjC,OAAO;YACL,QAAQ,SAAS,QAAQ;QAC3B;IACF;IAEA,IAAI,OAAO,CAAC,QAAQ,MAAM,IAAI,GAAG,CAAC,KAAK;IACvC,IAAI,SAAS,CAAC,QAAQ,MAAM,MAAM,GAAG,CAAC,KAAK;IAE3C,2BAA2B;IAC3B,IAAI,WAAW;IACf,6BAA6B,GAC7B,IAAI;IAEJ,mEAAmE;IACnE;IAEA,MAAO,EAAE,SAAS,MAAM,MAAM,CAAE;QAC9B,2CAA2C;QAC3C,IAAI,cAAc,GAAG,QAAQ,KAAI;YAC/B,SAAS,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,CAAC,KAAK;QAC3C;QAEA,YAAY,MAAM,UAAU,CAAC;QAE7B,IAAI,cAAc,GAAG,OAAO,KAAI;YAC9B,MAAM,YAAY,MAAM,UAAU,CAAC,QAAQ;YAE3C,8DAA8D;YAC9D,IACE,cAAc,EAAE,QAAQ,OACxB,cAAc,GAAG,QAAQ,OACzB,cAAc,GAAG,QAAQ,OACzB,cAAc,GAAG,OAAO,OACxB,cAAc,GAAG,OAAO,OACxB,cAAc,GAAG,OAAO,OACxB,OAAO,KAAK,CAAC,cACZ,cAAc,cAAc,YAC7B;gBACA,6BAA6B;gBAC7B,uDAAuD;gBACvD,gCAAgC;gBAChC,SAAS,OAAO,YAAY,CAAC;gBAC7B;gBACA;YACF;YAEA,MAAM,QAAQ,QAAQ;YACtB,IAAI,QAAQ;YACZ,IAAI,MAAM;YACV,mBAAmB,GACnB,IAAI;YAEJ,IAAI,cAAc,GAAG,OAAO,KAAI;gBAC9B,uBAAuB;gBACvB,MAAM,EAAE;gBAER,sDAAsD;gBACtD,MAAM,YAAY,MAAM,UAAU,CAAC;gBAEnC,IAAI,cAAc,GAAG,OAAO,OAAM,cAAc,IAAI,OAAO,KAAI;oBAC7D,4BAA4B;oBAC5B,OAAO;oBACP,MAAM,EAAE;gBACV,OAAO;oBACL,wBAAwB;oBACxB,OAAO;gBACT;YACF,OAAO;gBACL,mBAAmB;gBACnB,OAAO;YACT;YAEA,IAAI,+BAA+B;YACnC,IAAI,qBAAqB;YACzB,IAAI,aAAa;YACjB,iEAAiE;YACjE,8EAA8E;YAC9E,2BAA2B;YAC3B,MAAM,OACJ,SAAS,UACL,6IAAA,CAAA,mBAAgB,GAChB,SAAS,YACP,sIAAA,CAAA,YAAS,GACT,0IAAA,CAAA,gBAAa;YAErB;YAEA,MAAO,EAAE,OAAO,MAAM,MAAM,CAAE;gBAC5B,MAAM,YAAY,MAAM,UAAU,CAAC;gBAEnC,IAAI,CAAC,KAAK,YAAY;oBACpB;gBACF;gBAEA,cAAc,OAAO,YAAY,CAAC;gBAElC,kDAAkD;gBAClD,2DAA2D;gBAC3D,uDAAuD;gBACvD,IAAI,SAAS,WAAW,wJAAA,CAAA,0BAAuB,CAAC,QAAQ,CAAC,aAAa;oBACpE,+BAA+B;oBAC/B,2CAA2C;oBAC3C,qBAAqB,CAAA,GAAA,kKAAA,CAAA,gCAA6B,AAAD,EAAE;gBACrD;YACF;YAEA,IAAI,aAAa,MAAM,UAAU,CAAC,SAAS,GAAG,OAAO;YAErD,IAAI,YAAY;gBACd;gBAEA,MAAM,iBACJ,SAAS,UAAU,CAAA,GAAA,kKAAA,CAAA,gCAA6B,AAAD,EAAE,cAAc;gBAEjE,IAAI,gBAAgB;oBAClB,+BAA+B;oBAC/B,qBAAqB;gBACvB;YACF;YAEA,IAAI,OAAO,IAAI,MAAM;YACrB,IAAI,YAAY;YAEhB,IAAI,CAAC,cAAc,SAAS,aAAa,KAAK,OAAO;YACnD,SAAS;YACX,OAAO,IAAI,CAAC,YAAY;gBACtB,uEAAuE;gBACvE,uCAAuC;gBACvC,IAAI,SAAS,SAAS;oBACpB,QAAQ,EAAE,mBAAmB,KAAI;gBACnC;YACF,OAAO,IAAI,SAAS,SAAS;gBAC3B,oEAAoE;gBACpE,WAAW;gBACX,IAAI,cAAc,CAAC,oBAAoB;oBACrC,QAAQ,EAAE,mBAAmB,KAAI;gBACnC,OAAO;oBACL,oEAAoE;oBACpE,qBAAqB;oBACrB,IAAI,iCAAiC,YAAY;wBAC/C,MAAM,QAAQ,6BAA6B,MAAM;wBACjD,OAAO,IAAI,MAAM;wBACjB,aAAa;oBACf;oBAEA,4CAA4C;oBAC5C,IAAI,CAAC,YAAY;wBACf,MAAM,SAAS,+BACX,EAAE,0BAA0B,MAC5B,EAAE,iBAAiB;wBAEvB,IAAI,SAAS,SAAS,EAAE;4BACtB,MAAM,YAAY,MAAM,UAAU,CAAC;4BAEnC,IAAI,cAAc,GAAG,OAAO,KAAI;gCAC9B,QAAQ,QAAQ;gCAChB,qBAAqB;4BACvB,OAAO,IAAI,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;gCACtC,qBAAqB;4BACvB,OAAO;gCACL,QAAQ,QAAQ;4BAClB;wBACF,OAAO;4BACL,QAAQ,QAAQ;wBAClB;oBACF;gBACF;gBAEA,YAAY;YACd,OAAO;gBACL,IAAI,CAAC,YAAY;oBACf,oEAAoE;oBACpE,WAAW;oBACX,QAAQ,EAAE,4BAA4B,KAAI;gBAC5C;gBAEA,gEAAgE;gBAChE,WAAW;gBACX,IAAI,gBAAgB,OAAO,QAAQ,CACjC,YACA,SAAS,gBAAgB,KAAK;gBAGhC,wEAAwE;gBACxE,yBAAyB;gBACzB,IAAI,WAAW,gBAAgB;oBAC7B,QAAQ,EAAE,wBAAwB,KAAI;oBACtC,YAAY,OAAO,YAAY,CAAC,MAAM,OAAO;gBAC/C,OAAO,IAAI,iBAAiB,0JAAA,CAAA,4BAAyB,EAAE;oBACrD,sEAAsE;oBACtE,kBAAkB;oBAClB,QAAQ,EAAE,wBAAwB,KAAI;oBACtC,YAAY,0JAAA,CAAA,4BAAyB,CAAC,cAAc;gBACtD,OAAO;oBACL,oBAAoB;oBACpB,IAAI,SAAS;oBAEb,4DAA4D;oBAC5D,IAAI,WAAW,gBAAgB;wBAC7B,QAAQ,EAAE,wBAAwB,KAAI;oBACxC;oBAEA,wBAAwB;oBACxB,IAAI,gBAAgB,QAAQ;wBAC1B,iBAAiB;wBACjB,UAAU,OAAO,YAAY,CAC3B,AAAC,kBAAkB,CAAC,KAAK,KAAK,IAAK;wBAErC,gBAAgB,SAAU,gBAAgB;oBAC5C;oBAEA,YAAY,SAAS,OAAO,YAAY,CAAC;gBAC3C;YACF;YAEA,YAAY;YACZ,wEAAwE;YACxE,IAAI,WAAW;gBACb;gBAEA,WAAW;gBACX,QAAQ,MAAM;gBACd,UAAU,MAAM,QAAQ;gBACxB,OAAO,IAAI,CAAC;gBACZ,MAAM,OAAO;gBACb,KAAK,MAAM;gBAEX,IAAI,SAAS,SAAS,EAAE;oBACtB,SAAS,SAAS,CAAC,IAAI,CACrB,SAAS,gBAAgB,IAAI,WAC7B,WACA;wBAAC,OAAO;wBAAU,KAAK;oBAAI,GAC3B,MAAM,KAAK,CAAC,QAAQ,GAAG;gBAE3B;gBAEA,WAAW;YACb,OAAO;gBACL,qEAAqE;gBACrE,yDAAyD;gBACzD,kEAAkE;gBAClE,2BAA2B;gBAC3B,aAAa,MAAM,KAAK,CAAC,QAAQ,GAAG;gBACpC,SAAS;gBACT,UAAU,WAAW,MAAM;gBAC3B,QAAQ,MAAM;YAChB;QACF,OAAO;YACL,uEAAuE;YACvE,IAAI,cAAc,GAAG,QAAQ,KAAI;gBAC/B;gBACA;gBACA,SAAS;YACX;YAEA,IAAI,OAAO,KAAK,CAAC,YAAY;gBAC3B;YACF,OAAO;gBACL,SAAS,OAAO,YAAY,CAAC;gBAC7B;YACF;QACF;IACF;IAEA,4BAA4B;IAC5B,OAAO,OAAO,IAAI,CAAC;;;IAEnB,wBAAwB;IACxB,SAAS;QACP,OAAO;YACL;YACA;YACA,QAAQ,QAAQ,CAAC,CAAC,QAAQ,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC;QAClD;IACF;IAEA;;;;;GAKC,GACD,SAAS,QAAQ,IAAI,EAAE,MAAM;QAC3B,4BAA4B,GAC5B,IAAI;QAEJ,IAAI,SAAS,OAAO,EAAE;YACpB,WAAW;YACX,SAAS,MAAM,IAAI;YACnB,SAAS,MAAM,IAAI;YAEnB,SAAS,OAAO,CAAC,IAAI,CACnB,SAAS,cAAc,IAAI,WAC3B,QAAQ,CAAC,KAAK,EACd,UACA;QAEJ;IACF;IAEA;;;;GAIC,GACD,SAAS;QACP,IAAI,OAAO;YACT,OAAO,IAAI,CAAC;YAEZ,IAAI,SAAS,IAAI,EAAE;gBACjB,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,WAAW,IAAI,WAAW,OAAO;oBAC3D,OAAO;oBACP,KAAK;gBACP;YACF;YAEA,QAAQ;QACV;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,WAAW,IAAI;IACtB,OAAO,AAAC,QAAQ,UAAU,QAAQ,UAAW,OAAO;AACtD;AAEA;;;;;CAKC,GACD,SAAS,WAAW,IAAI;IACtB,OACE,AAAC,QAAQ,UAAU,QAAQ,UAC3B,SAAS,UACR,QAAQ,UAAU,QAAQ,UAC1B,QAAQ,UAAU,QAAQ,UAC1B,QAAQ,UAAU,QAAQ,UAC3B,CAAC,OAAO,MAAM,MAAM,UACpB,CAAC,OAAO,MAAM,MAAM;AAExB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10857, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/rehype-prism-plus/dist/index.es.js", "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/rehype-prism-plus/src/generator.js", "file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/rehype-prism-plus/src/common.js", "file:///D:/Cursor%20Project/T3%20App/prompt/node_modules/rehype-prism-plus/src/all.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Root} Root\n * @typedef Options options\n *   Configuration.\n * @property {boolean} [showLineNumbers]\n *   Set `showLineNumbers` to `true` to always display line number\n * @property {boolean} [ignoreMissing]\n *   Set `ignoreMissing` to `true` to ignore unsupported languages and line highlighting when no language is specified\n * @property {string} [defaultLanguage]\n *   Uses the specified language as the default if none is specified. Takes precedence over `ignoreMissing`.\n *   Note: The language must be registered with refractor.\n */\n\nimport { visit } from 'unist-util-visit'\nimport { toString } from 'hast-util-to-string'\nimport { filter } from 'unist-util-filter'\nimport rangeParser from 'parse-numeric-range'\n\nconst getLanguage = (node) => {\n  const className = node.properties.className\n  //@ts-ignore\n  for (const classListItem of className) {\n    if (classListItem.slice(0, 9) === 'language-') {\n      return classListItem.slice(9).toLowerCase()\n    }\n  }\n  return null\n}\n\n/**\n * @param {import('refractor/lib/core').Refractor} refractor\n * @param {string} defaultLanguage\n * @return {void}\n */\nconst checkIfLanguageIsRegistered = (refractor, defaultLanguage) => {\n  if (defaultLanguage && !refractor.registered(defaultLanguage)) {\n    throw new Error(`The default language \"${defaultLanguage}\" is not registered with refractor.`)\n  }\n}\n\n/**\n * Create a closure that determines if we have to highlight the given index\n *\n * @param {string} meta\n * @return { (index:number) => boolean }\n */\nconst calculateLinesToHighlight = (meta) => {\n  const RE = /{([\\d,-]+)}/\n  // Remove space between {} e.g. {1, 3}\n  const parsedMeta = meta\n    .split(',')\n    .map((str) => str.trim())\n    .join()\n  if (RE.test(parsedMeta)) {\n    const strlineNumbers = RE.exec(parsedMeta)[1]\n    const lineNumbers = rangeParser(strlineNumbers)\n    return (index) => lineNumbers.includes(index + 1)\n  } else {\n    return () => false\n  }\n}\n\n/**\n * Check if we want to start the line numbering from a given number or 1\n * showLineNumbers=5, will start the numbering from 5\n * @param {string} meta\n * @returns {number}\n */\nconst calculateStartingLine = (meta) => {\n  const RE = /showLineNumbers=(?<lines>\\d+)/i\n  // pick the line number after = using a named capturing group\n  if (RE.test(meta)) {\n    const {\n      groups: { lines },\n    } = RE.exec(meta)\n    return Number(lines)\n  }\n  return 1\n}\n\n/**\n * Create container AST for node lines\n *\n * @param {number} number\n * @return {Element[]}\n */\nconst createLineNodes = (number) => {\n  const a = new Array(number)\n  for (let i = 0; i < number; i++) {\n    a[i] = {\n      type: 'element',\n      tagName: 'span',\n      properties: { className: [] },\n      children: [],\n    }\n  }\n  return a\n}\n\n/**\n * Split multiline text nodes into individual nodes with positioning\n * Add a node start and end line position information for each text node\n *\n * @return { (ast:Element['children']) => Element['children'] }\n *\n */\nconst addNodePositionClosure = () => {\n  let startLineNum = 1\n  /**\n   * @param {Element['children']} ast\n   * @return {Element['children']}\n   */\n  const addNodePosition = (ast) => {\n    return ast.reduce((result, node) => {\n      if (node.type === 'text') {\n        const value = /** @type {string} */ (node.value)\n        const numLines = (value.match(/\\n/g) || '').length\n        if (numLines === 0) {\n          node.position = {\n            // column: 1 is needed to avoid error with @next/mdx\n            // https://github.com/timlrx/rehype-prism-plus/issues/44\n            start: { line: startLineNum, column: 1 },\n            end: { line: startLineNum, column: 1 },\n          }\n          result.push(node)\n        } else {\n          const lines = value.split('\\n')\n          for (const [i, line] of lines.entries()) {\n            result.push({\n              type: 'text',\n              value: i === lines.length - 1 ? line : line + '\\n',\n              position: {\n                start: { line: startLineNum + i, column: 1 },\n                end: { line: startLineNum + i, column: 1 },\n              },\n            })\n          }\n        }\n        startLineNum = startLineNum + numLines\n\n        return result\n      }\n\n      if (Object.prototype.hasOwnProperty.call(node, 'children')) {\n        const initialLineNum = startLineNum\n        // @ts-ignore\n        node.children = addNodePosition(node.children, startLineNum)\n        result.push(node)\n        node.position = {\n          start: { line: initialLineNum, column: 1 },\n          end: { line: startLineNum, column: 1 },\n        }\n        return result\n      }\n\n      result.push(node)\n      return result\n    }, [])\n  }\n  return addNodePosition\n}\n\n/**\n * Rehype prism plugin generator that highlights code blocks with refractor (prismjs)\n *\n * Pass in your own refractor object with the required languages registered:\n * https://github.com/wooorm/refractor#refractorregistersyntax\n *\n * @param {import('refractor/lib/core').Refractor} refractor\n * @return {import('unified').Plugin<[Options?], Root>}\n */\nconst rehypePrismGenerator = (refractor) => {\n  return (options = {}) => {\n    checkIfLanguageIsRegistered(refractor, options.defaultLanguage)\n    return (tree) => {\n      visit(tree, 'element', visitor)\n    }\n\n    /**\n     * @param {Element} node\n     * @param {number} index\n     * @param {Element} parent\n     */\n    function visitor(node, index, parent) {\n      if (!parent || parent.tagName !== 'pre' || node.tagName !== 'code') {\n        return\n      }\n\n      // @ts-ignore meta is a custom code block property\n      let meta = /** @type {string} */ (node?.data?.meta || node?.properties?.metastring || '')\n      // Coerce className to array\n      if (node.properties.className) {\n        if (typeof node.properties.className === 'boolean') {\n          node.properties.className = []\n        } else if (!Array.isArray(node.properties.className)) {\n          node.properties.className = [node.properties.className]\n        }\n      } else {\n        node.properties.className = []\n      }\n\n      let lang = getLanguage(node)\n      // If no language is set on the code block, use defaultLanguage if specified\n      if (!lang && options.defaultLanguage) {\n        lang = options.defaultLanguage\n        node.properties.className.push(`language-${lang}`)\n      }\n      node.properties.className.push('code-highlight')\n\n      /** @type {Element} */\n      let refractorRoot\n\n      // Syntax highlight\n      if (lang) {\n        try {\n          let rootLang\n          if (lang?.includes('diff-')) {\n            rootLang = lang.split('-')[1]\n          } else {\n            rootLang = lang\n          }\n          // @ts-ignore\n          refractorRoot = refractor.highlight(toString(node), rootLang)\n          // @ts-ignore className is already an array\n          parent.properties.className = (parent.properties.className || []).concat(\n            'language-' + rootLang\n          )\n        } catch (err) {\n          if (options.ignoreMissing && /Unknown language/.test(err.message)) {\n            refractorRoot = node\n          } else {\n            throw err\n          }\n        }\n      } else {\n        refractorRoot = node\n      }\n\n      refractorRoot.children = addNodePositionClosure()(refractorRoot.children)\n\n      // Add position info to root\n      if (refractorRoot.children.length > 0) {\n        refractorRoot.position = {\n          start: { line: refractorRoot.children[0].position.start.line, column: 0 },\n          end: {\n            line: refractorRoot.children[refractorRoot.children.length - 1].position.end.line,\n            column: 0,\n          },\n        }\n      } else {\n        refractorRoot.position = {\n          start: { line: 0, column: 0 },\n          end: { line: 0, column: 0 },\n        }\n      }\n\n      const shouldHighlightLine = calculateLinesToHighlight(meta)\n      const startingLineNumber = calculateStartingLine(meta)\n      const codeLineArray = createLineNodes(refractorRoot.position.end.line)\n\n      const falseShowLineNumbersStr = [\n        'showlinenumbers=false',\n        'showlinenumbers=\"false\"',\n        'showlinenumbers={false}',\n      ]\n      for (const [i, line] of codeLineArray.entries()) {\n        // Default class name for each line\n        line.properties.className = ['code-line']\n\n        // Syntax highlight\n        const treeExtract = filter(\n          refractorRoot,\n          (node) => node.position.start.line <= i + 1 && node.position.end.line >= i + 1\n        )\n        line.children = treeExtract.children\n\n        // Line number\n        if (\n          (meta.toLowerCase().includes('showLineNumbers'.toLowerCase()) ||\n            options.showLineNumbers) &&\n          !falseShowLineNumbersStr.some((str) => meta.toLowerCase().includes(str))\n        ) {\n          line.properties.line = [(i + startingLineNumber).toString()]\n          line.properties.className.push('line-number')\n        }\n\n        // Line highlight\n        if (shouldHighlightLine(i)) {\n          line.properties.className.push('highlight-line')\n        }\n\n        // Diff classes\n        if (\n          (lang === 'diff' || lang?.includes('diff-')) &&\n          toString(line).substring(0, 1) === '-'\n        ) {\n          line.properties.className.push('deleted')\n        } else if (\n          (lang === 'diff' || lang?.includes('diff-')) &&\n          toString(line).substring(0, 1) === '+'\n        ) {\n          line.properties.className.push('inserted')\n        }\n      }\n\n      // Remove possible trailing line when splitting by \\n which results in empty array\n      if (\n        codeLineArray.length > 0 &&\n        toString(codeLineArray[codeLineArray.length - 1]).trim() === ''\n      ) {\n        codeLineArray.pop()\n      }\n\n      node.children = codeLineArray\n    }\n  }\n}\n\nexport default rehypePrismGenerator\n", "import { refractor as refractorCommon } from 'refractor/lib/common.js'\nimport rehypePrismGenerator from './generator.js'\n\n/**\n * Rehype prism plugin that highlights code blocks with refractor (prismjs)\n * Supported languages: https://github.com/wooorm/refractor#data\n *\n * Consider using rehypePrismGenerator to generate a plugin\n * that supports your required languages.\n */\nconst rehypePrismCommon = rehypePrismGenerator(refractorCommon)\n\nexport default rehypePrismCommon\n", "import { refractor as refractorAll } from 'refractor/lib/all.js'\nimport rehypePrismGenerator from './generator.js'\n\n/**\n * Rehype prism plugin that highlights code blocks with refractor (prismjs)\n * This supports all the languages and should be used on the server side.\n *\n * Consider using rehypePrismCommon or rehypePrismGenerator to generate a plugin\n * that supports your required languages.\n */\nconst rehypePrismAll = rehypePrismGenerator(refractorAll)\n\nexport default rehypePrismAll\n"], "names": ["rehypePrismGenerator", "refractor", "options", "defaultLanguage", "registered", "Error", "checkIfLanguageIsRegistered", "tree", "visit", "visitor", "node", "index", "parent", "_node$data", "_node$properties", "tagName", "meta", "data", "properties", "metastring", "className", "Array", "isArray", "refractorRoot", "startLineNum", "lang", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "classListItem", "value", "slice", "toLowerCase", "getLanguage", "push", "_lang", "rootLang", "includes", "split", "highlight", "toString", "concat", "err", "ignoreMissing", "test", "message", "children", "addNodePosition", "ast", "reduce", "result", "type", "numLines", "match", "length", "position", "start", "line", "column", "end", "_step2", "lines", "_iterator2", "entries", "_step2$value", "i", "Object", "prototype", "hasOwnProperty", "call", "initialLineNum", "_step3", "shouldHighlightLine", "RE", "parsedMeta", "map", "str", "trim", "join", "strlineNumbers", "exec", "lineNumbers", "<PERSON><PERSON><PERSON><PERSON>", "calculateLinesToHighlight", "startingLineNumber", "_wrapRegExp", "_RE$exec", "Number", "groups", "calculateStartingLine", "codeLineArray", "number", "a", "createLineNodes", "falseShowLineNumbersStr", "_loop", "_lang2", "_lang3", "_step3$value", "treeExtract", "filter", "showLineNumbers", "some", "substring", "_iterator3", "pop", "rehypePrismCommon", "refractor<PERSON><PERSON><PERSON>", "rehypePrismAll", "refractorAll"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAyJMA,IAAuB,SAACC,CAAAA;IAC5B,OAAA,SAAQC,CAAAA;QAEN,OAAA,KAFa,MAAPA,KAAAA,CAAAA,IAAU,CAAE,CAAA,GA1Ic,SAACD,CAAAA,EAAWE,CAAAA;YAC9C,IAAIA,KAAAA,CAAoBF,EAAUG,UAAAA,CAAWD,IAC3C,MAAA,IAAUE,MAAAA,2BAA+BF,IAAe;QAE5D,CAuIIG,CAA4BL,GAAWC,EAAQC,eAAAA,GAAAA,SACvCI,CAAAA;sLACNC,SAAAA,EAAMD,GAAM,WAAWE;QACzB;;;QAOA,SAASA,EAAQC,CAAAA,EAAMC,CAAAA,EAAOC,CAAAA;YAAAA,IAAQC,GAAAC;YACpC,IAAKF,KAA6B,UAAnBA,EAAOG,OAAAA,IAAsC,WAAjBL,EAAKK,OAAAA,EAAhD;gBAKA,IAAIC,IAAAA,CAAkCH,QAAJH,KAAAG,QAAAA,CAAIA,IAAJH,EAAMO,IAAAA,IAAAA,KAANJ,IAAAA,EAAYG,IAAAA,KAAAA,CAAYF,QAAJJ,KAAAI,QAAAA,CAAIA,IAAJJ,EAAMQ,UAAAA,IAAAA,KAANJ,IAAAA,EAAkBK,UAAAA,KAAc;gBAElFT,EAAKQ,UAAAA,CAAWE,SAAAA,GACuB,aAAA,OAA9BV,EAAKQ,UAAAA,CAAWE,SAAAA,GACzBV,EAAKQ,UAAAA,CAAWE,SAAAA,GAAY,EAAA,GAClBC,MAAMC,OAAAA,CAAQZ,EAAKQ,UAAAA,CAAWE,SAAAA,KAAAA,CACxCV,EAAKQ,UAAAA,CAAWE,SAAAA,GAAY;oBAACV,EAAKQ,UAAAA,CAAWE,SAAAA;iBAAAA,IAG/CV,EAAKQ,UAAAA,CAAWE,SAAAA,GAAY,EAAA;gBAG9B,IASIG,GAvGJC,GA8FIC,IAvLU,SAACf,CAAAA;oBAGnB,IAFA,IAEqCgB,GAArCC,IAAAC,EAFkBlB,EAAKQ,UAAAA,CAAWE,SAAAA,GAAAA,CAAAA,CAEGM,IAAAC,GAAAA,EAAAE,IAAAA,EAAE;wBAAA,IAA5BC,IAAaJ,EAAAK,KAAAA;wBACtB,IAAkC,gBAA9BD,EAAcE,KAAAA,CAAM,GAAG,IACzB,OAAOF,EAAcE,KAAAA,CAAM,GAAGC,WAAAA;oBAElC;oBACA,OACF;gBAAA,CA8KiBC,CAAYxB;gBAYvB,IAAA,CAVKe,KAAQvB,EAAQC,eAAAA,IAEnBO,EAAKQ,UAAAA,CAAWE,SAAAA,CAAUe,IAAAA,CAAiBV,cAAAA,CAD3CA,IAAOvB,EAAQC,eAAAA,IAGjBO,EAAKQ,UAAAA,CAAWE,SAAAA,CAAUe,IAAAA,CAAK,mBAM3BV,GACF,IAAA;oBAAIW,IAAAA,GACEC;oBAEFA,IAAAA,QAAAA,CADFD,IAAIX,CAAAA,KAAAW,EAAME,QAAAA,CAAS,WACNb,EAAKc,KAAAA,CAAM,IAAA,CAAK,EAAA,GAEhBd,GAGbF,IAAgBtB,EAAUuC,SAAAA,EAAUC,2KAAAA,EAAS/B,IAAO2B,IAEpDzB,EAAOM,UAAAA,CAAWE,SAAAA,GAAAA,CAAaR,EAAOM,UAAAA,CAAWE,SAAAA,IAAa,EAAA,EAAIsB,MAAAA,CAChE,cAAcL;gBAElB,EAAE,OAAOM,GAAAA;oBACP,IAAA,CAAIzC,EAAQ0C,aAAAA,IAAAA,CAAiB,mBAAmBC,IAAAA,CAAKF,EAAIG,OAAAA,GAGvD,MAAMH;oBAFNpB,IAAgBb;gBAIpB;qBAEAa,IAAgBb;gBAGlBa,EAAcwB,QAAAA,GAAAA,CAnIdvB,IAAe,GAKK,SAAlBwB,EAAmBC,CAAAA;oBACvB,OAAOA,EAAIC,MAAAA,CAAO,SAACC,CAAAA,EAAQzC,CAAAA;wBACzB,IAAkB,WAAdA,EAAK0C,IAAAA,EAAiB;4BACxB,IAAMrB,IAA+BrB,EAAKqB,KAAAA,EACpCsB,IAAAA,CAAYtB,EAAMuB,KAAAA,CAAM,UAAU,EAAA,EAAIC,MAAAA;4BAC5C,IAAiB,MAAbF,GACF3C,EAAK8C,QAAAA,GAAW;gCAGdC,OAAO;oCAAEC,MAAMlC;oCAAcmC,QAAQ;gCAAA;gCACrCC,KAAK;oCAAEF,MAAMlC;oCAAcmC,QAAQ;gCAAA;4BAAA,GAErCR,EAAOhB,IAAAA,CAAKzB;iCAGZ,IADA,IACuCmD,GADjCC,IAAQ/B,EAAMQ,KAAAA,CAAM,OAC1BwB,IAAAnC,EAAwBkC,EAAME,OAAAA,KAAAA,CAAAA,CAASH,IAAAE,GAAAA,EAAAlC,IAAAA,EAAE;gCAAA,IAAAoC,IAAAJ,EAAA9B,KAAAA,EAA7BmC,IAACD,CAAAA,CAAEP,EAAAA,EAAAA,IAAIO,CAAAA,CAAA,EAAA;gCACjBd,EAAOhB,IAAAA,CAAK;oCACViB,MAAM;oCACNrB,OAAOmC,MAAMJ,EAAMP,MAAAA,GAAS,IAAIG,IAAOA,IAAO;oCAC9CF,UAAU;wCACRC,OAAO;4CAAEC,MAAMlC,IAAe0C;4CAAGP,QAAQ;wCAAA;wCACzCC,KAAK;4CAAEF,MAAMlC,IAAe0C;4CAAGP,QAAQ;wCAAA;oCAAA;gCAAA;4BAG7C;4BAIF,OAFAnC,KAA8B6B,GAEvBF;wBACT;wBAEA,IAAIgB,OAAOC,SAAAA,CAAUC,cAAAA,CAAeC,IAAAA,CAAK5D,GAAM,aAAa;4BAC1D,IAAM6D,IAAiB/C;4BAQvB,OANAd,EAAKqC,QAAAA,GAAWC,EAAgBtC,EAAKqC,QAAAA,GACrCI,EAAOhB,IAAAA,CAAKzB,IACZA,EAAK8C,QAAAA,GAAW;gCACdC,OAAO;oCAAEC,MAAMa;oCAAgBZ,QAAQ;gCAAA;gCACvCC,KAAK;oCAAEF,MAAMlC;oCAAcmC,QAAQ;gCAAA;4BAAA,GAE9BR;wBACT;wBAGA,OADAA,EAAOhB,IAAAA,CAAKzB,IACLyC;oBACT,GAAG,EAAA;gBACL,CAAA,EAgFsD5B,EAAcwB,QAAAA,GAI9DxB,EAAciC,QAAAA,GADZjC,EAAcwB,QAAAA,CAASQ,MAAAA,GAAS,IACT;oBACvBE,OAAO;wBAAEC,MAAMnC,EAAcwB,QAAAA,CAAS,EAAA,CAAGS,QAAAA,CAASC,KAAAA,CAAMC,IAAAA;wBAAMC,QAAQ;oBAAA;oBACtEC,KAAK;wBACHF,MAAMnC,EAAcwB,QAAAA,CAASxB,EAAcwB,QAAAA,CAASQ,MAAAA,GAAS,EAAA,CAAGC,QAAAA,CAASI,GAAAA,CAAIF,IAAAA;wBAC7EC,QAAQ;oBAAA;gBAAA,IAIa;oBACvBF,OAAO;wBAAEC,MAAM;wBAAGC,QAAQ;oBAAA;oBAC1BC,KAAK;wBAAEF,MAAM;wBAAGC,QAAQ;oBAAA;gBAAA;gBAa5B,IATA,IAS+Ca,GATzCC,IAlNsB,SAACzD,CAAAA;oBACjC,IAAM0D,IAAK,eAELC,IAAa3D,EAChBuB,KAAAA,CAAM,KACNqC,GAAAA,CAAI,SAACC,CAAAA;wBAAAA,OAAQA,EAAIC,IAAAA;oBAAM,GACvBC,IAAAA;oBACH,IAAIL,EAAG7B,IAAAA,CAAK8B,IAAa;wBACvB,IAAMK,IAAiBN,EAAGO,IAAAA,CAAKN,EAAAA,CAAY,EAAA,EACrCO,qKAAcC,EAAYH;wBAChC,OAAO,SAACrE,CAAAA;4BAAU,OAAAuE,EAAY5C,QAAAA,CAAS3B,IAAQ;wBAAE;oBACnD;oBACE,OAAa;wBAAA,OAAA,CAAA;oBAAK;gBAEtB,CAoMkCyE,CAA0BpE,IAChDqE,IA7LkB,SAACrE,CAAAA;oBAC7B,IAAM0D,IAAAA,WAAAA,GAAEY,EAAG,0BAAgCxB;wBAAAA,OAAAA;oBAAAA;oBAE3C,IAAIY,EAAG7B,IAAAA,CAAK7B,IAAO;wBACjB,IAAAuE,IAEIb,EAAGO,IAAAA,CAAKjE;wBACZ,OAAOwE,OAFUD,EAAfE,MAAAA,CAAU3B,KAAAA;oBAGd;oBACA,OACF;gBAAA,CAmLiC4B,CAAsB1E,IAC3C2E,IA5KY,SAACC,CAAAA;oBAEvB,IADA,IAAMC,IAAI,IAAIxE,MAAMuE,IACX1B,IAAI,GAAGA,IAAI0B,GAAQ1B,IAC1B2B,CAAAA,CAAE3B,EAAAA,GAAK;wBACLd,MAAM;wBACNrC,SAAS;wBACTG,YAAY;4BAAEE,WAAW,EAAA;wBAAA;wBACzB2B,UAAU,EAAA;oBAAA;oBAGd,OAAO8C;gBACT,CAiK4BC,CAAgBvE,EAAciC,QAAAA,CAASI,GAAAA,CAAIF,IAAAA,GAE3DqC,IAA0B;oBAC9B;oBACA;oBACA;iBAAA,EACDC,IAAA;oBACgD,IAAAC,GAAAC,GAAAC,IAAA3B,EAAAzC,KAAAA,EAArCmC,IAACiC,CAAAA,CAAEzC,EAAAA,EAAAA,IAAIyC,CAAAA,CAAA,EAAA;oBAEjBzC,EAAKxC,UAAAA,CAAWE,SAAAA,GAAY;wBAAC;qBAAA;oBAG7B,IAAMgF,yKAAcC,EAClB9E,GACA,SAACb,CAAAA;wBAAAA,OAASA,EAAK8C,QAAAA,CAASC,KAAAA,CAAMC,IAAAA,IAAQQ,IAAI,KAAKxD,EAAK8C,QAAAA,CAASI,GAAAA,CAAIF,IAAAA,IAAQQ,IAAI;oBAAC;oBAEhFR,EAAKX,QAAAA,GAAWqD,EAAYrD,QAAAA,EAAAA,CAIzB/B,EAAKiB,WAAAA,GAAcK,QAAAA,CAAS,kBAAkBL,WAAAA,OAAAA,CAC7C/B,EAAQoG,eAAAA,IACTP,EAAwBQ,IAAAA,CAAK,SAAC1B,CAAAA;wBAAQ,OAAA7D,EAAKiB,WAAAA,GAAcK,QAAAA,CAASuC;oBAAI,MAAA,CAEvEnB,EAAKxC,UAAAA,CAAWwC,IAAAA,GAAO;wBAAA,CAAEQ,IAAImB,CAAAA,EAAoB5C,QAAAA;qBAAAA,EACjDiB,EAAKxC,UAAAA,CAAWE,SAAAA,CAAUe,IAAAA,CAAK,cAAA,GAI7BsC,EAAoBP,MACtBR,EAAKxC,UAAAA,CAAWE,SAAAA,CAAUe,IAAAA,CAAK,mBAAA,CAKrB,WAATV,KAAAA,QAAAA,CAAewE,IAAIxE,CAAAA,KAAAwE,EAAM3D,QAAAA,CAAS,QAAA,KACA,QAAnCG,4KAAAA,EAASiB,GAAM8C,SAAAA,CAAU,GAAG,KAE5B9C,EAAKxC,UAAAA,CAAWE,SAAAA,CAAUe,IAAAA,CAAK,aAAA,CAErB,WAATV,KAAuB,QAAA,CAARyE,IAAIzE,CAAAA,KAAAyE,EAAM5D,QAAAA,CAAS,QAAA,KACA,QAAnCG,4KAAAA,EAASiB,GAAM8C,SAAAA,CAAU,GAAG,MAE5B9C,EAAKxC,UAAAA,CAAWE,SAAAA,CAAUe,IAAAA,CAAK;gBAEnC,GAtCAsE,IAAA7E,EAAwB+D,EAAc3B,OAAAA,KAAAA,CAAAA,CAASQ,IAAAiC,GAAAA,EAAA5E,IAAAA,EAAAmE;gBA0C7CL,EAAcpC,MAAAA,GAAS,KACsC,mLAA7Dd,EAASkD,CAAAA,CAAcA,EAAcpC,MAAAA,GAAS,EAAA,EAAIuB,IAAAA,MAElDa,EAAce,GAAAA,IAGhBhG,EAAKqC,QAAAA,GAAW4C;YA/HhB;QAgIF;IACF;AACF,GCnTMgB,IAAoB3G,2IAAqB4G,YAAAA,GCAzCC,IAAiB7G,2IAAqB8G,YAAAA", "debugId": null}}]}