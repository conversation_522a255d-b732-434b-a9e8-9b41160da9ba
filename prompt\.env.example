# Vercel 环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 应用配置
NEXT_PUBLIC_APP_NAME="提示词管理工具"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_URL="https://your-app.vercel.app"

# 数据库配置
DATABASE_URL="postgresql://user:password@host:port/database"

# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# NextAuth.js 配置
NEXTAUTH_URL="https://your-app.vercel.app"
NEXTAUTH_SECRET="your-secret-key"

# 功能开关
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
NEXT_PUBLIC_ENABLE_SENTRY="true"
NEXT_PUBLIC_ENABLE_HOTJAR="false"

# 第三方服务
SENTRY_DSN="your-sentry-dsn"
HOTJAR_ID="your-hotjar-id"
GOOGLE_ANALYTICS_ID="your-ga-id"

# 开发环境配置
NODE_ENV="development"
LOG_LEVEL="debug"

# 生产环境配置（仅在生产环境使用）
# NODE_ENV="production"
# LOG_LEVEL="error"

# 安全配置
CSRF_SECRET="your-csrf-secret"
ENCRYPTION_KEY="your-encryption-key"

# 缓存配置
REDIS_URL="redis://localhost:6379"
CACHE_TTL="3600"

# 文件上传配置
UPLOAD_MAX_SIZE="5242880"  # 5MB
ALLOWED_FILE_TYPES="application/json,text/plain"

# 邮件配置
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="提示词管理工具"
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# 速率限制
RATE_LIMIT_WINDOW="900000"  # 15分钟
RATE_LIMIT_MAX="100"

# 监控配置
HEALTH_CHECK_TOKEN="your-health-check-token"
MONITORING_ENABLED="true"