{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/api/trpc/:path*", "destination": "/api/trpc/:path*", "regex": "^\\/api\\/trpc(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}, {"source": "/login", "destination": "/auth/login", "permanent": true, "regex": "^(?!\\/_next)\\/login(?:\\/)?$"}, {"source": "/signup", "destination": "/auth/signup", "permanent": true, "regex": "^(?!\\/_next)\\/signup(?:\\/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=60, stale-while-revalidate=300"}], "regex": "^\\/api(?:\\/(.*))(?:\\/)?$"}]}