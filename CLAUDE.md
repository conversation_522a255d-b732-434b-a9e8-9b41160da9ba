### 语言规范
- **默认语言**: 始终使用中文简体回复
- **技术术语**: 保持英文原文，如 `function`、`class`、`API` 等
- **代码注释**: 使用中文注释，但保持代码本身的英文命名

## 代码规范

### 注释规范
- 使用 **JSDoc** 注释规范
- 为所有函数、类和复杂逻辑添加清晰注释

### 代码格式
- 严格遵循正确的代码格式
- 使用描述性、明确的变量名，增强代码可读性
- 遵循项目现有编码风格保持一致性
- 将硬编码值替换为命名常量

## MCP工具集成规则

### Context7 使用规则
- **优先使用**: 处理复杂技术问题时优先使用 Context7
- **适用场景**: 框架文档查询、API 使用说明、最佳实践参考
- **使用原则**: 根据任务需求灵活使用，非强制性

### Playwright 测试规则
- **自动测试**: 完成 Web 应用（HTML、PHP、ASP.NET 等）后进行自动化测试
- **测试范围**: 重点测试核心功能和用户交互流程
- **Bug 检测**: 自动识别常见的 UI 和功能问题

## 任务执行原则

### 任务管理
- **任务清单**: 执行任何任务前，必须创建详细的任务列表和创建 `todos.md` 文档，同步更新任务列表和 `todos.md` 文档进度
- **完整执行**: 任务开始后不得中途停止，直至全部完成
- **进度跟踪**: 完成每个特性或修复后及时更新进度记录
- **执行记录**: 任何任务都需创建 `execution-log.md` 文档实时记录每个执行过程，执行结果和遇到的问题

### 代码修改流程
1. **前置调研**: 查阅相关代码和逻辑，理解现有架构和逻辑
2. **思维链推理**: 使用sequential-thinking逻辑推理进行代码思考
3. **整体审视**: 充分理解修改需求和影响范围，评估修改对其他模块的潜在影响
4. **同步更新**: 所有涉及位置必须同步修改，不可遗漏
5. **分段处理**: 长代码可分段修改，防止系统卡死
6. **测试验证**: 修改后进行必要的功能验证

## 代码质量保证

### 安全与性能
- **安全考量**: 修改代码时始终考虑安全影响
- **性能优化**: 关注代码性能，避免不必要的资源消耗
- **错误处理**: 实现完善的错误处理和日志记录
- **边缘情况**: 充分考虑并处理各种边缘情况

### 测试与验证
- **单元测试**: 为新增或修改的代码添加适当测试
- **断言验证**: 包含断言验证假设，及早捕获错误
- **测试限制**: 非必要不创建测试页面，如需创建仅建一个统一测试文件

### 架构设计
- **模块化设计**: 遵循模块化设计原则，提高可维护性和可重用性
- **版本兼容**: 确保更改与项目语言/框架版本兼容
- **最小改动**: 保持对原有功能的最小改动，避免引入新缺陷

## 系统环境配置

### 系统适配
- **PowerShell限制**: PowerShell不支持 `&&` 命令链接，应分别执行
- **文件处理**: 逐文件进行更改，便于错误发现和修复
- **编码问题**: 注意中文路径和文件名的编码问题

## 执行约束

### 行为准则
- **谨慎决策**: 对不确定的内容不做假设，及时询问确认
- **精确修改**: 严格按照用户需求进行修改，不添加额外功能
- **保持完整**: 不删除相关代码，保持原有结构完整性
- **批量编辑**: 同文件的多处修改作为单次编辑提供
- **真实引用**: 提供真实的文件路径和链接

### 沟通与协作
- **自动化优先**: 提供自动化解决方案替代手动操作
- **适时解释**: 在必要时解释实现思路和技术选择
- **问题预警**: 发现潜在问题时主动提醒用户
- **效率平衡**: 在代码质量和开发效率间找到平衡

### 错误处理策略
- **错误预防**: 在编码阶段预防常见错误
- **快速定位**: 提供清晰的错误信息和定位方法
- **回滚机制**: 重要修改前备份，支持快速回滚
- **学习改进**: 从错误中学习，避免重复问题

---

**核心理念**: **核心理念**: 构建高效、安全、可维护的 AI 编程协作体系，在保证代码质量的同时确保系统整体稳定性和兼容性。