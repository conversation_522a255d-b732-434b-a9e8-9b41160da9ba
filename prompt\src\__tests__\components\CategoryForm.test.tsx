import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CategoryForm } from '~/components/categories/CategoryForm'

// Mock 依赖
vi.mock('~/trpc/react', () => ({
  api: {
    categories: {
      create: {
        useMutation: () => ({
          mutate: vi.fn(),
          isLoading: false,
        }),
      },
      update: {
        useMutation: () => ({
          mutate: vi.fn(),
          isLoading: false,
        }),
      },
      getAll: {
        useQuery: () => ({
          data: [
            { id: '1', name: '工作', color: '#3B82F6', icon: '💼' },
            { id: '2', name: '学习', color: '#10B981', icon: '📚' },
          ],
          isLoading: false,
        }),
      },
    },
  },
}))

const mockCategory = {
  id: '1',
  name: '测试分类',
  description: '测试分类描述',
  color: '#3B82F6',
  icon: '💼',
  parentId: null,
}

describe('CategoryForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该渲染表单字段', () => {
    render(<CategoryForm />)
    
    expect(screen.getByLabelText('分类名称')).toBeInTheDocument()
    expect(screen.getByLabelText('描述')).toBeInTheDocument()
    expect(screen.getByLabelText('颜色')).toBeInTheDocument()
    expect(screen.getByLabelText('图标')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '保存' })).toBeInTheDocument()
  })

  it('应该在编辑模式下填充现有数据', () => {
    render(<CategoryForm category={mockCategory} />)
    
    expect(screen.getByDisplayValue('测试分类')).toBeInTheDocument()
    expect(screen.getByDisplayValue('测试分类描述')).toBeInTheDocument()
    expect(screen.getByDisplayValue('#3B82F6')).toBeInTheDocument()
    expect(screen.getByDisplayValue('💼')).toBeInTheDocument()
  })

  it('应该验证必填字段', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    expect(screen.getByText('分类名称不能为空')).toBeInTheDocument()
    expect(screen.getByText('颜色不能为空')).toBeInTheDocument()
  })

  it('应该验证分类名称长度', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    const nameInput = screen.getByLabelText('分类名称')
    await user.type(nameInput, 'a'.repeat(51))
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    expect(screen.getByText('分类名称长度不能超过50个字符')).toBeInTheDocument()
  })

  it('应该验证颜色格式', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    const colorInput = screen.getByLabelText('颜色')
    await user.clear(colorInput)
    await user.type(colorInput, 'invalid-color')
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    expect(screen.getByText('无效的颜色格式，应为 #RRGGBB')).toBeInTheDocument()
  })

  it('应该处理表单提交', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<CategoryForm onSubmit={onSubmit} />)
    
    await user.type(screen.getByLabelText('分类名称'), '新分类')
    await user.type(screen.getByLabelText('描述'), '新分类描述')
    await user.type(screen.getByLabelText('颜色'), '#FF5733')
    await user.type(screen.getByLabelText('图标'), '🚀')
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        name: '新分类',
        description: '新分类描述',
        color: '#FF5733',
        icon: '🚀',
        parentId: undefined,
      })
    })
  })

  it('应该显示颜色预设选项', () => {
    render(<CategoryForm />)
    
    expect(screen.getByText('预设颜色')).toBeInTheDocument()
    
    // 检查是否有颜色预设按钮
    const colorPresets = screen.getAllByRole('button', { name: /选择颜色/ })
    expect(colorPresets.length).toBeGreaterThan(0)
  })

  it('应该处理颜色预设选择', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    // 点击第一个颜色预设
    const firstColorPreset = screen.getAllByRole('button', { name: /选择颜色/ })[0]
    await user.click(firstColorPreset)
    
    const colorInput = screen.getByLabelText('颜色')
    expect(colorInput).toHaveValue('#3B82F6')
  })

  it('应该支持自定义颜色', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    const customColorButton = screen.getByRole('button', { name: '自定义颜色' })
    await user.click(customColorButton)
    
    // 应该显示颜色选择器
    expect(screen.getByRole('dialog', { name: '颜色选择器' })).toBeInTheDocument()
  })

  it('应该显示图标选择器', () => {
    render(<CategoryForm />)
    
    expect(screen.getByText('选择图标')).toBeInTheDocument()
    
    // 检查是否有图标选项
    const iconOptions = screen.getAllByRole('button', { name: /选择图标/ })
    expect(iconOptions.length).toBeGreaterThan(0)
  })

  it('应该处理图标选择', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    // 点击第一个图标选项
    const firstIcon = screen.getAllByRole('button', { name: /选择图标/ })[0]
    await user.click(firstIcon)
    
    const iconInput = screen.getByLabelText('图标')
    expect(iconInput).toHaveValue('💼')
  })

  it('应该支持图标搜索', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    const iconSearchInput = screen.getByPlaceholderText('搜索图标...')
    await user.type(iconSearchInput, '工作')
    
    // 应该筛选相关图标
    expect(screen.getByText('💼')).toBeInTheDocument()
    expect(screen.getByText('📊')).toBeInTheDocument()
  })

  it('应该支持父分类选择', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    const parentSelect = screen.getByLabelText('父分类')
    await user.selectOptions(parentSelect, '1')
    
    expect(screen.getByDisplayValue('工作')).toBeInTheDocument()
  })

  it('应该实时预览分类样式', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    await user.type(screen.getByLabelText('分类名称'), '预览分类')
    await user.type(screen.getByLabelText('颜色'), '#FF5733')
    await user.type(screen.getByLabelText('图标'), '🎨')
    
    // 检查预览区域
    const preview = screen.getByTestId('category-preview')
    expect(preview).toHaveTextContent('🎨 预览分类')
    expect(preview).toHaveStyle({ backgroundColor: '#FF5733' })
  })

  it('应该处理取消操作', async () => {
    const user = userEvent.setup()
    const onCancel = vi.fn()
    
    render(<CategoryForm onCancel={onCancel} />)
    
    const cancelButton = screen.getByRole('button', { name: '取消' })
    await user.click(cancelButton)
    
    expect(onCancel).toHaveBeenCalled()
  })

  it('应该处理重置操作', async () => {
    const user = userEvent.setup()
    render(<CategoryForm />)
    
    // 输入一些数据
    await user.type(screen.getByLabelText('分类名称'), '测试名称')
    await user.type(screen.getByLabelText('描述'), '测试描述')
    
    const resetButton = screen.getByRole('button', { name: '重置' })
    await user.click(resetButton)
    
    expect(screen.getByLabelText('分类名称')).toHaveValue('')
    expect(screen.getByLabelText('描述')).toHaveValue('')
  })

  describe('颜色选择器', () => {
    it('应该显示颜色面板', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const colorButton = screen.getByRole('button', { name: '打开颜色选择器' })
      await user.click(colorButton)
      
      expect(screen.getByRole('dialog', { name: '颜色选择器' })).toBeInTheDocument()
      expect(screen.getByText('色相')).toBeInTheDocument()
      expect(screen.getByText('饱和度')).toBeInTheDocument()
      expect(screen.getByText('亮度')).toBeInTheDocument()
    })

    it('应该支持 HSL 调节', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const colorButton = screen.getByRole('button', { name: '打开颜色选择器' })
      await user.click(colorButton)
      
      const hueSlider = screen.getByRole('slider', { name: '色相' })
      await user.type(hueSlider, '180')
      
      // 检查颜色是否更新
      const colorInput = screen.getByLabelText('颜色')
      expect(colorInput.value).toMatch(/^#[0-9A-F]{6}$/i)
    })

    it('应该支持 RGB 输入', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const colorButton = screen.getByRole('button', { name: '打开颜色选择器' })
      await user.click(colorButton)
      
      const rgbInput = screen.getByLabelText('RGB 值')
      await user.clear(rgbInput)
      await user.type(rgbInput, 'rgb(255, 87, 51)')
      
      const colorInput = screen.getByLabelText('颜色')
      expect(colorInput).toHaveValue('#FF5733')
    })

    it('应该显示颜色历史', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      // 选择几个颜色
      await user.type(screen.getByLabelText('颜色'), '#FF5733')
      await user.clear(screen.getByLabelText('颜色'))
      await user.type(screen.getByLabelText('颜色'), '#10B981')
      
      const colorButton = screen.getByRole('button', { name: '打开颜色选择器' })
      await user.click(colorButton)
      
      expect(screen.getByText('最近使用')).toBeInTheDocument()
      expect(screen.getByTitle('#FF5733')).toBeInTheDocument()
      expect(screen.getByTitle('#10B981')).toBeInTheDocument()
    })
  })

  describe('图标选择器', () => {
    it('应该按分类显示图标', () => {
      render(<CategoryForm />)
      
      expect(screen.getByText('工作')).toBeInTheDocument()
      expect(screen.getByText('学习')).toBeInTheDocument()
      expect(screen.getByText('生活')).toBeInTheDocument()
      expect(screen.getByText('娱乐')).toBeInTheDocument()
    })

    it('应该支持图标分类切换', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const studyTab = screen.getByRole('tab', { name: '学习' })
      await user.click(studyTab)
      
      expect(screen.getByText('📚')).toBeInTheDocument()
      expect(screen.getByText('✏️')).toBeInTheDocument()
      expect(screen.getByText('📝')).toBeInTheDocument()
    })

    it('应该支持自定义图标输入', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const customIconInput = screen.getByPlaceholderText('输入自定义图标或 Emoji')
      await user.type(customIconInput, '🎯')
      
      const iconInput = screen.getByLabelText('图标')
      expect(iconInput).toHaveValue('🎯')
    })

    it('应该验证图标格式', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const iconInput = screen.getByLabelText('图标')
      await user.type(iconInput, 'invalid-icon-text')
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('请输入有效的图标或 Emoji')).toBeInTheDocument()
    })
  })

  describe('表单验证', () => {
    it('应该验证分类名称中的特殊字符', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const nameInput = screen.getByLabelText('分类名称')
      await user.type(nameInput, '分类\n换行')
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('分类名称不能包含换行符')).toBeInTheDocument()
    })

    it('应该验证描述长度', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const descriptionInput = screen.getByLabelText('描述')
      await user.type(descriptionInput, 'a'.repeat(201))
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('描述长度不能超过200个字符')).toBeInTheDocument()
    })

    it('应该防止创建重复的分类名称', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const nameInput = screen.getByLabelText('分类名称')
      await user.type(nameInput, '工作') // 已存在的分类名称
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('分类名称已存在')).toBeInTheDocument()
    })

    it('应该防止循环父子关系', async () => {
      const user = userEvent.setup()
      render(<CategoryForm category={{ ...mockCategory, id: '1' }} />)
      
      const parentSelect = screen.getByLabelText('父分类')
      await user.selectOptions(parentSelect, '1') // 选择自己作为父分类
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('不能选择自己作为父分类')).toBeInTheDocument()
    })
  })

  describe('无障碍性', () => {
    it('应该有正确的标签关联', () => {
      render(<CategoryForm />)
      
      const nameInput = screen.getByLabelText('分类名称')
      expect(nameInput).toHaveAttribute('aria-required', 'true')
      
      const colorInput = screen.getByLabelText('颜色')
      expect(colorInput).toHaveAttribute('aria-required', 'true')
    })

    it('应该显示验证错误的 ARIA 描述', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      const nameInput = screen.getByLabelText('分类名称')
      expect(nameInput).toHaveAttribute('aria-describedby')
      expect(nameInput).toHaveAttribute('aria-invalid', 'true')
    })

    it('应该支持键盘导航', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      await user.tab()
      expect(screen.getByLabelText('分类名称')).toHaveFocus()
      
      await user.tab()
      expect(screen.getByLabelText('描述')).toHaveFocus()
    })
  })

  describe('响应式设计', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<CategoryForm />)
      
      const form = screen.getByRole('form')
      expect(form).toHaveClass('w-full')
    })

    it('应该在移动设备上使用堆叠布局', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<CategoryForm />)
      
      const colorPreview = screen.getByTestId('color-preview')
      expect(colorPreview).toHaveClass('mb-4')
    })
  })

  describe('性能优化', () => {
    it('应该防抖表单验证', async () => {
      const user = userEvent.setup()
      render(<CategoryForm />)
      
      const nameInput = screen.getByLabelText('分类名称')
      
      // 快速输入多个字符
      await user.type(nameInput, 'abc', { delay: 10 })
      
      // 验证应该被防抖
      await waitFor(() => {
        expect(screen.queryByText('分类名称不能为空')).not.toBeInTheDocument()
      })
    })

    it('应该懒加载图标资源', () => {
      render(<CategoryForm />)
      
      // 检查是否只加载了当前分类的图标
      const workIcons = screen.getAllByRole('button', { name: /工作图标/ })
      expect(workIcons.length).toBeLessThan(50) // 应该有分页或懒加载
    })
  })
})