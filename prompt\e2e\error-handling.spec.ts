import { test, expect } from '@playwright/test'
import { testData } from './test-data'

test.describe('错误处理测试', () => {
  test.beforeEach(async ({ page }) => {
    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Console error:', msg.text())
      }
    })

    // 监听页面错误
    page.on('pageerror', error => {
      console.log('Page error:', error.message)
    })
  })

  test('应该处理网络连接错误', async ({ page }) => {
    // 拦截所有API请求，模拟网络错误
    await page.route('**/api/trpc/**', route => {
      route.abort('failed')
    })

    await page.goto('/')

    // 检查错误状态显示
    await expect(page.locator('[data-testid="error-state"]')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('text=网络连接失败')).toBeVisible()

    // 检查重试按钮
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()

    // 恢复网络连接
    await page.unroute('**/api/trpc/**')

    // 点击重试
    await page.click('[data-testid="retry-button"]')

    // 检查页面是否恢复正常
    await expect(page.locator('[data-testid="error-state"]')).not.toBeVisible()
    await expect(page.locator('[data-testid="stats-total-prompts"]')).toBeVisible()
  })

  test('应该处理服务器错误', async ({ page }) => {
    // 拦截API请求，返回服务器错误
    await page.route('**/api/trpc/prompts.getAll**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: {
            message: '服务器内部错误',
            code: 'INTERNAL_SERVER_ERROR'
          }
        })
      })
    })

    await page.goto('/prompts')

    // 检查错误消息
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
    await expect(page.locator('text=服务器内部错误')).toBeVisible()

    // 检查错误详情
    await page.click('[data-testid="show-error-details"]')
    await expect(page.locator('[data-testid="error-details"]')).toBeVisible()
    await expect(page.locator('[data-testid="error-code"]')).toContainText('INTERNAL_SERVER_ERROR')
  })

  test('应该处理认证错误', async ({ page }) => {
    // 拦截API请求，返回认证错误
    await page.route('**/api/trpc/**', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          error: {
            message: '身份验证失败',
            code: 'UNAUTHORIZED'
          }
        })
      })
    })

    await page.goto('/prompts')

    // 检查是否重定向到登录页面
    await expect(page).toHaveURL(/\/auth\/login/)

    // 检查错误提示
    await expect(page.locator('[data-testid="auth-error"]')).toBeVisible()
    await expect(page.locator('text=请重新登录')).toBeVisible()
  })

  test('应该处理权限错误', async ({ page }) => {
    // 拦截API请求，返回权限错误
    await page.route('**/api/trpc/prompts.delete**', route => {
      route.fulfill({
        status: 403,
        contentType: 'application/json',
        body: JSON.stringify({
          error: {
            message: '没有权限执行此操作',
            code: 'FORBIDDEN'
          }
        })
      })
    })

    await page.goto('/prompts')

    // 尝试删除提示词
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="delete-prompt-button"]')
    await page.click('[data-testid="confirm-delete-button"]')

    // 检查权限错误提示
    await expect(page.locator('[data-testid="error-toast"]')).toBeVisible()
    await expect(page.locator('text=没有权限执行此操作')).toBeVisible()

    // 检查删除对话框是否仍然打开
    await expect(page.locator('[data-testid="confirm-delete-modal"]')).toBeVisible()
  })

  test('应该处理表单验证错误', async ({ page }) => {
    await page.goto('/prompts/new')

    // 提交空表单
    await page.click('[data-testid="save-prompt-button"]')

    // 检查验证错误
    await expect(page.locator('[data-testid="title-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="title-error"]')).toContainText('标题不能为空')

    await expect(page.locator('[data-testid="content-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="content-error"]')).toContainText('内容不能为空')

    // 填写过长的标题
    await page.fill('[data-testid="prompt-title-input"]', testData.errorScenarios.invalidPrompt.description)

    // 检查长度验证
    await expect(page.locator('[data-testid="title-error"]')).toContainText('标题长度不能超过')

    // 填写无效的标签
    await page.fill('[data-testid="tags-input"]', '   ')
    await page.keyboard.press('Enter')

    // 检查标签验证
    await expect(page.locator('[data-testid="tags-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="tags-error"]')).toContainText('标签不能为空')
  })

  test('应该处理文件上传错误', async ({ page }) => {
    await page.goto('/prompts/import')

    // 上传过大的文件
    const largeFileContent = 'A'.repeat(10 * 1024 * 1024) // 10MB
    const fileInput = page.locator('[data-testid="import-file-input"]')
    
    await fileInput.setInputFiles({
      name: 'large-file.json',
      mimeType: 'application/json',
      buffer: Buffer.from(largeFileContent)
    })

    // 检查文件大小错误
    await expect(page.locator('[data-testid="file-size-error"]')).toBeVisible()
    await expect(page.locator('text=文件大小超过限制')).toBeVisible()

    // 上传无效文件类型
    await fileInput.setInputFiles({
      name: 'invalid.exe',
      mimeType: 'application/octet-stream',
      buffer: Buffer.from('invalid content')
    })

    // 检查文件类型错误
    await expect(page.locator('[data-testid="file-type-error"]')).toBeVisible()
    await expect(page.locator('text=不支持的文件类型')).toBeVisible()

    // 上传损坏的JSON文件
    await fileInput.setInputFiles({
      name: 'corrupted.json',
      mimeType: 'application/json',
      buffer: Buffer.from('{invalid json content')
    })

    // 检查JSON格式错误
    await expect(page.locator('[data-testid="json-format-error"]')).toBeVisible()
    await expect(page.locator('text=JSON格式错误')).toBeVisible()
  })

  test('应该处理数据库连接错误', async ({ page }) => {
    // 拦截API请求，模拟数据库错误
    await page.route('**/api/trpc/**', route => {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({
          error: {
            message: '数据库连接失败',
            code: 'DATABASE_ERROR'
          }
        })
      })
    })

    await page.goto('/')

    // 检查数据库错误提示
    await expect(page.locator('[data-testid="database-error"]')).toBeVisible()
    await expect(page.locator('text=数据库连接失败')).toBeVisible()

    // 检查是否显示联系管理员的提示
    await expect(page.locator('text=请联系系统管理员')).toBeVisible()
  })

  test('应该处理超时错误', async ({ page }) => {
    // 拦截API请求，添加长时间延迟
    await page.route('**/api/trpc/prompts.create**', async route => {
      await new Promise(resolve => setTimeout(resolve, 30000)) // 30秒延迟
      await route.continue()
    })

    await page.goto('/prompts/new')

    // 填写表单
    await page.fill('[data-testid="prompt-title-input"]', '超时测试')
    await page.fill('[data-testid="prompt-content-input"]', '这是超时测试内容')

    // 提交表单
    await page.click('[data-testid="save-prompt-button"]')

    // 检查加载状态
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible()

    // 等待超时错误出现
    await expect(page.locator('[data-testid="timeout-error"]')).toBeVisible({ timeout: 35000 })
    await expect(page.locator('text=请求超时')).toBeVisible()

    // 检查重试选项
    await expect(page.locator('[data-testid="retry-save-button"]')).toBeVisible()
  })

  test('应该处理并发操作冲突', async ({ page }) => {
    await page.goto('/prompts')

    // 模拟并发编辑冲突
    await page.route('**/api/trpc/prompts.update**', route => {
      route.fulfill({
        status: 409,
        contentType: 'application/json',
        body: JSON.stringify({
          error: {
            message: '数据已被其他用户修改',
            code: 'CONFLICT'
          }
        })
      })
    })

    // 编辑提示词
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="edit-prompt-button"]')
    await page.fill('[data-testid="prompt-title-input"]', '修改后的标题')
    await page.click('[data-testid="save-prompt-button"]')

    // 检查冲突错误
    await expect(page.locator('[data-testid="conflict-error"]')).toBeVisible()
    await expect(page.locator('text=数据已被其他用户修改')).toBeVisible()

    // 检查解决选项
    await expect(page.locator('[data-testid="reload-data-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="force-save-button"]')).toBeVisible()
  })

  test('应该处理浏览器兼容性错误', async ({ page }) => {
    // 模拟不支持的浏览器功能
    await page.addInitScript(() => {
      // 移除 localStorage 支持
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: false
      })
    })

    await page.goto('/')

    // 检查浏览器兼容性警告
    await expect(page.locator('[data-testid="browser-compatibility-warning"]')).toBeVisible()
    await expect(page.locator('text=您的浏览器不支持某些功能')).toBeVisible()
  })

  test('应该处理内存不足错误', async ({ page }) => {
    // 模拟大量数据导致内存问题
    await page.route('**/api/trpc/prompts.getAll**', route => {
      const largeData = Array.from({ length: 10000 }, (_, i) => ({
        id: i + 1,
        title: `大数据测试${i + 1}`,
        content: 'A'.repeat(10000),
        description: 'B'.repeat(1000),
        category: { name: '测试', color: '#000', icon: '📝' },
        tags: Array.from({ length: 100 }, (_, j) => `标签${j}`),
        usageCount: i,
        isFavorite: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))

      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ result: { data: largeData } })
      })
    })

    await page.goto('/prompts')

    // 检查是否有内存警告
    await expect(page.locator('[data-testid="memory-warning"]')).toBeVisible({ timeout: 10000 })
    await expect(page.locator('text=数据量过大')).toBeVisible()

    // 检查分页或虚拟滚动是否启用
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible()
  })

  test('应该处理离线状态', async ({ page }) => {
    await page.goto('/')

    // 模拟网络离线
    await page.context().setOffline(true)

    // 尝试执行需要网络的操作
    await page.click('[data-testid="quick-action-new-prompt"]')

    // 检查离线提示
    await expect(page.locator('[data-testid="offline-notice"]')).toBeVisible()
    await expect(page.locator('text=当前处于离线状态')).toBeVisible()

    // 检查离线功能
    await expect(page.locator('[data-testid="offline-mode-indicator"]')).toBeVisible()

    // 恢复网络连接
    await page.context().setOffline(false)

    // 检查重新连接提示
    await expect(page.locator('[data-testid="online-notice"]')).toBeVisible()
    await expect(page.locator('text=网络连接已恢复')).toBeVisible()
  })

  test('应该处理页面崩溃', async ({ page }) => {
    await page.goto('/')

    // 监听页面崩溃
    let crashed = false
    page.on('crash', () => {
      crashed = true
    })

    // 模拟导致崩溃的操作（注入恶意脚本）
    await page.evaluate(() => {
      // 尝试创建大量DOM元素导致崩溃
      try {
        for (let i = 0; i < 1000000; i++) {
          const div = document.createElement('div')
          div.innerHTML = 'A'.repeat(1000)
          document.body.appendChild(div)
        }
      } catch (error) {
        console.error('内存不足:', error)
      }
    })

    // 等待一段时间看是否崩溃
    await page.waitForTimeout(3000)

    if (crashed) {
      console.log('页面崩溃已被检测到')
    } else {
      // 如果没有崩溃，检查错误处理
      await expect(page.locator('body')).toBeVisible()
    }
  })

  test('应该优雅地处理JavaScript错误', async ({ page }) => {
    // 监听JavaScript错误
    const jsErrors: string[] = []
    page.on('pageerror', error => {
      jsErrors.push(error.message)
    })

    await page.goto('/')

    // 注入会产生错误的代码
    await page.evaluate(() => {
      // 尝试访问不存在的对象
      try {
        (window as any).nonExistentObject.someMethod()
      } catch (error) {
        console.error('捕获到错误:', error)
      }
    })

    // 检查错误是否被适当处理
    await page.waitForTimeout(1000)

    // 页面应该仍然可用
    await expect(page.locator('body')).toBeVisible()

    // 检查是否有错误边界组件
    const hasErrorBoundary = await page.locator('[data-testid="error-boundary"]').isVisible()
    
    if (hasErrorBoundary) {
      await expect(page.locator('[data-testid="error-boundary"]')).toContainText('出现了意外错误')
      await expect(page.locator('[data-testid="error-boundary-retry"]')).toBeVisible()
    }
  })
})