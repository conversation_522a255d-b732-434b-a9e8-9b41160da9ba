import { test, expect } from '@playwright/test'

test.describe('性能测试', () => {
  test('页面加载性能测试', async ({ page }) => {
    // 记录开始时间
    const startTime = Date.now()
    
    // 访问首页
    await page.goto('http://localhost:3000')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    
    // 计算加载时间
    const loadTime = Date.now() - startTime
    console.log(`页面加载时间: ${loadTime}ms`)
    
    // 页面应该在5秒内加载完成
    expect(loadTime).toBeLessThan(5000)
    
    // 获取性能指标
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        totalTime: navigation.loadEventEnd - navigation.fetchStart
      }
    })
    
    console.log('性能指标:', performanceMetrics)
    
    // 验证性能指标
    expect(performanceMetrics.domContentLoaded).toBeLessThan(2000)
    expect(performanceMetrics.loadComplete).toBeLessThan(3000)
  })

  test('响应式设计测试', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
    
    // 截图桌面版本
    await page.screenshot({ path: 'test-results/desktop-view.png' })
    
    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    await page.screenshot({ path: 'test-results/tablet-view.png' })
    
    // 测试移动端
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    await page.screenshot({ path: 'test-results/mobile-view.png' })
    
    // 检查移动端是否正常显示
    await expect(page.locator('body')).toBeVisible()
    console.log('响应式设计测试完成')
  })

  test('JavaScript 错误检测', async ({ page }) => {
    const errors: string[] = []
    
    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    // 监听页面错误
    page.on('pageerror', error => {
      errors.push(error.message)
    })
    
    // 访问页面
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
    
    // 等待一段时间收集错误
    await page.waitForTimeout(3000)
    
    // 报告错误
    console.log('检测到的JavaScript错误数量:', errors.length)
    if (errors.length > 0) {
      console.log('错误详情:', errors)
    }
    
    // 期望没有严重错误（某些开发模式的警告是可以接受的）
    const seriousErrors = errors.filter(error => 
      !error.includes('Warning') && 
      !error.includes('Development') &&
      !error.includes('HMR')
    )
    
    expect(seriousErrors.length).toBeLessThan(3)
  })

  test('网络请求测试', async ({ page }) => {
    const requests: any[] = []
    const responses: any[] = []
    
    // 监听网络请求
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType()
      })
    })
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        statusText: response.statusText()
      })
    })
    
    // 访问页面
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
    
    // 分析请求
    console.log(`总请求数: ${requests.length}`)
    console.log(`总响应数: ${responses.length}`)
    
    // 检查是否有失败的请求
    const failedRequests = responses.filter(response => response.status >= 400)
    console.log(`失败的请求数: ${failedRequests.length}`)
    
    if (failedRequests.length > 0) {
      console.log('失败的请求:', failedRequests)
    }
    
    // 允许一些请求失败（如API调用，因为没有真实的后端）
    expect(failedRequests.length).toBeLessThan(10)
  })

  test('页面大小和资源优化', async ({ page }) => {
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
    
    // 获取页面内容大小
    const pageContent = await page.content()
    const pageSize = pageContent.length
    
    console.log(`页面HTML大小: ${pageSize} 字节`)
    console.log(`页面HTML大小: ${(pageSize / 1024).toFixed(2)} KB`)
    
    // 检查页面大小是否合理（不超过1MB）
    expect(pageSize).toBeLessThan(1024 * 1024)
    
    // 获取资源加载信息
    const resourceMetrics = await page.evaluate(() => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      return {
        totalResources: resources.length,
        jsResources: resources.filter(r => r.name.includes('.js')).length,
        cssResources: resources.filter(r => r.name.includes('.css')).length,
        imageResources: resources.filter(r => r.initiatorType === 'img').length
      }
    })
    
    console.log('资源统计:', resourceMetrics)
    
    // 验证资源数量是否合理
    expect(resourceMetrics.totalResources).toBeLessThan(100)
    expect(resourceMetrics.jsResources).toBeLessThan(50)
    expect(resourceMetrics.cssResources).toBeLessThan(10)
  })
})