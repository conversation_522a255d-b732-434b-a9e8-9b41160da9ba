'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { api } from '~/trpc/react'
import { PromptGridWithSelection } from '~/components/prompts/PromptGridWithSelection'
import { FilterBar } from '~/components/filters'

export default function PromptsPage() {
  // 筛选状态
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<'created' | 'updated' | 'usage' | 'title'>('updated')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showFavorites, setShowFavorites] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // 获取提示词列表
  const { data: promptsData, isLoading, refetch } = api.prompts.getFiltered.useQuery({
    categoryIds: selectedCategories.length > 0 ? selectedCategories : undefined,
    tagIds: selectedTags.length > 0 ? selectedTags : undefined,
    sortBy,
    sortOrder,
    favoritesOnly: showFavorites,
  })

  const prompts = promptsData?.prompts || []

  // 处理排序变化
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy as 'created' | 'updated' | 'usage' | 'title')
    setSortOrder(newSortOrder)
  }

  // 检查是否有筛选条件
  const hasFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-base-content">提示词管理</h1>
          <p className="text-base-content/70 mt-1">
            {prompts ? `共 ${prompts.length} 个提示词` : '加载中...'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* 视图切换 */}
          <div className="btn-group">
            <button
              onClick={() => setViewMode('grid')}
              className={`btn btn-sm ${viewMode === 'grid' ? 'btn-active' : ''}`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z"
                />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`btn btn-sm ${viewMode === 'list' ? 'btn-active' : ''}`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                />
              </svg>
            </button>
          </div>

          {/* 新建按钮 */}
          <Link href="/prompts/new" className="btn btn-primary btn-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            新建提示词
          </Link>

          {/* 导入按钮 */}
          <Link href="/prompts/import" className="btn btn-secondary btn-sm">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
              />
            </svg>
            批量导入
          </Link>
        </div>
      </div>

      {/* 筛选栏 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-base-100 rounded-lg p-4 shadow-sm"
      >
        <FilterBar
          selectedCategories={selectedCategories}
          onCategoryChange={setSelectedCategories}
          selectedTags={selectedTags}
          onTagChange={setSelectedTags}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={handleSortChange}
          showFavorites={showFavorites}
          onShowFavoritesChange={setShowFavorites}
        />
      </motion.div>

      {/* 提示词列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <PromptGridWithSelection
          prompts={prompts}
          loading={isLoading}
          columns={viewMode === 'grid' ? 3 : 1}
          showCategory={true}
          showActions={true}
          emptyMessage={
            hasFilters
              ? "未找到符合条件的提示词"
              : "还没有创建任何提示词"
          }
          emptyAction={
            !hasFilters ? (
              <Link href="/prompts/new" className="btn btn-primary">
                创建第一个提示词
              </Link>
            ) : undefined
          }
          onPromptUpdate={refetch}
        />
      </motion.div>
    </div>
  )
}

// 注意：metadata 不能在客户端组件中导出，已移除