'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { api } from '~/trpc/react'
import { ImportPreview } from '~/components/import/ImportPreview'
import { ImportProgress } from '~/components/import/ImportProgress'
import { toast } from 'react-hot-toast'

interface ImportData {
  title: string
  content: string
  description?: string
  categoryId?: string
  tags?: string[]
  isFavorite?: boolean
}

interface ImportResult {
  success: number
  failed: number
  errors: string[]
  imported: any[]
}

export default function ImportPage() {
  const [step, setStep] = useState<'upload' | 'preview' | 'importing' | 'complete'>('upload')
  const [importData, setImportData] = useState<ImportData[]>([])
  const [selectedItems, setSelectedItems] = useState<number[]>([])
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [dragActive, setDragActive] = useState(false)

  // 获取分类列表
  const { data: categories } = api.categories.getAll.useQuery()

  // 批量导入mutation
  const importMutation = api.prompts.batchImport.useMutation({
    onSuccess: (result) => {
      setImportResult(result)
      setStep('complete')
      toast.success(`成功导入 ${result.success} 个提示词`)
    },
    onError: (error) => {
      toast.error('导入失败: ' + error.message)
    },
  })

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string
        const data = JSON.parse(text)
        
        // 验证数据格式
        if (!Array.isArray(data)) {
          throw new Error('文件格式错误：应该是包含提示词对象的数组')
        }

        // 验证每个项目的基本字段
        const validatedData: ImportData[] = data.map((item, index) => {
          if (!item.title || !item.content) {
            throw new Error(`第 ${index + 1} 个项目缺少必需字段 (title, content)`)
          }
          return {
            title: item.title,
            content: item.content,
            description: item.description || '',
            categoryId: item.categoryId || undefined,
            tags: Array.isArray(item.tags) ? item.tags : [],
            isFavorite: Boolean(item.isFavorite),
          }
        })

        setImportData(validatedData)
        setSelectedItems(validatedData.map((_, index) => index))
        setStep('preview')
        toast.success(`成功解析 ${validatedData.length} 个提示词`)
      } catch (error) {
        toast.error('文件解析失败: ' + (error instanceof Error ? error.message : '未知错误'))
      }
    }
    reader.readAsText(file)
  }

  // 处理文件拖拽
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = e.dataTransfer.files
    if (files && files[0]) {
      const file = files[0]
      if (file.type === 'application/json') {
        handleFileUpload(file)
      } else {
        toast.error('请上传 JSON 格式的文件')
      }
    }
  }

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      handleFileUpload(files[0])
    }
  }

  // 开始导入
  const startImport = () => {
    const selectedData = importData.filter((_, index) => selectedItems.includes(index))
    setStep('importing')
    importMutation.mutate(selectedData)
  }

  // 重新开始
  const restart = () => {
    setStep('upload')
    setImportData([])
    setSelectedItems([])
    setImportResult(null)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 标题和导航 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-base-content">批量导入提示词</h1>
          <p className="text-base-content/70 mt-2">
            支持从 JSON 文件批量导入提示词数据
          </p>
        </div>
        <Link href="/prompts" className="btn btn-ghost">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
          </svg>
          返回列表
        </Link>
      </div>

      {/* 步骤指示器 */}
      <div className="steps steps-horizontal w-full mb-8">
        <div className={`step ${step !== 'upload' ? 'step-primary' : ''}`}>上传文件</div>
        <div className={`step ${step === 'preview' || step === 'importing' || step === 'complete' ? 'step-primary' : ''}`}>
          预览数据
        </div>
        <div className={`step ${step === 'importing' || step === 'complete' ? 'step-primary' : ''}`}>
          导入进行中
        </div>
        <div className={`step ${step === 'complete' ? 'step-primary' : ''}`}>完成</div>
      </div>

      {/* 内容区域 */}
      <AnimatePresence mode="wait">
        {step === 'upload' && (
          <motion.div
            key="upload"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* 文件格式说明 */}
            <div className="bg-info/10 border border-info/20 rounded-lg p-4">
              <h3 className="font-semibold text-info mb-2">文件格式要求</h3>
              <div className="text-sm text-base-content/80 space-y-1">
                <p>• 文件格式：JSON</p>
                <p>• 数据结构：对象数组</p>
                <p>• 必需字段：title (标题), content (内容)</p>
                <p>• 可选字段：description (描述), categoryId (分类ID), tags (标签数组), isFavorite (是否收藏)</p>
              </div>
              
              <details className="mt-3">
                <summary className="cursor-pointer text-info font-medium">查看示例格式</summary>
                <pre className="bg-base-200 rounded p-3 mt-2 text-xs overflow-x-auto">
{`[
  {
    "title": "编程助手提示词",
    "content": "你是一个专业的编程助手，请帮助用户解决编程问题...",
    "description": "用于代码调试和优化的提示词",
    "tags": ["编程", "调试", "优化"],
    "isFavorite": false
  },
  {
    "title": "文案创作助手",
    "content": "请根据以下要求创作专业的营销文案...",
    "description": "营销文案创作专用",
    "tags": ["文案", "营销"],
    "isFavorite": true
  }
]`}
                </pre>
              </details>
            </div>

            {/* 文件上传区域 */}
            <div
              className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                dragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-base-300 hover:border-base-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-16 h-16 mx-auto text-base-content/50 mb-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
                />
              </svg>
              
              <h3 className="text-lg font-semibold text-base-content mb-2">
                拖拽文件到此处或点击选择
              </h3>
              <p className="text-base-content/70 mb-4">
                支持 JSON 格式文件，最大 10MB
              </p>
              
              <input
                type="file"
                accept=".json"
                onChange={handleFileSelect}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload" className="btn btn-primary">
                选择文件
              </label>
            </div>
          </motion.div>
        )}

        {step === 'preview' && (
          <motion.div
            key="preview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <ImportPreview
              data={importData}
              selectedItems={selectedItems}
              onSelectionChange={setSelectedItems}
              categories={categories || []}
              onStartImport={startImport}
              onCancel={restart}
            />
          </motion.div>
        )}

        {step === 'importing' && (
          <motion.div
            key="importing"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <ImportProgress
              total={selectedItems.length}
              isLoading={importMutation.isLoading}
            />
          </motion.div>
        )}

        {step === 'complete' && importResult && (
          <motion.div
            key="complete"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center space-y-6"
          >
            <div className="text-6xl mb-4">
              {importResult.failed === 0 ? '🎉' : '⚠️'}
            </div>
            
            <h2 className="text-2xl font-bold text-base-content">
              导入完成
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
              <div className="stat bg-success/10 rounded-lg">
                <div className="stat-title text-success">成功</div>
                <div className="stat-value text-success">{importResult.success}</div>
              </div>
              <div className="stat bg-error/10 rounded-lg">
                <div className="stat-title text-error">失败</div>
                <div className="stat-value text-error">{importResult.failed}</div>
              </div>
            </div>

            {importResult.errors.length > 0 && (
              <div className="bg-error/10 border border-error/20 rounded-lg p-4 text-left max-w-2xl mx-auto">
                <h3 className="font-semibold text-error mb-2">错误详情</h3>
                <div className="space-y-1 text-sm">
                  {importResult.errors.map((error, index) => (
                    <p key={index} className="text-base-content/80">
                      • {error}
                    </p>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-center space-x-4">
              <button onClick={restart} className="btn btn-ghost">
                重新导入
              </button>
              <Link href="/prompts" className="btn btn-primary">
                查看导入的提示词
              </Link>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

