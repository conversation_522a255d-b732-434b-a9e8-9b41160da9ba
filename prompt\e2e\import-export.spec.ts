import { test, expect } from '@playwright/test'
import { testData } from './test-data'

test.describe('导入导出功能', () => {
  test.beforeEach(async ({ page }) => {
    // 访问首页
    await page.goto('/')
  })

  test('应该能够导出提示词数据', async ({ page }) => {
    // 前往提示词管理页面
    await page.goto('/prompts')

    // 点击导出按钮
    await page.click('[data-testid="export-prompts-button"]')

    // 检查导出选项模态框
    await expect(page.locator('[data-testid="export-modal"]')).toBeVisible()

    // 选择导出格式
    await page.click('[data-testid="export-format-json"]')

    // 选择导出范围
    await page.click('[data-testid="export-scope-all"]')

    // 确认导出
    const downloadPromise = page.waitForEvent('download')
    await page.click('[data-testid="confirm-export-button"]')
    const download = await downloadPromise

    // 检查下载文件
    expect(download.suggestedFilename()).toMatch(/prompts.*\.json$/)

    // 检查成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=导出成功')).toBeVisible()
  })

  test('应该能够导出选中的提示词', async ({ page }) => {
    // 前往提示词管理页面
    await page.goto('/prompts')

    // 选择多个提示词
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="select-checkbox"]')
    await page.click('[data-testid="prompt-card"]:nth-child(2) [data-testid="select-checkbox"]')

    // 点击批量导出按钮
    await page.click('[data-testid="batch-export-button"]')

    // 检查导出选项
    await expect(page.locator('[data-testid="export-modal"]')).toBeVisible()
    await expect(page.locator('text=导出选中的 2 个提示词')).toBeVisible()

    // 选择导出格式
    await page.click('[data-testid="export-format-csv"]')

    // 确认导出
    const downloadPromise = page.waitForEvent('download')
    await page.click('[data-testid="confirm-export-button"]')
    const download = await downloadPromise

    // 检查下载文件
    expect(download.suggestedFilename()).toMatch(/prompts.*\.csv$/)
  })

  test('应该能够导入提示词数据', async ({ page }) => {
    // 前往导入页面
    await page.goto('/prompts/import')

    // 检查导入页面标题
    await expect(page.locator('h1')).toContainText('导入提示词')

    // 选择文件上传
    const fileInput = page.locator('[data-testid="import-file-input"]')
    
    // 创建测试文件内容
    const testFileContent = JSON.stringify({
      version: '1.0',
      prompts: testData.importData.prompts,
      categories: testData.importData.categories
    })

    // 模拟文件上传
    await fileInput.setInputFiles({
      name: 'test-prompts.json',
      mimeType: 'application/json',
      buffer: Buffer.from(testFileContent)
    })

    // 检查文件预览
    await expect(page.locator('[data-testid="import-preview"]')).toBeVisible()
    await expect(page.locator('[data-testid="import-stats"]')).toContainText('2 个提示词')
    await expect(page.locator('[data-testid="import-stats"]')).toContainText('2 个分类')

    // 检查预览列表
    await expect(page.locator('[data-testid="import-prompt-item"]')).toHaveCount(2)
    await expect(page.locator('[data-testid="import-category-item"]')).toHaveCount(2)

    // 配置导入选项
    await page.click('[data-testid="import-option-merge-categories"]')
    await page.click('[data-testid="import-option-skip-duplicates"]')

    // 确认导入
    await page.click('[data-testid="confirm-import-button"]')

    // 检查导入进度
    await expect(page.locator('[data-testid="import-progress"]')).toBeVisible()

    // 等待导入完成
    await expect(page.locator('[data-testid="import-success"]')).toBeVisible({ timeout: 10000 })

    // 检查导入结果
    await expect(page.locator('[data-testid="import-result"]')).toContainText('成功导入 2 个提示词')
    await expect(page.locator('[data-testid="import-result"]')).toContainText('成功创建 2 个分类')

    // 检查是否跳转到提示词列表
    await page.click('[data-testid="view-imported-prompts"]')
    await expect(page).toHaveURL('/prompts')

    // 验证导入的数据
    await expect(page.locator('[data-testid="prompt-card"]')).toContainText('测试提示词1')
    await expect(page.locator('[data-testid="prompt-card"]')).toContainText('测试提示词2')
  })

  test('应该处理导入文件验证错误', async ({ page }) => {
    // 前往导入页面
    await page.goto('/prompts/import')

    // 上传无效文件
    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles({
      name: 'invalid.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('This is not a valid JSON file')
    })

    // 检查错误消息
    await expect(page.locator('[data-testid="file-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="file-error"]')).toContainText('文件格式不正确')

    // 上传空 JSON 文件
    await fileInput.setInputFiles({
      name: 'empty.json',
      mimeType: 'application/json',
      buffer: Buffer.from('{}')
    })

    // 检查验证错误
    await expect(page.locator('[data-testid="validation-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="validation-error"]')).toContainText('文件内容为空')

    // 上传格式错误的文件
    await fileInput.setInputFiles({
      name: 'invalid-format.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify({ invalid: 'format' }))
    })

    // 检查格式错误
    await expect(page.locator('[data-testid="format-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="format-error"]')).toContainText('文件格式不符合要求')
  })

  test('应该支持大文件导入', async ({ page }) => {
    // 前往导入页面
    await page.goto('/prompts/import')

    // 创建大量测试数据
    const largeData = {
      version: '1.0',
      prompts: Array.from({ length: 100 }, (_, i) => ({
        title: `大批量测试提示词${i + 1}`,
        content: `这是第${i + 1}个测试提示词的内容`,
        description: `测试描述${i + 1}`,
        category: '测试分类',
        tags: [`标签${i + 1}`, '批量测试'],
        isPublic: i % 2 === 0
      })),
      categories: [
        {
          name: '测试分类',
          description: '大批量测试分类',
          color: '#3b82f6',
          icon: '📝'
        }
      ]
    }

    // 上传大文件
    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles({
      name: 'large-import.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify(largeData))
    })

    // 检查文件大小警告
    await expect(page.locator('[data-testid="large-file-warning"]')).toBeVisible()
    await expect(page.locator('[data-testid="large-file-warning"]')).toContainText('文件较大，导入可能需要较长时间')

    // 检查预览限制
    await expect(page.locator('[data-testid="preview-limit-notice"]')).toBeVisible()
    await expect(page.locator('[data-testid="preview-limit-notice"]')).toContainText('显示前 10 项')

    // 确认导入
    await page.click('[data-testid="confirm-import-button"]')

    // 检查导入进度条
    await expect(page.locator('[data-testid="import-progress-bar"]')).toBeVisible()

    // 等待导入完成（增加超时时间）
    await expect(page.locator('[data-testid="import-success"]')).toBeVisible({ timeout: 30000 })

    // 检查导入结果
    await expect(page.locator('[data-testid="import-result"]')).toContainText('成功导入 100 个提示词')
  })

  test('应该支持导入冲突处理', async ({ page }) => {
    // 先创建一个提示词
    await page.goto('/prompts/new')
    await page.fill('[data-testid="prompt-title-input"]', '测试提示词1')
    await page.fill('[data-testid="prompt-content-input"]', '原始内容')
    await page.click('[data-testid="save-prompt-button"]')

    // 前往导入页面
    await page.goto('/prompts/import')

    // 创建包含冲突数据的文件
    const conflictData = {
      version: '1.0',
      prompts: [
        {
          title: '测试提示词1', // 与现有提示词标题相同
          content: '新的内容',
          description: '新的描述',
          category: '工作',
          tags: ['冲突测试'],
          isPublic: true
        }
      ]
    }

    // 上传文件
    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles({
      name: 'conflict-test.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify(conflictData))
    })

    // 检查冲突警告
    await expect(page.locator('[data-testid="conflict-warning"]')).toBeVisible()
    await expect(page.locator('[data-testid="conflict-warning"]')).toContainText('发现 1 个重复项')

    // 查看冲突详情
    await page.click('[data-testid="view-conflicts-button"]')
    await expect(page.locator('[data-testid="conflict-list"]')).toBeVisible()
    await expect(page.locator('[data-testid="conflict-item"]')).toContainText('测试提示词1')

    // 选择处理方式：覆盖
    await page.click('[data-testid="conflict-resolution-overwrite"]')

    // 确认导入
    await page.click('[data-testid="confirm-import-button"]')

    // 等待导入完成
    await expect(page.locator('[data-testid="import-success"]')).toBeVisible()

    // 验证内容已被覆盖
    await page.goto('/prompts')
    await page.click('[data-testid="prompt-card"]:has-text("测试提示词1")')
    await expect(page.locator('[data-testid="prompt-detail-content"]')).toContainText('新的内容')
  })

  test('应该支持导出模板下载', async ({ page }) => {
    // 前往导入页面
    await page.goto('/prompts/import')

    // 点击下载模板按钮
    const downloadPromise = page.waitForEvent('download')
    await page.click('[data-testid="download-template-button"]')
    const download = await downloadPromise

    // 检查模板文件
    expect(download.suggestedFilename()).toBe('prompt-template.json')

    // 验证模板内容结构（如果可能的话）
    const templatePath = await download.path()
    if (templatePath) {
      // 可以进一步验证模板文件的内容结构
      console.log('Template downloaded:', templatePath)
    }
  })

  test('应该支持分类数据导入导出', async ({ page }) => {
    // 前往分类管理页面
    await page.goto('/categories')

    // 导出分类数据
    await page.click('[data-testid="export-categories-button"]')
    await page.click('[data-testid="export-format-json"]')

    const downloadPromise = page.waitForEvent('download')
    await page.click('[data-testid="confirm-export-button"]')
    const download = await downloadPromise

    expect(download.suggestedFilename()).toMatch(/categories.*\.json$/)

    // 导入分类数据
    await page.click('[data-testid="import-categories-button"]')

    const categoryData = {
      version: '1.0',
      categories: [
        {
          name: '新分类',
          description: '导入的新分类',
          color: '#ef4444',
          icon: '🆕'
        }
      ]
    }

    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles({
      name: 'categories.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify(categoryData))
    })

    await page.click('[data-testid="confirm-import-button"]')

    // 验证导入结果
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('[data-testid="category-card"]')).toContainText('新分类')
  })

  test('应该处理网络错误', async ({ page }) => {
    // 前往导入页面
    await page.goto('/prompts/import')

    // 拦截导入请求，模拟网络错误
    await page.route('**/api/trpc/prompts.import**', route => {
      route.abort('failed')
    })

    // 上传文件
    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles({
      name: 'test.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify({
        version: '1.0',
        prompts: [testData.importData.prompts[0]]
      }))
    })

    // 尝试导入
    await page.click('[data-testid="confirm-import-button"]')

    // 检查错误处理
    await expect(page.locator('[data-testid="import-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="import-error"]')).toContainText('导入失败')

    // 检查重试按钮
    await expect(page.locator('[data-testid="retry-import-button"]')).toBeVisible()
  })

  test('应该支持导入历史记录', async ({ page }) => {
    // 执行一次导入
    await page.goto('/prompts/import')

    const fileInput = page.locator('[data-testid="import-file-input"]')
    await fileInput.setInputFiles({
      name: 'history-test.json',
      mimeType: 'application/json',
      buffer: Buffer.from(JSON.stringify({
        version: '1.0',
        prompts: [testData.importData.prompts[0]]
      }))
    })

    await page.click('[data-testid="confirm-import-button"]')
    await expect(page.locator('[data-testid="import-success"]')).toBeVisible()

    // 检查导入历史
    await page.click('[data-testid="import-history-button"]')
    await expect(page.locator('[data-testid="import-history-modal"]')).toBeVisible()

    // 验证历史记录
    await expect(page.locator('[data-testid="history-item"]')).toHaveCount(1, { timeout: 5000 })
    await expect(page.locator('[data-testid="history-item"]')).toContainText('history-test.json')
    await expect(page.locator('[data-testid="history-item"]')).toContainText('1 个提示词')

    // 查看历史详情
    await page.click('[data-testid="history-item"]:first-child [data-testid="view-details-button"]')
    await expect(page.locator('[data-testid="history-details"]')).toBeVisible()
  })
})