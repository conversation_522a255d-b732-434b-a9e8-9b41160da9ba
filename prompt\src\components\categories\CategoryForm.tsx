'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { api } from '~/trpc/react'
import { Category } from '~/types'
import { toast } from 'react-hot-toast'

interface CategoryFormProps {
  category?: Category | null
  onClose: () => void
}

const DEFAULT_COLORS = [
  '#3b82f6', // blue
  '#ef4444', // red
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#ec4899', // pink
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#6b7280', // gray
]

const DEFAULT_ICONS = [
  '📁', '📂', '📋', '📝', '💡', '🎯', '🚀', '⭐', '🔥', '💎',
  '🎨', '🔧', '⚙️', '📊', '🎮', '💻', '📱', '🌟', '🎪', '🎭',
]

export const CategoryForm = ({ category, onClose }: CategoryFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: DEFAULT_COLORS[0],
    icon: '',
    parentId: null as string | null,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [showIconPicker, setShowIconPicker] = useState(false)
  const [customColor, setCustomColor] = useState('')

  // 获取所有分类（用于父分类选择）
  const { data: categories } = api.categories.getAll.useQuery()

  // 创建分类
  const createMutation = api.categories.create.useMutation({
    onSuccess: () => {
      toast.success('分类创建成功')
      onClose()
    },
    onError: (error) => {
      toast.error('创建失败: ' + error.message)
    },
  })

  // 更新分类
  const updateMutation = api.categories.update.useMutation({
    onSuccess: () => {
      toast.success('分类更新成功')
      onClose()
    },
    onError: (error) => {
      toast.error('更新失败: ' + error.message)
    },
  })

  // 初始化表单数据
  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        description: category.description || '',
        color: category.color,
        icon: category.icon || '',
        parentId: category.parentId,
      })
    }
  }, [category])

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '分类名称不能为空'
    } else if (formData.name.length > 50) {
      newErrors.name = '分类名称不能超过50个字符'
    }

    if (formData.description && formData.description.length > 200) {
      newErrors.description = '描述不能超过200个字符'
    }

    if (!formData.color) {
      newErrors.color = '请选择分类颜色'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const submitData = {
      ...formData,
      description: formData.description || undefined,
      icon: formData.icon || undefined,
      parentId: formData.parentId || undefined,
    }

    if (category) {
      updateMutation.mutate({
        id: category.id,
        ...submitData,
      })
    } else {
      createMutation.mutate(submitData)
    }
  }

  // 处理颜色选择
  const handleColorSelect = (color: string) => {
    setFormData(prev => ({ ...prev, color }))
    setShowColorPicker(false)
  }

  // 处理自定义颜色
  const handleCustomColor = () => {
    if (customColor && /^#[0-9A-F]{6}$/i.test(customColor)) {
      handleColorSelect(customColor)
      setCustomColor('')
    } else {
      toast.error('请输入有效的颜色值 (如: #FF0000)')
    }
  }

  // 处理图标选择
  const handleIconSelect = (icon: string) => {
    setFormData(prev => ({ ...prev, icon }))
    setShowIconPicker(false)
  }

  // 获取可用的父分类选项
  const getParentOptions = () => {
    if (!categories) return []
    
    return categories.filter(cat => {
      // 排除当前分类和其子分类
      if (category) {
        return cat.id !== category.id && cat.parentId !== category.id
      }
      return true
    })
  }

  const isLoading = createMutation.isLoading || updateMutation.isLoading

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <motion.div
        initial={{ scale: 0.95, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.95, opacity: 0, y: 20 }}
        transition={{ type: "spring", duration: 0.3 }}
        className="bg-base-100 rounded-xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-hidden border border-base-200"
      >
        {/* 表单头部 */}
        <div className="flex items-center justify-between p-6 border-b border-base-200 bg-base-50">
          <h2 className="text-2xl font-bold text-base-content">
            {category ? '编辑分类' : '新建分类'}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-circle hover:bg-base-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={2}
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 表单内容 */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form id="category-form" onSubmit={handleSubmit} className="p-6 space-y-6">

            {/* 分类名称 */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">分类名称 *</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`input input-bordered w-full ${errors.name ? 'input-error' : 'focus:input-primary'}`}
                placeholder="输入分类名称"
                maxLength={50}
                autoFocus
              />
              {errors.name && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.name}</span>
                </label>
              )}
              <label className="label">
                <span className="label-text-alt text-base-content/60">{formData.name.length}/50</span>
              </label>
            </div>

            {/* 分类描述 */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">描述</span>
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className={`textarea textarea-bordered h-24 resize-none ${errors.description ? 'textarea-error' : 'focus:textarea-primary'}`}
                placeholder="输入分类描述（可选）"
                maxLength={200}
              />
              {errors.description && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.description}</span>
                </label>
              )}
              <label className="label">
                <span className="label-text-alt text-base-content/60">{formData.description.length}/200</span>
              </label>
            </div>

            {/* 父分类选择 */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">父分类</span>
              </label>
              <select
                value={formData.parentId || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, parentId: e.target.value || null }))}
                className="select select-bordered w-full focus:select-primary"
              >
                <option value="">无（顶级分类）</option>
                {getParentOptions().map(cat => (
                  <option key={cat.id} value={cat.id}>
                    {cat.name}
                  </option>
                ))}
              </select>
            </div>

            {/* 颜色和图标选择 */}
            <div className="grid grid-cols-2 gap-6">
              {/* 颜色选择 */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">颜色 *</span>
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowColorPicker(!showColorPicker)}
                    className="w-full h-12 rounded-lg border-2 border-base-300 flex items-center justify-center hover:border-primary transition-colors"
                    style={{ backgroundColor: formData.color }}
                  >
                    <span className="text-white font-medium text-sm">{formData.color}</span>
                  </button>
                
                  <AnimatePresence>
                    {showColorPicker && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9, y: -10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.9, y: -10 }}
                        className="absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-4 shadow-xl z-20 min-w-[200px]"
                      >
                        <div className="grid grid-cols-5 gap-2 mb-4">
                          {DEFAULT_COLORS.map(color => (
                            <button
                              key={color}
                              type="button"
                              onClick={() => handleColorSelect(color)}
                              className={`w-8 h-8 rounded-md border-2 hover:scale-110 transition-all ${
                                formData.color === color ? 'border-primary ring-2 ring-primary/30' : 'border-base-300'
                              }`}
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            value={customColor}
                            onChange={(e) => setCustomColor(e.target.value)}
                            className="input input-sm flex-1"
                            placeholder="#FF0000"
                          />
                          <button
                            type="button"
                            onClick={handleCustomColor}
                            className="btn btn-sm btn-primary"
                          >
                            应用
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* 图标选择 */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">图标</span>
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowIconPicker(!showIconPicker)}
                    className="w-full h-12 border-2 border-base-300 rounded-lg flex items-center justify-center text-2xl hover:border-primary hover:bg-base-50 transition-colors"
                  >
                    {formData.icon || '📁'}
                  </button>
                
                  <AnimatePresence>
                    {showIconPicker && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9, y: -10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.9, y: -10 }}
                        className="absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-4 shadow-xl z-20 min-w-[200px]"
                      >
                        <div className="grid grid-cols-5 gap-2 mb-3">
                          {DEFAULT_ICONS.map(icon => (
                            <button
                              key={icon}
                              type="button"
                              onClick={() => handleIconSelect(icon)}
                              className={`w-8 h-8 flex items-center justify-center text-lg hover:bg-primary hover:text-white rounded-md transition-all ${
                                formData.icon === icon ? 'bg-primary text-white' : 'hover:bg-base-200'
                              }`}
                            >
                              {icon}
                            </button>
                          ))}
                        </div>
                        <button
                          type="button"
                          onClick={() => handleIconSelect('')}
                          className="btn btn-sm btn-ghost w-full"
                        >
                          使用默认图标
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>

            {/* 预览 */}
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">预览</span>
              </label>
              <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-base-200 to-base-100 rounded-lg border border-base-300">
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg shadow-md"
                  style={{ backgroundColor: formData.color }}
                >
                  {formData.icon || formData.name.charAt(0) || '📁'}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-semibold text-base-content truncate">
                    {formData.name || '分类名称'}
                  </p>
                  {formData.description && (
                    <p className="text-sm text-base-content/70 truncate mt-1">
                      {formData.description}
                    </p>
                  )}
                  {formData.parentId && (
                    <p className="text-xs text-primary mt-1">
                      子分类 • 父级：{categories?.find(c => c.id === formData.parentId)?.name}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 p-6 border-t border-base-200 bg-base-50">
          <button
            type="button"
            onClick={onClose}
            className="btn btn-ghost"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            type="submit"
            form="category-form"
            className="btn btn-primary min-w-[100px]"
            disabled={isLoading || !formData.name.trim()}
          >
            {isLoading && <span className="loading loading-spinner loading-sm mr-2"></span>}
            {category ? '更新分类' : '创建分类'}
          </button>
        </div>
      </motion.div>
    </motion.div>
  )
}