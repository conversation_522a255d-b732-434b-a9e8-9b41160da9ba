import { test, expect } from '@playwright/test'

test.describe('搜索功能', () => {
  test.beforeEach(async ({ page }) => {
    // 访问首页
    await page.goto('/')
  })

  test('应该能够执行基本搜索', async ({ page }) => {
    // 在搜索框中输入关键词
    await page.fill('[data-testid="search-input"]', '代码审查')
    
    // 按回车搜索
    await page.keyboard.press('Enter')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL(/\/search\?q=代码审查/)
    
    // 检查搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-query"]')).toContainText('代码审查')
    
    // 检查结果数量
    await expect(page.locator('[data-testid="search-results-count"]')).toContainText(/找到 \d+ 个结果/)
  })

  test('应该显示搜索建议', async ({ page }) => {
    // 在搜索框中输入部分关键词
    await page.fill('[data-testid="search-input"]', '代码')
    
    // 等待搜索建议出现
    await expect(page.locator('[data-testid="search-suggestions"]')).toBeVisible()
    
    // 检查建议列表
    await expect(page.locator('[data-testid="search-suggestion"]')).toHaveCount(5, { timeout: 5000 })
    
    // 点击第一个建议
    await page.click('[data-testid="search-suggestion"]:first-child')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL(/\/search\?q=/)
  })

  test('应该支持键盘导航搜索建议', async ({ page }) => {
    // 在搜索框中输入关键词
    await page.fill('[data-testid="search-input"]', '代码')
    
    // 等待搜索建议出现
    await expect(page.locator('[data-testid="search-suggestions"]')).toBeVisible()
    
    // 使用向下箭头键导航
    await page.keyboard.press('ArrowDown')
    
    // 检查第一个建议是否被选中
    await expect(page.locator('[data-testid="search-suggestion"]:first-child')).toHaveClass(/selected/)
    
    // 继续向下导航
    await page.keyboard.press('ArrowDown')
    
    // 检查第二个建议是否被选中
    await expect(page.locator('[data-testid="search-suggestion"]:nth-child(2)')).toHaveClass(/selected/)
    
    // 按回车确认选择
    await page.keyboard.press('Enter')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL(/\/search\?q=/)
  })

  test('应该显示搜索历史', async ({ page }) => {
    // 执行一次搜索
    await page.fill('[data-testid="search-input"]', '代码审查')
    await page.keyboard.press('Enter')
    
    // 返回首页
    await page.goto('/')
    
    // 点击搜索框
    await page.click('[data-testid="search-input"]')
    
    // 检查搜索历史是否显示
    await expect(page.locator('[data-testid="search-history"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-history-item"]')).toContainText('代码审查')
    
    // 点击历史记录项
    await page.click('[data-testid="search-history-item"]:first-child')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL(/\/search\?q=代码审查/)
  })

  test('应该能够清除搜索历史', async ({ page }) => {
    // 执行一次搜索
    await page.fill('[data-testid="search-input"]', '代码审查')
    await page.keyboard.press('Enter')
    
    // 返回首页
    await page.goto('/')
    
    // 点击搜索框
    await page.click('[data-testid="search-input"]')
    
    // 检查搜索历史是否显示
    await expect(page.locator('[data-testid="search-history"]')).toBeVisible()
    
    // 点击清除历史按钮
    await page.click('[data-testid="clear-search-history-button"]')
    
    // 检查历史记录是否清除
    await expect(page.locator('[data-testid="search-history-item"]')).not.toBeVisible()
  })

  test('应该支持高级搜索', async ({ page }) => {
    // 点击高级搜索按钮
    await page.click('[data-testid="advanced-search-button"]')
    
    // 检查高级搜索面板是否打开
    await expect(page.locator('[data-testid="advanced-search-panel"]')).toBeVisible()
    
    // 填写高级搜索表单
    await page.fill('[data-testid="advanced-search-query"]', '代码')
    await page.selectOption('[data-testid="advanced-search-category"]', '工作')
    await page.fill('[data-testid="advanced-search-tags"]', '测试')
    
    // 设置日期范围
    await page.fill('[data-testid="advanced-search-date-from"]', '2024-01-01')
    await page.fill('[data-testid="advanced-search-date-to"]', '2024-12-31')
    
    // 执行高级搜索
    await page.click('[data-testid="advanced-search-submit"]')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL(/\/search\?/)
    
    // 检查搜索参数是否正确
    await expect(page.url()).toContain('q=代码')
    await expect(page.url()).toContain('category=工作')
    await expect(page.url()).toContain('tags=测试')
  })

  test('应该支持搜索结果高亮', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码审查')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 检查搜索关键词是否高亮
    await expect(page.locator('[data-testid="search-highlight"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-highlight"]')).toContainText('代码审查')
    
    // 检查高亮样式
    await expect(page.locator('[data-testid="search-highlight"]')).toHaveClass(/highlight/)
  })

  test('应该支持搜索结果分页', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '测试')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 检查分页控件
    await expect(page.locator('[data-testid="pagination"]')).toBeVisible()
    
    // 点击下一页
    await page.click('[data-testid="pagination-next"]')
    
    // 检查页码是否更新
    await expect(page.locator('[data-testid="pagination-current"]')).toContainText('2')
    
    // 检查 URL 是否更新
    await expect(page.url()).toContain('page=2')
  })

  test('应该支持搜索结果排序', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 点击排序按钮
    await page.click('[data-testid="sort-button"]')
    
    // 检查排序选项
    await expect(page.locator('[data-testid="sort-options"]')).toBeVisible()
    
    // 选择按相关性排序
    await page.click('[data-testid="sort-by-relevance"]')
    
    // 检查排序是否生效
    await expect(page.locator('[data-testid="sort-indicator"]')).toContainText('相关性')
    
    // 检查 URL 参数
    await expect(page.url()).toContain('sort=relevance')
  })

  test('应该支持搜索结果筛选', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 点击筛选按钮
    await page.click('[data-testid="filter-button"]')
    
    // 检查筛选面板
    await expect(page.locator('[data-testid="filter-panel"]')).toBeVisible()
    
    // 选择分类筛选
    await page.click('[data-testid="filter-category-工作"]')
    
    // 应用筛选
    await page.click('[data-testid="apply-filter"]')
    
    // 检查筛选是否生效
    await expect(page.locator('[data-testid="active-filters"]')).toContainText('工作')
    
    // 检查 URL 参数
    await expect(page.url()).toContain('category=工作')
  })

  test('应该显示搜索结果统计', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 检查搜索统计
    await expect(page.locator('[data-testid="search-stats"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-stats"]')).toContainText(/找到 \d+ 个结果/)
    await expect(page.locator('[data-testid="search-stats"]')).toContainText(/耗时 \d+ms/)
    
    // 检查结果分类统计
    await expect(page.locator('[data-testid="search-category-stats"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-category-stats"]')).toContainText(/工作 \(\d+\)/)
    await expect(page.locator('[data-testid="search-category-stats"]')).toContainText(/学习 \(\d+\)/)
  })

  test('应该处理无搜索结果', async ({ page }) => {
    // 执行搜索不存在的内容
    await page.fill('[data-testid="search-input"]', '不存在的内容12345')
    await page.keyboard.press('Enter')
    
    // 等待搜索完成
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 检查无结果状态
    await expect(page.locator('[data-testid="no-results"]')).toBeVisible()
    await expect(page.locator('text=未找到匹配的结果')).toBeVisible()
    
    // 检查搜索建议
    await expect(page.locator('[data-testid="search-suggestions-no-results"]')).toBeVisible()
    await expect(page.locator('text=尝试使用其他关键词')).toBeVisible()
  })

  test('应该支持搜索自动完成', async ({ page }) => {
    // 开始输入搜索词
    await page.fill('[data-testid="search-input"]', '代')
    
    // 等待自动完成出现
    await expect(page.locator('[data-testid="search-autocomplete"]')).toBeVisible()
    
    // 检查自动完成选项
    await expect(page.locator('[data-testid="autocomplete-option"]')).toHaveCount(5, { timeout: 5000 })
    
    // 点击第一个自动完成选项
    await page.click('[data-testid="autocomplete-option"]:first-child')
    
    // 检查输入框是否更新
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('代码审查')
  })

  test('应该支持快捷键搜索', async ({ page }) => {
    // 使用 Ctrl+K 快捷键打开搜索
    await page.keyboard.press('Control+k')
    
    // 检查搜索框是否聚焦
    await expect(page.locator('[data-testid="search-input"]')).toBeFocused()
    
    // 输入搜索词
    await page.keyboard.type('代码审查')
    
    // 按回车搜索
    await page.keyboard.press('Enter')
    
    // 检查是否跳转到搜索结果页面
    await expect(page).toHaveURL(/\/search\?q=代码审查/)
  })

  test('应该支持实时搜索', async ({ page }) => {
    // 在搜索框中输入关键词
    await page.fill('[data-testid="search-input"]', '代码')
    
    // 等待实时搜索结果
    await expect(page.locator('[data-testid="live-search-results"]')).toBeVisible()
    
    // 检查实时搜索结果
    await expect(page.locator('[data-testid="live-search-item"]')).toHaveCount(3, { timeout: 5000 })
    
    // 点击实时搜索结果
    await page.click('[data-testid="live-search-item"]:first-child')
    
    // 检查是否跳转到对应的提示词页面
    await expect(page).toHaveURL(/\/prompts\/\d+/)
  })

  test('应该支持搜索结果导出', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 点击导出按钮
    await page.click('[data-testid="export-search-results"]')
    
    // 检查导出选项
    await expect(page.locator('[data-testid="export-options"]')).toBeVisible()
    
    // 选择导出为 JSON
    await page.click('[data-testid="export-json"]')
    
    // 检查下载是否开始
    const downloadPromise = page.waitForEvent('download')
    await page.click('[data-testid="confirm-export"]')
    const download = await downloadPromise
    
    // 检查下载文件名
    expect(download.suggestedFilename()).toContain('search-results')
    expect(download.suggestedFilename()).toContain('.json')
  })

  test('应该支持搜索结果书签', async ({ page }) => {
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码审查')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 点击书签按钮
    await page.click('[data-testid="bookmark-search"]')
    
    // 填写书签名称
    await page.fill('[data-testid="bookmark-name"]', '代码审查相关')
    
    // 保存书签
    await page.click('[data-testid="save-bookmark"]')
    
    // 检查书签是否保存成功
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=搜索已保存为书签')).toBeVisible()
    
    // 检查书签是否出现在书签列表中
    await page.click('[data-testid="bookmarks-button"]')
    await expect(page.locator('[data-testid="bookmark-item"]')).toContainText('代码审查相关')
  })
})

test.describe('搜索性能测试', () => {
  test('应该快速返回搜索结果', async ({ page }) => {
    // 前往搜索页面
    await page.goto('/')
    
    // 记录搜索开始时间
    const startTime = Date.now()
    
    // 执行搜索
    await page.fill('[data-testid="search-input"]', '代码')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果出现
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 计算搜索耗时
    const searchTime = Date.now() - startTime
    
    // 检查搜索时间（应该在2秒内）
    expect(searchTime).toBeLessThan(2000)
  })

  test('应该支持搜索结果缓存', async ({ page }) => {
    // 执行第一次搜索
    await page.fill('[data-testid="search-input"]', '代码审查')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果加载
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 记录第二次搜索开始时间
    const startTime = Date.now()
    
    // 执行相同的搜索
    await page.fill('[data-testid="search-input"]', '代码审查')
    await page.keyboard.press('Enter')
    
    // 等待搜索结果出现
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    
    // 计算缓存搜索耗时
    const cacheTime = Date.now() - startTime
    
    // 检查缓存搜索时间（应该在500毫秒内）
    expect(cacheTime).toBeLessThan(500)
  })

  test('应该支持搜索防抖', async ({ page }) => {
    // 快速连续输入
    await page.fill('[data-testid="search-input"]', '代')
    await page.fill('[data-testid="search-input"]', '代码')
    await page.fill('[data-testid="search-input"]', '代码审')
    await page.fill('[data-testid="search-input"]', '代码审查')
    
    // 等待防抖延迟
    await page.waitForTimeout(500)
    
    // 检查搜索建议是否出现
    await expect(page.locator('[data-testid="search-suggestions"]')).toBeVisible()
    
    // 检查只有最后一次搜索生效
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('代码审查')
  })
})