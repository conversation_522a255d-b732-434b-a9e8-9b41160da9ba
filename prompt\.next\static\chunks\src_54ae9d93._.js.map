{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/editor/CodeEditor.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect, useRef } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport CodeEditor from '@uiw/react-textarea-code-editor'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CodeEditorProps {\r\n  value: string\r\n  onChange: (value: string) => void\r\n  language?: string\r\n  placeholder?: string\r\n  minHeight?: number\r\n  maxHeight?: number\r\n  readOnly?: boolean\r\n  showLineNumbers?: boolean\r\n  showGutter?: boolean\r\n  theme?: 'light' | 'dark'\r\n  fontSize?: number\r\n  tabSize?: number\r\n  autoSave?: boolean\r\n  autoSaveDelay?: number\r\n  onSave?: (value: string) => void\r\n  className?: string\r\n}\r\n\r\nexport const PromptCodeEditor = ({\r\n  value,\r\n  onChange,\r\n  language = 'text',\r\n  placeholder = '输入提示词内容...',\r\n  minHeight = 200,\r\n  maxHeight = 600,\r\n  readOnly = false,\r\n  showLineNumbers = true,\r\n  showGutter = true,\r\n  theme = 'light',\r\n  fontSize = 14,\r\n  tabSize = 2,\r\n  autoSave = false,\r\n  autoSaveDelay = 2000,\r\n  onSave,\r\n  className = '',\r\n}: CodeEditorProps) => {\r\n  const [localValue, setLocalValue] = useState(value)\r\n  const [isModified, setIsModified] = useState(false)\r\n  const [wordCount, setWordCount] = useState(0)\r\n  const [lineCount, setLineCount] = useState(1)\r\n  const [charCount, setCharCount] = useState(0)\r\n  const [selectedCount, setSelectedCount] = useState(0)\r\n  const [cursorPosition, setCursorPosition] = useState({ line: 1, column: 1 })\r\n  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>()\r\n  const editorRef = useRef<HTMLTextAreaElement>(null)\r\n\r\n  // 同步外部值\r\n  useEffect(() => {\r\n    if (value !== localValue) {\r\n      setLocalValue(value)\r\n      setIsModified(false)\r\n    }\r\n  }, [value, localValue])\r\n\r\n  // 计算统计信息\r\n  useEffect(() => {\r\n    const text = localValue\r\n    setCharCount(text.length)\r\n    setWordCount(text.split(/\\s+/).filter(word => word.length > 0).length)\r\n    setLineCount(text.split('\\n').length)\r\n  }, [localValue])\r\n\r\n  // 自动保存\r\n  useEffect(() => {\r\n    if (autoSave && isModified && onSave) {\r\n      if (autoSaveTimeoutRef.current) {\r\n        clearTimeout(autoSaveTimeoutRef.current)\r\n      }\r\n      \r\n      autoSaveTimeoutRef.current = setTimeout(() => {\r\n        onSave(localValue)\r\n        setIsModified(false)\r\n        toast.success('已自动保存')\r\n      }, autoSaveDelay)\r\n    }\r\n\r\n    return () => {\r\n      if (autoSaveTimeoutRef.current) {\r\n        clearTimeout(autoSaveTimeoutRef.current)\r\n      }\r\n    }\r\n  }, [localValue, isModified, autoSave, autoSaveDelay, onSave])\r\n\r\n  // 处理内容变化\r\n  const handleChange = (val: string) => {\r\n    setLocalValue(val)\r\n    setIsModified(val !== value)\r\n    onChange(val)\r\n  }\r\n\r\n  // 处理选择变化\r\n  const handleSelectionChange = () => {\r\n    if (editorRef.current) {\r\n      const textarea = editorRef.current\r\n      const start = textarea.selectionStart\r\n      const end = textarea.selectionEnd\r\n      \r\n      if (start !== end) {\r\n        const selectedText = localValue.substring(start, end)\r\n        setSelectedCount(selectedText.length)\r\n      } else {\r\n        setSelectedCount(0)\r\n      }\r\n\r\n      // 计算光标位置\r\n      const textBeforeCursor = localValue.substring(0, start)\r\n      const lines = textBeforeCursor.split('\\n')\r\n      const line = lines.length\r\n      const column = lines[lines.length - 1].length + 1\r\n      setCursorPosition({ line, column })\r\n    }\r\n  }\r\n\r\n  // 手动保存\r\n  const handleSave = () => {\r\n    if (onSave && isModified) {\r\n      onSave(localValue)\r\n      setIsModified(false)\r\n      toast.success('保存成功')\r\n    }\r\n  }\r\n\r\n  // 复制内容\r\n  const handleCopy = async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(localValue)\r\n      toast.success('内容已复制到剪贴板')\r\n    } catch (error) {\r\n      toast.error('复制失败')\r\n    }\r\n  }\r\n\r\n  // 清空内容\r\n  const handleClear = () => {\r\n    if (localValue.trim() && window.confirm('确定要清空所有内容吗？')) {\r\n      handleChange('')\r\n    }\r\n  }\r\n\r\n  // 插入文本\r\n  const insertText = (text: string) => {\r\n    if (editorRef.current) {\r\n      const textarea = editorRef.current\r\n      const start = textarea.selectionStart\r\n      const end = textarea.selectionEnd\r\n      const newValue = localValue.substring(0, start) + text + localValue.substring(end)\r\n      handleChange(newValue)\r\n      \r\n      // 设置光标位置\r\n      setTimeout(() => {\r\n        textarea.focus()\r\n        textarea.setSelectionRange(start + text.length, start + text.length)\r\n      }, 0)\r\n    }\r\n  }\r\n\r\n  // 格式化文本\r\n  const formatText = () => {\r\n    const formatted = localValue\r\n      .split('\\n')\r\n      .map(line => line.trim())\r\n      .filter(line => line.length > 0)\r\n      .join('\\n')\r\n    \r\n    handleChange(formatted)\r\n    toast.success('格式化完成')\r\n  }\r\n\r\n  // 键盘快捷键\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.ctrlKey || e.metaKey) {\r\n      switch (e.key) {\r\n        case 's':\r\n          e.preventDefault()\r\n          handleSave()\r\n          break\r\n        case 'a':\r\n          // 全选 - 默认行为\r\n          break\r\n        case 'c':\r\n          if (selectedCount > 0) {\r\n            // 复制选中内容 - 默认行为\r\n          } else {\r\n            e.preventDefault()\r\n            handleCopy()\r\n          }\r\n          break\r\n        case 'k':\r\n          e.preventDefault()\r\n          handleClear()\r\n          break\r\n      }\r\n    }\r\n  }\r\n\r\n  // 动态计算样式\r\n  const editorStyle = {\r\n    fontSize: `${fontSize}px`,\r\n    minHeight: `${minHeight}px`,\r\n    maxHeight: `${maxHeight}px`,\r\n    tabSize: tabSize,\r\n  }\r\n\r\n  return (\r\n    <div className={`border border-base-300 rounded-lg overflow-hidden ${className}`}>\r\n      {/* 工具栏 */}\r\n      <div className=\"bg-base-200 px-4 py-2 border-b border-base-300 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <span className=\"text-sm font-medium text-base-content\">提示词编辑器</span>\r\n          {isModified && (\r\n            <motion.span\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              className=\"text-xs text-warning\"\r\n            >\r\n              未保存\r\n            </motion.span>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {/* 格式化按钮 */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={formatText}\r\n            className=\"btn btn-xs btn-ghost\"\r\n            title=\"格式化文本 (整理空行和缩进)\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25\"\r\n              />\r\n            </svg>\r\n          </motion.button>\r\n\r\n          {/* 复制按钮 */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={handleCopy}\r\n            className=\"btn btn-xs btn-ghost\"\r\n            title=\"复制内容 (Ctrl+C)\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n              />\r\n            </svg>\r\n          </motion.button>\r\n\r\n          {/* 清空按钮 */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={handleClear}\r\n            className=\"btn btn-xs btn-ghost\"\r\n            title=\"清空内容 (Ctrl+K)\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n              />\r\n            </svg>\r\n          </motion.button>\r\n\r\n          {/* 保存按钮 */}\r\n          {onSave && (\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              onClick={handleSave}\r\n              disabled={!isModified}\r\n              className={`btn btn-xs ${isModified ? 'btn-primary' : 'btn-ghost'}`}\r\n              title=\"保存 (Ctrl+S)\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0111.186 0z\"\r\n                />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 编辑器 */}\r\n      <div className=\"relative\">\r\n        <CodeEditor\r\n          ref={editorRef}\r\n          value={localValue}\r\n          language={language}\r\n          placeholder={placeholder}\r\n          onChange={handleChange}\r\n          onKeyDown={handleKeyDown}\r\n          onSelect={handleSelectionChange}\r\n          padding={16}\r\n          style={{\r\n            ...editorStyle,\r\n            fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Menlo, Monaco, \"Roboto Mono\", monospace',\r\n            backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',\r\n            color: theme === 'dark' ? '#f9fafb' : '#111827',\r\n            resize: 'none',\r\n            outline: 'none',\r\n            border: 'none',\r\n            overflowY: 'auto',\r\n          }}\r\n          data-color-mode={theme}\r\n          readOnly={readOnly}\r\n        />\r\n\r\n        {/* 快捷插入模板 */}\r\n        {!readOnly && (\r\n          <div className=\"absolute top-2 right-2 opacity-50 hover:opacity-100 transition-opacity\">\r\n            <div className=\"dropdown dropdown-end\">\r\n              <div tabIndex={0} role=\"button\" className=\"btn btn-xs btn-ghost\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M12 6.75a.75.75 0 110-********* 0 010 1.5zM12 12.75a.75.75 0 110-********* 0 010 1.5zM12 18.75a.75.75 0 110-********* 0 010 1.5z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n              <ul tabIndex={0} className=\"dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52\">\r\n                <li>\r\n                  <a onClick={() => insertText('角色：\\n任务：\\n输出格式：\\n')}>\r\n                    插入角色模板\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a onClick={() => insertText('Context: \\nQuestion: \\nAnswer: \\n')}>\r\n                    插入问答模板\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a onClick={() => insertText('请按照以下步骤：\\n1. \\n2. \\n3. \\n')}>\r\n                    插入步骤模板\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* 状态栏 */}\r\n      <div className=\"bg-base-200 px-4 py-1 border-t border-base-300 flex items-center justify-between text-xs text-base-content/70\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <span>行 {cursorPosition.line}，列 {cursorPosition.column}</span>\r\n          <span>共 {lineCount} 行</span>\r\n          <span>{charCount} 字符</span>\r\n          <span>{wordCount} 词</span>\r\n          {selectedCount > 0 && (\r\n            <span className=\"text-primary\">已选择 {selectedCount} 字符</span>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {autoSave && (\r\n            <span className=\"text-info\">自动保存</span>\r\n          )}\r\n          <span className=\"text-base-content/50\">\r\n            {readOnly ? '只读' : '可编辑'}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AA0BO,MAAM,mBAAmB;QAAC,EAC/B,KAAK,EACL,QAAQ,EACR,WAAW,MAAM,EACjB,cAAc,YAAY,EAC1B,YAAY,GAAG,EACf,YAAY,GAAG,EACf,WAAW,KAAK,EAChB,kBAAkB,IAAI,EACtB,aAAa,IAAI,EACjB,QAAQ,OAAO,EACf,WAAW,EAAE,EACb,UAAU,CAAC,EACX,WAAW,KAAK,EAChB,gBAAgB,IAAI,EACpB,MAAM,EACN,YAAY,EAAE,EACE;;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAG,QAAQ;IAAE;IAC1E,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAChC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAE9C,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU,YAAY;gBACxB,cAAc;gBACd,cAAc;YAChB;QACF;qCAAG;QAAC;QAAO;KAAW;IAEtB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,OAAO;YACb,aAAa,KAAK,MAAM;YACxB,aAAa,KAAK,KAAK,CAAC,OAAO,MAAM;8CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;6CAAG,MAAM;YACrE,aAAa,KAAK,KAAK,CAAC,MAAM,MAAM;QACtC;qCAAG;QAAC;KAAW;IAEf,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,YAAY,cAAc,QAAQ;gBACpC,IAAI,mBAAmB,OAAO,EAAE;oBAC9B,aAAa,mBAAmB,OAAO;gBACzC;gBAEA,mBAAmB,OAAO,GAAG;kDAAW;wBACtC,OAAO;wBACP,cAAc;wBACd,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;iDAAG;YACL;YAEA;8CAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;qCAAG;QAAC;QAAY;QAAY;QAAU;QAAe;KAAO;IAE5D,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,cAAc,QAAQ;QACtB,SAAS;IACX;IAEA,SAAS;IACT,MAAM,wBAAwB;QAC5B,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,WAAW,UAAU,OAAO;YAClC,MAAM,QAAQ,SAAS,cAAc;YACrC,MAAM,MAAM,SAAS,YAAY;YAEjC,IAAI,UAAU,KAAK;gBACjB,MAAM,eAAe,WAAW,SAAS,CAAC,OAAO;gBACjD,iBAAiB,aAAa,MAAM;YACtC,OAAO;gBACL,iBAAiB;YACnB;YAEA,SAAS;YACT,MAAM,mBAAmB,WAAW,SAAS,CAAC,GAAG;YACjD,MAAM,QAAQ,iBAAiB,KAAK,CAAC;YACrC,MAAM,OAAO,MAAM,MAAM;YACzB,MAAM,SAAS,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG;YAChD,kBAAkB;gBAAE;gBAAM;YAAO;QACnC;IACF;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,UAAU,YAAY;YACxB,OAAO;YACP,cAAc;YACd,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,IAAI,WAAW,IAAI,MAAM,OAAO,OAAO,CAAC,gBAAgB;YACtD,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,WAAW,UAAU,OAAO;YAClC,MAAM,QAAQ,SAAS,cAAc;YACrC,MAAM,MAAM,SAAS,YAAY;YACjC,MAAM,WAAW,WAAW,SAAS,CAAC,GAAG,SAAS,OAAO,WAAW,SAAS,CAAC;YAC9E,aAAa;YAEb,SAAS;YACT,WAAW;gBACT,SAAS,KAAK;gBACd,SAAS,iBAAiB,CAAC,QAAQ,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM;YACrE,GAAG;QACL;IACF;IAEA,QAAQ;IACR,MAAM,aAAa;QACjB,MAAM,YAAY,WACf,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,IAAI,CAAC;QAER,aAAa;QACb,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,QAAQ;IACR,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE;YAC1B,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,EAAE,cAAc;oBAChB;oBACA;gBACF,KAAK;oBAEH;gBACF,KAAK;oBACH,IAAI,gBAAgB,GAAG;oBACrB,gBAAgB;oBAClB,OAAO;wBACL,EAAE,cAAc;wBAChB;oBACF;oBACA;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB;oBACA;YACJ;QACF;IACF;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,UAAU,AAAC,GAAW,OAAT,UAAS;QACtB,WAAW,AAAC,GAAY,OAAV,WAAU;QACxB,WAAW,AAAC,GAAY,OAAV,WAAU;QACxB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,qDAA8D,OAAV;;0BAEnE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwC;;;;;;4BACvD,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,WAAU;0CACX;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;4BAMP,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS;gCACT,UAAU,CAAC;gCACX,WAAW,AAAC,cAAsD,OAAzC,aAAa,gBAAgB;gCACtD,OAAM;0CAEN,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+LAAA,CAAA,UAAU;wBACT,KAAK;wBACL,OAAO;wBACP,UAAU;wBACV,aAAa;wBACb,UAAU;wBACV,WAAW;wBACX,UAAU;wBACV,SAAS;wBACT,OAAO;4BACL,GAAG,WAAW;4BACd,YAAY;4BACZ,iBAAiB,UAAU,SAAS,YAAY;4BAChD,OAAO,UAAU,SAAS,YAAY;4BACtC,QAAQ;4BACR,SAAS;4BACT,QAAQ;4BACR,WAAW;wBACb;wBACA,mBAAiB;wBACjB,UAAU;;;;;;oBAIX,CAAC,0BACA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,UAAU;oCAAG,MAAK;oCAAS,WAAU;8CACxC,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAG,UAAU;oCAAG,WAAU;;sDACzB,6LAAC;sDACC,cAAA,6LAAC;gDAAE,SAAS,IAAM,WAAW;0DAAsB;;;;;;;;;;;sDAIrD,6LAAC;sDACC,cAAA,6LAAC;gDAAE,SAAS,IAAM,WAAW;0DAAsC;;;;;;;;;;;sDAIrE,6LAAC;sDACC,cAAA,6LAAC;gDAAE,SAAS,IAAM,WAAW;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAK;oCAAG,eAAe,IAAI;oCAAC;oCAAI,eAAe,MAAM;;;;;;;0CACtD,6LAAC;;oCAAK;oCAAG;oCAAU;;;;;;;0CACnB,6LAAC;;oCAAM;oCAAU;;;;;;;0CACjB,6LAAC;;oCAAM;oCAAU;;;;;;;4BAChB,gBAAgB,mBACf,6LAAC;gCAAK,WAAU;;oCAAe;oCAAK;oCAAc;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;4BACZ,0BACC,6LAAC;gCAAK,WAAU;0CAAY;;;;;;0CAE9B,6LAAC;gCAAK,WAAU;0CACb,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAM/B;GA1Ya;KAAA", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/editor/SimpleEditor.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport CodeEditor from '@uiw/react-textarea-code-editor'\r\n\r\ninterface SimpleEditorProps {\r\n  value: string\r\n  onChange: (value: string) => void\r\n  placeholder?: string\r\n  minHeight?: number\r\n  maxHeight?: number\r\n  readOnly?: boolean\r\n  theme?: 'light' | 'dark'\r\n  fontSize?: number\r\n  className?: string\r\n  showWordCount?: boolean\r\n}\r\n\r\nexport const SimpleEditor = ({\r\n  value,\r\n  onChange,\r\n  placeholder = '输入内容...',\r\n  minHeight = 120,\r\n  maxHeight = 400,\r\n  readOnly = false,\r\n  theme = 'light',\r\n  fontSize = 14,\r\n  className = '',\r\n  showWordCount = true,\r\n}: SimpleEditorProps) => {\r\n  const [localValue, setLocalValue] = useState(value)\r\n  const [wordCount, setWordCount] = useState(0)\r\n  const [charCount, setCharCount] = useState(0)\r\n\r\n  // 同步外部值\r\n  useEffect(() => {\r\n    setLocalValue(value)\r\n  }, [value])\r\n\r\n  // 计算统计信息\r\n  useEffect(() => {\r\n    const text = localValue\r\n    setCharCount(text.length)\r\n    setWordCount(text.split(/\\s+/).filter(word => word.length > 0).length)\r\n  }, [localValue])\r\n\r\n  // 处理内容变化\r\n  const handleChange = (val: string) => {\r\n    setLocalValue(val)\r\n    onChange(val)\r\n  }\r\n\r\n  // 动态计算样式\r\n  const editorStyle = {\r\n    fontSize: `${fontSize}px`,\r\n    minHeight: `${minHeight}px`,\r\n    maxHeight: `${maxHeight}px`,\r\n    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Menlo, Monaco, \"Roboto Mono\", monospace',\r\n    backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',\r\n    color: theme === 'dark' ? '#f9fafb' : '#111827',\r\n    resize: 'none',\r\n    outline: 'none',\r\n    border: 'none',\r\n    overflowY: 'auto',\r\n  }\r\n\r\n  return (\r\n    <div className={`border border-base-300 rounded-lg overflow-hidden ${className}`}>\r\n      {/* 编辑器 */}\r\n      <CodeEditor\r\n        value={localValue}\r\n        language=\"text\"\r\n        placeholder={placeholder}\r\n        onChange={handleChange}\r\n        padding={12}\r\n        style={editorStyle}\r\n        data-color-mode={theme}\r\n        readOnly={readOnly}\r\n      />\r\n\r\n      {/* 状态栏 */}\r\n      {showWordCount && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-base-200 px-3 py-1 border-t border-base-300 flex items-center justify-between text-xs text-base-content/70\"\r\n        >\r\n          <div className=\"flex items-center space-x-3\">\r\n            <span>{charCount} 字符</span>\r\n            <span>{wordCount} 词</span>\r\n          </div>\r\n          \r\n          {readOnly && (\r\n            <span className=\"text-base-content/50\">只读模式</span>\r\n          )}\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAmBO,MAAM,eAAe;QAAC,EAC3B,KAAK,EACL,QAAQ,EACR,cAAc,SAAS,EACvB,YAAY,GAAG,EACf,YAAY,GAAG,EACf,WAAW,KAAK,EAChB,QAAQ,OAAO,EACf,WAAW,EAAE,EACb,YAAY,EAAE,EACd,gBAAgB,IAAI,EACF;;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,cAAc;QAChB;iCAAG;QAAC;KAAM;IAEV,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO;YACb,aAAa,KAAK,MAAM;YACxB,aAAa,KAAK,KAAK,CAAC,OAAO,MAAM;0CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;yCAAG,MAAM;QACvE;iCAAG;QAAC;KAAW;IAEf,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,SAAS;IACX;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,UAAU,AAAC,GAAW,OAAT,UAAS;QACtB,WAAW,AAAC,GAAY,OAAV,WAAU;QACxB,WAAW,AAAC,GAAY,OAAV,WAAU;QACxB,YAAY;QACZ,iBAAiB,UAAU,SAAS,YAAY;QAChD,OAAO,UAAU,SAAS,YAAY;QACtC,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,WAAW;IACb;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,qDAA8D,OAAV;;0BAEnE,6LAAC,+LAAA,CAAA,UAAU;gBACT,OAAO;gBACP,UAAS;gBACT,aAAa;gBACb,UAAU;gBACV,SAAS;gBACT,OAAO;gBACP,mBAAiB;gBACjB,UAAU;;;;;;YAIX,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAM;oCAAU;;;;;;;0CACjB,6LAAC;;oCAAM;oCAAU;;;;;;;;;;;;;oBAGlB,0BACC,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;;;AAMnD;GAjFa;KAAA", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/editor/index.ts"], "sourcesContent": ["export { PromptCodeEditor } from './CodeEditor'\r\nexport { SimpleEditor } from './SimpleEditor'"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/forms/PromptForm.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { useRouter } from 'next/navigation'\r\nimport { PromptCodeEditor } from '~/components/editor'\r\nimport { usePromptAutoSave } from '~/hooks'\r\nimport { api } from '~/trpc/react'\r\nimport { toast } from 'react-hot-toast'\r\nimport { Prompt, Category, Tag } from '~/types'\r\n\r\ninterface PromptFormProps {\r\n  prompt?: Prompt\r\n  onSuccess?: (prompt: Prompt) => void\r\n  onCancel?: () => void\r\n  autoSave?: boolean\r\n  className?: string\r\n}\r\n\r\nexport const PromptForm = ({\r\n  prompt,\r\n  onSuccess,\r\n  onCancel,\r\n  autoSave = true,\r\n  className = '',\r\n}: PromptFormProps) => {\r\n  const router = useRouter()\r\n  const [formData, setFormData] = useState({\r\n    title: prompt?.title || '',\r\n    content: prompt?.content || '',\r\n    description: prompt?.description || '',\r\n    categoryId: prompt?.categoryId || '',\r\n    tags: prompt?.tags?.map(t => t.name) || [],\r\n    isPublic: prompt?.isPublic || false,\r\n    isFavorite: prompt?.isFavorite || false,\r\n  })\r\n\r\n  const [newTag, setNewTag] = useState('')\r\n\r\n  // 自动保存功能\r\n  const autoSaveResult = usePromptAutoSave(formData, {\r\n    enabled: autoSave && !!prompt,\r\n    promptId: prompt?.id,\r\n    onSave: async (data) => {\r\n      if (prompt) {\r\n        await updatePromptMutation.mutateAsync({\r\n          id: prompt.id,\r\n          ...data,\r\n        })\r\n      }\r\n    },\r\n  })\r\n\r\n  // 获取分类列表\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 获取用户标签\r\n  const { data: userTags } = api.tags.getUserTags.useQuery()\r\n\r\n  // 创建提示词\r\n  const createPromptMutation = api.prompts.create.useMutation({\r\n    onSuccess: (data) => {\r\n      toast.success('提示词创建成功')\r\n      setIsDirty(false)\r\n      setLastSaved(new Date())\r\n      if (onSuccess) {\r\n        onSuccess(data)\r\n      } else {\r\n        router.push(`/prompts/${data.id}`)\r\n      }\r\n    },\r\n    onError: (error) => {\r\n      toast.error('创建失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 更新提示词\r\n  const updatePromptMutation = api.prompts.update.useMutation({\r\n    onSuccess: (data) => {\r\n      toast.success('提示词更新成功')\r\n      setIsDirty(false)\r\n      setLastSaved(new Date())\r\n      if (onSuccess) {\r\n        onSuccess(data)\r\n      }\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 恢复草稿\r\n  useEffect(() => {\r\n    if (!prompt) {\r\n      const draft = autoSaveResult.restoreDraft()\r\n      if (draft && window.confirm('检测到未保存的草稿，是否恢复？')) {\r\n        setFormData(draft)\r\n        toast.success('草稿已恢复')\r\n      }\r\n    }\r\n  }, [prompt, autoSaveResult])\r\n\r\n  // 处理表单提交\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    \r\n    if (!formData.title.trim()) {\r\n      toast.error('请输入提示词标题')\r\n      return\r\n    }\r\n\r\n    if (!formData.content.trim()) {\r\n      toast.error('请输入提示词内容')\r\n      return\r\n    }\r\n\r\n    const promptData = {\r\n      title: formData.title.trim(),\r\n      content: formData.content.trim(),\r\n      description: formData.description.trim() || null,\r\n      categoryId: formData.categoryId || null,\r\n      tags: formData.tags,\r\n      isPublic: formData.isPublic,\r\n      isFavorite: formData.isFavorite,\r\n    }\r\n\r\n    if (prompt) {\r\n      await updatePromptMutation.mutateAsync({\r\n        id: prompt.id,\r\n        ...promptData,\r\n      })\r\n    } else {\r\n      await createPromptMutation.mutateAsync(promptData)\r\n    }\r\n    \r\n    // 清除草稿\r\n    autoSaveResult.clearDraft()\r\n  }\r\n\r\n  // 添加标签\r\n  const handleAddTag = () => {\r\n    const tag = newTag.trim()\r\n    if (tag && !formData.tags.includes(tag)) {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        tags: [...prev.tags, tag],\r\n      }))\r\n      setNewTag('')\r\n    }\r\n  }\r\n\r\n  // 移除标签\r\n  const handleRemoveTag = (tagToRemove: string) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      tags: prev.tags.filter(tag => tag !== tagToRemove),\r\n    }))\r\n  }\r\n\r\n  // 处理键盘事件\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault()\r\n      handleAddTag()\r\n    }\r\n  }\r\n\r\n  const isLoading = createPromptMutation.isLoading || updatePromptMutation.isLoading\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      className={`space-y-6 ${className}`}\r\n    >\r\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        {/* 表单头部 */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h2 className=\"text-2xl font-bold text-base-content\">\r\n              {prompt ? '编辑提示词' : '创建提示词'}\r\n            </h2>\r\n            <div className=\"flex items-center space-x-4 mt-2\">\r\n              {autoSaveResult.hasUnsavedChanges && (\r\n                <motion.span\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  className=\"text-sm text-warning\"\r\n                >\r\n                  有未保存的更改\r\n                </motion.span>\r\n              )}\r\n              {autoSaveResult.isSaving && (\r\n                <motion.span\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  className=\"text-sm text-info flex items-center space-x-1\"\r\n                >\r\n                  <span className=\"loading loading-spinner loading-xs\"></span>\r\n                  <span>自动保存中...</span>\r\n                </motion.span>\r\n              )}\r\n              {autoSaveResult.lastSaved && (\r\n                <span className=\"text-sm text-base-content/70\">\r\n                  上次保存: {autoSaveResult.lastSaved.toLocaleTimeString()}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-center space-x-2\">\r\n            {onCancel && (\r\n              <motion.button\r\n                type=\"button\"\r\n                onClick={onCancel}\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-ghost\"\r\n              >\r\n                取消\r\n              </motion.button>\r\n            )}\r\n            <motion.button\r\n              type=\"submit\"\r\n              disabled={isLoading || !autoSaveResult.hasUnsavedChanges}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"btn btn-primary\"\r\n            >\r\n              {isLoading ? (\r\n                <span className=\"loading loading-spinner loading-sm\"></span>\r\n              ) : prompt ? (\r\n                '更新'\r\n              ) : (\r\n                '创建'\r\n              )}\r\n            </motion.button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 基本信息 */}\r\n        <div className=\"card bg-base-100 shadow-sm\">\r\n          <div className=\"card-body\">\r\n            <h3 className=\"card-title text-lg mb-4\">基本信息</h3>\r\n            \r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              {/* 标题 */}\r\n              <div className=\"form-control\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">标题 *</span>\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={formData.title}\r\n                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\r\n                  placeholder=\"请输入提示词标题\"\r\n                  className=\"input input-bordered w-full\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              {/* 分类 */}\r\n              <div className=\"form-control\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">分类</span>\r\n                </label>\r\n                <select\r\n                  value={formData.categoryId}\r\n                  onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}\r\n                  className=\"select select-bordered w-full\"\r\n                >\r\n                  <option value=\"\">选择分类</option>\r\n                  {categories?.map(category => (\r\n                    <option key={category.id} value={category.id}>\r\n                      {category.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 描述 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">描述</span>\r\n              </label>\r\n              <textarea\r\n                value={formData.description}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n                placeholder=\"请输入提示词描述（可选）\"\r\n                className=\"textarea textarea-bordered w-full\"\r\n                rows={3}\r\n              />\r\n            </div>\r\n\r\n            {/* 标签 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">标签</span>\r\n              </label>\r\n              \r\n              {/* 现有标签 */}\r\n              <div className=\"flex flex-wrap gap-2 mb-2\">\r\n                <AnimatePresence>\r\n                  {formData.tags.map(tag => (\r\n                    <motion.span\r\n                      key={tag}\r\n                      initial={{ opacity: 0, scale: 0.8 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.8 }}\r\n                      className=\"badge badge-primary gap-2\"\r\n                    >\r\n                      {tag}\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleRemoveTag(tag)}\r\n                        className=\"btn btn-xs btn-ghost btn-circle\"\r\n                      >\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          strokeWidth={1.5}\r\n                          stroke=\"currentColor\"\r\n                          className=\"w-3 h-3\"\r\n                        >\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                        </svg>\r\n                      </button>\r\n                    </motion.span>\r\n                  ))}\r\n                </AnimatePresence>\r\n              </div>\r\n\r\n              {/* 添加标签 */}\r\n              <div className=\"flex gap-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={newTag}\r\n                  onChange={(e) => setNewTag(e.target.value)}\r\n                  onKeyDown={handleKeyDown}\r\n                  placeholder=\"输入标签名称\"\r\n                  className=\"input input-bordered flex-1\"\r\n                />\r\n                <motion.button\r\n                  type=\"button\"\r\n                  onClick={handleAddTag}\r\n                  disabled={!newTag.trim()}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className=\"btn btn-outline\"\r\n                >\r\n                  添加\r\n                </motion.button>\r\n              </div>\r\n\r\n              {/* 推荐标签 */}\r\n              {userTags && userTags.length > 0 && (\r\n                <div className=\"mt-2\">\r\n                  <span className=\"text-sm text-base-content/70 mb-2 block\">推荐标签:</span>\r\n                  <div className=\"flex flex-wrap gap-1\">\r\n                    {userTags.slice(0, 10).map(tag => (\r\n                      <button\r\n                        key={tag.name}\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          if (!formData.tags.includes(tag.name)) {\r\n                            setFormData(prev => ({\r\n                              ...prev,\r\n                              tags: [...prev.tags, tag.name],\r\n                            }))\r\n                          }\r\n                        }}\r\n                        className=\"badge badge-ghost badge-sm hover:badge-primary\"\r\n                      >\r\n                        {tag.name}\r\n                      </button>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 选项 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">选项</span>\r\n              </label>\r\n              <div className=\"flex flex-wrap gap-4\">\r\n                <label className=\"label cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={formData.isFavorite}\r\n                    onChange={(e) => setFormData(prev => ({ ...prev, isFavorite: e.target.checked }))}\r\n                    className=\"checkbox checkbox-primary\"\r\n                  />\r\n                  <span className=\"label-text ml-2\">收藏</span>\r\n                </label>\r\n                <label className=\"label cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={formData.isPublic}\r\n                    onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}\r\n                    className=\"checkbox checkbox-primary\"\r\n                  />\r\n                  <span className=\"label-text ml-2\">公开</span>\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 内容编辑器 */}\r\n        <div className=\"card bg-base-100 shadow-sm\">\r\n          <div className=\"card-body\">\r\n            <h3 className=\"card-title text-lg mb-4\">内容 *</h3>\r\n            <PromptCodeEditor\r\n              value={formData.content}\r\n              onChange={(value) => setFormData(prev => ({ ...prev, content: value }))}\r\n              placeholder=\"请输入提示词内容...\"\r\n              minHeight={300}\r\n              maxHeight={600}\r\n              autoSave={false} // 由 usePromptAutoSave 处理\r\n              showLineNumbers={true}\r\n              showGutter={true}\r\n            />\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAmBO,MAAM,aAAa;QAAC,EACzB,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,YAAY,EAAE,EACE;QAOR;;IANR,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;QACxB,SAAS,CAAA,mBAAA,6BAAA,OAAQ,OAAO,KAAI;QAC5B,aAAa,CAAA,mBAAA,6BAAA,OAAQ,WAAW,KAAI;QACpC,YAAY,CAAA,mBAAA,6BAAA,OAAQ,UAAU,KAAI;QAClC,MAAM,CAAA,mBAAA,8BAAA,eAAA,OAAQ,IAAI,cAAZ,mCAAA,aAAc,GAAG;mCAAC,CAAA,IAAK,EAAE,IAAI;sCAAK,EAAE;QAC1C,UAAU,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,KAAI;QAC9B,YAAY,CAAA,mBAAA,6BAAA,OAAQ,UAAU,KAAI;IACpC;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;QACjD,SAAS,YAAY,CAAC,CAAC;QACvB,QAAQ,EAAE,mBAAA,6BAAA,OAAQ,EAAE;QACpB,MAAM;4DAAE,OAAO;gBACb,IAAI,QAAQ;oBACV,MAAM,qBAAqB,WAAW,CAAC;wBACrC,IAAI,OAAO,EAAE;wBACb,GAAG,IAAI;oBACT;gBACF;YACF;;IACF;IAEA,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,SAAS;IACT,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ;IAExD,QAAQ;IACR,MAAM,uBAAuB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;QAC1D,SAAS;4DAAE,CAAC;gBACV,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;gBACX,aAAa,IAAI;gBACjB,IAAI,WAAW;oBACb,UAAU;gBACZ,OAAO;oBACL,OAAO,IAAI,CAAC,AAAC,YAAmB,OAAR,KAAK,EAAE;gBACjC;YACF;;QACA,OAAO;4DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,QAAQ;IACR,MAAM,uBAAuB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;QAC1D,SAAS;4DAAE,CAAC;gBACV,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;gBACX,aAAa,IAAI;gBACjB,IAAI,WAAW;oBACb,UAAU;gBACZ;YACF;;QACA,OAAO;4DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,QAAQ;gBACX,MAAM,QAAQ,eAAe,YAAY;gBACzC,IAAI,SAAS,OAAO,OAAO,CAAC,oBAAoB;oBAC9C,YAAY;oBACZ,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF;QACF;+BAAG;QAAC;QAAQ;KAAe;IAE3B,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,aAAa;YACjB,OAAO,SAAS,KAAK,CAAC,IAAI;YAC1B,SAAS,SAAS,OAAO,CAAC,IAAI;YAC9B,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;YAC5C,YAAY,SAAS,UAAU,IAAI;YACnC,MAAM,SAAS,IAAI;YACnB,UAAU,SAAS,QAAQ;YAC3B,YAAY,SAAS,UAAU;QACjC;QAEA,IAAI,QAAQ;YACV,MAAM,qBAAqB,WAAW,CAAC;gBACrC,IAAI,OAAO,EAAE;gBACb,GAAG,UAAU;YACf;QACF,OAAO;YACL,MAAM,qBAAqB,WAAW,CAAC;QACzC;QAEA,OAAO;QACP,eAAe,UAAU;IAC3B;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,MAAM,OAAO,IAAI;QACvB,IAAI,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;YACvC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,MAAM;2BAAI,KAAK,IAAI;wBAAE;qBAAI;gBAC3B,CAAC;YACD,UAAU;QACZ;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YACxC,CAAC;IACH;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,YAAY,qBAAqB,SAAS,IAAI,qBAAqB,SAAS;IAElF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAW,AAAC,aAAsB,OAAV;kBAExB,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BAEtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,UAAU;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;wCACZ,eAAe,iBAAiB,kBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;sDACX;;;;;;wCAIF,eAAe,QAAQ,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;wCAGT,eAAe,SAAS,kBACvB,6LAAC;4CAAK,WAAU;;gDAA+B;gDACtC,eAAe,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;sCAM1D,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,MAAK;oCACL,UAAU,aAAa,CAAC,eAAe,iBAAiB;oCACxD,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAET,0BACC,6LAAC;wCAAK,WAAU;;;;;mFACd,SACF,OAEA;;;;;;;;;;;;;;;;;;8BAOR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAExC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;0DAE3C,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACxE,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;0DAE3C,6LAAC;gDACC,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC7E,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,uBAAA,iCAAA,WAAY,GAAG,CAAC,CAAA,yBACf,6LAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAShC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC;wCACC,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,aAAY;wCACZ,WAAU;wCACV,MAAM;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAI3C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sDACb,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,oBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oDAEV,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,MAAM;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAC/B,WAAU;;wDAET;sEACD,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;sEAEV,cAAA,6LAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,GAAE;;;;;;;;;;;;;;;;;mDApBpD;;;;;;;;;;;;;;;kDA6Bb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAW;gDACX,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,OAAO,IAAI;gDACtB,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;;oCAMF,YAAY,SAAS,MAAM,GAAG,mBAC7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA0C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DACZ,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,oBACzB,6LAAC;wDAEC,MAAK;wDACL,SAAS;4DACP,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,GAAG;gEACrC,YAAY,CAAA,OAAQ,CAAC;wEACnB,GAAG,IAAI;wEACP,MAAM;+EAAI,KAAK,IAAI;4EAAE,IAAI,IAAI;yEAAC;oEAChC,CAAC;4DACH;wDACF;wDACA,WAAU;kEAET,IAAI,IAAI;uDAZJ,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;0CAqBzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,SAAS,UAAU;wDAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,YAAY,EAAE,MAAM,CAAC,OAAO;gEAAC,CAAC;wDAC/E,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;;;;;;;0DAEpC,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,SAAS,SAAS,QAAQ;wDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,OAAO;gEAAC,CAAC;wDAC7E,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC,6IAAA,CAAA,mBAAgB;gCACf,OAAO,SAAS,OAAO;gCACvB,UAAU,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,SAAS;wCAAM,CAAC;gCACrE,aAAY;gCACZ,WAAW;gCACX,WAAW;gCACX,UAAU;gCACV,iBAAiB;gCACjB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GA5Za;;QAOI,qIAAA,CAAA,YAAS;QAcD,8HAAA,CAAA,oBAAiB;;;KArB7B", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/forms/index.ts"], "sourcesContent": ["export { PromptForm } from './PromptForm'"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/prompts/new/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { MainLayout } from '~/components/layout/MainLayout'\r\nimport { PromptForm } from '~/components/forms'\r\n\r\nexport default function NewPromptPage() {\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        <PromptForm />\r\n      </div>\r\n    </MainLayout>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC,6IAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,4IAAA,CAAA,aAAU;;;;;;;;;;;;;;;AAInB;KARwB", "debugId": null}}]}