require('../getQueryKey-PyKLS56S.cjs');
const require_shared = require('../shared-Dt4RsQVp.cjs');

exports.TRPCContext = require_shared.TRPCContext;
exports.contextProps = require_shared.contextProps;
exports.createQueryUtilsProxy = require_shared.createQueryUtilsProxy;
exports.createReactDecoration = require_shared.createReactDecoration;
exports.createReactQueryUtils = require_shared.createReactQueryUtils;
exports.createRootHooks = require_shared.createRootHooks;
exports.createUseQueries = require_shared.createUseQueries;
exports.getClientArgs = require_shared.getClientArgs;
exports.getQueryClient = require_shared.getQueryClient;
exports.getQueryType = require_shared.getQueryType;