'use client'

import { useEffect } from 'react'
import { useAuth } from '~/lib/auth/context'
import { useMainStore } from '~/stores'
import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { PageTransition } from '~/components/transitions'

interface MainLayoutProps {
  children: React.ReactNode
}

export const MainLayout = ({ children }: MainLayoutProps) => {
  const { user, loading } = useAuth()
  const { ui } = useMainStore()

  // 初始化主题
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', ui.theme)
  }, [ui.theme])

  // 如果正在加载认证状态，显示加载页面
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-200">
        <div className="text-center">
          <div className="loading loading-spinner loading-lg"></div>
          <p className="mt-4 text-base-content/70">加载中...</p>
        </div>
      </div>
    )
  }

  // 如果未登录，显示登录提示
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-200">
        <div className="card w-96 bg-base-100 shadow-xl">
          <div className="card-body text-center">
            <h2 className="card-title justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
                />
              </svg>
              需要登录
            </h2>
            <p className="text-base-content/70">
              请先登录以访问提示词管理工具
            </p>
            <div className="card-actions justify-center mt-4">
              <a href="/auth/login" className="btn btn-primary">
                前往登录
              </a>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-base-200">
      {/* 头部导航 */}
      <Header />

      <div className="flex">
        {/* 侧边栏 */}
        <Sidebar />

        {/* 主内容区域 */}
        <main
          className={`flex-1 min-h-screen transition-all duration-300 ease-in-out ${
            ui.sidebarOpen ? 'lg:ml-0' : 'lg:ml-0'
          }`}
        >
          <PageTransition>
            {children}
          </PageTransition>
        </main>
      </div>
    </div>
  )
}