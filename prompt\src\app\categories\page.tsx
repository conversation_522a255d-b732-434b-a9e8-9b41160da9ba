'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { api } from '~/trpc/react'
import { Category } from '~/types'
import { CategoryCard } from '~/components/categories/CategoryCard'
import { CategoryForm } from '~/components/categories/CategoryForm'
import { ConfirmDeleteModal } from '~/components/modals/ConfirmDeleteModal'
import { toast } from 'react-hot-toast'

export default function CategoriesPage() {
  const [isCreating, setIsCreating] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // 获取分类列表
  const { data: categories, isLoading, refetch } = api.categories.getAll.useQuery()

  // 删除分类
  const deleteMutation = api.categories.delete.useMutation({
    onSuccess: () => {
      toast.success('分类删除成功')
      setDeletingCategory(null)
      refetch()
    },
    onError: (error) => {
      toast.error('删除失败: ' + error.message)
    },
  })

  // 处理删除
  const handleDelete = (category: Category) => {
    setDeletingCategory(category)
  }

  // 确认删除
  const confirmDelete = () => {
    if (deletingCategory) {
      deleteMutation.mutate({ id: deletingCategory.id })
    }
  }

  // 处理编辑
  const handleEdit = (category: Category) => {
    setEditingCategory(category)
  }

  // 处理表单关闭
  const handleFormClose = () => {
    setIsCreating(false)
    setEditingCategory(null)
    refetch()
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          {/* 标题骨架 */}
          <div className="flex items-center justify-between">
            <div className="h-8 w-32 bg-base-300 rounded"></div>
            <div className="h-10 w-24 bg-base-300 rounded"></div>
          </div>
          
          {/* 统计骨架 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className="bg-base-100 rounded-lg p-4 space-y-3">
                <div className="h-4 w-20 bg-base-300 rounded"></div>
                <div className="h-8 w-16 bg-base-300 rounded"></div>
              </div>
            ))}
          </div>
          
          {/* 分类卡片骨架 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }, (_, i) => (
              <div key={i} className="bg-base-100 rounded-lg p-6 space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-base-300 rounded-lg"></div>
                  <div className="space-y-2 flex-1">
                    <div className="h-5 w-24 bg-base-300 rounded"></div>
                    <div className="h-4 w-32 bg-base-300 rounded"></div>
                  </div>
                </div>
                <div className="h-12 bg-base-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const totalCategories = categories?.length || 0
  const totalPrompts = categories?.reduce((sum, cat) => sum + (cat.promptCount || 0), 0) || 0
  const totalUsage = categories?.reduce((sum, cat) => sum + (cat.usageCount || 0), 0) || 0

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-base-content">分类管理</h1>
        <div className="flex items-center space-x-3">
          {/* 视图切换 */}
          <div className="btn-group">
            <button
              onClick={() => setViewMode('grid')}
              className={`btn btn-sm ${viewMode === 'grid' ? 'btn-active' : ''}`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z"
                />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`btn btn-sm ${viewMode === 'list' ? 'btn-active' : ''}`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                />
              </svg>
            </button>
          </div>

          {/* 新建分类按钮 */}
          <button
            onClick={() => setIsCreating(true)}
            className="btn btn-primary btn-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            新建分类
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="stat bg-base-100 rounded-lg shadow-md"
        >
          <div className="stat-figure text-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
              />
            </svg>
          </div>
          <div className="stat-title">总分类数</div>
          <div className="stat-value text-primary">{totalCategories}</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="stat bg-base-100 rounded-lg shadow-md"
        >
          <div className="stat-figure text-secondary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
              />
            </svg>
          </div>
          <div className="stat-title">提示词总数</div>
          <div className="stat-value text-secondary">{totalPrompts}</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="stat bg-base-100 rounded-lg shadow-md"
        >
          <div className="stat-figure text-accent">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-8 h-8"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
              />
            </svg>
          </div>
          <div className="stat-title">使用总次数</div>
          <div className="stat-value text-accent">{totalUsage}</div>
        </motion.div>
      </div>

      {/* 分类列表 */}
      {categories && categories.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          }
        >
          <AnimatePresence>
            {categories.map((category, index) => (
              <CategoryCard
                key={category.id}
                category={category}
                viewMode={viewMode}
                onEdit={handleEdit}
                onDelete={handleDelete}
                delay={index * 0.1}
              />
            ))}
          </AnimatePresence>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center py-12"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-16 h-16 mx-auto text-base-content/50 mb-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
            />
          </svg>
          <h3 className="text-lg font-semibold text-base-content mb-2">暂无分类</h3>
          <p className="text-base-content/70 mb-4">创建第一个分类来组织你的提示词</p>
          <button
            onClick={() => setIsCreating(true)}
            className="btn btn-primary"
          >
            创建分类
          </button>
        </motion.div>
      )}

      {/* 新建/编辑分类表单 */}
      <AnimatePresence>
        {(isCreating || editingCategory) && (
          <CategoryForm
            category={editingCategory}
            onClose={handleFormClose}
          />
        )}
      </AnimatePresence>

      {/* 删除确认对话框 */}
      <AnimatePresence>
        {deletingCategory && (
          <ConfirmDeleteModal
            isOpen={!!deletingCategory}
            onClose={() => setDeletingCategory(null)}
            onConfirm={confirmDelete}
            title="删除分类"
            message={`确定要删除分类 "${deletingCategory.name}" 吗？删除后该分类下的所有提示词将移至"未分类"。`}
            type="warning"
            confirmText="删除"
            isLoading={deleteMutation.isLoading}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

