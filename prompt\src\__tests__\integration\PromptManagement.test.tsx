import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Mock 完整的提示词管理流程组件
const PromptManagementFlow = () => {
  const [prompts, setPrompts] = React.useState([
    {
      id: '1',
      title: '代码审查提示',
      content: '请审查以下代码并提供改进建议...',
      category: { id: '1', name: '工作', color: '#3B82F6' },
      tags: [{ id: '1', name: '代码' }],
      isFavorite: false,
      usageCount: 5,
    },
    {
      id: '2',
      title: '文档写作助手',
      content: '帮我写一份技术文档的大纲...',
      category: { id: '2', name: '学习', color: '#10B981' },
      tags: [{ id: '2', name: '文档' }],
      isFavorite: true,
      usageCount: 3,
    },
  ])

  const [selectedPrompts, setSelectedPrompts] = React.useState<string[]>([])
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [editingPrompt, setEditingPrompt] = React.useState(null)
  const [searchQuery, setSearchQuery] = React.useState('')

  const handleCreate = (data: any) => {
    const newPrompt = {
      id: Date.now().toString(),
      ...data,
      usageCount: 0,
      isFavorite: false,
    }
    setPrompts(prev => [...prev, newPrompt])
    setIsFormOpen(false)
  }

  const handleEdit = (prompt: any) => {
    setEditingPrompt(prompt)
    setIsFormOpen(true)
  }

  const handleUpdate = (data: any) => {
    setPrompts(prev =>
      prev.map(p => (p.id === editingPrompt?.id ? { ...p, ...data } : p))
    )
    setIsFormOpen(false)
    setEditingPrompt(null)
  }

  const handleDelete = (id: string) => {
    setPrompts(prev => prev.filter(p => p.id !== id))
  }

  const handleBatchDelete = () => {
    setPrompts(prev => prev.filter(p => !selectedPrompts.includes(p.id)))
    setSelectedPrompts([])
  }

  const handleToggleFavorite = (id: string) => {
    setPrompts(prev =>
      prev.map(p => (p.id === id ? { ...p, isFavorite: !p.isFavorite } : p))
    )
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const filteredPrompts = prompts.filter(prompt =>
    prompt.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    prompt.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="container mx-auto p-4">
      {/* 搜索栏 */}
      <div className="mb-6">
        <div className="form-control">
          <input
            type="text"
            placeholder="搜索提示词..."
            className="input input-bordered w-full"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
      </div>

      {/* 工具栏 */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex gap-2">
          <button
            className="btn btn-primary"
            onClick={() => setIsFormOpen(true)}
          >
            新建提示词
          </button>
          
          {selectedPrompts.length > 0 && (
            <button
              className="btn btn-error"
              onClick={handleBatchDelete}
            >
              批量删除 ({selectedPrompts.length})
            </button>
          )}
        </div>
        
        <div className="text-sm text-base-content/70">
          共 {filteredPrompts.length} 个提示词
        </div>
      </div>

      {/* 提示词网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {filteredPrompts.map(prompt => (
          <div key={prompt.id} className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="card-title text-lg">{prompt.title}</h3>
                  <p className="text-sm text-base-content/70 mt-2">
                    {prompt.content.substring(0, 100)}...
                  </p>
                </div>
                
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    className="checkbox checkbox-primary"
                    checked={selectedPrompts.includes(prompt.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedPrompts(prev => [...prev, prompt.id])
                      } else {
                        setSelectedPrompts(prev => prev.filter(id => id !== prompt.id))
                      }
                    }}
                  />
                  
                  <button
                    className={`btn btn-ghost btn-sm ${prompt.isFavorite ? 'text-red-500' : ''}`}
                    onClick={() => handleToggleFavorite(prompt.id)}
                  >
                    ♥
                  </button>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                <div
                  className="badge badge-primary"
                  style={{ backgroundColor: prompt.category.color }}
                >
                  {prompt.category.name}
                </div>
                
                {prompt.tags.map(tag => (
                  <div key={tag.id} className="badge badge-outline">
                    {tag.name}
                  </div>
                ))}
              </div>

              <div className="card-actions justify-end mt-4">
                <div className="text-xs text-base-content/70">
                  使用 {prompt.usageCount} 次
                </div>
                
                <button
                  className="btn btn-outline btn-sm"
                  onClick={() => handleEdit(prompt)}
                >
                  编辑
                </button>
                
                <button
                  className="btn btn-error btn-sm"
                  onClick={() => handleDelete(prompt.id)}
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 空状态 */}
      {filteredPrompts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📝</div>
          <h3 className="text-2xl font-bold mb-2">
            {searchQuery ? '没有找到匹配的提示词' : '暂无提示词'}
          </h3>
          <p className="text-base-content/70 mb-6">
            {searchQuery ? '尝试使用其他关键词搜索' : '开始创建您的第一个提示词'}
          </p>
          
          {!searchQuery && (
            <button
              className="btn btn-primary"
              onClick={() => setIsFormOpen(true)}
            >
              创建提示词
            </button>
          )}
        </div>
      )}

      {/* 表单模态框 */}
      {isFormOpen && (
        <div className="modal modal-open">
          <div className="modal-box max-w-2xl">
            <h3 className="font-bold text-lg mb-4">
              {editingPrompt ? '编辑提示词' : '新建提示词'}
            </h3>
            
            <form
              onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.target as HTMLFormElement)
                const data = {
                  title: formData.get('title') as string,
                  content: formData.get('content') as string,
                  category: { id: '1', name: '工作', color: '#3B82F6' },
                  tags: [{ id: '1', name: '测试' }],
                }
                
                if (editingPrompt) {
                  handleUpdate(data)
                } else {
                  handleCreate(data)
                }
              }}
            >
              <div className="form-control mb-4">
                <label className="label">
                  <span className="label-text">标题</span>
                </label>
                <input
                  type="text"
                  name="title"
                  className="input input-bordered"
                  defaultValue={editingPrompt?.title || ''}
                  required
                />
              </div>

              <div className="form-control mb-4">
                <label className="label">
                  <span className="label-text">内容</span>
                </label>
                <textarea
                  name="content"
                  className="textarea textarea-bordered h-32"
                  defaultValue={editingPrompt?.content || ''}
                  required
                />
              </div>

              <div className="modal-action">
                <button type="submit" className="btn btn-primary">
                  保存
                </button>
                <button
                  type="button"
                  className="btn"
                  onClick={() => {
                    setIsFormOpen(false)
                    setEditingPrompt(null)
                  }}
                >
                  取消
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

// Mock 依赖
vi.mock('~/trpc/react', () => ({
  api: {
    prompts: {
      create: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
      update: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
      delete: {
        useMutation: () => ({
          mutate: vi.fn(),
        }),
      },
    },
  },
}))

describe('PromptManagement Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该完整渲染提示词管理界面', () => {
    render(<PromptManagementFlow />)
    
    // 检查主要元素
    expect(screen.getByPlaceholderText('搜索提示词...')).toBeInTheDocument()
    expect(screen.getByText('新建提示词')).toBeInTheDocument()
    expect(screen.getByText('共 2 个提示词')).toBeInTheDocument()
    expect(screen.getByText('代码审查提示')).toBeInTheDocument()
    expect(screen.getByText('文档写作助手')).toBeInTheDocument()
  })

  it('应该完整执行搜索流程', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 搜索功能
    const searchInput = screen.getByPlaceholderText('搜索提示词...')
    await user.type(searchInput, '代码')
    
    // 检查搜索结果
    expect(screen.getByText('代码审查提示')).toBeInTheDocument()
    expect(screen.queryByText('文档写作助手')).not.toBeInTheDocument()
    expect(screen.getByText('共 1 个提示词')).toBeInTheDocument()
  })

  it('应该完整执行创建提示词流程', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 点击新建按钮
    const createButton = screen.getByText('新建提示词')
    await user.click(createButton)
    
    // 检查模态框出现
    expect(screen.getByText('新建提示词')).toBeInTheDocument()
    expect(screen.getByRole('dialog')).toBeInTheDocument()
    
    // 填写表单
    const titleInput = screen.getByLabelText('标题')
    const contentInput = screen.getByLabelText('内容')
    
    await user.type(titleInput, '新的测试提示词')
    await user.type(contentInput, '这是一个新的测试提示词内容')
    
    // 提交表单
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    // 检查新提示词是否出现
    await waitFor(() => {
      expect(screen.getByText('新的测试提示词')).toBeInTheDocument()
    })
    
    // 检查模态框是否关闭
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    
    // 检查计数更新
    expect(screen.getByText('共 3 个提示词')).toBeInTheDocument()
  })

  it('应该完整执行编辑提示词流程', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 点击编辑按钮
    const editButtons = screen.getAllByText('编辑')
    await user.click(editButtons[0])
    
    // 检查模态框出现，并填充了数据
    expect(screen.getByText('编辑提示词')).toBeInTheDocument()
    expect(screen.getByDisplayValue('代码审查提示')).toBeInTheDocument()
    
    // 修改标题
    const titleInput = screen.getByLabelText('标题')
    await user.clear(titleInput)
    await user.type(titleInput, '修改后的代码审查提示')
    
    // 提交表单
    const saveButton = screen.getByText('保存')
    await user.click(saveButton)
    
    // 检查修改是否生效
    await waitFor(() => {
      expect(screen.getByText('修改后的代码审查提示')).toBeInTheDocument()
    })
    
    // 检查原标题不再存在
    expect(screen.queryByText('代码审查提示')).not.toBeInTheDocument()
  })

  it('应该完整执行删除提示词流程', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 点击删除按钮
    const deleteButtons = screen.getAllByText('删除')
    await user.click(deleteButtons[0])
    
    // 检查提示词是否被删除
    await waitFor(() => {
      expect(screen.queryByText('代码审查提示')).not.toBeInTheDocument()
    })
    
    // 检查计数更新
    expect(screen.getByText('共 1 个提示词')).toBeInTheDocument()
  })

  it('应该完整执行批量删除流程', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 选择多个提示词
    const checkboxes = screen.getAllByRole('checkbox')
    await user.click(checkboxes[0])
    await user.click(checkboxes[1])
    
    // 检查批量删除按钮出现
    expect(screen.getByText('批量删除 (2)')).toBeInTheDocument()
    
    // 点击批量删除
    const batchDeleteButton = screen.getByText('批量删除 (2)')
    await user.click(batchDeleteButton)
    
    // 检查所有选中的提示词都被删除
    await waitFor(() => {
      expect(screen.queryByText('代码审查提示')).not.toBeInTheDocument()
      expect(screen.queryByText('文档写作助手')).not.toBeInTheDocument()
    })
    
    // 检查显示空状态
    expect(screen.getByText('暂无提示词')).toBeInTheDocument()
    expect(screen.getByText('开始创建您的第一个提示词')).toBeInTheDocument()
  })

  it('应该完整执行收藏切换流程', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 点击第一个提示词的收藏按钮
    const favoriteButtons = screen.getAllByText('♥')
    const firstFavoriteButton = favoriteButtons[0]
    
    // 检查初始状态
    expect(firstFavoriteButton).not.toHaveClass('text-red-500')
    
    // 点击收藏
    await user.click(firstFavoriteButton)
    
    // 检查收藏状态改变
    expect(firstFavoriteButton).toHaveClass('text-red-500')
    
    // 再次点击取消收藏
    await user.click(firstFavoriteButton)
    
    // 检查收藏状态取消
    expect(firstFavoriteButton).not.toHaveClass('text-red-500')
  })

  it('应该处理空搜索结果', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 搜索不存在的内容
    const searchInput = screen.getByPlaceholderText('搜索提示词...')
    await user.type(searchInput, '不存在的内容')
    
    // 检查空状态
    expect(screen.getByText('没有找到匹配的提示词')).toBeInTheDocument()
    expect(screen.getByText('尝试使用其他关键词搜索')).toBeInTheDocument()
    expect(screen.getByText('共 0 个提示词')).toBeInTheDocument()
  })

  it('应该处理表单取消操作', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 打开新建表单
    const createButton = screen.getByText('新建提示词')
    await user.click(createButton)
    
    // 填写一些内容
    const titleInput = screen.getByLabelText('标题')
    await user.type(titleInput, '测试标题')
    
    // 点击取消
    const cancelButton = screen.getByText('取消')
    await user.click(cancelButton)
    
    // 检查模态框关闭
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    
    // 检查没有创建新的提示词
    expect(screen.getByText('共 2 个提示词')).toBeInTheDocument()
  })

  it('应该处理单选和全选操作', async () => {
    const user = userEvent.setup()
    render(<PromptManagementFlow />)
    
    // 选择第一个提示词
    const checkboxes = screen.getAllByRole('checkbox')
    await user.click(checkboxes[0])
    
    // 检查选中状态
    expect(checkboxes[0]).toBeChecked()
    expect(screen.queryByText('批量删除 (1)')).toBeInTheDocument()
    
    // 取消选择
    await user.click(checkboxes[0])
    
    // 检查取消选中状态
    expect(checkboxes[0]).not.toBeChecked()
    expect(screen.queryByText('批量删除')).not.toBeInTheDocument()
  })

  describe('数据持久化', () => {
    it('应该在操作后保持数据一致性', async () => {
      const user = userEvent.setup()
      render(<PromptManagementFlow />)
      
      // 创建新提示词
      const createButton = screen.getByText('新建提示词')
      await user.click(createButton)
      
      const titleInput = screen.getByLabelText('标题')
      await user.type(titleInput, '持久化测试')
      
      const contentInput = screen.getByLabelText('内容')
      await user.type(contentInput, '测试内容')
      
      const saveButton = screen.getByText('保存')
      await user.click(saveButton)
      
      // 搜索新创建的提示词
      const searchInput = screen.getByPlaceholderText('搜索提示词...')
      await user.type(searchInput, '持久化')
      
      // 检查数据是否正确保存
      expect(screen.getByText('持久化测试')).toBeInTheDocument()
      expect(screen.getByText('共 1 个提示词')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理表单验证错误', async () => {
      const user = userEvent.setup()
      render(<PromptManagementFlow />)
      
      // 打开表单但不填写内容
      const createButton = screen.getByText('新建提示词')
      await user.click(createButton)
      
      const saveButton = screen.getByText('保存')
      await user.click(saveButton)
      
      // 检查表单验证
      const titleInput = screen.getByLabelText('标题')
      expect(titleInput).toBeInvalid()
      
      const contentInput = screen.getByLabelText('内容')
      expect(contentInput).toBeInvalid()
    })
  })

  describe('用户体验', () => {
    it('应该提供即时反馈', async () => {
      const user = userEvent.setup()
      render(<PromptManagementFlow />)
      
      // 搜索时应该立即显示结果
      const searchInput = screen.getByPlaceholderText('搜索提示词...')
      await user.type(searchInput, '代码')
      
      // 不需要等待，结果应该立即显示
      expect(screen.getByText('代码审查提示')).toBeInTheDocument()
      expect(screen.getByText('共 1 个提示词')).toBeInTheDocument()
    })

    it('应该保持操作的连贯性', async () => {
      const user = userEvent.setup()
      render(<PromptManagementFlow />)
      
      // 选择提示词
      const checkboxes = screen.getAllByRole('checkbox')
      await user.click(checkboxes[0])
      
      // 在其他操作后选择状态应该保持
      const searchInput = screen.getByPlaceholderText('搜索提示词...')
      await user.type(searchInput, '代码')
      
      // 检查选择状态保持
      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).toBeChecked()
    })
  })
})