import { test as setup, expect } from '@playwright/test'

const authFile = 'playwright/.auth/user.json'

setup('authenticate', async ({ page }) => {
  // 前往登录页面
  await page.goto('/auth/login')
  
  // 填写登录信息
  await page.fill('[data-testid="email-input"]', '<EMAIL>')
  await page.fill('[data-testid="password-input"]', 'password123')
  
  // 点击登录按钮
  await page.click('[data-testid="login-button"]')
  
  // 等待登录成功并跳转到主页
  await page.waitForURL('/')
  
  // 验证用户已登录
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible()
  
  // 保存认证状态
  await page.context().storageState({ path: authFile })
})

setup.describe.configure({ mode: 'serial' })