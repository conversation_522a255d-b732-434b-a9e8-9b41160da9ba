{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: var(--font-geist-sans), ui-sans-serif, system-ui; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n:root,\n[data-theme] {\n  background-color: var(--fallback-b1,oklch(var(--b1)/1));\n  color: var(--fallback-bc,oklch(var(--bc)/1));\n}\n\n@supports not (color: oklch(0% 0 0)) {\n\n  :root {\n    color-scheme: light;\n    --fallback-p: #491eff;\n    --fallback-pc: #d4dbff;\n    --fallback-s: #ff41c7;\n    --fallback-sc: #fff9fc;\n    --fallback-a: #00cfbd;\n    --fallback-ac: #00100d;\n    --fallback-n: #2b3440;\n    --fallback-nc: #d7dde4;\n    --fallback-b1: #ffffff;\n    --fallback-b2: #e5e6e6;\n    --fallback-b3: #e5e6e6;\n    --fallback-bc: #1f2937;\n    --fallback-in: #00b3f0;\n    --fallback-inc: #000000;\n    --fallback-su: #00ca92;\n    --fallback-suc: #000000;\n    --fallback-wa: #ffc22d;\n    --fallback-wac: #000000;\n    --fallback-er: #ff6f70;\n    --fallback-erc: #000000;\n  }\n\n  @media (prefers-color-scheme: dark) {\n\n    :root {\n      color-scheme: dark;\n      --fallback-p: #7582ff;\n      --fallback-pc: #050617;\n      --fallback-s: #ff71cf;\n      --fallback-sc: #190211;\n      --fallback-a: #00c7b5;\n      --fallback-ac: #000e0c;\n      --fallback-n: #2a323c;\n      --fallback-nc: #a6adbb;\n      --fallback-b1: #1d232a;\n      --fallback-b2: #191e24;\n      --fallback-b3: #15191e;\n      --fallback-bc: #a6adbb;\n      --fallback-in: #00b3f0;\n      --fallback-inc: #000000;\n      --fallback-su: #00ca92;\n      --fallback-suc: #000000;\n      --fallback-wa: #ffc22d;\n      --fallback-wac: #000000;\n      --fallback-er: #ff6f70;\n      --fallback-erc: #000000;\n    }\n  }\n}\n\nhtml {\n  -webkit-tap-highlight-color: transparent;\n}\n\n* {\n  scrollbar-color: color-mix(in oklch, currentColor 35%, transparent) transparent;\n}\n\n*:hover {\n  scrollbar-color: color-mix(in oklch, currentColor 60%, transparent) transparent;\n}\n\n:root {\n  --p: 62.3083% 0.188015 259.814527;\n  --bc: 20% 0 0;\n  --pc: 12.4617% 0.037603 259.814527;\n  --sc: 91.0878% 0.008143 257.416613;\n  --ac: 14.2967% 0.025147 215.220903;\n  --nc: 85.5616% 0.005919 256.847952;\n  --inc: 13.6937% 0.029574 237.322518;\n  --suc: 13.9175% 0.029815 162.479602;\n  --wac: 15.3718% 0.032932 70.08039;\n  --erc: 12.7367% 0.04157 25.331328;\n  --rounded-box: 1rem;\n  --rounded-btn: 0.5rem;\n  --rounded-badge: 1.9rem;\n  --animation-btn: 0.25s;\n  --animation-input: .2s;\n  --btn-focus-scale: 0.95;\n  --border-btn: 1px;\n  --tab-border: 1px;\n  --tab-radius: 0.5rem;\n  --s: 55.4391% 0.040717 257.416613;\n  --a: 71.4837% 0.125737 215.220903;\n  --n: 27.8078% 0.029596 256.847952;\n  --b1: 100% 0 0;\n  --b2: 98.4152% 0.003413 247.857544;\n  --b3: 92.8761% 0.012619 255.507846;\n  --in: 68.4687% 0.147869 237.322518;\n  --su: 69.5873% 0.149074 162.479602;\n  --wa: 76.859% 0.164659 70.08039;\n  --er: 63.6834% 0.207849 25.331328;\n}\n\n@media (prefers-color-scheme: dark) {\n\n  :root {\n    color-scheme: dark;\n    --in: 72.06% 0.191 231.6;\n    --su: 64.8% 0.150 160;\n    --wa: 84.71% 0.199 83.87;\n    --er: 71.76% 0.221 22.18;\n    --pc: 13.138% 0.0392 275.75;\n    --sc: 14.96% 0.052 342.55;\n    --ac: 14.902% 0.0334 183.61;\n    --inc: 0% 0 0;\n    --suc: 0% 0 0;\n    --wac: 0% 0 0;\n    --erc: 0% 0 0;\n    --rounded-box: 1rem;\n    --rounded-btn: 0.5rem;\n    --rounded-badge: 1.9rem;\n    --animation-btn: 0.25s;\n    --animation-input: .2s;\n    --btn-focus-scale: 0.95;\n    --border-btn: 1px;\n    --tab-border: 1px;\n    --tab-radius: 0.5rem;\n    --p: 65.69% 0.196 275.75;\n    --s: 74.8% 0.26 342.55;\n    --a: 74.51% 0.167 183.61;\n    --n: 31.3815% 0.021108 254.139175;\n    --nc: 74.6477% 0.0216 264.435964;\n    --b1: 25.3267% 0.015896 252.417568;\n    --b2: 23.2607% 0.013807 253.100675;\n    --b3: 21.1484% 0.01165 254.087939;\n    --bc: 74.6477% 0.0216 264.435964;\n  }\n}\n\n[data-theme=light] {\n  --p: 62.3083% 0.188015 259.814527;\n  --bc: 20% 0 0;\n  --pc: 12.4617% 0.037603 259.814527;\n  --sc: 91.0878% 0.008143 257.416613;\n  --ac: 14.2967% 0.025147 215.220903;\n  --nc: 85.5616% 0.005919 256.847952;\n  --inc: 13.6937% 0.029574 237.322518;\n  --suc: 13.9175% 0.029815 162.479602;\n  --wac: 15.3718% 0.032932 70.08039;\n  --erc: 12.7367% 0.04157 25.331328;\n  --rounded-box: 1rem;\n  --rounded-btn: 0.5rem;\n  --rounded-badge: 1.9rem;\n  --animation-btn: 0.25s;\n  --animation-input: .2s;\n  --btn-focus-scale: 0.95;\n  --border-btn: 1px;\n  --tab-border: 1px;\n  --tab-radius: 0.5rem;\n  --s: 55.4391% 0.040717 257.416613;\n  --a: 71.4837% 0.125737 215.220903;\n  --n: 27.8078% 0.029596 256.847952;\n  --b1: 100% 0 0;\n  --b2: 98.4152% 0.003413 247.857544;\n  --b3: 92.8761% 0.012619 255.507846;\n  --in: 68.4687% 0.147869 237.322518;\n  --su: 69.5873% 0.149074 162.479602;\n  --wa: 76.859% 0.164659 70.08039;\n  --er: 63.6834% 0.207849 25.331328;\n}\n\n[data-theme=dark] {\n  color-scheme: dark;\n  --in: 72.06% 0.191 231.6;\n  --su: 64.8% 0.150 160;\n  --wa: 84.71% 0.199 83.87;\n  --er: 71.76% 0.221 22.18;\n  --pc: 13.138% 0.0392 275.75;\n  --sc: 14.96% 0.052 342.55;\n  --ac: 14.902% 0.0334 183.61;\n  --inc: 0% 0 0;\n  --suc: 0% 0 0;\n  --wac: 0% 0 0;\n  --erc: 0% 0 0;\n  --rounded-box: 1rem;\n  --rounded-btn: 0.5rem;\n  --rounded-badge: 1.9rem;\n  --animation-btn: 0.25s;\n  --animation-input: .2s;\n  --btn-focus-scale: 0.95;\n  --border-btn: 1px;\n  --tab-border: 1px;\n  --tab-radius: 0.5rem;\n  --p: 65.69% 0.196 275.75;\n  --s: 74.8% 0.26 342.55;\n  --a: 74.51% 0.167 183.61;\n  --n: 31.3815% 0.021108 254.139175;\n  --nc: 74.6477% 0.0216 264.435964;\n  --b1: 25.3267% 0.015896 252.417568;\n  --b2: 23.2607% 0.013807 253.100675;\n  --b3: 21.1484% 0.01165 254.087939;\n  --bc: 74.6477% 0.0216 264.435964;\n}\n.container {\n  width: 100%;\n}\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\n.alert {\n  display: grid;\n  width: 100%;\n  grid-auto-flow: row;\n  align-content: flex-start;\n  align-items: center;\n  justify-items: center;\n  gap: 1rem;\n  text-align: center;\n  border-radius: var(--rounded-box, 1rem);\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n  padding: 1rem;\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  --alert-bg: var(--fallback-b2,oklch(var(--b2)/1));\n  --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1));\n  background-color: var(--alert-bg);\n}\n@media (min-width: 640px) {\n\n  .alert {\n    grid-auto-flow: column;\n    grid-template-columns: auto minmax(auto,1fr);\n    justify-items: start;\n    text-align: start;\n  }\n}\n.avatar {\n  position: relative;\n  display: inline-flex;\n}\n.avatar > div {\n  display: block;\n  aspect-ratio: 1 / 1;\n  overflow: hidden;\n}\n.avatar img {\n  height: 100%;\n  width: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.avatar.placeholder > div {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.badge {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  transition-duration: 200ms;\n  height: 1.25rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  width: -moz-fit-content;\n  width: fit-content;\n  padding-left: 0.563rem;\n  padding-right: 0.563rem;\n  border-radius: var(--rounded-badge, 1.9rem);\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n@media (hover:hover) {\n\n  .checkbox-primary:hover {\n    --tw-border-opacity: 1;\n    border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));\n  }\n\n  .checkbox-error:hover {\n    --tw-border-opacity: 1;\n    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  }\n\n  .label a:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  }\n\n  .menu li > *:not(ul, .menu-title, details, .btn):active,\n.menu li > *:not(ul, .menu-title, details, .btn).active,\n.menu li > details > summary:active {\n    --tw-bg-opacity: 1;\n    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n    --tw-text-opacity: 1;\n    color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));\n  }\n\n  .radio-error:hover {\n    --tw-border-opacity: 1;\n    border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  }\n\n  .tab:hover {\n    --tw-text-opacity: 1;\n  }\n\n  .tabs-boxed :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):hover, .tabs-boxed :is(input:checked):hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n  }\n\n  .table tr.hover:hover,\n  .table tr.hover:nth-child(even):hover {\n    --tw-bg-opacity: 1;\n    background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  }\n\n  .table-zebra tr.hover:hover,\n  .table-zebra tr.hover:nth-child(even):hover {\n    --tw-bg-opacity: 1;\n    background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));\n  }\n}\n.btn {\n  display: inline-flex;\n  height: 3rem;\n  min-height: 3rem;\n  flex-shrink: 0;\n  cursor: pointer;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n  border-radius: var(--rounded-btn, 0.5rem);\n  border-color: transparent;\n  border-color: oklch(var(--btn-color, var(--b2)) / var(--tw-border-opacity));\n  padding-left: 1rem;\n  padding-right: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n  line-height: 1em;\n  gap: 0.5rem;\n  font-weight: 600;\n  text-decoration-line: none;\n  transition-duration: 200ms;\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  border-width: var(--border-btn, 1px);\n  transition-property: color, background-color, border-color, opacity, box-shadow, transform;\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  outline-color: var(--fallback-bc,oklch(var(--bc)/1));\n  background-color: oklch(var(--btn-color, var(--b2)) / var(--tw-bg-opacity));\n  --tw-bg-opacity: 1;\n  --tw-border-opacity: 1;\n}\n.btn-disabled,\n  .btn[disabled],\n  .btn:disabled {\n  pointer-events: none;\n}\n.btn-square {\n  height: 3rem;\n  width: 3rem;\n  padding: 0px;\n}\n.btn-circle {\n  height: 3rem;\n  width: 3rem;\n  border-radius: 9999px;\n  padding: 0px;\n}\n:where(.btn:is(input[type=\"checkbox\"])),\n:where(.btn:is(input[type=\"radio\"])) {\n  width: auto;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n}\n.btn:is(input[type=\"checkbox\"]):after,\n.btn:is(input[type=\"radio\"]):after {\n  --tw-content: attr(aria-label);\n  content: var(--tw-content);\n}\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  border-radius: var(--rounded-box, 1rem);\n}\n.card:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.card-body {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  padding: var(--padding-card, 2rem);\n  gap: 0.5rem;\n}\n.card-body :where(p) {\n  flex-grow: 1;\n}\n.card-actions {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: flex-start;\n  gap: 0.5rem;\n}\n.card figure {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.card.image-full {\n  display: grid;\n}\n.card.image-full:before {\n  position: relative;\n  content: \"\";\n  z-index: 10;\n  border-radius: var(--rounded-box, 1rem);\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n  opacity: 0.75;\n}\n.card.image-full:before,\n    .card.image-full > * {\n  grid-column-start: 1;\n  grid-row-start: 1;\n}\n.card.image-full > figure img {\n  height: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.card.image-full > .card-body {\n  position: relative;\n  z-index: 20;\n  --tw-text-opacity: 1;\n  color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));\n}\n.checkbox {\n  flex-shrink: 0;\n  --chkbg: var(--fallback-bc,oklch(var(--bc)/1));\n  --chkfg: var(--fallback-b1,oklch(var(--b1)/1));\n  height: 1.5rem;\n  width: 1.5rem;\n  cursor: pointer;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  border-radius: var(--rounded-btn, 0.5rem);\n  border-width: 1px;\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-border-opacity: 0.2;\n}\n.divider {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n  height: 1rem;\n  white-space: nowrap;\n}\n.divider:before,\n  .divider:after {\n  height: 0.125rem;\n  width: 100%;\n  flex-grow: 1;\n  --tw-content: '';\n  content: var(--tw-content);\n  background-color: var(--fallback-bc,oklch(var(--bc)/0.1));\n}\n.dropdown {\n  position: relative;\n  display: inline-block;\n}\n.dropdown > *:not(summary):focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.dropdown .dropdown-content {\n  position: absolute;\n}\n.dropdown:is(:not(details)) .dropdown-content {\n  visibility: hidden;\n  opacity: 0;\n  transform-origin: top;\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  transition-duration: 200ms;\n}\n.dropdown-end .dropdown-content {\n  inset-inline-end: 0px;\n}\n.dropdown-left .dropdown-content {\n  bottom: auto;\n  inset-inline-end: 100%;\n  top: 0px;\n  transform-origin: right;\n}\n.dropdown-right .dropdown-content {\n  bottom: auto;\n  inset-inline-start: 100%;\n  top: 0px;\n  transform-origin: left;\n}\n.dropdown-bottom .dropdown-content {\n  bottom: auto;\n  top: 100%;\n  transform-origin: top;\n}\n.dropdown-top .dropdown-content {\n  bottom: 100%;\n  top: auto;\n  transform-origin: bottom;\n}\n.dropdown-end.dropdown-right .dropdown-content {\n  bottom: 0px;\n  top: auto;\n}\n.dropdown-end.dropdown-left .dropdown-content {\n  bottom: 0px;\n  top: auto;\n}\n.dropdown.dropdown-open .dropdown-content,\n.dropdown:not(.dropdown-hover):focus .dropdown-content,\n.dropdown:focus-within .dropdown-content {\n  visibility: visible;\n  opacity: 1;\n}\n@media (hover: hover) {\n\n  .dropdown.dropdown-hover:hover .dropdown-content {\n    visibility: visible;\n    opacity: 1;\n  }\n\n  .btm-nav > *.disabled:hover,\n      .btm-nav > *[disabled]:hover {\n    pointer-events: none;\n    --tw-border-opacity: 0;\n    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n    --tw-bg-opacity: 0.1;\n    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n    --tw-text-opacity: 0.2;\n  }\n\n  .btn:hover {\n    --tw-border-opacity: 1;\n    border-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-border-opacity)));\n    --tw-bg-opacity: 1;\n    background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn:hover {\n      background-color: color-mix(\n            in oklab,\n            oklch(var(--btn-color, var(--b2)) / var(--tw-bg-opacity, 1)) 90%,\n            black\n          );\n      border-color: color-mix(\n            in oklab,\n            oklch(var(--btn-color, var(--b2)) / var(--tw-border-opacity, 1)) 90%,\n            black\n          );\n    }\n  }\n\n  @supports not (color: oklch(0% 0 0)) {\n\n    .btn:hover {\n      background-color: var(--btn-color, var(--fallback-b2));\n      border-color: var(--btn-color, var(--fallback-b2));\n    }\n  }\n\n  .btn.glass:hover {\n    --glass-opacity: 25%;\n    --glass-border-opacity: 15%;\n  }\n\n  .btn-ghost:hover {\n    border-color: transparent;\n  }\n\n  @supports (color: oklch(0% 0 0)) {\n\n    .btn-ghost:hover {\n      background-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n    }\n  }\n\n  .btn-outline:hover {\n    --tw-border-opacity: 1;\n    border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n    --tw-bg-opacity: 1;\n    background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n    --tw-text-opacity: 1;\n    color: var(--fallback-b1,oklch(var(--b1)/var(--tw-text-opacity)));\n  }\n\n  .btn-outline.btn-primary:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-primary:hover {\n      background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n    }\n  }\n\n  .btn-outline.btn-secondary:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-secondary:hover {\n      background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n    }\n  }\n\n  .btn-outline.btn-accent:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-accent:hover {\n      background-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black);\n    }\n  }\n\n  .btn-outline.btn-success:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-success:hover {\n      background-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black);\n    }\n  }\n\n  .btn-outline.btn-info:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-info:hover {\n      background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n    }\n  }\n\n  .btn-outline.btn-warning:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-warning:hover {\n      background-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black);\n    }\n  }\n\n  .btn-outline.btn-error:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn-outline.btn-error:hover {\n      background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n    }\n  }\n\n  .btn-disabled:hover,\n    .btn[disabled]:hover,\n    .btn:disabled:hover {\n    --tw-border-opacity: 0;\n    background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n    --tw-bg-opacity: 0.2;\n    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n    --tw-text-opacity: 0.2;\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .btn:is(input[type=\"checkbox\"]:checked):hover, .btn:is(input[type=\"radio\"]:checked):hover {\n      background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n    }\n  }\n\n  .dropdown.dropdown-hover:hover .dropdown-content {\n    --tw-scale-x: 1;\n    --tw-scale-y: 1;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(.active, .btn):hover, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(.active, .btn):hover {\n    cursor: pointer;\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n  }\n\n  @supports (color: oklch(0% 0 0)) {\n\n    :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(.active, .btn):hover, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(.active, .btn):hover {\n      background-color: var(--fallback-bc,oklch(var(--bc)/0.1));\n    }\n  }\n\n  .tab[disabled],\n    .tab[disabled]:hover {\n    cursor: not-allowed;\n    color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n    --tw-text-opacity: 0.2;\n  }\n}\n.dropdown:is(details) summary::-webkit-details-marker {\n  display: none;\n}\n.form-control {\n  display: flex;\n  flex-direction: column;\n}\n.label {\n  display: flex;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  align-items: center;\n  justify-content: space-between;\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.indicator {\n  position: relative;\n  display: inline-flex;\n  width: -moz-max-content;\n  width: max-content;\n}\n.indicator :where(.indicator-item) {\n  z-index: 1;\n  position: absolute;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  white-space: nowrap;\n}\n.input {\n  flex-shrink: 1;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  height: 3rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  font-size: 1rem;\n  line-height: 2;\n  line-height: 1.5rem;\n  border-radius: var(--rounded-btn, 0.5rem);\n  border-width: 1px;\n  border-color: transparent;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n}\n.input[type=\"number\"]::-webkit-inner-spin-button,\n.input-md[type=\"number\"]::-webkit-inner-spin-button {\n  margin-top: -1rem;\n  margin-bottom: -1rem;\n  margin-inline-end: -1rem;\n}\n.input-xs[type=\"number\"]::-webkit-inner-spin-button {\n  margin-top: -0.25rem;\n  margin-bottom: -0.25rem;\n  margin-inline-end: -0px;\n}\n.input-sm[type=\"number\"]::-webkit-inner-spin-button {\n  margin-top: 0px;\n  margin-bottom: 0px;\n  margin-inline-end: -0px;\n}\n.input-lg[type=\"number\"]::-webkit-inner-spin-button {\n  margin-top: -1.5rem;\n  margin-bottom: -1.5rem;\n  margin-inline-end: -1.5rem;\n}\n.join {\n  display: inline-flex;\n  align-items: stretch;\n  border-radius: var(--rounded-btn, 0.5rem);\n}\n.join :where(.join-item) {\n  border-start-end-radius: 0;\n  border-end-end-radius: 0;\n  border-end-start-radius: 0;\n  border-start-start-radius: 0;\n}\n.join .join-item:not(:first-child):not(:last-child),\n  .join *:not(:first-child):not(:last-child) .join-item {\n  border-start-end-radius: 0;\n  border-end-end-radius: 0;\n  border-end-start-radius: 0;\n  border-start-start-radius: 0;\n}\n.join .join-item:first-child:not(:last-child),\n  .join *:first-child:not(:last-child) .join-item {\n  border-start-end-radius: 0;\n  border-end-end-radius: 0;\n}\n.join .dropdown .join-item:first-child:not(:last-child),\n  .join *:first-child:not(:last-child) .dropdown .join-item {\n  border-start-end-radius: inherit;\n  border-end-end-radius: inherit;\n}\n.join :where(.join-item:first-child:not(:last-child)),\n  .join :where(*:first-child:not(:last-child) .join-item) {\n  border-end-start-radius: inherit;\n  border-start-start-radius: inherit;\n}\n.join .join-item:last-child:not(:first-child),\n  .join *:last-child:not(:first-child) .join-item {\n  border-end-start-radius: 0;\n  border-start-start-radius: 0;\n}\n.join :where(.join-item:last-child:not(:first-child)),\n  .join :where(*:last-child:not(:first-child) .join-item) {\n  border-start-end-radius: inherit;\n  border-end-end-radius: inherit;\n}\n@supports not selector(:has(*)) {\n\n  :where(.join *) {\n    border-radius: inherit;\n  }\n}\n@supports selector(:has(*)) {\n\n  :where(.join *:has(.join-item)) {\n    border-radius: inherit;\n  }\n}\n.kbd {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: var(--rounded-btn, 0.5rem);\n  border-width: 1px;\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-border-opacity: 0.2;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  border-bottom-width: 2px;\n  min-height: 2.2em;\n  min-width: 2.2em;\n}\n.link {\n  cursor: pointer;\n  text-decoration-line: underline;\n}\n.menu {\n  display: flex;\n  flex-direction: column;\n  flex-wrap: wrap;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  padding: 0.5rem;\n}\n.menu :where(li ul) {\n  position: relative;\n  white-space: nowrap;\n  margin-inline-start: 1rem;\n  padding-inline-start: 0.5rem;\n}\n.menu :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), .menu :where(li:not(.menu-title) > details > summary:not(.menu-title)) {\n  display: grid;\n  grid-auto-flow: column;\n  align-content: flex-start;\n  align-items: center;\n  gap: 0.5rem;\n  grid-auto-columns: minmax(auto, max-content) auto max-content;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.menu li.disabled {\n  cursor: not-allowed;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  color: var(--fallback-bc,oklch(var(--bc)/0.3));\n}\n.menu :where(li > .menu-dropdown:not(.menu-dropdown-show)) {\n  display: none;\n}\n:where(.menu li) {\n  position: relative;\n  display: flex;\n  flex-shrink: 0;\n  flex-direction: column;\n  flex-wrap: wrap;\n  align-items: stretch;\n}\n:where(.menu li) .badge {\n  justify-self: end;\n}\n.modal {\n  pointer-events: none;\n  position: fixed;\n  inset: 0px;\n  margin: 0px;\n  display: grid;\n  height: 100%;\n  max-height: none;\n  width: 100%;\n  max-width: none;\n  justify-items: center;\n  padding: 0px;\n  opacity: 0;\n  overscroll-behavior: contain;\n  z-index: 999;\n  background-color: transparent;\n  color: inherit;\n  transition-duration: 200ms;\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  transition-property: transform, opacity, visibility;\n  overflow-y: hidden;\n}\n:where(.modal) {\n  align-items: center;\n}\n.modal-box {\n  max-height: calc(100vh - 5em);\n  grid-column-start: 1;\n  grid-row-start: 1;\n  width: 91.666667%;\n  max-width: 32rem;\n  --tw-scale-x: .9;\n  --tw-scale-y: .9;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-bottom-right-radius: var(--rounded-box, 1rem);\n  border-bottom-left-radius: var(--rounded-box, 1rem);\n  border-top-left-radius: var(--rounded-box, 1rem);\n  border-top-right-radius: var(--rounded-box, 1rem);\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n  padding: 1.5rem;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  transition-duration: 200ms;\n  box-shadow: rgba(0, 0, 0, 0.25) 0px 25px 50px -12px;\n  overflow-y: auto;\n  overscroll-behavior: contain;\n}\n.modal-open,\n.modal:target,\n.modal-toggle:checked + .modal,\n.modal[open] {\n  pointer-events: auto;\n  visibility: visible;\n  opacity: 1;\n}\n.modal-action {\n  display: flex;\n  margin-top: 1.5rem;\n  justify-content: flex-end;\n}\n:root:has(:is(.modal-open, .modal:target, .modal-toggle:checked + .modal, .modal[open])) {\n  overflow: hidden;\n  scrollbar-gutter: stable;\n}\n.navbar {\n  display: flex;\n  align-items: center;\n  padding: var(--navbar-padding, 0.5rem);\n  min-height: 4rem;\n  width: 100%;\n}\n:where(.navbar > *:not(script, style)) {\n  display: inline-flex;\n  align-items: center;\n}\n.navbar-start {\n  width: 50%;\n  justify-content: flex-start;\n}\n.navbar-center {\n  flex-shrink: 0;\n}\n.navbar-end {\n  width: 50%;\n  justify-content: flex-end;\n}\n.radio {\n  flex-shrink: 0;\n  --chkbg: var(--bc);\n  height: 1.5rem;\n  width: 1.5rem;\n  cursor: pointer;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  border-radius: 9999px;\n  border-width: 1px;\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-border-opacity: 0.2;\n}\n.select {\n  display: inline-flex;\n  cursor: pointer;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  height: 3rem;\n  min-height: 3rem;\n  padding-inline-start: 1rem;\n  padding-inline-end: 2.5rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  line-height: 2;\n  border-radius: var(--rounded-btn, 0.5rem);\n  border-width: 1px;\n  border-color: transparent;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n  background-image: linear-gradient(45deg, transparent 50%, currentColor 50%),\n    linear-gradient(135deg, currentColor 50%, transparent 50%);\n  background-position: calc(100% - 20px) calc(1px + 50%),\n    calc(100% - 16.1px) calc(1px + 50%);\n  background-size: 4px 4px,\n    4px 4px;\n  background-repeat: no-repeat;\n}\n.select[multiple] {\n  height: auto;\n}\n.stack {\n  display: inline-grid;\n  place-items: center;\n  align-items: flex-end;\n}\n.stack > * {\n  grid-column-start: 1;\n  grid-row-start: 1;\n  transform: translateY(10%) scale(0.9);\n  z-index: 1;\n  width: 100%;\n  opacity: 0.6;\n}\n.stack > *:nth-child(2) {\n  transform: translateY(5%) scale(0.95);\n  z-index: 2;\n  opacity: 0.8;\n}\n.stack > *:nth-child(1) {\n  transform: translateY(0) scale(1);\n  z-index: 3;\n  opacity: 1;\n}\n.stats {\n  display: inline-grid;\n  border-radius: var(--rounded-box, 1rem);\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n:where(.stats) {\n  grid-auto-flow: column;\n  overflow-x: auto;\n}\n.stat {\n  display: inline-grid;\n  width: 100%;\n  grid-template-columns: repeat(1, 1fr);\n  -moz-column-gap: 1rem;\n       column-gap: 1rem;\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-border-opacity: 0.1;\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.stat-figure {\n  grid-column-start: 2;\n  grid-row: span 3 / span 3;\n  grid-row-start: 1;\n  place-self: center;\n  justify-self: end;\n}\n.stat-title {\n  grid-column-start: 1;\n  white-space: nowrap;\n  color: var(--fallback-bc,oklch(var(--bc)/0.6));\n}\n.stat-value {\n  grid-column-start: 1;\n  white-space: nowrap;\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n  font-weight: 800;\n}\n.stat-desc {\n  grid-column-start: 1;\n  white-space: nowrap;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  color: var(--fallback-bc,oklch(var(--bc)/0.6));\n}\n.steps {\n  display: inline-grid;\n  grid-auto-flow: column;\n  overflow: hidden;\n  overflow-x: auto;\n  counter-reset: step;\n  grid-auto-columns: 1fr;\n}\n.steps .step {\n  display: grid;\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n  grid-template-columns: auto;\n  grid-template-rows: repeat(2, minmax(0, 1fr));\n  grid-template-rows: 40px 1fr;\n  place-items: center;\n  text-align: center;\n  min-width: 4rem;\n}\n.tabs {\n  display: grid;\n  align-items: flex-end;\n}\n.tabs-lifted:has(.tab-content[class^=\"rounded-\"])\n    .tab:first-child:not(:is(.tab-active, [aria-selected=\"true\"])), .tabs-lifted:has(.tab-content[class*=\" rounded-\"])\n    .tab:first-child:not(:is(.tab-active, [aria-selected=\"true\"])) {\n  border-bottom-color: transparent;\n}\n.tab {\n  position: relative;\n  grid-row-start: 1;\n  display: inline-flex;\n  height: 2rem;\n  cursor: pointer;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  line-height: 2;\n  --tab-padding: 1rem;\n  --tw-text-opacity: 0.5;\n  --tab-color: var(--fallback-bc,oklch(var(--bc)/1));\n  --tab-bg: var(--fallback-b1,oklch(var(--b1)/1));\n  --tab-border-color: var(--fallback-b3,oklch(var(--b3)/1));\n  color: var(--tab-color);\n  padding-inline-start: var(--tab-padding, 1rem);\n  padding-inline-end: var(--tab-padding, 1rem);\n}\n.tab:is(input[type=\"radio\"]) {\n  width: auto;\n  border-bottom-right-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\n.tab:is(input[type=\"radio\"]):after {\n  --tw-content: attr(aria-label);\n  content: var(--tw-content);\n}\n.tab:not(input):empty {\n  cursor: default;\n  grid-column-start: span 9999;\n}\n:checked + .tab-content:nth-child(2),\n  :is(.tab-active, [aria-selected=\"true\"]) + .tab-content:nth-child(2) {\n  border-start-start-radius: 0px;\n}\ninput.tab:checked + .tab-content,\n:is(.tab-active, [aria-selected=\"true\"]) + .tab-content {\n  display: block;\n}\n.table {\n  position: relative;\n  width: 100%;\n  border-radius: var(--rounded-box, 1rem);\n  text-align: left;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.table :where(.table-pin-rows thead tr) {\n  position: sticky;\n  top: 0px;\n  z-index: 1;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n}\n.table :where(.table-pin-rows tfoot tr) {\n  position: sticky;\n  bottom: 0px;\n  z-index: 1;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n}\n.table :where(.table-pin-cols tr th) {\n  position: sticky;\n  left: 0px;\n  right: 0px;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n}\n.textarea {\n  min-height: 3rem;\n  flex-shrink: 1;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  line-height: 2;\n  border-radius: var(--rounded-btn, 0.5rem);\n  border-width: 1px;\n  border-color: transparent;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity)));\n}\n.\\!toast {\n  position: fixed !important;\n  display: flex !important;\n  min-width: -moz-fit-content !important;\n  min-width: fit-content !important;\n  flex-direction: column !important;\n  white-space: nowrap !important;\n  gap: 0.5rem !important;\n  padding: 1rem !important;\n}\n.toast {\n  position: fixed;\n  display: flex;\n  min-width: -moz-fit-content;\n  min-width: fit-content;\n  flex-direction: column;\n  white-space: nowrap;\n  gap: 0.5rem;\n  padding: 1rem;\n}\n.toggle {\n  flex-shrink: 0;\n  --tglbg: var(--fallback-b1,oklch(var(--b1)/1));\n  --handleoffset: 1.5rem;\n  --handleoffsetcalculator: calc(var(--handleoffset) * -1);\n  --togglehandleborder: 0 0;\n  height: 1.5rem;\n  width: 3rem;\n  cursor: pointer;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  border-radius: var(--rounded-badge, 1.9rem);\n  border-width: 1px;\n  border-color: currentColor;\n  background-color: currentColor;\n  color: var(--fallback-bc,oklch(var(--bc)/0.5));\n  transition: background,\n    box-shadow var(--animation-input, 0.2s) ease-out;\n  box-shadow: var(--handleoffsetcalculator) 0 0 2px var(--tglbg) inset,\n    0 0 0 2px var(--tglbg) inset,\n    var(--togglehandleborder);\n}\n.alert-info {\n  border-color: var(--fallback-in,oklch(var(--in)/0.2));\n  --tw-text-opacity: 1;\n  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n  --alert-bg: var(--fallback-in,oklch(var(--in)/1));\n  --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1));\n}\n.alert-success {\n  border-color: var(--fallback-su,oklch(var(--su)/0.2));\n  --tw-text-opacity: 1;\n  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));\n  --alert-bg: var(--fallback-su,oklch(var(--su)/1));\n  --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1));\n}\n.alert-error {\n  border-color: var(--fallback-er,oklch(var(--er)/0.2));\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n  --alert-bg: var(--fallback-er,oklch(var(--er)/1));\n  --alert-bg-mix: var(--fallback-b1,oklch(var(--b1)/1));\n}\n.avatar-group :where(.avatar) {\n  overflow: hidden;\n  border-radius: 9999px;\n  border-width: 4px;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-border-opacity)));\n}\n.badge-primary {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n.badge-secondary {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-s,oklch(var(--s)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n}\n.badge-warning {\n  border-color: transparent;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));\n}\n.badge-ghost {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n.badge-outline {\n  border-color: currentColor;\n  --tw-border-opacity: 0.5;\n  background-color: transparent;\n  color: currentColor;\n}\n.badge-outline.badge-neutral {\n  --tw-text-opacity: 1;\n  color: var(--fallback-n,oklch(var(--n)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-primary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-secondary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-accent {\n  --tw-text-opacity: 1;\n  color: var(--fallback-a,oklch(var(--a)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-info {\n  --tw-text-opacity: 1;\n  color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-success {\n  --tw-text-opacity: 1;\n  color: var(--fallback-su,oklch(var(--su)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-warning {\n  --tw-text-opacity: 1;\n  color: var(--fallback-wa,oklch(var(--wa)/var(--tw-text-opacity)));\n}\n.badge-outline.badge-error {\n  --tw-text-opacity: 1;\n  color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity)));\n}\n.btm-nav > *.disabled,\n    .btm-nav > *[disabled] {\n  pointer-events: none;\n  --tw-border-opacity: 0;\n  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n  --tw-bg-opacity: 0.1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  --tw-text-opacity: 0.2;\n}\n.btm-nav > * .label {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n@media (prefers-reduced-motion: no-preference) {\n\n  .btn {\n    animation: button-pop var(--animation-btn, 0.25s) ease-out;\n  }\n}\n.btn:active:hover,\n  .btn:active:focus {\n  animation: button-pop 0s ease-out;\n  transform: scale(var(--btn-focus-scale, 0.97));\n}\n@supports not (color: oklch(0% 0 0)) {\n\n  .btn {\n    background-color: var(--btn-color, var(--fallback-b2));\n    border-color: var(--btn-color, var(--fallback-b2));\n  }\n\n  .btn-primary {\n    --btn-color: var(--fallback-p);\n  }\n\n  .btn-secondary {\n    --btn-color: var(--fallback-s);\n  }\n\n  .btn-accent {\n    --btn-color: var(--fallback-a);\n  }\n\n  .btn-success {\n    --btn-color: var(--fallback-su);\n  }\n\n  .btn-warning {\n    --btn-color: var(--fallback-wa);\n  }\n\n  .btn-error {\n    --btn-color: var(--fallback-er);\n  }\n\n  .prose :where(code):not(:where([class~=\"not-prose\"] *, pre *)) {\n    background-color: var(--fallback-b3,oklch(var(--b3)/1));\n  }\n}\n@supports (color: color-mix(in oklab, black, black)) {\n\n  .btn-active {\n    background-color: color-mix(\n          in oklab,\n          oklch(var(--btn-color, var(--b3)) / var(--tw-bg-opacity, 1)) 90%,\n          black\n        );\n    border-color: color-mix(\n          in oklab,\n          oklch(var(--btn-color, var(--b3)) / var(--tw-border-opacity, 1)) 90%,\n          black\n        );\n  }\n\n  .btn-outline.btn-primary.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n  }\n\n  .btn-outline.btn-secondary.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n  }\n\n  .btn-outline.btn-accent.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-a,oklch(var(--a)/1)) 90%, black);\n  }\n\n  .btn-outline.btn-success.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-su,oklch(var(--su)/1)) 90%, black);\n  }\n\n  .btn-outline.btn-info.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n  }\n\n  .btn-outline.btn-warning.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-wa,oklch(var(--wa)/1)) 90%, black);\n  }\n\n  .btn-outline.btn-error.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n  }\n}\n.btn:focus-visible {\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n}\n.btn-primary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-p,oklch(var(--p)/1));\n}\n@supports (color: oklch(0% 0 0)) {\n\n  .btn-primary {\n    --btn-color: var(--p);\n  }\n\n  .btn-secondary {\n    --btn-color: var(--s);\n  }\n\n  .btn-accent {\n    --btn-color: var(--a);\n  }\n\n  .btn-success {\n    --btn-color: var(--su);\n  }\n\n  .btn-warning {\n    --btn-color: var(--wa);\n  }\n\n  .btn-error {\n    --btn-color: var(--er);\n  }\n}\n.btn-secondary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-s,oklch(var(--s)/1));\n}\n.btn-accent {\n  --tw-text-opacity: 1;\n  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-a,oklch(var(--a)/1));\n}\n.btn-success {\n  --tw-text-opacity: 1;\n  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-su,oklch(var(--su)/1));\n}\n.btn-warning {\n  --tw-text-opacity: 1;\n  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-wa,oklch(var(--wa)/1));\n}\n.btn-error {\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n.btn.glass {\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  outline-color: currentColor;\n}\n.btn.glass.btn-active {\n  --glass-opacity: 25%;\n  --glass-border-opacity: 15%;\n}\n.btn-ghost {\n  border-width: 1px;\n  border-color: transparent;\n  background-color: transparent;\n  color: currentColor;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  outline-color: currentColor;\n}\n.btn-ghost.btn-active {\n  border-color: transparent;\n  background-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.btn-link.btn-active {\n  border-color: transparent;\n  background-color: transparent;\n  text-decoration-line: underline;\n}\n.btn-outline {\n  border-color: currentColor;\n  background-color: transparent;\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.btn-outline.btn-active {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-b1,oklch(var(--b1)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-primary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-primary.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-secondary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-secondary.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-accent {\n  --tw-text-opacity: 1;\n  color: var(--fallback-a,oklch(var(--a)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-accent.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-success {\n  --tw-text-opacity: 1;\n  color: var(--fallback-su,oklch(var(--su)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-success.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-info {\n  --tw-text-opacity: 1;\n  color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-info.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-warning {\n  --tw-text-opacity: 1;\n  color: var(--fallback-wa,oklch(var(--wa)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-warning.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-error {\n  --tw-text-opacity: 1;\n  color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity)));\n}\n.btn-outline.btn-error.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n}\n.btn.btn-disabled,\n  .btn[disabled],\n  .btn:disabled {\n  --tw-border-opacity: 0;\n  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n  --tw-bg-opacity: 0.2;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  --tw-text-opacity: 0.2;\n}\n.btn:is(input[type=\"checkbox\"]:checked),\n.btn:is(input[type=\"radio\"]:checked) {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n.btn:is(input[type=\"checkbox\"]:checked):focus-visible, .btn:is(input[type=\"radio\"]:checked):focus-visible {\n  outline-color: var(--fallback-p,oklch(var(--p)/1));\n}\n@keyframes button-pop {\n\n  0% {\n    transform: scale(var(--btn-focus-scale, 0.98));\n  }\n\n  40% {\n    transform: scale(1.02);\n  }\n\n  100% {\n    transform: scale(1);\n  }\n}\n.card :where(figure:first-child) {\n  overflow: hidden;\n  border-start-start-radius: inherit;\n  border-start-end-radius: inherit;\n  border-end-start-radius: unset;\n  border-end-end-radius: unset;\n}\n.card :where(figure:last-child) {\n  overflow: hidden;\n  border-start-start-radius: unset;\n  border-start-end-radius: unset;\n  border-end-start-radius: inherit;\n  border-end-end-radius: inherit;\n}\n.card:focus-visible {\n  outline: 2px solid currentColor;\n  outline-offset: 2px;\n}\n.card.bordered {\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n}\n.card.compact .card-body {\n  padding: 1rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n  font-weight: 600;\n}\n.card.image-full :where(figure) {\n  overflow: hidden;\n  border-radius: inherit;\n}\n.checkbox:focus {\n  box-shadow: none;\n}\n.checkbox:focus-visible {\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: var(--fallback-bc,oklch(var(--bc)/1));\n}\n.checkbox:disabled {\n  border-width: 0px;\n  cursor: not-allowed;\n  border-color: transparent;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n  opacity: 0.2;\n}\n.checkbox:checked,\n  .checkbox[aria-checked=\"true\"] {\n  background-repeat: no-repeat;\n  animation: checkmark var(--animation-input, 0.2s) ease-out;\n  background-color: var(--chkbg);\n  background-image: linear-gradient(-45deg, transparent 65%, var(--chkbg) 65.99%),\n      linear-gradient(45deg, transparent 75%, var(--chkbg) 75.99%),\n      linear-gradient(-45deg, var(--chkbg) 40%, transparent 40.99%),\n      linear-gradient(\n        45deg,\n        var(--chkbg) 30%,\n        var(--chkfg) 30.99%,\n        var(--chkfg) 40%,\n        transparent 40.99%\n      ),\n      linear-gradient(-45deg, var(--chkfg) 50%, var(--chkbg) 50.99%);\n}\n.checkbox:indeterminate {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n  background-repeat: no-repeat;\n  animation: checkmark var(--animation-input, 0.2s) ease-out;\n  background-image: linear-gradient(90deg, transparent 80%, var(--chkbg) 80%),\n      linear-gradient(-90deg, transparent 80%, var(--chkbg) 80%),\n      linear-gradient(0deg, var(--chkbg) 43%, var(--chkfg) 43%, var(--chkfg) 57%, var(--chkbg) 57%);\n}\n.checkbox-primary {\n  --chkbg: var(--fallback-p,oklch(var(--p)/1));\n  --chkfg: var(--fallback-pc,oklch(var(--pc)/1));\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));\n}\n.checkbox-primary:focus-visible {\n  outline-color: var(--fallback-p,oklch(var(--p)/1));\n}\n.checkbox-primary:checked,\n    .checkbox-primary[aria-checked=\"true\"] {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n.checkbox-error {\n  --chkbg: var(--fallback-er,oklch(var(--er)/1));\n  --chkfg: var(--fallback-erc,oklch(var(--erc)/1));\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n}\n.checkbox-error:focus-visible {\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n.checkbox-error:checked,\n    .checkbox-error[aria-checked=\"true\"] {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n}\n@keyframes checkmark {\n\n  0% {\n    background-position-y: 5px;\n  }\n\n  50% {\n    background-position-y: -2px;\n  }\n\n  100% {\n    background-position-y: 0;\n  }\n}\n.divider:not(:empty) {\n  gap: 1rem;\n}\n.dropdown.dropdown-open .dropdown-content,\n.dropdown:focus .dropdown-content,\n.dropdown:focus-within .dropdown-content {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.label-text {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n.label-text-alt {\n  font-size: 0.75rem;\n  line-height: 1rem;\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n.input input {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  background-color: transparent;\n}\n.input input:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.input[list]::-webkit-calendar-picker-indicator {\n  line-height: 1em;\n}\n.input-bordered {\n  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.input:focus,\n  .input:focus-within {\n  box-shadow: none;\n  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.input-error {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n}\n.input-error:focus,\n    .input-error:focus-within {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n.input:has(> input[disabled]),\n  .input-disabled,\n  .input:disabled,\n  .input[disabled] {\n  cursor: not-allowed;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  color: var(--fallback-bc,oklch(var(--bc)/0.4));\n}\n.input:has(> input[disabled])::-moz-placeholder, .input-disabled::-moz-placeholder, .input:disabled::-moz-placeholder, .input[disabled]::-moz-placeholder {\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));\n  --tw-placeholder-opacity: 0.2;\n}\n.input:has(> input[disabled])::placeholder,\n  .input-disabled::placeholder,\n  .input:disabled::placeholder,\n  .input[disabled]::placeholder {\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));\n  --tw-placeholder-opacity: 0.2;\n}\n.input:has(> input[disabled]) > input[disabled] {\n  cursor: not-allowed;\n}\n.input::-webkit-date-and-time-value {\n  text-align: inherit;\n}\n.join > :where(*:not(:first-child)) {\n  margin-top: 0px;\n  margin-bottom: 0px;\n  margin-inline-start: -1px;\n}\n.join > :where(*:not(:first-child)):is(.btn) {\n  margin-inline-start: calc(var(--border-btn) * -1);\n}\n.link-primary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));\n}\n@supports (color:color-mix(in oklab,black,black)) {\n\n  @media (hover:hover) {\n\n    .link-primary:hover {\n      color: color-mix(in oklab,var(--fallback-p,oklch(var(--p)/1)) 80%,black);\n    }\n  }\n}\n.link:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.link:focus-visible {\n  outline: 2px solid currentColor;\n  outline-offset: 2px;\n}\n.\\!loading {\n  pointer-events: none !important;\n  display: inline-block !important;\n  aspect-ratio: 1 / 1 !important;\n  width: 1.5rem !important;\n  background-color: currentColor !important;\n  -webkit-mask-size: 100% !important;\n          mask-size: 100% !important;\n  -webkit-mask-repeat: no-repeat !important;\n          mask-repeat: no-repeat !important;\n  -webkit-mask-position: center !important;\n          mask-position: center !important;\n  -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\") !important;\n          mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\") !important;\n}\n.loading {\n  pointer-events: none;\n  display: inline-block;\n  aspect-ratio: 1 / 1;\n  width: 1.5rem;\n  background-color: currentColor;\n  -webkit-mask-size: 100%;\n          mask-size: 100%;\n  -webkit-mask-repeat: no-repeat;\n          mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n          mask-position: center;\n  -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n          mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n}\n.loading-spinner {\n  -webkit-mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n          mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n}\n.loading-xs {\n  width: 1rem;\n}\n.loading-sm {\n  width: 1.25rem;\n}\n.loading-md {\n  width: 1.5rem;\n}\n.loading-lg {\n  width: 2.5rem;\n}\n:where(.menu li:empty) {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n  opacity: 0.1;\n  margin: 0.5rem 1rem;\n  height: 1px;\n}\n.menu :where(li ul):before {\n  position: absolute;\n  bottom: 0.75rem;\n  inset-inline-start: 0px;\n  top: 0.75rem;\n  width: 1px;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n  opacity: 0.1;\n  content: \"\";\n}\n.menu :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)),\n.menu :where(li:not(.menu-title) > details > summary:not(.menu-title)) {\n  border-radius: var(--rounded-btn, 0.5rem);\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  text-align: start;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  transition-duration: 200ms;\n  text-wrap: balance;\n}\n:where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(summary, .active, .btn).focus, :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):not(summary, .active, .btn):focus, :where(.menu li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title)):is(summary):not(.active, .btn):focus-visible, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(summary, .active, .btn).focus, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):not(summary, .active, .btn):focus, :where(.menu li:not(.menu-title, .disabled) > details > summary:not(.menu-title)):is(summary):not(.active, .btn):focus-visible {\n  cursor: pointer;\n  background-color: var(--fallback-bc,oklch(var(--bc)/0.1));\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.menu li > *:not(ul, .menu-title, details, .btn):active,\n.menu li > *:not(ul, .menu-title, details, .btn).active,\n.menu li > details > summary:active {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));\n}\n.menu :where(li > details > summary)::-webkit-details-marker {\n  display: none;\n}\n.menu :where(li > details > summary):after,\n.menu :where(li > .menu-dropdown-toggle):after {\n  justify-self: end;\n  display: block;\n  margin-top: -0.5rem;\n  height: 0.5rem;\n  width: 0.5rem;\n  transform: rotate(45deg);\n  transition-property: transform, margin-top;\n  transition-duration: 0.3s;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  content: \"\";\n  transform-origin: 75% 75%;\n  box-shadow: 2px 2px;\n  pointer-events: none;\n}\n.menu :where(li > details[open] > summary):after,\n.menu :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {\n  transform: rotate(225deg);\n  margin-top: 0;\n}\n.mockup-browser .mockup-browser-toolbar .input {\n  position: relative;\n  margin-left: auto;\n  margin-right: auto;\n  display: block;\n  height: 1.75rem;\n  width: 24rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  padding-left: 2rem;\n  direction: ltr;\n}\n.mockup-browser .mockup-browser-toolbar .input:before {\n  content: \"\";\n  position: absolute;\n  left: 0.5rem;\n  top: 50%;\n  aspect-ratio: 1 / 1;\n  height: 0.75rem;\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-radius: 9999px;\n  border-width: 2px;\n  border-color: currentColor;\n  opacity: 0.6;\n}\n.mockup-browser .mockup-browser-toolbar .input:after {\n  content: \"\";\n  position: absolute;\n  left: 1.25rem;\n  top: 50%;\n  height: 0.5rem;\n  --tw-translate-y: 25%;\n  --tw-rotate: -45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-radius: 9999px;\n  border-width: 1px;\n  border-color: currentColor;\n  opacity: 0.6;\n}\n.modal:not(dialog:not(.modal-open)),\n  .modal::backdrop {\n  background-color: #0006;\n  animation: modal-pop 0.2s ease-out;\n}\n.modal-open .modal-box,\n.modal-toggle:checked + .modal .modal-box,\n.modal:target .modal-box,\n.modal[open] .modal-box {\n  --tw-translate-y: 0px;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.modal-action > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.modal-action:where([dir=\"rtl\"], [dir=\"rtl\"] *) > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 1;\n}\n@keyframes modal-pop {\n\n  0% {\n    opacity: 0;\n  }\n}\n@keyframes progress-loading {\n\n  50% {\n    background-position-x: -115%;\n  }\n}\n.radio:focus {\n  box-shadow: none;\n}\n.radio:focus-visible {\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: var(--fallback-bc,oklch(var(--bc)/1));\n}\n.radio:checked,\n  .radio[aria-checked=\"true\"] {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-bg-opacity)));\n  background-image: none;\n  animation: radiomark var(--animation-input, 0.2s) ease-out;\n  box-shadow: 0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset,\n      0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset;\n}\n.radio-error {\n  --chkbg: var(--er);\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n}\n.radio-error:focus-visible {\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n.radio-error:checked,\n    .radio-error[aria-checked=\"true\"] {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n}\n.radio:disabled {\n  cursor: not-allowed;\n  opacity: 0.2;\n}\n@keyframes radiomark {\n\n  0% {\n    box-shadow: 0 0 0 12px var(--fallback-b1,oklch(var(--b1)/1)) inset,\n      0 0 0 12px var(--fallback-b1,oklch(var(--b1)/1)) inset;\n  }\n\n  50% {\n    box-shadow: 0 0 0 3px var(--fallback-b1,oklch(var(--b1)/1)) inset,\n      0 0 0 3px var(--fallback-b1,oklch(var(--b1)/1)) inset;\n  }\n\n  100% {\n    box-shadow: 0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset,\n      0 0 0 4px var(--fallback-b1,oklch(var(--b1)/1)) inset;\n  }\n}\n@keyframes rating-pop {\n\n  0% {\n    transform: translateY(-0.125em);\n  }\n\n  40% {\n    transform: translateY(-0.125em);\n  }\n\n  100% {\n    transform: translateY(0);\n  }\n}\n.select-bordered {\n  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.select:focus {\n  box-shadow: none;\n  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.select-error {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n}\n.select-error:focus {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n.select-disabled,\n  .select:disabled,\n  .select[disabled] {\n  cursor: not-allowed;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  color: var(--fallback-bc,oklch(var(--bc)/0.4));\n}\n.select-disabled::-moz-placeholder, .select:disabled::-moz-placeholder, .select[disabled]::-moz-placeholder {\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));\n  --tw-placeholder-opacity: 0.2;\n}\n.select-disabled::placeholder,\n  .select:disabled::placeholder,\n  .select[disabled]::placeholder {\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));\n  --tw-placeholder-opacity: 0.2;\n}\n.select-multiple,\n  .select[multiple],\n  .select[size].select:not([size=\"1\"]) {\n  background-image: none;\n  padding-right: 1rem;\n}\n[dir=\"rtl\"] .select {\n  background-position: calc(0% + 12px) calc(1px + 50%),\n    calc(0% + 16px) calc(1px + 50%);\n}\n@keyframes skeleton {\n\n  from {\n    background-position: 150%;\n  }\n\n  to {\n    background-position: -50%;\n  }\n}\n:where(.stats) > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-x-reverse: 0;\n  border-right-width: calc(1px * var(--tw-divide-x-reverse));\n  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(0px * var(--tw-divide-y-reverse));\n}\n[dir=\"rtl\"] .stats > *:not([hidden]) ~ *:not([hidden]) {\n  --tw-divide-x-reverse: 1;\n}\n.steps .step:before {\n  top: 0px;\n  grid-column-start: 1;\n  grid-row-start: 1;\n  height: 0.5rem;\n  width: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  content: \"\";\n  margin-inline-start: -100%;\n}\n.steps .step:after {\n  content: counter(step);\n  counter-increment: step;\n  z-index: 1;\n  position: relative;\n  grid-column-start: 1;\n  grid-row-start: 1;\n  display: grid;\n  height: 2rem;\n  width: 2rem;\n  place-items: center;\n  place-self: center;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n.steps .step:first-child:before {\n  content: none;\n}\n.steps .step[data-content]:after {\n  content: attr(data-content);\n}\n.steps .step-neutral + .step-neutral:before,\n  .steps .step-neutral:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-n,oklch(var(--n)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-nc,oklch(var(--nc)/var(--tw-text-opacity)));\n}\n.steps .step-primary + .step-primary:before,\n  .steps .step-primary:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n.steps .step-secondary + .step-secondary:before,\n  .steps .step-secondary:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-s,oklch(var(--s)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n}\n.steps .step-accent + .step-accent:before,\n  .steps .step-accent:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-a,oklch(var(--a)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-ac,oklch(var(--ac)/var(--tw-text-opacity)));\n}\n.steps .step-info + .step-info:before {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));\n}\n.steps .step-info:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-in,oklch(var(--in)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n}\n.steps .step-success + .step-success:before {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));\n}\n.steps .step-success:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity)));\n}\n.steps .step-warning + .step-warning:before {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));\n}\n.steps .step-warning:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity)));\n}\n.steps .step-error + .step-error:before {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));\n}\n.steps .step-error:after {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n}\n.tabs-lifted > .tab:focus-visible {\n  border-end-end-radius: 0;\n  border-end-start-radius: 0;\n}\n.tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]), .tab:is(input:checked) {\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-border-opacity: 1;\n  --tw-text-opacity: 1;\n}\n.tab:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.tab:focus-visible {\n  outline: 2px solid currentColor;\n  outline-offset: -5px;\n}\n.tab-disabled,\n  .tab[disabled] {\n  cursor: not-allowed;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  --tw-text-opacity: 0.2;\n}\n.tabs-bordered > .tab {\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  --tw-border-opacity: 0.2;\n  border-style: solid;\n  border-bottom-width: calc(var(--tab-border, 1px) + 1px);\n}\n.tabs-lifted > .tab {\n  border: var(--tab-border, 1px) solid transparent;\n  border-width: 0 0 var(--tab-border, 1px) 0;\n  border-start-start-radius: var(--tab-radius, 0.5rem);\n  border-start-end-radius: var(--tab-radius, 0.5rem);\n  border-bottom-color: var(--tab-border-color);\n  padding-inline-start: var(--tab-padding, 1rem);\n  padding-inline-end: var(--tab-padding, 1rem);\n  padding-top: var(--tab-border, 1px);\n}\n.tabs-lifted > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]), .tabs-lifted > .tab:is(input:checked) {\n  background-color: var(--tab-bg);\n  border-width: var(--tab-border, 1px) var(--tab-border, 1px) 0 var(--tab-border, 1px);\n  border-inline-start-color: var(--tab-border-color);\n  border-inline-end-color: var(--tab-border-color);\n  border-top-color: var(--tab-border-color);\n  padding-inline-start: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));\n  padding-inline-end: calc(var(--tab-padding, 1rem) - var(--tab-border, 1px));\n  padding-bottom: var(--tab-border, 1px);\n  padding-top: 0;\n}\n.tabs-lifted > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):before, .tabs-lifted > .tab:is(input:checked):before {\n  z-index: 1;\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: calc(100% + var(--tab-radius, 0.5rem) * 2);\n  height: var(--tab-radius, 0.5rem);\n  bottom: 0;\n  background-size: var(--tab-radius, 0.5rem);\n  background-position: top left,\n        top right;\n  background-repeat: no-repeat;\n  --tab-grad: calc(69% - var(--tab-border, 1px));\n  --radius-start: radial-gradient(\n        circle at top left,\n        transparent var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)\n      );\n  --radius-end: radial-gradient(\n        circle at top right,\n        transparent var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--tab-border, 1px)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--tab-border, 1px) + 0.25px)\n      );\n  background-image: var(--radius-start), var(--radius-end);\n}\n.tabs-lifted > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):first-child:before, .tabs-lifted > .tab:is(input:checked):first-child:before {\n  background-image: var(--radius-end);\n  background-position: top right;\n}\n[dir=\"rtl\"] .tabs-lifted > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):first-child:before, [dir=\"rtl\"] .tabs-lifted > .tab:is(input:checked):first-child:before {\n  background-image: var(--radius-start);\n  background-position: top left;\n}\n.tabs-lifted > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):last-child:before, .tabs-lifted > .tab:is(input:checked):last-child:before {\n  background-image: var(--radius-start);\n  background-position: top left;\n}\n[dir=\"rtl\"] .tabs-lifted > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):last-child:before, [dir=\"rtl\"] .tabs-lifted > .tab:is(input:checked):last-child:before {\n  background-image: var(--radius-end);\n  background-position: top right;\n}\n.tabs-lifted\n  > :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled])\n  + .tabs-lifted\n  :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]):before, .tabs-lifted > .tab:is(input:checked) + .tabs-lifted .tab:is(input:checked):before {\n  background-image: var(--radius-end);\n  background-position: top right;\n}\n.tabs-boxed {\n  border-radius: var(--rounded-btn, 0.5rem);\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  padding: 0.25rem;\n}\n.tabs-boxed .tab {\n  border-radius: var(--rounded-btn, 0.5rem);\n}\n.tabs-boxed :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled):not([disabled]), .tabs-boxed :is(input:checked) {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n.table:where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  text-align: right;\n}\n.table :where(th, td) {\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  vertical-align: middle;\n}\n.table tr.active,\n  .table tr.active:nth-child(even),\n  .table-zebra tbody tr:nth-child(even) {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n}\n.table :where(thead tr, tbody tr:not(:last-child), tbody tr:first-child:last-child) {\n  border-bottom-width: 1px;\n  --tw-border-opacity: 1;\n  border-bottom-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n}\n.table :where(thead, tfoot) {\n  white-space: nowrap;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  font-weight: 700;\n  color: var(--fallback-bc,oklch(var(--bc)/0.6));\n}\n.table :where(tfoot) {\n  border-top-width: 1px;\n  --tw-border-opacity: 1;\n  border-top-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n}\n.textarea-bordered {\n  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.textarea:focus {\n  box-shadow: none;\n  border-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.textarea-error {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n}\n.textarea-error:focus {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-er,oklch(var(--er)/var(--tw-border-opacity)));\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n.textarea-disabled,\n  .textarea:disabled,\n  .textarea[disabled] {\n  cursor: not-allowed;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));\n  color: var(--fallback-bc,oklch(var(--bc)/0.4));\n}\n.textarea-disabled::-moz-placeholder, .textarea:disabled::-moz-placeholder, .textarea[disabled]::-moz-placeholder {\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));\n  --tw-placeholder-opacity: 0.2;\n}\n.textarea-disabled::placeholder,\n  .textarea:disabled::placeholder,\n  .textarea[disabled]::placeholder {\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-placeholder-opacity)));\n  --tw-placeholder-opacity: 0.2;\n}\n.\\!toast > * {\n  animation: toast-pop 0.25s ease-out !important;\n}\n.toast > * {\n  animation: toast-pop 0.25s ease-out;\n}\n@keyframes toast-pop {\n\n  0% {\n    transform: scale(0.9);\n    opacity: 0;\n  }\n\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n[dir=\"rtl\"] .toggle {\n  --handleoffsetcalculator: calc(var(--handleoffset) * 1);\n}\n.toggle:focus-visible {\n  outline-style: solid;\n  outline-width: 2px;\n  outline-offset: 2px;\n  outline-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.toggle:hover {\n  background-color: currentColor;\n}\n.toggle:checked,\n  .toggle[aria-checked=\"true\"] {\n  background-image: none;\n  --handleoffsetcalculator: var(--handleoffset);\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n}\n[dir=\"rtl\"] .toggle:checked, [dir=\"rtl\"] .toggle[aria-checked=\"true\"] {\n  --handleoffsetcalculator: calc(var(--handleoffset) * -1);\n}\n.toggle:indeterminate {\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity)));\n  box-shadow: calc(var(--handleoffset) / 2) 0 0 2px var(--tglbg) inset,\n      calc(var(--handleoffset) / -2) 0 0 2px var(--tglbg) inset,\n      0 0 0 2px var(--tglbg) inset;\n}\n[dir=\"rtl\"] .toggle:indeterminate {\n  box-shadow: calc(var(--handleoffset) / 2) 0 0 2px var(--tglbg) inset,\n        calc(var(--handleoffset) / -2) 0 0 2px var(--tglbg) inset,\n        0 0 0 2px var(--tglbg) inset;\n}\n.toggle:disabled {\n  cursor: not-allowed;\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-bc,oklch(var(--bc)/var(--tw-border-opacity)));\n  background-color: transparent;\n  opacity: 0.3;\n  --togglehandleborder: 0 0 0 3px var(--fallback-bc,oklch(var(--bc)/1)) inset,\n      var(--handleoffsetcalculator) 0 0 3px var(--fallback-bc,oklch(var(--bc)/1)) inset;\n}\n:root .prose {\n  --tw-prose-body: var(--fallback-bc,oklch(var(--bc)/0.8));\n  --tw-prose-headings: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-lead: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-links: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-bold: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-counters: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-bullets: var(--fallback-bc,oklch(var(--bc)/0.5));\n  --tw-prose-hr: var(--fallback-bc,oklch(var(--bc)/0.2));\n  --tw-prose-quotes: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-quote-borders: var(--fallback-bc,oklch(var(--bc)/0.2));\n  --tw-prose-captions: var(--fallback-bc,oklch(var(--bc)/0.5));\n  --tw-prose-code: var(--fallback-bc,oklch(var(--bc)/1));\n  --tw-prose-pre-code: var(--fallback-nc,oklch(var(--nc)/1));\n  --tw-prose-pre-bg: var(--fallback-n,oklch(var(--n)/1));\n  --tw-prose-th-borders: var(--fallback-bc,oklch(var(--bc)/0.5));\n  --tw-prose-td-borders: var(--fallback-bc,oklch(var(--bc)/0.2));\n  --tw-prose-kbd: var(--fallback-bc,oklch(var(--bc)/0.8));\n}\n.prose :where(code):not(:where([class~=\"not-prose\"] *, pre *)) {\n  padding: 1px 8px;\n  border-radius: var(--rounded-badge);\n  font-weight: initial;\n  background-color: var(--fallback-bc,oklch(var(--bc)/0.1));\n}\n.prose :where(code):not(:where([class~=\"not-prose\"], [class~=\"not-prose\"] *))::before, .prose :where(code):not(:where([class~=\"not-prose\"], [class~=\"not-prose\"] *))::after {\n  display: none;\n}\n.prose pre code {\n  border-radius: 0;\n  padding: 0;\n}\n.prose :where(tbody tr, thead):not(:where([class~=\"not-prose\"] *)) {\n  border-bottom-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.glass,\n  .glass.btn-active {\n  border: none;\n  -webkit-backdrop-filter: blur(var(--glass-blur, 40px));\n          backdrop-filter: blur(var(--glass-blur, 40px));\n  background-color: transparent;\n  background-image: linear-gradient(\n        135deg,\n        rgb(255 255 255 / var(--glass-opacity, 30%)) 0%,\n        rgb(0 0 0 / 0%) 100%\n      ),\n      linear-gradient(\n        var(--glass-reflex-degree, 100deg),\n        rgb(255 255 255 / var(--glass-reflex-opacity, 10%)) 25%,\n        rgb(0 0 0 / 0%) 25%\n      );\n  box-shadow: 0 0 0 1px rgb(255 255 255 / var(--glass-border-opacity, 10%)) inset,\n      0 0 0 2px rgb(0 0 0 / 5%);\n  text-shadow: 0 1px rgb(0 0 0 / var(--glass-text-shadow-opacity, 5%));\n}\n@media (hover: hover) {\n\n  .glass.btn-active {\n    border: none;\n    -webkit-backdrop-filter: blur(var(--glass-blur, 40px));\n            backdrop-filter: blur(var(--glass-blur, 40px));\n    background-color: transparent;\n    background-image: linear-gradient(\n          135deg,\n          rgb(255 255 255 / var(--glass-opacity, 30%)) 0%,\n          rgb(0 0 0 / 0%) 100%\n        ),\n        linear-gradient(\n          var(--glass-reflex-degree, 100deg),\n          rgb(255 255 255 / var(--glass-reflex-opacity, 10%)) 25%,\n          rgb(0 0 0 / 0%) 25%\n        );\n    box-shadow: 0 0 0 1px rgb(255 255 255 / var(--glass-border-opacity, 10%)) inset,\n        0 0 0 2px rgb(0 0 0 / 5%);\n    text-shadow: 0 1px rgb(0 0 0 / var(--glass-text-shadow-opacity, 5%));\n  }\n}\n.badge-xs {\n  height: 0.75rem;\n  font-size: 0.75rem;\n  line-height: .75rem;\n  padding-left: 0.313rem;\n  padding-right: 0.313rem;\n}\n.badge-sm {\n  height: 1rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  padding-left: 0.438rem;\n  padding-right: 0.438rem;\n}\n.badge-lg {\n  height: 1.5rem;\n  font-size: 1rem;\n  line-height: 1.5rem;\n  padding-left: 0.688rem;\n  padding-right: 0.688rem;\n}\n.btn-xs {\n  height: 1.5rem;\n  min-height: 1.5rem;\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n  font-size: 0.75rem;\n}\n.btn-sm {\n  height: 2rem;\n  min-height: 2rem;\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  font-size: 0.875rem;\n}\n.btn-square:where(.btn-xs) {\n  height: 1.5rem;\n  width: 1.5rem;\n  padding: 0px;\n}\n.btn-square:where(.btn-sm) {\n  height: 2rem;\n  width: 2rem;\n  padding: 0px;\n}\n.btn-square:where(.btn-md) {\n  height: 3rem;\n  width: 3rem;\n  padding: 0px;\n}\n.btn-square:where(.btn-lg) {\n  height: 4rem;\n  width: 4rem;\n  padding: 0px;\n}\n.btn-circle:where(.btn-xs) {\n  height: 1.5rem;\n  width: 1.5rem;\n  border-radius: 9999px;\n  padding: 0px;\n}\n.btn-circle:where(.btn-sm) {\n  height: 2rem;\n  width: 2rem;\n  border-radius: 9999px;\n  padding: 0px;\n}\n.btn-circle:where(.btn-md) {\n  height: 3rem;\n  width: 3rem;\n  border-radius: 9999px;\n  padding: 0px;\n}\n.btn-circle:where(.btn-lg) {\n  height: 4rem;\n  width: 4rem;\n  border-radius: 9999px;\n  padding: 0px;\n}\n[type=\"checkbox\"].checkbox-sm {\n  height: 1.25rem;\n  width: 1.25rem;\n}\n.indicator :where(.indicator-item) {\n  bottom: auto;\n  inset-inline-end: 0px;\n  inset-inline-start: auto;\n  top: 0px;\n  --tw-translate-y: -50%;\n  --tw-translate-x: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item):where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-start) {\n  inset-inline-end: auto;\n  inset-inline-start: 0px;\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-start):where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  --tw-translate-x: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-center) {\n  inset-inline-end: 50%;\n  inset-inline-start: 50%;\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-center):where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  --tw-translate-x: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-end) {\n  inset-inline-end: 0px;\n  inset-inline-start: auto;\n  --tw-translate-x: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-end):where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-bottom) {\n  bottom: 0px;\n  top: auto;\n  --tw-translate-y: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-middle) {\n  bottom: 50%;\n  top: 50%;\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.indicator :where(.indicator-item.indicator-top) {\n  bottom: auto;\n  top: 0px;\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.input-xs {\n  height: 1.5rem;\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  line-height: 1.625;\n}\n.input-md {\n  height: 3rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  line-height: 2;\n}\n.input-lg {\n  height: 4rem;\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n  line-height: 2;\n}\n.input-sm {\n  height: 2rem;\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  font-size: 0.875rem;\n  line-height: 2rem;\n}\n.join.join-vertical {\n  flex-direction: column;\n}\n.join.join-vertical .join-item:first-child:not(:last-child),\n  .join.join-vertical *:first-child:not(:last-child) .join-item {\n  border-end-start-radius: 0;\n  border-end-end-radius: 0;\n  border-start-start-radius: inherit;\n  border-start-end-radius: inherit;\n}\n.join.join-vertical .join-item:last-child:not(:first-child),\n  .join.join-vertical *:last-child:not(:first-child) .join-item {\n  border-start-start-radius: 0;\n  border-start-end-radius: 0;\n  border-end-start-radius: inherit;\n  border-end-end-radius: inherit;\n}\n.join.join-horizontal {\n  flex-direction: row;\n}\n.join.join-horizontal .join-item:first-child:not(:last-child),\n  .join.join-horizontal *:first-child:not(:last-child) .join-item {\n  border-end-end-radius: 0;\n  border-start-end-radius: 0;\n  border-end-start-radius: inherit;\n  border-start-start-radius: inherit;\n}\n.join.join-horizontal .join-item:last-child:not(:first-child),\n  .join.join-horizontal *:last-child:not(:first-child) .join-item {\n  border-end-start-radius: 0;\n  border-start-start-radius: 0;\n  border-end-end-radius: inherit;\n  border-start-end-radius: inherit;\n}\n.kbd-xs {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  min-height: 1.2em;\n  min-width: 1.2em;\n}\n.select-sm {\n  height: 2rem;\n  min-height: 2rem;\n  padding-left: 0.75rem;\n  padding-right: 2rem;\n  font-size: 0.875rem;\n  line-height: 2rem;\n}\n[dir=\"rtl\"] .select-sm {\n  padding-left: 2rem;\n  padding-right: 0.75rem;\n}\n.steps-horizontal {\n  grid-auto-columns: 1fr;\n  display: inline-grid;\n  grid-auto-flow: column;\n  overflow: hidden;\n  overflow-x: auto;\n}\n.steps-horizontal .step {\n  display: grid;\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n  grid-template-rows: repeat(2, minmax(0, 1fr));\n  place-items: center;\n  text-align: center;\n}\n.steps-vertical .step {\n  display: grid;\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n  grid-template-rows: repeat(1, minmax(0, 1fr));\n}\n.tabs-md :where(.tab) {\n  height: 2rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  line-height: 2;\n  --tab-padding: 1rem;\n}\n.tabs-lg :where(.tab) {\n  height: 3rem;\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n  line-height: 2;\n  --tab-padding: 1.25rem;\n}\n.tabs-sm :where(.tab) {\n  height: 1.5rem;\n  font-size: 0.875rem;\n  line-height: .75rem;\n  --tab-padding: 0.75rem;\n}\n.tabs-xs :where(.tab) {\n  height: 1.25rem;\n  font-size: 0.75rem;\n  line-height: .75rem;\n  --tab-padding: 0.5rem;\n}\n:where(.\\!toast) {\n  bottom: 0px !important;\n  inset-inline-end: 0px !important;\n  inset-inline-start: auto !important;\n  top: auto !important;\n  --tw-translate-x: 0px !important;\n  --tw-translate-y: 0px !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n:where(.toast) {\n  bottom: 0px;\n  inset-inline-end: 0px;\n  inset-inline-start: auto;\n  top: auto;\n  --tw-translate-x: 0px;\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-start) {\n  inset-inline-end: auto !important;\n  inset-inline-start: 0px !important;\n  --tw-translate-x: 0px !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-start) {\n  inset-inline-end: auto;\n  inset-inline-start: 0px;\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-center) {\n  inset-inline-end: 50% !important;\n  inset-inline-start: 50% !important;\n  --tw-translate-x: -50% !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-center) {\n  inset-inline-end: 50%;\n  inset-inline-start: 50%;\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-center):where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  --tw-translate-x: 50% !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-center):where([dir=\"rtl\"], [dir=\"rtl\"] *) {\n  --tw-translate-x: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-end) {\n  inset-inline-end: 0px !important;\n  inset-inline-start: auto !important;\n  --tw-translate-x: 0px !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-end) {\n  inset-inline-end: 0px;\n  inset-inline-start: auto;\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-bottom) {\n  bottom: 0px !important;\n  top: auto !important;\n  --tw-translate-y: 0px !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-bottom) {\n  bottom: 0px;\n  top: auto;\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-middle) {\n  bottom: auto !important;\n  top: 50% !important;\n  --tw-translate-y: -50% !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-middle) {\n  bottom: auto;\n  top: 50%;\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.\\!toast:where(.toast-top) {\n  bottom: auto !important;\n  top: 0px !important;\n  --tw-translate-y: 0px !important;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;\n}\n.toast:where(.toast-top) {\n  bottom: auto;\n  top: 0px;\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.tooltip {\n  position: relative;\n  display: inline-block;\n  --tooltip-offset: calc(100% + 1px + var(--tooltip-tail, 0px));\n}\n.tooltip:before {\n  position: absolute;\n  pointer-events: none;\n  z-index: 1;\n  content: var(--tw-content);\n  --tw-content: attr(data-tip);\n}\n.tooltip:before, .tooltip-top:before {\n  transform: translateX(-50%);\n  top: auto;\n  left: 50%;\n  right: auto;\n  bottom: var(--tooltip-offset);\n}\n.tooltip-left:before {\n  transform: translateY(-50%);\n  top: 50%;\n  left: auto;\n  right: var(--tooltip-offset);\n  bottom: auto;\n}\n.avatar.online:before {\n  content: \"\";\n  position: absolute;\n  z-index: 10;\n  display: block;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-su,oklch(var(--su)/var(--tw-bg-opacity)));\n  outline-style: solid;\n  outline-width: 2px;\n  outline-color: var(--fallback-b1,oklch(var(--b1)/1));\n  width: 15%;\n  height: 15%;\n  top: 7%;\n  right: 7%;\n}\n.avatar.offline:before {\n  content: \"\";\n  position: absolute;\n  z-index: 10;\n  display: block;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity)));\n  outline-style: solid;\n  outline-width: 2px;\n  outline-color: var(--fallback-b1,oklch(var(--b1)/1));\n  width: 15%;\n  height: 15%;\n  top: 7%;\n  right: 7%;\n}\n.card-compact .card-body {\n  padding: 1rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.card-compact .card-title {\n  margin-bottom: 0.25rem;\n}\n.card-normal .card-body {\n  padding: var(--padding-card, 2rem);\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.card-normal .card-title {\n  margin-bottom: 0.75rem;\n}\n.join.join-vertical > :where(*:not(:first-child)) {\n  margin-left: 0px;\n  margin-right: 0px;\n  margin-top: -1px;\n}\n.join.join-vertical > :where(*:not(:first-child)):is(.btn) {\n  margin-top: calc(var(--border-btn) * -1);\n}\n.join.join-horizontal > :where(*:not(:first-child)) {\n  margin-top: 0px;\n  margin-bottom: 0px;\n  margin-inline-start: -1px;\n}\n.join.join-horizontal > :where(*:not(:first-child)):is(.btn) {\n  margin-inline-start: calc(var(--border-btn) * -1);\n  margin-top: 0px;\n}\n.menu-sm :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), .menu-sm :where(li:not(.menu-title) > details > summary:not(.menu-title)) {\n  border-radius: var(--rounded-btn, 0.5rem);\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.menu-sm .menu-title {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.modal-top :where(.modal-box) {\n  width: 100%;\n  max-width: none;\n  --tw-translate-y: -2.5rem;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-bottom-right-radius: var(--rounded-box, 1rem);\n  border-bottom-left-radius: var(--rounded-box, 1rem);\n  border-top-left-radius: 0px;\n  border-top-right-radius: 0px;\n}\n.modal-middle :where(.modal-box) {\n  width: 91.666667%;\n  max-width: 32rem;\n  --tw-translate-y: 0px;\n  --tw-scale-x: .9;\n  --tw-scale-y: .9;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-top-left-radius: var(--rounded-box, 1rem);\n  border-top-right-radius: var(--rounded-box, 1rem);\n  border-bottom-right-radius: var(--rounded-box, 1rem);\n  border-bottom-left-radius: var(--rounded-box, 1rem);\n}\n.modal-bottom :where(.modal-box) {\n  width: 100%;\n  max-width: none;\n  --tw-translate-y: 2.5rem;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-top-left-radius: var(--rounded-box, 1rem);\n  border-top-right-radius: var(--rounded-box, 1rem);\n  border-bottom-right-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\n.steps-horizontal .step {\n  grid-template-rows: 40px 1fr;\n  grid-template-columns: auto;\n  min-width: 4rem;\n}\n.steps-horizontal .step:before {\n  height: 0.5rem;\n  width: 100%;\n  --tw-translate-x: 0px;\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  content: \"\";\n  margin-inline-start: -100%;\n}\n.steps-horizontal .step:where([dir=\"rtl\"], [dir=\"rtl\"] *):before {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.steps-vertical .step {\n  gap: 0.5rem;\n  grid-template-columns: 40px 1fr;\n  grid-template-rows: auto;\n  min-height: 4rem;\n  justify-items: start;\n}\n.steps-vertical .step:before {\n  height: 100%;\n  width: 0.5rem;\n  --tw-translate-x: -50%;\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  margin-inline-start: 50%;\n}\n.steps-vertical .step:where([dir=\"rtl\"], [dir=\"rtl\"] *):before {\n  --tw-translate-x: 50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.tooltip {\n  position: relative;\n  display: inline-block;\n  text-align: center;\n  --tooltip-tail: 0.1875rem;\n  --tooltip-color: var(--fallback-n,oklch(var(--n)/1));\n  --tooltip-text-color: var(--fallback-nc,oklch(var(--nc)/1));\n  --tooltip-tail-offset: calc(100% + 0.0625rem - var(--tooltip-tail));\n}\n.tooltip:before,\n.tooltip:after {\n  opacity: 0;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-delay: 100ms;\n  transition-duration: 200ms;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.tooltip:after {\n  position: absolute;\n  content: \"\";\n  border-style: solid;\n  border-width: var(--tooltip-tail, 0);\n  width: 0;\n  height: 0;\n  display: block;\n}\n.tooltip:before {\n  max-width: 20rem;\n  white-space: normal;\n  border-radius: 0.25rem;\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  background-color: var(--tooltip-color);\n  color: var(--tooltip-text-color);\n  width: -moz-max-content;\n  width: max-content;\n}\n.tooltip.tooltip-open:before {\n  opacity: 1;\n  transition-delay: 75ms;\n}\n.tooltip.tooltip-open:after {\n  opacity: 1;\n  transition-delay: 75ms;\n}\n.tooltip:hover:before {\n  opacity: 1;\n  transition-delay: 75ms;\n}\n.tooltip:hover:after {\n  opacity: 1;\n  transition-delay: 75ms;\n}\n.tooltip:has(:focus-visible):after,\n.tooltip:has(:focus-visible):before {\n  opacity: 1;\n  transition-delay: 75ms;\n}\n.tooltip:not([data-tip]):hover:before,\n.tooltip:not([data-tip]):hover:after {\n  visibility: hidden;\n  opacity: 0;\n}\n.tooltip:after, .tooltip-top:after {\n  transform: translateX(-50%);\n  border-color: var(--tooltip-color) transparent transparent transparent;\n  top: auto;\n  left: 50%;\n  right: auto;\n  bottom: var(--tooltip-tail-offset);\n}\n.tooltip-left:after {\n  transform: translateY(-50%);\n  border-color: transparent transparent transparent var(--tooltip-color);\n  top: 50%;\n  left: auto;\n  right: calc(var(--tooltip-tail-offset) + 0.0625rem);\n  bottom: auto;\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.visible {\n  visibility: visible;\n}\n.static {\n  position: static;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.sticky {\n  position: sticky;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-4 {\n  inset: 1rem;\n}\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\n.bottom-4 {\n  bottom: 1rem;\n}\n.bottom-full {\n  bottom: 100%;\n}\n.left-0 {\n  left: 0px;\n}\n.left-1\\/2 {\n  left: 50%;\n}\n.left-2 {\n  left: 0.5rem;\n}\n.left-2\\.5 {\n  left: 0.625rem;\n}\n.left-3 {\n  left: 0.75rem;\n}\n.left-4 {\n  left: 1rem;\n}\n.right-0 {\n  right: 0px;\n}\n.right-2 {\n  right: 0.5rem;\n}\n.right-3 {\n  right: 0.75rem;\n}\n.right-4 {\n  right: 1rem;\n}\n.top-0 {\n  top: 0px;\n}\n.top-1\\/2 {\n  top: 50%;\n}\n.top-2 {\n  top: 0.5rem;\n}\n.top-2\\.5 {\n  top: 0.625rem;\n}\n.top-4 {\n  top: 1rem;\n}\n.top-full {\n  top: 100%;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-30 {\n  z-index: 30;\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.z-\\[1\\] {\n  z-index: 1;\n}\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.mb-8 {\n  margin-bottom: 2rem;\n}\n.ml-1 {\n  margin-left: 0.25rem;\n}\n.ml-2 {\n  margin-left: 0.5rem;\n}\n.ml-4 {\n  margin-left: 1rem;\n}\n.ml-auto {\n  margin-left: auto;\n}\n.mr-1 {\n  margin-right: 0.25rem;\n}\n.mr-2 {\n  margin-right: 0.5rem;\n}\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\n.mt-1 {\n  margin-top: 0.25rem;\n}\n.mt-2 {\n  margin-top: 0.5rem;\n}\n.mt-3 {\n  margin-top: 0.75rem;\n}\n.mt-4 {\n  margin-top: 1rem;\n}\n.mt-6 {\n  margin-top: 1.5rem;\n}\n.mt-8 {\n  margin-top: 2rem;\n}\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.block {\n  display: block;\n}\n.inline-block {\n  display: inline-block;\n}\n.flex {\n  display: flex;\n}\n.table {\n  display: table;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.h-1 {\n  height: 0.25rem;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-2 {\n  height: 0.5rem;\n}\n.h-20 {\n  height: 5rem;\n}\n.h-24 {\n  height: 6rem;\n}\n.h-3 {\n  height: 0.75rem;\n}\n.h-32 {\n  height: 8rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-48 {\n  height: 12rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-6 {\n  height: 1.5rem;\n}\n.h-64 {\n  height: 16rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-full {\n  height: 100%;\n}\n.max-h-32 {\n  max-height: 8rem;\n}\n.max-h-64 {\n  max-height: 16rem;\n}\n.max-h-80 {\n  max-height: 20rem;\n}\n.max-h-96 {\n  max-height: 24rem;\n}\n.max-h-\\[90vh\\] {\n  max-height: 90vh;\n}\n.min-h-\\[400px\\] {\n  min-height: 400px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-1 {\n  width: 0.25rem;\n}\n.w-1\\/2 {\n  width: 50%;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-14 {\n  width: 3.5rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-2\\/3 {\n  width: 66.666667%;\n}\n.w-20 {\n  width: 5rem;\n}\n.w-24 {\n  width: 6rem;\n}\n.w-3 {\n  width: 0.75rem;\n}\n.w-3\\/4 {\n  width: 75%;\n}\n.w-32 {\n  width: 8rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-48 {\n  width: 12rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-52 {\n  width: 13rem;\n}\n.w-6 {\n  width: 1.5rem;\n}\n.w-64 {\n  width: 16rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-96 {\n  width: 24rem;\n}\n.w-full {\n  width: 100%;\n}\n.w-px {\n  width: 1px;\n}\n.min-w-0 {\n  min-width: 0px;\n}\n.min-w-48 {\n  min-width: 12rem;\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-6xl {\n  max-width: 72rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-none {\n  max-width: none;\n}\n.max-w-sm {\n  max-width: 24rem;\n}\n.max-w-xs {\n  max-width: 20rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.origin-left {\n  transform-origin: left;\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-full {\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.resize {\n  resize: both;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-5 {\n  grid-template-columns: repeat(5, minmax(0, 1fr));\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-center {\n  align-items: center;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.gap-1 {\n  gap: 0.25rem;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-6 {\n  gap: 1.5rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\n.break-words {\n  overflow-wrap: break-word;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-box {\n  border-radius: var(--rounded-box, 1rem);\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: 0.5rem;\n}\n.rounded-md {\n  border-radius: 0.375rem;\n}\n.rounded-sm {\n  border-radius: 0.125rem;\n}\n.border {\n  border-width: 1px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-4 {\n  border-width: 4px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-dashed {\n  border-style: dashed;\n}\n.border-base-200 {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-border-opacity, 1)));\n}\n.border-base-300 {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-border-opacity, 1)));\n}\n.border-error\\/20 {\n  border-color: var(--fallback-er,oklch(var(--er)/0.2));\n}\n.border-info\\/20 {\n  border-color: var(--fallback-in,oklch(var(--in)/0.2));\n}\n.border-warning {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-border-opacity, 1)));\n}\n.border-t-transparent {\n  border-top-color: transparent;\n}\n.bg-base-100 {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b1,oklch(var(--b1)/var(--tw-bg-opacity, 1)));\n}\n.bg-base-200 {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity, 1)));\n}\n.bg-base-300 {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity, 1)));\n}\n.bg-base-content\\/20 {\n  background-color: var(--fallback-bc,oklch(var(--bc)/0.2));\n}\n.bg-black\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/60 {\n  background-color: rgb(0 0 0 / 0.6);\n}\n.bg-current {\n  background-color: currentColor;\n}\n.bg-error {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-er,oklch(var(--er)/var(--tw-bg-opacity, 1)));\n}\n.bg-error\\/10 {\n  background-color: var(--fallback-er,oklch(var(--er)/0.1));\n}\n.bg-error\\/20 {\n  background-color: var(--fallback-er,oklch(var(--er)/0.2));\n}\n.bg-info\\/10 {\n  background-color: var(--fallback-in,oklch(var(--in)/0.1));\n}\n.bg-primary-content\\/20 {\n  background-color: var(--fallback-pc,oklch(var(--pc)/0.2));\n}\n.bg-success\\/10 {\n  background-color: var(--fallback-su,oklch(var(--su)/0.1));\n}\n.bg-warning {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-wa,oklch(var(--wa)/var(--tw-bg-opacity, 1)));\n}\n.bg-warning\\/20 {\n  background-color: var(--fallback-wa,oklch(var(--wa)/0.2));\n}\n.bg-yellow-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.to-secondary {\n  --tw-gradient-to: var(--fallback-s,oklch(var(--s)/1)) var(--tw-gradient-to-position);\n}\n.stroke-current {\n  stroke: currentColor;\n}\n.object-cover {\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n.p-1 {\n  padding: 0.25rem;\n}\n.p-12 {\n  padding: 3rem;\n}\n.p-2 {\n  padding: 0.5rem;\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-8 {\n  padding: 2rem;\n}\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-1\\.5 {\n  padding-left: 0.375rem;\n  padding-right: 0.375rem;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pl-10 {\n  padding-left: 2.5rem;\n}\n.pl-8 {\n  padding-left: 2rem;\n}\n.pr-10 {\n  padding-right: 2.5rem;\n}\n.pr-20 {\n  padding-right: 5rem;\n}\n.pr-4 {\n  padding-right: 1rem;\n}\n.pt-2 {\n  padding-top: 0.5rem;\n}\n.pt-3 {\n  padding-top: 0.75rem;\n}\n.pt-4 {\n  padding-top: 1rem;\n}\n.pt-8 {\n  padding-top: 2rem;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.font-mono {\n  font-family: var(--font-geist-mono), ui-monospace, SFMono-Regular;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-6xl {\n  font-size: 3.75rem;\n  line-height: 1;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.text-accent {\n  --tw-text-opacity: 1;\n  color: var(--fallback-a,oklch(var(--a)/var(--tw-text-opacity, 1)));\n}\n.text-base-content {\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity, 1)));\n}\n.text-base-content\\/30 {\n  color: var(--fallback-bc,oklch(var(--bc)/0.3));\n}\n.text-base-content\\/40 {\n  color: var(--fallback-bc,oklch(var(--bc)/0.4));\n}\n.text-base-content\\/50 {\n  color: var(--fallback-bc,oklch(var(--bc)/0.5));\n}\n.text-base-content\\/60 {\n  color: var(--fallback-bc,oklch(var(--bc)/0.6));\n}\n.text-base-content\\/70 {\n  color: var(--fallback-bc,oklch(var(--bc)/0.7));\n}\n.text-base-content\\/80 {\n  color: var(--fallback-bc,oklch(var(--bc)/0.8));\n}\n.text-error {\n  --tw-text-opacity: 1;\n  color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity, 1)));\n}\n.text-error-content {\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity, 1)));\n}\n.text-error\\/80 {\n  color: var(--fallback-er,oklch(var(--er)/0.8));\n}\n.text-info {\n  --tw-text-opacity: 1;\n  color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity, 1)));\n}\n.text-info-content {\n  --tw-text-opacity: 1;\n  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity, 1)));\n}\n.text-primary-content {\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity, 1)));\n}\n.text-primary-content\\/70 {\n  color: var(--fallback-pc,oklch(var(--pc)/0.7));\n}\n.text-primary-content\\/90 {\n  color: var(--fallback-pc,oklch(var(--pc)/0.9));\n}\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-secondary {\n  --tw-text-opacity: 1;\n  color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity, 1)));\n}\n.text-success {\n  --tw-text-opacity: 1;\n  color: var(--fallback-su,oklch(var(--su)/var(--tw-text-opacity, 1)));\n}\n.text-success-content {\n  --tw-text-opacity: 1;\n  color: var(--fallback-suc,oklch(var(--suc)/var(--tw-text-opacity, 1)));\n}\n.text-warning {\n  --tw-text-opacity: 1;\n  color: var(--fallback-wa,oklch(var(--wa)/var(--tw-text-opacity, 1)));\n}\n.text-warning-content {\n  --tw-text-opacity: 1;\n  color: var(--fallback-wac,oklch(var(--wac)/var(--tw-text-opacity, 1)));\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-yellow-800 {\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\n.text-yellow-900 {\n  --tw-text-opacity: 1;\n  color: rgb(113 63 18 / var(--tw-text-opacity, 1));\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline {\n  outline-style: solid;\n}\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* 自定义样式 */\n.card-hover {\n  transition: all 0.3s ease;\n}\n\n.card-hover:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 24px -4px rgba(0, 0, 0, 0.1);\n}\n\n.search-highlight {\n  background-color: #fef3c7;\n  color: #92400e;\n  padding: 1px 3px;\n  border-radius: 2px;\n}\n\n.prose-code {\n  background-color: #f1f5f9;\n  color: #334155;\n  padding: 2px 4px;\n  border-radius: 3px;\n  font-size: 0.875em;\n}\n\n.animate-fade-in {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; transform: translateY(10px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n.animate-slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from { transform: translateX(-100%); }\n  to { transform: translateX(0); }\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n@media (hover: hover) {\n\n  .hover\\:btn-primary:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-primary:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-primary:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-primary:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-secondary:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-secondary:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-secondary:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-secondary:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-info:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-info:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-info:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-info:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-error:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-error:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n    }\n  }\n\n  .hover\\:btn-error:hover.btn-outline:hover {\n    --tw-text-opacity: 1;\n    color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n  }\n\n  @supports (color: color-mix(in oklab, black, black)) {\n\n    .hover\\:btn-error:hover.btn-outline:hover {\n      background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n      border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n    }\n  }\n}\n\n.hover\\:badge-primary:hover {\n  --tw-border-opacity: 1;\n  border-color: var(--fallback-p,oklch(var(--p)/var(--tw-border-opacity)));\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-p,oklch(var(--p)/var(--tw-bg-opacity)));\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n\n.hover\\:badge-primary:hover.badge-outline {\n  --tw-text-opacity: 1;\n  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));\n}\n\n@supports not (color: oklch(0% 0 0)) {\n\n  .hover\\:btn-primary:hover {\n    --btn-color: var(--fallback-p);\n  }\n\n  .hover\\:btn-secondary:hover {\n    --btn-color: var(--fallback-s);\n  }\n\n  .hover\\:btn-info:hover {\n    --btn-color: var(--fallback-in);\n  }\n\n  .hover\\:btn-error:hover {\n    --btn-color: var(--fallback-er);\n  }\n}\n\n@supports (color: color-mix(in oklab, black, black)) {\n\n  .hover\\:btn-primary:hover.btn-outline.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-p,oklch(var(--p)/1)) 90%, black);\n  }\n\n  .hover\\:btn-secondary:hover.btn-outline.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-s,oklch(var(--s)/1)) 90%, black);\n  }\n\n  .hover\\:btn-info:hover.btn-outline.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-in,oklch(var(--in)/1)) 90%, black);\n  }\n\n  .hover\\:btn-error:hover.btn-outline.btn-active {\n    background-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n    border-color: color-mix(in oklab, var(--fallback-er,oklch(var(--er)/1)) 90%, black);\n  }\n}\n\n.hover\\:btn-primary:hover {\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-p,oklch(var(--p)/1));\n}\n\n@supports (color: oklch(0% 0 0)) {\n\n  .hover\\:btn-primary:hover {\n    --btn-color: var(--p);\n  }\n\n  .hover\\:btn-secondary:hover {\n    --btn-color: var(--s);\n  }\n\n  .hover\\:btn-info:hover {\n    --btn-color: var(--in);\n  }\n\n  .hover\\:btn-error:hover {\n    --btn-color: var(--er);\n  }\n}\n\n.hover\\:btn-secondary:hover {\n  --tw-text-opacity: 1;\n  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-s,oklch(var(--s)/1));\n}\n\n.hover\\:btn-info:hover {\n  --tw-text-opacity: 1;\n  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-in,oklch(var(--in)/1));\n}\n\n.hover\\:btn-error:hover {\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n  outline-color: var(--fallback-er,oklch(var(--er)/1));\n}\n\n.hover\\:btn-primary:hover.btn-outline {\n  --tw-text-opacity: 1;\n  color: var(--fallback-p,oklch(var(--p)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-primary:hover.btn-outline.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-pc,oklch(var(--pc)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-secondary:hover.btn-outline {\n  --tw-text-opacity: 1;\n  color: var(--fallback-s,oklch(var(--s)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-secondary:hover.btn-outline.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-sc,oklch(var(--sc)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-info:hover.btn-outline {\n  --tw-text-opacity: 1;\n  color: var(--fallback-in,oklch(var(--in)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-info:hover.btn-outline.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-inc,oklch(var(--inc)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-error:hover.btn-outline {\n  --tw-text-opacity: 1;\n  color: var(--fallback-er,oklch(var(--er)/var(--tw-text-opacity)));\n}\n\n.hover\\:btn-error:hover.btn-outline.btn-active {\n  --tw-text-opacity: 1;\n  color: var(--fallback-erc,oklch(var(--erc)/var(--tw-text-opacity)));\n}\n\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-110:hover {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:bg-base-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity, 1)));\n}\n\n.hover\\:bg-base-300:hover {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity, 1)));\n}\n\n.hover\\:bg-base-content\\/30:hover {\n  background-color: var(--fallback-bc,oklch(var(--bc)/0.3));\n}\n\n.hover\\:text-base-content:hover {\n  --tw-text-opacity: 1;\n  color: var(--fallback-bc,oklch(var(--bc)/var(--tw-text-opacity, 1)));\n}\n\n.hover\\:text-base-content\\/70:hover {\n  color: var(--fallback-bc,oklch(var(--bc)/0.7));\n}\n\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n\n.group:hover .group-hover\\:bg-base-300 {\n  --tw-bg-opacity: 1;\n  background-color: var(--fallback-b3,oklch(var(--b3)/var(--tw-bg-opacity, 1)));\n}\n\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n\n@media (min-width: 640px) {\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n}\n\n@media (min-width: 768px) {\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n}\n\n@media (min-width: 1024px) {\n\n  .lg\\:static {\n    position: static;\n  }\n\n  .lg\\:inset-0 {\n    inset: 0px;\n  }\n\n  .lg\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:ml-0 {\n    margin-left: 0px;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:translate-x-0 {\n    --tw-translate-x: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n\n@media (min-width: 1280px) {\n\n  .xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n\n@media (min-width: 1536px) {\n\n  .\\32xl\\:grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAkBA;;;;;AAWA;;;;;;AAUA;;;;;AASA;;;;;AAcA;;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAAA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAAA;;;;;AAKA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAIA;;;;;AAMA;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;EAwBA;IAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BJ;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;;;;;;;;;;;AAoBA;EAEE;;;;;;;;AAOF;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;EAEE;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EASA;;;;;EAKA;;;;EAIA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAKA;;;;;EAMA;;;;;;AAMF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAMA;;;;;;AAKA;;;;AAGA;;;;;;;;;;AASA;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;AAcA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAGA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAMA;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;;AAQA;;;;;AAMA;EAEE;;;;;EAKA;;;;;;;;;EAUA;;;;;;;EAOA;IAEE;;;;;;EAcF;IAEE;;;;;;EAMF;;;;;EAKA;;;;EAIA;IAEE;;;;;EAKF;;;;;;;;;EASA;;;;;EAKA;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAMF;;;;;;;;EAUA;IAEE;;;;;IAAA;;;;;IAAA;;;;;IAAA;;;;;IAAA;;;;;IAAA;;;;;;EAMF;;;;;;EAMA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAMA;IAEE;;;;IAAA;;;;IAAA;;;;IAAA;;;;IAAA;;;;IAAA;;;;;EAKF;;;;;;;AAOF;;;;AAAA;;;;AAAA;;;;AAGA;;;;;AAIA;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;;;;;;;AAiBA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAMA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;;;AAKA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAOA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAKA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAKA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAKA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAKA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAKA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;;;;;;;;;;AAkBA;;;;;;AAIA;;;;;;;;AAQA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;;;;;;;;AAAA;;;;;;;;;;;;AAAA;;;;;;;;;;;;AAAA;;;;;;;;;;;;AAWA;;;;;;;;AAOA;;;;AAGA;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;AAQA;;;;;;AAKA;;;;;AAIA;;;;;;;;AAOA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AA6BA;;;;AAGA;;;;;AAKA;;;;;;;;;AAQA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;AAQA;;;;;AAIA;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;AAUA;;;;;AAIA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AA4BA;;;;;;AAAA;;;;;;AAAA;;;;;;AAKA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAIA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAIA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAIA;;;;;;;;;AAQA;;;;;;;;;AAOA;;;;;;;;;AAOA;;;;;;;;;AAOA;;;;;;;;;;;;;AAgBA;;;;;;;;;;;AAUA;;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;;AAuBA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;;AASA;;;;;AAIA;EAEE;;;;;AAIF;;;;;AAKA;EAEE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;EAEE;;;;;EAaA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;;;;;;AAKA;;;;;;AAKA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;;;;;;AAUA;;;;;AAIA;;;;;;;AAKA;;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;AASA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AASA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAGA;;;;;;;;;;;;;;AAcA;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;AAQA;;;;;AAIA;;;;AAGA;;;;;;;AAMA;;;;;;;;;AAQA;;;;;;;AAiBA;;;;;;;;AASA;;;;;;;AAMA;;;;AAGA;;;;;;;;;AASA;;;;;;;AAMA;;;;AAGA;;;;;;;;;AASA;;;;;;;;;;;;;;AAcA;;;;AAGA;;;;;;AAOA;;;;;;;AAMA;;;;;;;AAMA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;AASA;;;;;AAIA;;;;;;AAMA;;;;;;;;;AAAA;;;;;;;;;AAWA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAAA;;;;;AAOA;;;;AAGA;;;;AAGA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAGA;;;;;AAIA;EAEE;IAEE;;;;;;AAKJ;;;;;AAIA;;;;;AAIA;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;AAeA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAWA;;;;;;;;;;AAAA;;;;;;;;;;AAAA;;;;;;;;;;AAAA;;;;;;;;;;AAgBA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAQA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAQA;;;;AAGA;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;AAgBA;;;;;AAAA;;;;;AAKA;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;AAcA;;;;;AAAA;;;;;AAKA;;;;;;;AASA;;;;;;AAKA;;;;AAGA;;;;;;AAMA;;;;;;AAMA;;;;AAGA;;;;;;;AAMA;;;;;;;;AASA;;;;;;AAKA;;;;AAGA;;;;;;;;;AASA;;;;;AAIA;;;;;;;;;;;;;;AAiBA;;;;;;;;;;;;;;AAcA;;;;AAGA;;;;;;;;;AAQA;;;;;AAIA;;;;;;AAKA;;;;;;;;;AAUA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAMA;;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAcA;;;;;;;;;;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAUA;;;;;;;;AAAA;;;;;;;;AAAA;;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;;AAAA;;;;;;;;AAAA;;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAWA;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;AA6BA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAOA;;;;;;;AAMA;;;;AAGA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAMA;;;;AAGA;;;;;AAOA;;;;;AAMA;;;;;;AAKA;;;;;;;;AAOA;;;;;;AAKA;;;;AAGA;;;;;;;;;AAQA;;;;;AAIA;;;;;;AAKA;;;;;;;;;AAUA;;;;;AAAA;;;;;AAAA;;;;;AAIA;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;;;AAOA;;;;AAGA;;;;;;AAOA;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;AAMA;;;;AAAA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;;;AAoBA;EAEE;;;;;;;;;;;AAoBF;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AASA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;;AAOA;;;;AAGA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;AAGA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;AAIA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AASA;;;;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AASA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAAA;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;;AAgBA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAKA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAIA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AAAA;;;;;;;AASA;;;;AAMA;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAYA;;;;;;AAKA;;;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AASA;;;;;AAIA;;;;;;;;AAOA;;;;;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAQA;;;;;AAIA;;;;;;;;;;AASA;;;;;AAUA;;;;;;;;;;AASA;;;;;;;;;;;;AAeA;;;;;AAgBA;;;;;AAAA;;;;;AAKA;;;;;AAKA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;AAKA;;;;AAIA;;;;;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;EAOE;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAWF;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAWF;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;EAWF;IAEE;;;;;;EAMF;;;;;EAKA;IAEE;;;;;;;AAOJ;;;;;;;;;AASA;;;;;AAKA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EAEE;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAMF;;;;;;AAMA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;EAEE;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAKF;EAEE;;;;;AAKF;EAEE"}}]}