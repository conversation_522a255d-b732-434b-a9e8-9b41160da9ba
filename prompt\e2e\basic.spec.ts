import { test, expect } from '@playwright/test'

test.describe('基础功能测试', () => {
  test('应用应该能够启动', async ({ page }) => {
    // 访问首页
    await page.goto('http://localhost:3000')
    
    // 等待页面加载完成
    await page.waitForTimeout(5000)
    
    // 检查页面标题
    await expect(page).toHaveTitle(/提示词管理工具/)
    
    // 检查页面是否包含基本内容
    const body = await page.locator('body').textContent()
    console.log('页面内容:', body?.substring(0, 500))
    
    // 检查是否有加载状态
    const loadingElement = page.locator('.loading')
    if (await loadingElement.count() > 0) {
      console.log('检测到加载状态')
      await loadingElement.waitFor({ state: 'hidden', timeout: 10000 })
    }
    
    // 截图保存
    await page.screenshot({ path: 'test-results/homepage.png' })
  })

  test('应该响应基本的 API 健康检查', async ({ page }) => {
    const response = await page.request.get('http://localhost:3000/api/health')
    
    if (response.status() === 200) {
      const health = await response.json()
      console.log('健康检查响应:', health)
      expect(health.status).toBe('healthy')
    } else {
      console.log('健康检查失败，状态码:', response.status())
      // 即使健康检查失败，我们也继续测试其他功能
    }
  })

  test('应该能加载基本的页面结构', async ({ page }) => {
    await page.goto('http://localhost:3000')
    
    // 等待页面稳定
    await page.waitForLoadState('networkidle')
    
    // 检查基本HTML结构
    await expect(page.locator('html')).toBeVisible()
    await expect(page.locator('body')).toBeVisible()
    
    // 检查是否有错误信息
    const errorElements = await page.locator('[data-testid="error"]').count()
    console.log('错误元素数量:', errorElements)
    
    // 检查页面的基本状态
    const pageContent = await page.content()
    console.log('页面HTML长度:', pageContent.length)
    
    // 保存完整的页面截图用于调试
    await page.screenshot({ 
      path: 'test-results/full-page.png', 
      fullPage: true 
    })
  })
})