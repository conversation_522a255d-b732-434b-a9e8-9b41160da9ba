#!/bin/bash

# Vercel 部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境: development, preview, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境参数
ENVIRONMENT=${1:-preview}
if [[ ! "$ENVIRONMENT" =~ ^(development|preview|production)$ ]]; then
    print_error "无效的环境参数: $ENVIRONMENT"
    print_info "支持的环境: development, preview, production"
    exit 1
fi

print_info "开始部署到 $ENVIRONMENT 环境..."

# 检查 Vercel CLI 是否已安装
if ! command -v vercel &> /dev/null; then
    print_error "Vercel CLI 未安装"
    print_info "请运行: npm install -g vercel"
    exit 1
fi

# 检查是否已登录 Vercel
if ! vercel whoami &> /dev/null; then
    print_error "未登录 Vercel"
    print_info "请运行: vercel login"
    exit 1
fi

# 检查项目是否已链接
if [ ! -f ".vercel/project.json" ]; then
    print_warning "项目未链接到 Vercel"
    print_info "开始链接项目..."
    vercel link
fi

# 预部署检查
print_info "执行预部署检查..."

# 检查环境变量文件
if [ ! -f ".env.local" ] && [ "$ENVIRONMENT" != "production" ]; then
    print_warning "未找到 .env.local 文件"
    print_info "请确保已设置必要的环境变量"
fi

# 检查 package.json
if [ ! -f "package.json" ]; then
    print_error "未找到 package.json 文件"
    exit 1
fi

# 检查构建脚本
if ! npm run build:check &> /dev/null; then
    print_warning "构建检查失败，继续部署..."
fi

# 运行测试（如果存在）
if npm run test:ci &> /dev/null; then
    print_info "运行测试..."
    npm run test:ci
    print_success "测试通过"
else
    print_warning "跳过测试（未配置或失败）"
fi

# 运行类型检查
if npm run type-check &> /dev/null; then
    print_info "运行类型检查..."
    npm run type-check
    print_success "类型检查通过"
else
    print_warning "跳过类型检查（未配置或失败）"
fi

# 根据环境选择部署命令
case $ENVIRONMENT in
    "development")
        print_info "部署到开发环境..."
        vercel --dev
        ;;
    "preview")
        print_info "部署到预览环境..."
        vercel
        ;;
    "production")
        print_info "部署到生产环境..."
        print_warning "这将部署到生产环境，确定要继续吗？"
        read -p "输入 'yes' 继续: " -r
        if [[ $REPLY == "yes" ]]; then
            vercel --prod
        else
            print_info "取消部署"
            exit 0
        fi
        ;;
esac

# 部署后操作
print_info "部署完成，执行后续操作..."

# 获取部署 URL
DEPLOY_URL=$(vercel ls --limit 1 --json | jq -r '.[0].url')
if [ "$DEPLOY_URL" != "null" ]; then
    print_success "部署 URL: https://$DEPLOY_URL"
    
    # 打开浏览器（可选）
    if command -v open &> /dev/null; then
        read -p "是否在浏览器中打开？ (y/N): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            open "https://$DEPLOY_URL"
        fi
    fi
else
    print_warning "无法获取部署 URL"
fi

# 运行部署后测试（如果是生产环境）
if [ "$ENVIRONMENT" == "production" ]; then
    print_info "运行生产环境测试..."
    
    # 健康检查
    if curl -f -s "https://$DEPLOY_URL/api/health" > /dev/null; then
        print_success "健康检查通过"
    else
        print_error "健康检查失败"
    fi
    
    # 可以添加更多生产环境检查
    # 例如：Lighthouse 测试、安全检查等
fi

print_success "部署流程完成！"

# 显示有用的命令
print_info "有用的命令："
print_info "  查看部署日志: vercel logs"
print_info "  查看部署列表: vercel ls"
print_info "  查看域名: vercel domains"
print_info "  查看环境变量: vercel env ls"