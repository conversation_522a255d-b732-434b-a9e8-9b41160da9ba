import { describe, it, expect, vi } from 'vitest'
import {
  ValidationError,
  AuthorizationError,
  NotFoundError,
  BusinessError,
  ErrorHandler,
  handleComponentError,
  handleAsyncError,
  handleFormError,
  withRetry,
  logError,
  ERROR_MESSAGES,
} from '~/lib/errors'

describe('Error Classes', () => {
  describe('ValidationError', () => {
    it('应该创建验证错误', () => {
      const error = new ValidationError('验证失败', { field: 'title' })
      
      expect(error.message).toBe('验证失败')
      expect(error.name).toBe('ValidationError')
      expect(error.code).toBe('VALIDATION_ERROR')
      expect(error.details).toEqual({ field: 'title' })
      expect(error.statusCode).toBe(400)
    })

    it('应该是 Error 的实例', () => {
      const error = new ValidationError('验证失败')
      
      expect(error).toBeInstanceOf(Error)
      expect(error).toBeInstanceOf(ValidationError)
    })
  })

  describe('AuthorizationError', () => {
    it('应该创建授权错误', () => {
      const error = new AuthorizationError('权限不足')
      
      expect(error.message).toBe('权限不足')
      expect(error.name).toBe('AuthorizationError')
      expect(error.code).toBe('AUTHORIZATION_ERROR')
      expect(error.statusCode).toBe(403)
    })
  })

  describe('NotFoundError', () => {
    it('应该创建未找到错误', () => {
      const error = new NotFoundError('资源不存在')
      
      expect(error.message).toBe('资源不存在')
      expect(error.name).toBe('NotFoundError')
      expect(error.code).toBe('NOT_FOUND_ERROR')
      expect(error.statusCode).toBe(404)
    })
  })

  describe('BusinessError', () => {
    it('应该创建业务错误', () => {
      const error = new BusinessError('业务逻辑错误', 'CUSTOM_ERROR')
      
      expect(error.message).toBe('业务逻辑错误')
      expect(error.name).toBe('BusinessError')
      expect(error.code).toBe('CUSTOM_ERROR')
      expect(error.statusCode).toBe(400)
    })
  })
})

describe('ErrorHandler', () => {
  describe('handle', () => {
    it('应该处理已知的错误类型', () => {
      const validationError = new ValidationError('验证失败')
      const result = ErrorHandler.handle(validationError)
      
      expect(result).toEqual({
        message: '验证失败',
        code: 'VALIDATION_ERROR',
        statusCode: 400,
        details: undefined,
      })
    })

    it('应该处理未知的错误类型', () => {
      const unknownError = new Error('未知错误')
      const result = ErrorHandler.handle(unknownError)
      
      expect(result).toEqual({
        message: '未知错误',
        code: 'UNKNOWN_ERROR',
        statusCode: 500,
      })
    })

    it('应该处理字符串错误', () => {
      const stringError = '字符串错误'
      const result = ErrorHandler.handle(stringError)
      
      expect(result).toEqual({
        message: '字符串错误',
        code: 'UNKNOWN_ERROR',
        statusCode: 500,
      })
    })

    it('应该处理 null 和 undefined', () => {
      const nullResult = ErrorHandler.handle(null)
      const undefinedResult = ErrorHandler.handle(undefined)
      
      expect(nullResult.message).toBe('未知错误')
      expect(undefinedResult.message).toBe('未知错误')
    })
  })

  describe('getErrorMessage', () => {
    it('应该获取错误消息', () => {
      const validationError = new ValidationError('字段验证失败')
      const message = ErrorHandler.getErrorMessage(validationError)
      
      expect(message).toBe('字段验证失败')
    })

    it('应该处理网络错误', () => {
      const networkError = new TypeError('fetch failed')
      const message = ErrorHandler.getErrorMessage(networkError)
      
      expect(message).toBe('网络请求失败，请检查网络连接')
    })
  })

  describe('getValidationErrors', () => {
    it('应该获取验证错误详情', () => {
      const validationError = new ValidationError('验证失败', [
        { field: 'title', message: '标题不能为空' }
      ])
      
      const errors = ErrorHandler.getValidationErrors(validationError)
      expect(errors).toEqual([{ field: 'title', message: '标题不能为空' }])
    })
  })
})

describe('Error Handling Integration', () => {
  it('应该完整处理错误流程', () => {
    const originalError = new ValidationError('标题长度不能超过100个字符', {
      field: 'title',
      maxLength: 100,
    })
    
    // 处理错误
    const handled = ErrorHandler.handle(originalError)
    
    // 验证处理结果
    expect(handled.code).toBe('VALIDATION_ERROR')
    expect(handled.statusCode).toBe(400)
    
    // 创建用户友好消息
    const userMessage = ErrorHandler.getErrorMessage(originalError)
    expect(userMessage).toBe('标题长度不能超过100个字符')
  })
  
  it('应该处理复杂的错误场景', () => {
    const errors = [
      new ValidationError('验证失败'),
      new AuthorizationError('权限不足'),
      new NotFoundError('资源不存在'),
      new BusinessError('业务错误'),
      new Error('系统错误'),
    ]
    
    errors.forEach(error => {
      const handled = ErrorHandler.handle(error)
      const message = ErrorHandler.getErrorMessage(error)
      
      expect(handled.code).toBeDefined()
      expect(handled.statusCode).toBeDefined()
      expect(message).toBeDefined()
    })
  })

  it('应该支持异步错误处理', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    const error = new ValidationError('异步验证失败')
    const result = handleAsyncError(error, '用户注册')
    
    expect(result.code).toBe('VALIDATION_ERROR')
    expect(consoleSpy).toHaveBeenCalledWith(
      '异步操作错误 (用户注册):',
      expect.objectContaining({
        code: 'VALIDATION_ERROR',
        message: '异步验证失败',
      })
    )
    
    consoleSpy.mockRestore()
  })

  it('应该支持表单错误处理', () => {
    const error = new ValidationError('表单验证失败', [
      { field: 'title', message: '标题不能为空' },
      { field: 'content', message: '内容不能为空' }
    ])
    
    const formErrors = handleFormError(error)
    
    expect(formErrors).toEqual({
      title: '标题不能为空',
      content: '内容不能为空'
    })
  })

  it('应该支持重试机制', async () => {
    let attempts = 0
    const fn = vi.fn().mockImplementation(() => {
      attempts++
      if (attempts < 3) {
        throw new Error('临时错误')
      }
      return '成功'
    })
    
    const result = await withRetry(fn, 3, 10)
    
    expect(result).toBe('成功')
    expect(fn).toHaveBeenCalledTimes(3)
  })
})