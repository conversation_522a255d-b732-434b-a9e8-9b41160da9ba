import { test, expect } from '@playwright/test'

test.describe('认证功能测试', () => {
  test('应该显示登录界面', async ({ page }) => {
    await page.goto('http://localhost:3000')
    
    // 检查登录提示
    await expect(page.locator('text=需要登录')).toBeVisible()
    await expect(page.locator('text=请先登录以访问提示词管理工具')).toBeVisible()
    
    // 查找登录按钮或链接
    const loginButton = page.locator('text=前往登录')
    await expect(loginButton).toBeVisible()
    
    // 截图记录
    await page.screenshot({ path: 'test-results/login-prompt.png' })
  })

  test('应该能够导航到登录页面', async ({ page }) => {
    await page.goto('http://localhost:3000')
    
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 尝试点击登录链接
    const loginLink = page.locator('text=前往登录').first()
    if (await loginLink.count() > 0) {
      await loginLink.click()
      
      // 等待页面导航
      await page.waitForTimeout(2000)
      
      // 检查是否导航到登录页面
      const url = page.url()
      console.log('导航后的URL:', url)
      
      // 截图记录登录页面
      await page.screenshot({ path: 'test-results/login-page.png' })
    } else {
      console.log('未找到登录链接')
    }
  })

  test('应该显示正确的应用标题和信息', async ({ page }) => {
    await page.goto('http://localhost:3000')
    
    // 检查页面标题
    await expect(page).toHaveTitle(/提示词管理工具/)
    
    // 检查页面语言设置
    const htmlLang = await page.locator('html').getAttribute('lang')
    expect(htmlLang).toBe('zh-CN')
    
    // 检查主题设置
    const theme = await page.locator('html').getAttribute('data-theme')
    expect(theme).toBe('light')
    
    console.log('页面语言:', htmlLang)
    console.log('页面主题:', theme)
  })

  test('应该正确处理未认证状态', async ({ page }) => {
    await page.goto('http://localhost:3000')
    
    // 等待页面稳定
    await page.waitForLoadState('networkidle')
    
    // 检查页面是否显示认证提示而不是应用内容
    const needLogin = await page.locator('text=需要登录').count()
    const promptManagement = await page.locator('text=提示词管理').count()
    
    console.log('需要登录提示数量:', needLogin)
    console.log('提示词管理内容数量:', promptManagement)
    
    // 应用正确显示了认证状态
    expect(needLogin).toBeGreaterThan(0)
  })

  test('应该能响应键盘导航', async ({ page }) => {
    await page.goto('http://localhost:3000')
    
    // 等待页面稳定
    await page.waitForLoadState('networkidle')
    
    // 测试Tab键导航
    await page.keyboard.press('Tab')
    
    // 检查是否有聚焦的元素
    const focusedElement = await page.locator(':focus').count()
    console.log('聚焦元素数量:', focusedElement)
    
    // 如果有聚焦元素，测试Enter键
    if (focusedElement > 0) {
      await page.keyboard.press('Enter')
      await page.waitForTimeout(1000)
      
      console.log('Enter键响应测试完成')
    }
  })
})