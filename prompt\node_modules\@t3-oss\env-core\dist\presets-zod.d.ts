/**
 * Vercel System Environment Variables
 * @see https://vercel.com/docs/projects/environment-variables/system-environment-variables#system-environment-variables
 */
declare const vercel: () => Readonly<{
    VERCEL: string | undefined;
    CI: string | undefined;
    VERCEL_ENV: "development" | "preview" | "production" | undefined;
    VERCEL_URL: string | undefined;
    VERCEL_PROJECT_PRODUCTION_URL: string | undefined;
    VERCEL_BRANCH_URL: string | undefined;
    VERCEL_REGION: string | undefined;
    VERCEL_DEPLOYMENT_ID: string | undefined;
    VERCEL_SKEW_PROTECTION_ENABLED: string | undefined;
    VERCEL_AUTOMATION_BYPASS_SECRET: string | undefined;
    VERCEL_GIT_PROVIDER: string | undefined;
    VERCEL_GIT_REPO_SLUG: string | undefined;
    VERCEL_GIT_REPO_OWNER: string | undefined;
    VERCEL_GIT_REPO_ID: string | undefined;
    VERCEL_GIT_COMMIT_REF: string | undefined;
    VERCEL_GIT_COMMIT_SHA: string | undefined;
    VERCEL_GIT_COMMIT_MESSAGE: string | undefined;
    VERCEL_GIT_COMMIT_AUTHOR_LOGIN: string | undefined;
    VERCEL_GIT_COMMIT_AUTHOR_NAME: string | undefined;
    VERCEL_GIT_PREVIOUS_SHA: string | undefined;
    VERCEL_GIT_PULL_REQUEST_ID: string | undefined;
}>;
/**
 * Neon for Vercel Environment Variables
 * @see https://neon.tech/docs/guides/vercel-native-integration#environment-variables-set-by-the-integration
 */
declare const neonVercel: () => Readonly<{
    DATABASE_URL: string;
    DATABASE_URL_UNPOOLED: string | undefined;
    PGHOST: string | undefined;
    PGHOST_UNPOOLED: string | undefined;
    PGUSER: string | undefined;
    PGDATABASE: string | undefined;
    PGPASSWORD: string | undefined;
    POSTGRES_URL: string | undefined;
    POSTGRES_URL_NON_POOLING: string | undefined;
    POSTGRES_USER: string | undefined;
    POSTGRES_HOST: string | undefined;
    POSTGRES_PASSWORD: string | undefined;
    POSTGRES_DATABASE: string | undefined;
    POSTGRES_URL_NO_SSL: string | undefined;
    POSTGRES_PRISMA_URL: string | undefined;
}>;
/**
 * @see https://v6.docs.uploadthing.com/getting-started/nuxt#add-env-variables
 */
declare const uploadthingV6: () => Readonly<{
    UPLOADTHING_TOKEN: string;
}>;
/**
 * @see https://docs.uploadthing.com/getting-started/appdir#add-env-variables
 */
declare const uploadthing: () => Readonly<{
    UPLOADTHING_TOKEN: string;
}>;
/**
 * Render System Environment Variables
 * @see https://docs.render.com/environment-variables#all-runtimes
 */
declare const render: () => Readonly<{
    IS_PULL_REQUEST: string | undefined;
    RENDER_DISCOVERY_SERVICE: string | undefined;
    RENDER_EXTERNAL_HOSTNAME: string | undefined;
    RENDER_EXTERNAL_URL: string | undefined;
    RENDER_GIT_BRANCH: string | undefined;
    RENDER_GIT_COMMIT: string | undefined;
    RENDER_GIT_REPO_SLUG: string | undefined;
    RENDER_INSTANCE_ID: string | undefined;
    RENDER_SERVICE_ID: string | undefined;
    RENDER_SERVICE_NAME: string | undefined;
    RENDER_SERVICE_TYPE: "web" | "pserv" | "cron" | "worker" | "static" | undefined;
    RENDER: string | undefined;
}>;
/**
 * Railway Environment Variables
 * @see https://docs.railway.app/reference/variables#railway-provided-variables
 */
declare const railway: () => Readonly<{
    RAILWAY_PUBLIC_DOMAIN: string | undefined;
    RAILWAY_PRIVATE_DOMAIN: string | undefined;
    RAILWAY_TCP_PROXY_DOMAIN: string | undefined;
    RAILWAY_TCP_PROXY_PORT: string | undefined;
    RAILWAY_TCP_APPLICATION_PORT: string | undefined;
    RAILWAY_PROJECT_NAME: string | undefined;
    RAILWAY_PROJECT_ID: string | undefined;
    RAILWAY_ENVIRONMENT_NAME: string | undefined;
    RAILWAY_ENVIRONMENT_ID: string | undefined;
    RAILWAY_SERVICE_NAME: string | undefined;
    RAILWAY_SERVICE_ID: string | undefined;
    RAILWAY_REPLICA_ID: string | undefined;
    RAILWAY_DEPLOYMENT_ID: string | undefined;
    RAILWAY_SNAPSHOT_ID: string | undefined;
    RAILWAY_VOLUME_NAME: string | undefined;
    RAILWAY_VOLUME_MOUNT_PATH: string | undefined;
    RAILWAY_RUN_UID: string | undefined;
    RAILWAY_GIT_COMMIT_SHA: string | undefined;
    RAILWAY_GIT_AUTHOR_EMAIL: string | undefined;
    RAILWAY_GIT_BRANCH: string | undefined;
    RAILWAY_GIT_REPO_NAME: string | undefined;
    RAILWAY_GIT_REPO_OWNER: string | undefined;
    RAILWAY_GIT_COMMIT_MESSAGE: string | undefined;
}>;
/**
 * Fly.io Environment Variables
 * @see https://fly.io/docs/machines/runtime-environment/#environment-variables
 */
declare const fly: () => Readonly<{
    FLY_APP_NAME: string | undefined;
    FLY_MACHINE_ID: string | undefined;
    FLY_ALLOC_ID: string | undefined;
    FLY_REGION: string | undefined;
    FLY_PUBLIC_IP: string | undefined;
    FLY_IMAGE_REF: string | undefined;
    FLY_MACHINE_VERSION: string | undefined;
    FLY_PRIVATE_IP: string | undefined;
    FLY_PROCESS_GROUP: string | undefined;
    FLY_VM_MEMORY_MB: string | undefined;
    PRIMARY_REGION: string | undefined;
}>;
/**
 * Netlify Environment Variables
 * @see https://docs.netlify.com/configure-builds/environment-variables
 */
declare const netlify: () => Readonly<{
    NETLIFY: string | undefined;
    BUILD_ID: string | undefined;
    CONTEXT: "production" | "deploy-preview" | "branch-deploy" | "dev" | undefined;
    REPOSITORY_URL: string | undefined;
    BRANCH: string | undefined;
    URL: string | undefined;
    DEPLOY_URL: string | undefined;
    DEPLOY_PRIME_URL: string | undefined;
    DEPLOY_ID: string | undefined;
    SITE_NAME: string | undefined;
    SITE_ID: string | undefined;
}>;

export { fly, neonVercel, netlify, railway, render, uploadthing, uploadthingV6, vercel };
