{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/CategoryFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\n\r\ninterface CategoryFilterProps {\r\n  selectedCategories: string[]\r\n  onCategoryChange: (categoryIds: string[]) => void\r\n  showCounts?: boolean\r\n  allowMultiple?: boolean\r\n  placeholder?: string\r\n  className?: string\r\n}\r\n\r\nexport const CategoryFilter = ({\r\n  selectedCategories,\r\n  onCategoryChange,\r\n  showCounts = true,\r\n  allowMultiple = true,\r\n  placeholder = \"选择分类...\",\r\n  className = '',\r\n}: CategoryFilterProps) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n\r\n  // 获取所有分类\r\n  const { data: categories, isLoading } = api.categories.getAll.useQuery()\r\n\r\n  // 过滤分类\r\n  const filteredCategories = categories?.filter(category =>\r\n    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))\r\n  ) || []\r\n\r\n  // 处理分类选择\r\n  const handleCategoryToggle = (categoryId: string) => {\r\n    if (allowMultiple) {\r\n      if (selectedCategories.includes(categoryId)) {\r\n        onCategoryChange(selectedCategories.filter(id => id !== categoryId))\r\n      } else {\r\n        onCategoryChange([...selectedCategories, categoryId])\r\n      }\r\n    } else {\r\n      if (selectedCategories.includes(categoryId)) {\r\n        onCategoryChange([])\r\n      } else {\r\n        onCategoryChange([categoryId])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 清除所有选择\r\n  const clearAll = () => {\r\n    onCategoryChange([])\r\n  }\r\n\r\n  // 获取选中的分类名称\r\n  const getSelectedCategoryNames = () => {\r\n    if (!categories) return []\r\n    return categories\r\n      .filter(cat => selectedCategories.includes(cat.id))\r\n      .map(cat => cat.name)\r\n  }\r\n\r\n  // 显示文本\r\n  const getDisplayText = () => {\r\n    const selectedNames = getSelectedCategoryNames()\r\n    if (selectedNames.length === 0) return placeholder\r\n    if (selectedNames.length === 1) return selectedNames[0]\r\n    return `${selectedNames.length} 个分类`\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 触发按钮 */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 text-sm border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n      >\r\n        <span className={`truncate ${selectedCategories.length === 0 ? 'text-base-content/50' : 'text-base-content'}`}>\r\n          {getDisplayText()}\r\n        </span>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedCategories.length > 0 && (\r\n            <motion.button\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                clearAll()\r\n              }}\r\n              whileHover={{ scale: 1.1 }}\r\n              whileTap={{ scale: 0.9 }}\r\n              className=\"flex items-center justify-center w-4 h-4 bg-base-content/20 rounded-full hover:bg-base-content/30\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n          \r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        </div>\r\n      </button>\r\n\r\n      {/* 下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden\"\r\n          >\r\n            {/* 搜索框 */}\r\n            <div className=\"p-3 border-b border-base-300\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"搜索分类...\"\r\n                  className=\"w-full pl-8 pr-4 py-2 text-sm border border-base-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n                />\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4 absolute left-2.5 top-2.5 text-base-content/50\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 分类列表 */}\r\n            <div className=\"max-h-64 overflow-y-auto\">\r\n              {isLoading ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  <span className=\"ml-2\">加载中...</span>\r\n                </div>\r\n              ) : filteredCategories.length === 0 ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  {searchTerm ? '未找到匹配的分类' : '暂无分类'}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-2 space-y-1\">\r\n                  {filteredCategories.map((category) => (\r\n                    <motion.label\r\n                      key={category.id}\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200 cursor-pointer\"\r\n                    >\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type={allowMultiple ? 'checkbox' : 'radio'}\r\n                          name=\"category\"\r\n                          checked={selectedCategories.includes(category.id)}\r\n                          onChange={() => handleCategoryToggle(category.id)}\r\n                          className=\"sr-only\"\r\n                        />\r\n                        <div\r\n                          className={`w-4 h-4 rounded ${\r\n                            allowMultiple ? 'rounded-sm' : 'rounded-full'\r\n                          } border-2 transition-colors ${\r\n                            selectedCategories.includes(category.id)\r\n                              ? 'bg-primary border-primary'\r\n                              : 'border-base-300'\r\n                          }`}\r\n                        >\r\n                          {selectedCategories.includes(category.id) && (\r\n                            <svg\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                              fill=\"none\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              strokeWidth={3}\r\n                              stroke=\"currentColor\"\r\n                              className=\"w-3 h-3 text-primary-content\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M4.5 12.75l6 6 9-13.5\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 分类图标 */}\r\n                      <div\r\n                        className=\"w-6 h-6 rounded flex items-center justify-center text-white text-xs font-semibold\"\r\n                        style={{ backgroundColor: category.color }}\r\n                      >\r\n                        {category.icon || category.name.charAt(0)}\r\n                      </div>\r\n\r\n                      {/* 分类信息 */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"text-sm font-medium text-base-content truncate\">\r\n                          {category.name}\r\n                        </p>\r\n                        {category.description && (\r\n                          <p className=\"text-xs text-base-content/70 truncate\">\r\n                            {category.description}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* 统计信息 */}\r\n                      {showCounts && (\r\n                        <div className=\"flex items-center space-x-2 text-xs text-base-content/50\">\r\n                          <span>{category.promptCount || 0}</span>\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth={1.5}\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-3 h-3\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                            />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </motion.label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 底部操作 */}\r\n            {selectedCategories.length > 0 && (\r\n              <div className=\"p-3 border-t border-base-300 bg-base-50\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectedCategories.length} 个分类\r\n                  </span>\r\n                  <button\r\n                    onClick={clearAll}\r\n                    className=\"text-sm text-primary hover:text-primary-focus\"\r\n                  >\r\n                    清除全部\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 点击外部关闭 */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAgBO,MAAM,iBAAiB,CAAC,EAC7B,kBAAkB,EAClB,gBAAgB,EAChB,aAAa,IAAI,EACjB,gBAAgB,IAAI,EACpB,cAAc,SAAS,EACvB,YAAY,EAAE,EACM;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAEtE,OAAO;IACP,MAAM,qBAAqB,YAAY,OAAO,CAAA,WAC5C,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QACxF,EAAE;IAEP,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe;YACjB,IAAI,mBAAmB,QAAQ,CAAC,aAAa;gBAC3C,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO;YAC1D,OAAO;gBACL,iBAAiB;uBAAI;oBAAoB;iBAAW;YACtD;QACF,OAAO;YACL,IAAI,mBAAmB,QAAQ,CAAC,aAAa;gBAC3C,iBAAiB,EAAE;YACrB,OAAO;gBACL,iBAAiB;oBAAC;iBAAW;YAC/B;QACF;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,YAAY;IACZ,MAAM,2BAA2B;QAC/B,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,OAAO,WACJ,MAAM,CAAC,CAAA,MAAO,mBAAmB,QAAQ,CAAC,IAAI,EAAE,GAChD,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IACxB;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,MAAM,gBAAgB;QACtB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,aAAa,CAAC,EAAE;QACvD,OAAO,GAAG,cAAc,MAAM,CAAC,IAAI,CAAC;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAK,WAAW,CAAC,SAAS,EAAE,mBAAmB,MAAM,KAAK,IAAI,yBAAyB,qBAAqB;kCAC1G;;;;;;kCAGH,8OAAC;wBAAI,WAAU;;4BACZ,mBAAmB,MAAM,GAAG,mBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;0CAK3D,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;0CAEvE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;2EAEvB,mBAAmB,MAAM,KAAK,kBAChC,8OAAC;gCAAI,WAAU;0CACZ,aAAa,aAAa;;;;;yFAG7B,8OAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;wCAEX,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAM,gBAAgB,aAAa;wDACnC,MAAK;wDACL,SAAS,mBAAmB,QAAQ,CAAC,SAAS,EAAE;wDAChD,UAAU,IAAM,qBAAqB,SAAS,EAAE;wDAChD,WAAU;;;;;;kEAEZ,8OAAC;wDACC,WAAW,CAAC,gBAAgB,EAC1B,gBAAgB,eAAe,eAChC,4BAA4B,EAC3B,mBAAmB,QAAQ,CAAC,SAAS,EAAE,IACnC,8BACA,mBACJ;kEAED,mBAAmB,QAAQ,CAAC,SAAS,EAAE,mBACtC,8OAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAQZ,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;0DAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,SAAS,IAAI;;;;;;oDAEf,SAAS,WAAW,kBACnB,8OAAC;wDAAE,WAAU;kEACV,SAAS,WAAW;;;;;;;;;;;;4CAM1B,4BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,SAAS,WAAW,IAAI;;;;;;kEAC/B,8OAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;;;;;;;;uCA5EL,SAAS,EAAE;;;;;;;;;;;;;;;wBAwFzB,mBAAmB,MAAM,GAAG,mBAC3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAA+B;4CACxC,mBAAmB,MAAM;4CAAC;;;;;;;kDAEjC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/TagFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Tag } from '~/types'\r\n\r\ninterface TagFilterProps {\r\n  selectedTags: string[]\r\n  onTagChange: (tagIds: string[]) => void\r\n  showCounts?: boolean\r\n  allowMultiple?: boolean\r\n  placeholder?: string\r\n  className?: string\r\n}\r\n\r\nexport const TagFilter = ({\r\n  selectedTags,\r\n  onTagChange,\r\n  showCounts = true,\r\n  allowMultiple = true,\r\n  placeholder = \"选择标签...\",\r\n  className = '',\r\n}: TagFilterProps) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n\r\n  // 获取所有标签\r\n  const { data: tags, isLoading } = api.tags.getAll.useQuery()\r\n\r\n  // 过滤标签\r\n  const filteredTags = tags?.filter(tag =>\r\n    tag.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  ) || []\r\n\r\n  // 处理标签选择\r\n  const handleTagToggle = (tagId: string) => {\r\n    if (allowMultiple) {\r\n      if (selectedTags.includes(tagId)) {\r\n        onTagChange(selectedTags.filter(id => id !== tagId))\r\n      } else {\r\n        onTagChange([...selectedTags, tagId])\r\n      }\r\n    } else {\r\n      if (selectedTags.includes(tagId)) {\r\n        onTagChange([])\r\n      } else {\r\n        onTagChange([tagId])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 清除所有选择\r\n  const clearAll = () => {\r\n    onTagChange([])\r\n  }\r\n\r\n  // 获取选中的标签名称\r\n  const getSelectedTagNames = () => {\r\n    if (!tags) return []\r\n    return tags\r\n      .filter(tag => selectedTags.includes(tag.id))\r\n      .map(tag => tag.name)\r\n  }\r\n\r\n  // 显示文本\r\n  const getDisplayText = () => {\r\n    const selectedNames = getSelectedTagNames()\r\n    if (selectedNames.length === 0) return placeholder\r\n    if (selectedNames.length === 1) return selectedNames[0]\r\n    return `${selectedNames.length} 个标签`\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 触发按钮 */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 text-sm border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n      >\r\n        <span className={`truncate ${selectedTags.length === 0 ? 'text-base-content/50' : 'text-base-content'}`}>\r\n          {getDisplayText()}\r\n        </span>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedTags.length > 0 && (\r\n            <motion.button\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                clearAll()\r\n              }}\r\n              whileHover={{ scale: 1.1 }}\r\n              whileTap={{ scale: 0.9 }}\r\n              className=\"flex items-center justify-center w-4 h-4 bg-base-content/20 rounded-full hover:bg-base-content/30\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n          \r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        </div>\r\n      </button>\r\n\r\n      {/* 下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden\"\r\n          >\r\n            {/* 搜索框 */}\r\n            <div className=\"p-3 border-b border-base-300\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"搜索标签...\"\r\n                  className=\"w-full pl-8 pr-4 py-2 text-sm border border-base-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n                />\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4 absolute left-2.5 top-2.5 text-base-content/50\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 标签列表 */}\r\n            <div className=\"max-h-64 overflow-y-auto\">\r\n              {isLoading ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  <span className=\"ml-2\">加载中...</span>\r\n                </div>\r\n              ) : filteredTags.length === 0 ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  {searchTerm ? '未找到匹配的标签' : '暂无标签'}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-2 space-y-1\">\r\n                  {filteredTags.map((tag) => (\r\n                    <motion.label\r\n                      key={tag.id}\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200 cursor-pointer\"\r\n                    >\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type={allowMultiple ? 'checkbox' : 'radio'}\r\n                          name=\"tag\"\r\n                          checked={selectedTags.includes(tag.id)}\r\n                          onChange={() => handleTagToggle(tag.id)}\r\n                          className=\"sr-only\"\r\n                        />\r\n                        <div\r\n                          className={`w-4 h-4 rounded ${\r\n                            allowMultiple ? 'rounded-sm' : 'rounded-full'\r\n                          } border-2 transition-colors ${\r\n                            selectedTags.includes(tag.id)\r\n                              ? 'bg-primary border-primary'\r\n                              : 'border-base-300'\r\n                          }`}\r\n                        >\r\n                          {selectedTags.includes(tag.id) && (\r\n                            <svg\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                              fill=\"none\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              strokeWidth={3}\r\n                              stroke=\"currentColor\"\r\n                              className=\"w-3 h-3 text-primary-content\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M4.5 12.75l6 6 9-13.5\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 标签图标 */}\r\n                      <div className=\"flex items-center justify-center w-6 h-6 bg-primary/10 rounded-full\">\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          strokeWidth={1.5}\r\n                          stroke=\"currentColor\"\r\n                          className=\"w-3 h-3 text-primary\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n                          />\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n                        </svg>\r\n                      </div>\r\n\r\n                      {/* 标签信息 */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"text-sm font-medium text-base-content truncate\">\r\n                          {tag.name}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* 统计信息 */}\r\n                      {showCounts && (\r\n                        <div className=\"flex items-center space-x-1 text-xs text-base-content/50\">\r\n                          <span>{tag.promptCount || 0}</span>\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth={1.5}\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-3 h-3\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                            />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </motion.label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 底部操作 */}\r\n            {selectedTags.length > 0 && (\r\n              <div className=\"p-3 border-t border-base-300 bg-base-50\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectedTags.length} 个标签\r\n                  </span>\r\n                  <button\r\n                    onClick={clearAll}\r\n                    className=\"text-sm text-primary hover:text-primary-focus\"\r\n                  >\r\n                    清除全部\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 点击外部关闭 */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAgBO,MAAM,YAAY,CAAC,EACxB,YAAY,EACZ,WAAW,EACX,aAAa,IAAI,EACjB,gBAAgB,IAAI,EACpB,cAAc,SAAS,EACvB,YAAY,EAAE,EACC;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAE1D,OAAO;IACP,MAAM,eAAe,MAAM,OAAO,CAAA,MAChC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QACnD,EAAE;IAEP,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe;YACjB,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,YAAY,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YAC/C,OAAO;gBACL,YAAY;uBAAI;oBAAc;iBAAM;YACtC;QACF,OAAO;YACL,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,YAAY,EAAE;YAChB,OAAO;gBACL,YAAY;oBAAC;iBAAM;YACrB;QACF;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,YAAY,EAAE;IAChB;IAEA,YAAY;IACZ,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,OAAO,EAAE;QACpB,OAAO,KACJ,MAAM,CAAC,CAAA,MAAO,aAAa,QAAQ,CAAC,IAAI,EAAE,GAC1C,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IACxB;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,MAAM,gBAAgB;QACtB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,aAAa,CAAC,EAAE;QACvD,OAAO,GAAG,cAAc,MAAM,CAAC,IAAI,CAAC;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAK,WAAW,CAAC,SAAS,EAAE,aAAa,MAAM,KAAK,IAAI,yBAAyB,qBAAqB;kCACpG;;;;;;kCAGH,8OAAC;wBAAI,WAAU;;4BACZ,aAAa,MAAM,GAAG,mBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;0CAK3D,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;0CAEvE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;2EAEvB,aAAa,MAAM,KAAK,kBAC1B,8OAAC;gCAAI,WAAU;0CACZ,aAAa,aAAa;;;;;yFAG7B,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;wCAEX,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAM,gBAAgB,aAAa;wDACnC,MAAK;wDACL,SAAS,aAAa,QAAQ,CAAC,IAAI,EAAE;wDACrC,UAAU,IAAM,gBAAgB,IAAI,EAAE;wDACtC,WAAU;;;;;;kEAEZ,8OAAC;wDACC,WAAW,CAAC,gBAAgB,EAC1B,gBAAgB,eAAe,eAChC,4BAA4B,EAC3B,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,8BACA,mBACJ;kEAED,aAAa,QAAQ,CAAC,IAAI,EAAE,mBAC3B,8OAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAQZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAU;;sEAEV,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;sEAEJ,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,GAAE;;;;;;;;;;;;;;;;;0DAKzD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DACV,IAAI,IAAI;;;;;;;;;;;4CAKZ,4BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,IAAI,WAAW,IAAI;;;;;;kEAC1B,8OAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;;;;;;;;uCAlFL,IAAI,EAAE;;;;;;;;;;;;;;;wBA8FpB,aAAa,MAAM,GAAG,mBACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAA+B;4CACxC,aAAa,MAAM;4CAAC;;;;;;;kDAE3B,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/FilterBar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { CategoryFilter } from './CategoryFilter'\r\nimport { TagFilter } from './TagFilter'\r\n\r\ninterface FilterBarProps {\r\n  // 分类筛选\r\n  selectedCategories: string[]\r\n  onCategoryChange: (categoryIds: string[]) => void\r\n  \r\n  // 标签筛选\r\n  selectedTags: string[]\r\n  onTagChange: (tagIds: string[]) => void\r\n  \r\n  // 排序\r\n  sortBy: 'created' | 'updated' | 'usage' | 'title'\r\n  sortOrder: 'asc' | 'desc'\r\n  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void\r\n  \r\n  // 其他筛选\r\n  showFavorites?: boolean\r\n  onShowFavoritesChange?: (showFavorites: boolean) => void\r\n  \r\n  // 显示控制\r\n  showCategoryFilter?: boolean\r\n  showTagFilter?: boolean\r\n  showSortFilter?: boolean\r\n  showFavoriteFilter?: boolean\r\n  \r\n  className?: string\r\n}\r\n\r\nexport const FilterBar = ({\r\n  selectedCategories,\r\n  onCategoryChange,\r\n  selectedTags,\r\n  onTagChange,\r\n  sortBy,\r\n  sortOrder,\r\n  onSortChange,\r\n  showFavorites = false,\r\n  onShowFavoritesChange,\r\n  showCategoryFilter = true,\r\n  showTagFilter = true,\r\n  showSortFilter = true,\r\n  showFavoriteFilter = true,\r\n  className = '',\r\n}: FilterBarProps) => {\r\n  const [isExpanded, setIsExpanded] = useState(false)\r\n\r\n  // 排序选项\r\n  const sortOptions = [\r\n    { value: 'updated-desc', label: '最近更新', sortBy: 'updated', sortOrder: 'desc' },\r\n    { value: 'created-desc', label: '最新创建', sortBy: 'created', sortOrder: 'desc' },\r\n    { value: 'usage-desc', label: '使用最多', sortBy: 'usage', sortOrder: 'desc' },\r\n    { value: 'title-asc', label: '标题 A-Z', sortBy: 'title', sortOrder: 'asc' },\r\n    { value: 'title-desc', label: '标题 Z-A', sortBy: 'title', sortOrder: 'desc' },\r\n    { value: 'created-asc', label: '最早创建', sortBy: 'created', sortOrder: 'asc' },\r\n    { value: 'updated-asc', label: '最早更新', sortBy: 'updated', sortOrder: 'asc' },\r\n    { value: 'usage-asc', label: '使用最少', sortBy: 'usage', sortOrder: 'asc' },\r\n  ]\r\n\r\n  // 处理排序变化\r\n  const handleSortChange = (value: string) => {\r\n    const option = sortOptions.find(opt => opt.value === value)\r\n    if (option) {\r\n      onSortChange(option.sortBy, option.sortOrder as 'asc' | 'desc')\r\n    }\r\n  }\r\n\r\n  // 清除所有筛选\r\n  const clearAllFilters = () => {\r\n    onCategoryChange([])\r\n    onTagChange([])\r\n    if (onShowFavoritesChange) {\r\n      onShowFavoritesChange(false)\r\n    }\r\n  }\r\n\r\n  // 检查是否有活跃筛选\r\n  const hasActiveFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n      {/* 主筛选栏 */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 分类筛选 */}\r\n          {showCategoryFilter && (\r\n            <CategoryFilter\r\n              selectedCategories={selectedCategories}\r\n              onCategoryChange={onCategoryChange}\r\n              className=\"w-48\"\r\n            />\r\n          )}\r\n\r\n          {/* 标签筛选 */}\r\n          {showTagFilter && (\r\n            <TagFilter\r\n              selectedTags={selectedTags}\r\n              onTagChange={onTagChange}\r\n              className=\"w-48\"\r\n            />\r\n          )}\r\n\r\n          {/* 收藏筛选 */}\r\n          {showFavoriteFilter && onShowFavoritesChange && (\r\n            <motion.button\r\n              onClick={() => onShowFavoritesChange(!showFavorites)}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className={`flex items-center space-x-2 px-3 py-2 text-sm rounded-lg border transition-colors ${\r\n                showFavorites\r\n                  ? 'bg-warning text-warning-content border-warning'\r\n                  : 'bg-base-100 text-base-content border-base-300 hover:bg-base-200'\r\n              }`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill={showFavorites ? 'currentColor' : 'none'}\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\r\n                />\r\n              </svg>\r\n              <span>收藏</span>\r\n            </motion.button>\r\n          )}\r\n\r\n          {/* 展开/收起高级筛选 */}\r\n          <motion.button\r\n            onClick={() => setIsExpanded(!isExpanded)}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm text-base-content/70 hover:text-base-content\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 12H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 18H7.5\"\r\n              />\r\n            </svg>\r\n            <span>高级筛选</span>\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n            </svg>\r\n          </motion.button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 排序选择 */}\r\n          {showSortFilter && (\r\n            <select\r\n              value={`${sortBy}-${sortOrder}`}\r\n              onChange={(e) => handleSortChange(e.target.value)}\r\n              className=\"select select-bordered select-sm\"\r\n            >\r\n              {sortOptions.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n\r\n          {/* 清除筛选 */}\r\n          {hasActiveFilters && (\r\n            <motion.button\r\n              onClick={clearAllFilters}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"flex items-center space-x-2 px-3 py-2 text-sm text-base-content/70 hover:text-base-content\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              <span>清除筛选</span>\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 高级筛选面板 */}\r\n      <AnimatePresence>\r\n        {isExpanded && (\r\n          <motion.div\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: 'auto' }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"overflow-hidden\"\r\n          >\r\n            <div className=\"bg-base-100 border border-base-300 rounded-lg p-4 space-y-4\">\r\n              <h3 className=\"text-sm font-medium text-base-content\">高级筛选选项</h3>\r\n              \r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                {/* 更多筛选选项可以在这里添加 */}\r\n                \r\n                {/* 日期范围筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">创建日期</label>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"开始日期\"\r\n                    />\r\n                    <span className=\"text-base-content/50\">至</span>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"结束日期\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* 使用次数筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">使用次数</label>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"最少\"\r\n                    />\r\n                    <span className=\"text-base-content/50\">至</span>\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"最多\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* 内容长度筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">内容长度</label>\r\n                  <select className=\"select select-bordered select-sm w-full\">\r\n                    <option value=\"\">全部</option>\r\n                    <option value=\"short\">短 (少于100字)</option>\r\n                    <option value=\"medium\">中 (100-500字)</option>\r\n                    <option value=\"long\">长 (超过500字)</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 应用/重置按钮 */}\r\n              <div className=\"flex justify-end space-x-2\">\r\n                <button className=\"btn btn-ghost btn-sm\">重置</button>\r\n                <button className=\"btn btn-primary btn-sm\">应用筛选</button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 活跃筛选标签 */}\r\n      {hasActiveFilters && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"flex items-center space-x-2 text-sm\"\r\n        >\r\n          <span className=\"text-base-content/70\">活跃筛选:</span>\r\n          \r\n          {/* 分类标签 */}\r\n          {selectedCategories.length > 0 && (\r\n            <span className=\"badge badge-primary badge-sm\">\r\n              {selectedCategories.length} 个分类\r\n            </span>\r\n          )}\r\n          \r\n          {/* 标签标签 */}\r\n          {selectedTags.length > 0 && (\r\n            <span className=\"badge badge-secondary badge-sm\">\r\n              {selectedTags.length} 个标签\r\n            </span>\r\n          )}\r\n          \r\n          {/* 收藏标签 */}\r\n          {showFavorites && (\r\n            <span className=\"badge badge-warning badge-sm\">\r\n              仅收藏\r\n            </span>\r\n          )}\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAkCO,MAAM,YAAY,CAAC,EACxB,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,YAAY,EACZ,gBAAgB,KAAK,EACrB,qBAAqB,EACrB,qBAAqB,IAAI,EACzB,gBAAgB,IAAI,EACpB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,YAAY,EAAE,EACC;IACf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,OAAO;IACP,MAAM,cAAc;QAClB;YAAE,OAAO;YAAgB,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAO;QAC7E;YAAE,OAAO;YAAgB,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAO;QAC7E;YAAE,OAAO;YAAc,OAAO;YAAQ,QAAQ;YAAS,WAAW;QAAO;QACzE;YAAE,OAAO;YAAa,OAAO;YAAU,QAAQ;YAAS,WAAW;QAAM;QACzE;YAAE,OAAO;YAAc,OAAO;YAAU,QAAQ;YAAS,WAAW;QAAO;QAC3E;YAAE,OAAO;YAAe,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAM;QAC3E;YAAE,OAAO;YAAe,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAM;QAC3E;YAAE,OAAO;YAAa,OAAO;YAAQ,QAAQ;YAAS,WAAW;QAAM;KACxE;IAED,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACrD,IAAI,QAAQ;YACV,aAAa,OAAO,MAAM,EAAE,OAAO,SAAS;QAC9C;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,iBAAiB,EAAE;QACnB,YAAY,EAAE;QACd,IAAI,uBAAuB;YACzB,sBAAsB;QACxB;IACF;IAEA,YAAY;IACZ,MAAM,mBAAmB,mBAAmB,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;IAErF,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAEZ,oCACC,8OAAC,+IAAA,CAAA,iBAAc;gCACb,oBAAoB;gCACpB,kBAAkB;gCAClB,WAAU;;;;;;4BAKb,+BACC,8OAAC,0IAAA,CAAA,YAAS;gCACR,cAAc;gCACd,aAAa;gCACb,WAAU;;;;;;4BAKb,sBAAsB,uCACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,sBAAsB,CAAC;gCACtC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAW,CAAC,kFAAkF,EAC5F,gBACI,mDACA,mEACJ;;kDAEF,8OAAC;wCACC,OAAM;wCACN,MAAM,gBAAgB,iBAAiB;wCACvC,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,8OAAC;kDAAK;;;;;;;;;;;;0CAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,cAAc,CAAC;gCAC9B,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAW,CAAC,6BAA6B,EAAE,aAAa,eAAe,IAAI;kDAE3E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAK3D,8OAAC;wBAAI,WAAU;;4BAEZ,gCACC,8OAAC;gCACC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;gCAC/B,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;0CAET,YAAY,GAAG,CAAC,CAAA,uBACf,8OAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;4BAQ9B,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;kDAEvD,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDAIb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;kEACvC,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;kEACvC,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAM3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAuB;;;;;;kDACzC,8OAAC;wCAAO,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpD,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAK,WAAU;kCAAuB;;;;;;oBAGtC,mBAAmB,MAAM,GAAG,mBAC3B,8OAAC;wBAAK,WAAU;;4BACb,mBAAmB,MAAM;4BAAC;;;;;;;oBAK9B,aAAa,MAAM,GAAG,mBACrB,8OAAC;wBAAK,WAAU;;4BACb,aAAa,MAAM;4BAAC;;;;;;;oBAKxB,+BACC,8OAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;AAQ3D", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/index.ts"], "sourcesContent": ["export { CategoryFilter } from './CategoryFilter'\r\nexport { TagFilter } from './TagFilter'\r\nexport { FilterBar } from './FilterBar'"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { PromptGrid } from \"~/components/prompts\";\nimport { FilterBar } from \"~/components/filters\";\nimport { api } from \"~/trpc/react\";\n\nexport default function Home() {\n  // 筛选状态\n  const [selectedCategories, setSelectedCategories] = useState<string[]>([])\n  const [selectedTags, setSelectedTags] = useState<string[]>([])\n  const [sortBy, setSortBy] = useState<'created' | 'updated' | 'usage' | 'title'>('updated')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [showFavorites, setShowFavorites] = useState(false)\n\n  // 获取筛选后的提示词\n  const { data: promptsData, isLoading } = api.prompts.getFiltered.useQuery({\n    categoryIds: selectedCategories.length > 0 ? selectedCategories : undefined,\n    tagIds: selectedTags.length > 0 ? selectedTags : undefined,\n    sortBy,\n    sortOrder,\n    favoritesOnly: showFavorites,\n    limit: 12,\n  })\n\n  const prompts = promptsData?.prompts || []\n\n  // 获取最近使用的提示词（不受筛选影响）\n  const { data: recentPrompts, isLoading: recentLoading } = api.prompts.getRecent.useQuery({\n    limit: 6,\n  })\n\n  const { data: stats } = api.prompts.getStats.useQuery()\n\n  // 处理排序变化\n  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {\n    setSortBy(newSortBy as 'created' | 'updated' | 'usage' | 'title')\n    setSortOrder(newSortOrder)\n  }\n\n  // 检查是否有筛选条件\n  const hasFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites\n\n  return (\n    <div className=\"space-y-6\">\n        {/* 欢迎横幅 */}\n        <div className=\"bg-gradient-to-r from-primary to-secondary text-primary-content rounded-lg p-6\">\n          <h1 className=\"text-3xl font-bold mb-2\">欢迎使用提示词管理工具</h1>\n          <p className=\"text-primary-content/90\">\n            管理你的 AI 提示词，提升工作效率\n          </p>\n        </div>\n\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-primary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">提示词总数</div>\n            <div className=\"stat-value text-primary\">{stats?.totalPrompts || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalPrompts ? '个人收藏' : '等待数据加载'}</div>\n          </div>\n\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-secondary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">分类数量</div>\n            <div className=\"stat-value text-secondary\">{stats?.totalCategories || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalCategories ? '个分类' : '等待数据加载'}</div>\n          </div>\n\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-accent\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">收藏数量</div>\n            <div className=\"stat-value text-accent\">{stats?.totalFavorites || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalFavorites ? '个收藏' : '等待数据加载'}</div>\n          </div>\n\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-warning\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">总使用次数</div>\n            <div className=\"stat-value text-warning\">{stats?.totalUsage || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalUsage ? '次使用' : '等待数据加载'}</div>\n          </div>\n        </div>\n\n        {/* 快速操作 */}\n        <div className=\"bg-base-100 rounded-lg p-6 shadow\">\n          <h2 className=\"text-xl font-semibold mb-4\">快速操作</h2>\n          <div className=\"flex flex-wrap gap-4\">\n            <a href=\"/prompts/new\" className=\"btn btn-primary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-5 h-5\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              新建提示词\n            </a>\n            <a href=\"/categories\" className=\"btn btn-secondary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-5 h-5\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\n                />\n              </svg>\n              新建分类\n            </a>\n            <a href=\"/prompts/import\" className=\"btn btn-accent\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-5 h-5\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5\"\n                />\n              </svg>\n              批量导入\n            </a>\n          </div>\n        </div>\n\n        {/* 筛选和浏览 */}\n        <div className=\"bg-base-100 rounded-lg p-6 shadow\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-xl font-semibold\">\n              {hasFilters ? '筛选结果' : '浏览提示词'}\n            </h2>\n            <a href=\"/prompts\" className=\"text-sm text-primary hover:text-primary-focus\">\n              查看全部\n            </a>\n          </div>\n          \n          {/* 筛选栏 */}\n          <div className=\"mb-6\">\n            <FilterBar\n              selectedCategories={selectedCategories}\n              onCategoryChange={setSelectedCategories}\n              selectedTags={selectedTags}\n              onTagChange={setSelectedTags}\n              sortBy={sortBy}\n              sortOrder={sortOrder}\n              onSortChange={handleSortChange}\n              showFavorites={showFavorites}\n              onShowFavoritesChange={setShowFavorites}\n            />\n          </div>\n\n          {/* 提示词网格 */}\n          <PromptGrid\n            prompts={prompts}\n            loading={isLoading}\n            columns={3}\n            showCategory={true}\n            showActions={true}\n            emptyMessage={hasFilters ? \"未找到符合条件的提示词\" : \"暂无提示词\"}\n          />\n        </div>\n\n        {/* 最近使用 */}\n        {!hasFilters && (\n          <div className=\"bg-base-100 rounded-lg p-6 shadow\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-semibold\">最近使用</h2>\n              <a href=\"/prompts\" className=\"text-sm text-primary hover:text-primary-focus\">\n                查看全部\n              </a>\n            </div>\n            <PromptGrid\n              prompts={recentPrompts || []}\n              loading={recentLoading}\n              columns={3}\n              showCategory={true}\n              showActions={true}\n              emptyMessage=\"暂无最近使用的提示词\"\n            />\n          </div>\n        )}\n      </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,OAAO;IACP,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,YAAY;IACZ,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC;QACxE,aAAa,mBAAmB,MAAM,GAAG,IAAI,qBAAqB;QAClE,QAAQ,aAAa,MAAM,GAAG,IAAI,eAAe;QACjD;QACA;QACA,eAAe;QACf,OAAO;IACT;IAEA,MAAM,UAAU,aAAa,WAAW,EAAE;IAE1C,qBAAqB;IACrB,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,aAAa,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvF,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ;IAErD,SAAS;IACT,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,UAAU;QACV,aAAa;IACf;IAEA,YAAY;IACZ,MAAM,aAAa,mBAAmB,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;IAE/E,qBACE,8OAAC;QAAI,WAAU;;0BAEX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAY;wCACZ,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;0CAA2B,OAAO,gBAAgB;;;;;;0CACjE,8OAAC;gCAAI,WAAU;0CAAa,OAAO,eAAe,SAAS;;;;;;;;;;;;kCAG7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAY;wCACZ,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;0CAA6B,OAAO,mBAAmB;;;;;;0CACtE,8OAAC;gCAAI,WAAU;0CAAa,OAAO,kBAAkB,QAAQ;;;;;;;;;;;;kCAG/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAY;wCACZ,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;0CAA0B,OAAO,kBAAkB;;;;;;0CAClE,8OAAC;gCAAI,WAAU;0CAAa,OAAO,iBAAiB,QAAQ;;;;;;;;;;;;kCAG9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAY;wCACZ,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;0CAA2B,OAAO,cAAc;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;0CAAa,OAAO,aAAa,QAAQ;;;;;;;;;;;;;;;;;;0BAK5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAe,WAAU;;kDAC/B,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;0CAGR,8OAAC;gCAAE,MAAK;gCAAc,WAAU;;kDAC9B,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;0CAGR,8OAAC;gCAAE,MAAK;gCAAkB,WAAU;;kDAClC,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;oCAEA;;;;;;;;;;;;;;;;;;;0BAOZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,aAAa,SAAS;;;;;;0CAEzB,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAgD;;;;;;;;;;;;kCAM/E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,YAAS;4BACR,oBAAoB;4BACpB,kBAAkB;4BAClB,cAAc;4BACd,aAAa;4BACb,QAAQ;4BACR,WAAW;4BACX,cAAc;4BACd,eAAe;4BACf,uBAAuB;;;;;;;;;;;kCAK3B,8OAAC,2IAAA,CAAA,aAAU;wBACT,SAAS;wBACT,SAAS;wBACT,SAAS;wBACT,cAAc;wBACd,aAAa;wBACb,cAAc,aAAa,gBAAgB;;;;;;;;;;;;YAK9C,CAAC,4BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAgD;;;;;;;;;;;;kCAI/E,8OAAC,2IAAA,CAAA,aAAU;wBACT,SAAS,iBAAiB,EAAE;wBAC5B,SAAS;wBACT,SAAS;wBACT,cAAc;wBACd,aAAa;wBACb,cAAa;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}]}