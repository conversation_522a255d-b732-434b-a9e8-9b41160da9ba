{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/stores/index.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\nimport { StoreState, UIState, LoadingState, ErrorState } from '~/types'\r\n\r\n// 初始状态\r\nconst initialUIState: UIState = {\r\n  sidebarOpen: true,\r\n  currentView: 'grid',\r\n  theme: 'light',\r\n  selectedCategory: null,\r\n  selectedTags: [],\r\n  searchQuery: '',\r\n}\r\n\r\nconst initialLoadingState: LoadingState = {\r\n  categories: false,\r\n  prompts: false,\r\n  tags: false,\r\n  search: false,\r\n  creating: false,\r\n  updating: false,\r\n  deleting: false,\r\n}\r\n\r\nconst initialErrorState: ErrorState = {\r\n  categories: null,\r\n  prompts: null,\r\n  tags: null,\r\n  search: null,\r\n  general: null,\r\n}\r\n\r\n// 主 Store 接口\r\nexport interface MainStore extends StoreState {\r\n  // UI 操作\r\n  toggleSidebar: () => void\r\n  setCurrentView: (view: 'grid' | 'list') => void\r\n  setTheme: (theme: 'light' | 'dark') => void\r\n  setSelectedCategory: (categoryId: string | null) => void\r\n  setSelectedTags: (tags: string[]) => void\r\n  setSearchQuery: (query: string) => void\r\n  \r\n  // 加载状态管理\r\n  setLoading: (key: keyof LoadingState, value: boolean) => void\r\n  \r\n  // 错误状态管理\r\n  setError: (key: keyof ErrorState, error: string | null) => void\r\n  clearErrors: () => void\r\n  \r\n  // 重置状态\r\n  resetState: () => void\r\n}\r\n\r\n// 创建主 Store\r\nexport const useMainStore = create<MainStore>()(\r\n  devtools(\r\n    persist(\r\n      immer((set, get) => ({\r\n        // 初始数据状态\r\n        categories: [],\r\n        prompts: [],\r\n        tags: [],\r\n        searchHistory: [],\r\n        searchResults: null,\r\n        searchSuggestions: null,\r\n        \r\n        // 初始 UI 状态\r\n        ui: initialUIState,\r\n        \r\n        // 初始加载状态\r\n        loading: initialLoadingState,\r\n        \r\n        // 初始错误状态\r\n        errors: initialErrorState,\r\n        \r\n        // 初始分页状态\r\n        pagination: {\r\n          prompts: {\r\n            offset: 0,\r\n            limit: 20,\r\n            total: 0,\r\n            hasMore: false,\r\n          },\r\n          search: {\r\n            offset: 0,\r\n            limit: 20,\r\n            total: 0,\r\n            hasMore: false,\r\n          },\r\n        },\r\n        \r\n        // UI 操作\r\n        toggleSidebar: () => set((state) => {\r\n          state.ui.sidebarOpen = !state.ui.sidebarOpen\r\n        }),\r\n        \r\n        setCurrentView: (view) => set((state) => {\r\n          state.ui.currentView = view\r\n        }),\r\n        \r\n        setTheme: (theme) => set((state) => {\r\n          state.ui.theme = theme\r\n        }),\r\n        \r\n        setSelectedCategory: (categoryId) => set((state) => {\r\n          state.ui.selectedCategory = categoryId\r\n        }),\r\n        \r\n        setSelectedTags: (tags) => set((state) => {\r\n          state.ui.selectedTags = tags\r\n        }),\r\n        \r\n        setSearchQuery: (query) => set((state) => {\r\n          state.ui.searchQuery = query\r\n        }),\r\n        \r\n        // 加载状态管理\r\n        setLoading: (key, value) => set((state) => {\r\n          state.loading[key] = value\r\n        }),\r\n        \r\n        // 错误状态管理\r\n        setError: (key, error) => set((state) => {\r\n          state.errors[key] = error\r\n        }),\r\n        \r\n        clearErrors: () => set((state) => {\r\n          state.errors = initialErrorState\r\n        }),\r\n        \r\n        // 重置状态\r\n        resetState: () => set((state) => {\r\n          state.categories = []\r\n          state.prompts = []\r\n          state.tags = []\r\n          state.searchHistory = []\r\n          state.searchResults = null\r\n          state.searchSuggestions = null\r\n          state.ui = initialUIState\r\n          state.loading = initialLoadingState\r\n          state.errors = initialErrorState\r\n          state.pagination = {\r\n            prompts: {\r\n              offset: 0,\r\n              limit: 20,\r\n              total: 0,\r\n              hasMore: false,\r\n            },\r\n            search: {\r\n              offset: 0,\r\n              limit: 20,\r\n              total: 0,\r\n              hasMore: false,\r\n            },\r\n          }\r\n        }),\r\n      })),\r\n      {\r\n        name: 'prompt-manager-store',\r\n        // 只持久化 UI 状态\r\n        partialize: (state) => ({\r\n          ui: {\r\n            sidebarOpen: state.ui.sidebarOpen,\r\n            currentView: state.ui.currentView,\r\n            theme: state.ui.theme,\r\n          },\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'prompt-manager-store',\r\n    }\r\n  )\r\n)\r\n\r\n// 导出 Store 类型\r\nexport type { MainStore }"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGA,OAAO;AACP,MAAM,iBAA0B;IAC9B,aAAa;IACb,aAAa;IACb,OAAO;IACP,kBAAkB;IAClB,cAAc,EAAE;IAChB,aAAa;AACf;AAEA,MAAM,sBAAoC;IACxC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;AACZ;AAEA,MAAM,oBAAgC;IACpC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,QAAQ;IACR,SAAS;AACX;AAwBO,MAAM,eAAe,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnB,SAAS;QACT,YAAY,EAAE;QACd,SAAS,EAAE;QACX,MAAM,EAAE;QACR,eAAe,EAAE;QACjB,eAAe;QACf,mBAAmB;QAEnB,WAAW;QACX,IAAI;QAEJ,SAAS;QACT,SAAS;QAET,SAAS;QACT,QAAQ;QAER,SAAS;QACT,YAAY;YACV,SAAS;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;YACA,QAAQ;gBACN,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;QACF;QAEA,QAAQ;QACR,eAAe,IAAM,IAAI,CAAC;gBACxB,MAAM,EAAE,CAAC,WAAW,GAAG,CAAC,MAAM,EAAE,CAAC,WAAW;YAC9C;QAEA,gBAAgB,CAAC,OAAS,IAAI,CAAC;gBAC7B,MAAM,EAAE,CAAC,WAAW,GAAG;YACzB;QAEA,UAAU,CAAC,QAAU,IAAI,CAAC;gBACxB,MAAM,EAAE,CAAC,KAAK,GAAG;YACnB;QAEA,qBAAqB,CAAC,aAAe,IAAI,CAAC;gBACxC,MAAM,EAAE,CAAC,gBAAgB,GAAG;YAC9B;QAEA,iBAAiB,CAAC,OAAS,IAAI,CAAC;gBAC9B,MAAM,EAAE,CAAC,YAAY,GAAG;YAC1B;QAEA,gBAAgB,CAAC,QAAU,IAAI,CAAC;gBAC9B,MAAM,EAAE,CAAC,WAAW,GAAG;YACzB;QAEA,SAAS;QACT,YAAY,CAAC,KAAK,QAAU,IAAI,CAAC;gBAC/B,MAAM,OAAO,CAAC,IAAI,GAAG;YACvB;QAEA,SAAS;QACT,UAAU,CAAC,KAAK,QAAU,IAAI,CAAC;gBAC7B,MAAM,MAAM,CAAC,IAAI,GAAG;YACtB;QAEA,aAAa,IAAM,IAAI,CAAC;gBACtB,MAAM,MAAM,GAAG;YACjB;QAEA,OAAO;QACP,YAAY,IAAM,IAAI,CAAC;gBACrB,MAAM,UAAU,GAAG,EAAE;gBACrB,MAAM,OAAO,GAAG,EAAE;gBAClB,MAAM,IAAI,GAAG,EAAE;gBACf,MAAM,aAAa,GAAG,EAAE;gBACxB,MAAM,aAAa,GAAG;gBACtB,MAAM,iBAAiB,GAAG;gBAC1B,MAAM,EAAE,GAAG;gBACX,MAAM,OAAO,GAAG;gBAChB,MAAM,MAAM,GAAG;gBACf,MAAM,UAAU,GAAG;oBACjB,SAAS;wBACP,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,SAAS;oBACX;oBACA,QAAQ;wBACN,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,SAAS;oBACX;gBACF;YACF;IACF,CAAC,IACD;IACE,MAAM;IACN,aAAa;IACb,YAAY,CAAC,QAAU,CAAC;YACtB,IAAI;gBACF,aAAa,MAAM,EAAE,CAAC,WAAW;gBACjC,aAAa,MAAM,EAAE,CAAC,WAAW;gBACjC,OAAO,MAAM,EAAE,CAAC,KAAK;YACvB;QACF,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/SearchInput.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect, useRef } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { useRouter } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface SearchInputProps {\r\n  placeholder?: string\r\n  onSearch?: (query: string) => void\r\n  onClear?: () => void\r\n  showHistory?: boolean\r\n  showSuggestions?: boolean\r\n  size?: 'sm' | 'md' | 'lg'\r\n  className?: string\r\n}\r\n\r\nexport const SearchInput = ({\r\n  placeholder = '搜索提示词...',\r\n  onSearch,\r\n  onClear,\r\n  showHistory = true,\r\n  showSuggestions = true,\r\n  size = 'md',\r\n  className = '',\r\n}: SearchInputProps) => {\r\n  const router = useRouter()\r\n  const [query, setQuery] = useState('')\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [selectedIndex, setSelectedIndex] = useState(-1)\r\n  const [recentSearches, setRecentSearches] = useState<string[]>([])\r\n  const inputRef = useRef<HTMLInputElement>(null)\r\n  const containerRef = useRef<HTMLDivElement>(null)\r\n\r\n  // 获取搜索建议\r\n  const { data: suggestions, isLoading: suggestionsLoading } = api.search.getSuggestions.useQuery({\r\n    query: query.trim(),\r\n    limit: 5,\r\n  }, {\r\n    enabled: showSuggestions && query.trim().length > 0,\r\n  })\r\n\r\n  // 获取搜索历史\r\n  const { data: history } = api.search.getHistory.useQuery({\r\n    limit: 10,\r\n  }, {\r\n    enabled: showHistory,\r\n  })\r\n\r\n  // 保存搜索历史\r\n  const saveSearchMutation = api.search.saveToHistory.useMutation()\r\n\r\n  // 从本地存储加载最近搜索\r\n  useEffect(() => {\r\n    const saved = localStorage.getItem('recent-searches')\r\n    if (saved) {\r\n      try {\r\n        setRecentSearches(JSON.parse(saved))\r\n      } catch (error) {\r\n        console.error('Failed to parse recent searches:', error)\r\n      }\r\n    }\r\n  }, [])\r\n\r\n  // 保存到本地存储\r\n  const saveToLocal = (searches: string[]) => {\r\n    try {\r\n      localStorage.setItem('recent-searches', JSON.stringify(searches))\r\n      setRecentSearches(searches)\r\n    } catch (error) {\r\n      console.error('Failed to save recent searches:', error)\r\n    }\r\n  }\r\n\r\n  // 处理搜索\r\n  const handleSearch = async (searchQuery: string) => {\r\n    const trimmedQuery = searchQuery.trim()\r\n    if (!trimmedQuery) return\r\n\r\n    try {\r\n      // 保存到搜索历史\r\n      await saveSearchMutation.mutateAsync({ query: trimmedQuery })\r\n      \r\n      // 更新本地历史记录\r\n      const newSearches = [trimmedQuery, ...recentSearches.filter(s => s !== trimmedQuery)].slice(0, 10)\r\n      saveToLocal(newSearches)\r\n\r\n      // 执行搜索\r\n      if (onSearch) {\r\n        onSearch(trimmedQuery)\r\n      } else {\r\n        router.push(`/search?q=${encodeURIComponent(trimmedQuery)}`)\r\n      }\r\n\r\n      setIsOpen(false)\r\n    } catch (error) {\r\n      toast.error('搜索失败，请稍后重试')\r\n    }\r\n  }\r\n\r\n  // 处理输入变化\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value\r\n    setQuery(value)\r\n    setSelectedIndex(-1)\r\n    setIsOpen(value.length > 0 || showHistory)\r\n  }\r\n\r\n  // 处理键盘事件\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    const items = [\r\n      ...(suggestions || []).map(s => s.query),\r\n      ...(showHistory ? (history || []).map(h => h.query) : []),\r\n      ...recentSearches,\r\n    ].filter((item, index, arr) => arr.indexOf(item) === index)\r\n\r\n    switch (e.key) {\r\n      case 'ArrowDown':\r\n        e.preventDefault()\r\n        setSelectedIndex(prev => (prev + 1) % items.length)\r\n        break\r\n      case 'ArrowUp':\r\n        e.preventDefault()\r\n        setSelectedIndex(prev => (prev - 1 + items.length) % items.length)\r\n        break\r\n      case 'Enter':\r\n        e.preventDefault()\r\n        if (selectedIndex >= 0) {\r\n          const selectedItem = items[selectedIndex]\r\n          setQuery(selectedItem)\r\n          handleSearch(selectedItem)\r\n        } else {\r\n          handleSearch(query)\r\n        }\r\n        break\r\n      case 'Escape':\r\n        setIsOpen(false)\r\n        setSelectedIndex(-1)\r\n        inputRef.current?.blur()\r\n        break\r\n    }\r\n  }\r\n\r\n  // 处理点击外部关闭\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\r\n        setIsOpen(false)\r\n        setSelectedIndex(-1)\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside)\r\n    return () => document.removeEventListener('mousedown', handleClickOutside)\r\n  }, [])\r\n\r\n  // 清空搜索\r\n  const handleClear = () => {\r\n    setQuery('')\r\n    setIsOpen(false)\r\n    setSelectedIndex(-1)\r\n    if (onClear) {\r\n      onClear()\r\n    }\r\n    inputRef.current?.focus()\r\n  }\r\n\r\n  // 删除历史记录项\r\n  const removeFromHistory = (item: string) => {\r\n    const newSearches = recentSearches.filter(s => s !== item)\r\n    saveToLocal(newSearches)\r\n  }\r\n\r\n  // 清空所有历史记录\r\n  const clearAllHistory = () => {\r\n    saveToLocal([])\r\n    toast.success('历史记录已清空')\r\n  }\r\n\r\n  // 获取输入框尺寸样式\r\n  const getSizeClass = () => {\r\n    switch (size) {\r\n      case 'sm': return 'input-sm'\r\n      case 'lg': return 'input-lg'\r\n      default: return 'input-md'\r\n    }\r\n  }\r\n\r\n  // 合并和去重搜索项\r\n  const getSearchItems = () => {\r\n    const items = []\r\n    \r\n    // 添加搜索建议\r\n    if (suggestions && query.trim()) {\r\n      items.push({\r\n        type: 'suggestion',\r\n        title: '搜索建议',\r\n        items: suggestions.map(s => ({\r\n          text: s.query,\r\n          count: s.count,\r\n          type: 'suggestion' as const,\r\n        }))\r\n      })\r\n    }\r\n\r\n    // 添加搜索历史\r\n    if (showHistory && history && history.length > 0) {\r\n      items.push({\r\n        type: 'history',\r\n        title: '搜索历史',\r\n        items: history.map(h => ({\r\n          text: h.query,\r\n          count: h.count,\r\n          type: 'history' as const,\r\n        }))\r\n      })\r\n    }\r\n\r\n    // 添加本地历史\r\n    if (recentSearches.length > 0 && !query.trim()) {\r\n      items.push({\r\n        type: 'recent',\r\n        title: '最近搜索',\r\n        items: recentSearches.map(r => ({\r\n          text: r,\r\n          type: 'recent' as const,\r\n        }))\r\n      })\r\n    }\r\n\r\n    return items\r\n  }\r\n\r\n  return (\r\n    <div ref={containerRef} className={`relative ${className}`}>\r\n      <div className=\"form-control\">\r\n        <div className=\"input-group\">\r\n          <input\r\n            ref={inputRef}\r\n            type=\"text\"\r\n            value={query}\r\n            onChange={handleInputChange}\r\n            onKeyDown={handleKeyDown}\r\n            onFocus={() => setIsOpen(true)}\r\n            placeholder={placeholder}\r\n            className={`input input-bordered ${getSizeClass()} w-full pr-20`}\r\n          />\r\n          \r\n          {/* 搜索和清空按钮 */}\r\n          <div className=\"flex items-center absolute right-2 top-1/2 transform -translate-y-1/2 space-x-1\">\r\n            {query && (\r\n              <motion.button\r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                exit={{ opacity: 0, scale: 0.8 }}\r\n                onClick={handleClear}\r\n                className=\"btn btn-ghost btn-sm btn-square\"\r\n                title=\"清空\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </motion.button>\r\n            )}\r\n            \r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              onClick={() => handleSearch(query)}\r\n              className=\"btn btn-primary btn-sm btn-square\"\r\n              title=\"搜索\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                />\r\n              </svg>\r\n            </motion.button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 搜索下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            className=\"absolute top-full left-0 right-0 z-50 mt-2 bg-base-100 rounded-lg shadow-lg border border-base-200 max-h-96 overflow-y-auto\"\r\n          >\r\n            {suggestionsLoading ? (\r\n              <div className=\"p-4 text-center\">\r\n                <div className=\"loading loading-spinner loading-sm\"></div>\r\n                <span className=\"ml-2 text-sm text-base-content/70\">搜索中...</span>\r\n              </div>\r\n            ) : (\r\n              <div className=\"py-2\">\r\n                {getSearchItems().map((section, sectionIndex) => (\r\n                  <div key={section.type} className={sectionIndex > 0 ? 'border-t border-base-200' : ''}>\r\n                    {/* 分组标题 */}\r\n                    <div className=\"px-4 py-2 flex items-center justify-between\">\r\n                      <h3 className=\"text-sm font-medium text-base-content/70\">{section.title}</h3>\r\n                      {section.type === 'recent' && recentSearches.length > 0 && (\r\n                        <button\r\n                          onClick={clearAllHistory}\r\n                          className=\"text-xs text-base-content/50 hover:text-base-content/70\"\r\n                        >\r\n                          清空\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* 搜索项 */}\r\n                    {section.items.map((item, itemIndex) => {\r\n                      const globalIndex = getSearchItems()\r\n                        .slice(0, sectionIndex)\r\n                        .reduce((acc, s) => acc + s.items.length, 0) + itemIndex\r\n                      \r\n                      return (\r\n                        <motion.div\r\n                          key={`${section.type}-${item.text}`}\r\n                          whileHover={{ backgroundColor: 'hsl(var(--b2))' }}\r\n                          className={`px-4 py-2 cursor-pointer flex items-center justify-between ${\r\n                            selectedIndex === globalIndex ? 'bg-base-200' : ''\r\n                          }`}\r\n                          onClick={() => {\r\n                            setQuery(item.text)\r\n                            handleSearch(item.text)\r\n                          }}\r\n                        >\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            {/* 图标 */}\r\n                            {item.type === 'suggestion' && (\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                strokeWidth={1.5}\r\n                                stroke=\"currentColor\"\r\n                                className=\"w-4 h-4 text-base-content/50\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n                            \r\n                            {item.type === 'history' && (\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                strokeWidth={1.5}\r\n                                stroke=\"currentColor\"\r\n                                className=\"w-4 h-4 text-base-content/50\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n                            \r\n                            {item.type === 'recent' && (\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                strokeWidth={1.5}\r\n                                stroke=\"currentColor\"\r\n                                className=\"w-4 h-4 text-base-content/50\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n\r\n                            <span className=\"text-sm text-base-content\">{item.text}</span>\r\n                          </div>\r\n\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            {/* 搜索次数 */}\r\n                            {item.count && (\r\n                              <span className=\"text-xs text-base-content/50\">\r\n                                {item.count}次\r\n                              </span>\r\n                            )}\r\n\r\n                            {/* 删除按钮 */}\r\n                            {item.type === 'recent' && (\r\n                              <button\r\n                                onClick={(e) => {\r\n                                  e.stopPropagation()\r\n                                  removeFromHistory(item.text)\r\n                                }}\r\n                                className=\"p-1 hover:bg-base-300 rounded\"\r\n                              >\r\n                                <svg\r\n                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                  fill=\"none\"\r\n                                  viewBox=\"0 0 24 24\"\r\n                                  strokeWidth={1.5}\r\n                                  stroke=\"currentColor\"\r\n                                  className=\"w-3 h-3\"\r\n                                >\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                                </svg>\r\n                              </button>\r\n                            )}\r\n                          </div>\r\n                        </motion.div>\r\n                      )\r\n                    })}\r\n                  </div>\r\n                ))}\r\n\r\n                {/* 空状态 */}\r\n                {getSearchItems().length === 0 && (\r\n                  <div className=\"px-4 py-8 text-center text-base-content/50\">\r\n                    <p className=\"text-sm\">暂无搜索记录</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAkBO,MAAM,cAAc;QAAC,EAC1B,cAAc,UAAU,EACxB,QAAQ,EACR,OAAO,EACP,cAAc,IAAI,EAClB,kBAAkB,IAAI,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACG;;IACjB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,SAAS;IACT,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,kBAAkB,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;QAC9F,OAAO,MAAM,IAAI;QACjB,OAAO;IACT,GAAG;QACD,SAAS,mBAAmB,MAAM,IAAI,GAAG,MAAM,GAAG;IACpD;IAEA,SAAS;IACT,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QACvD,OAAO;IACT,GAAG;QACD,SAAS;IACX;IAEA,SAAS;IACT,MAAM,qBAAqB,wHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW;IAE/D,cAAc;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,IAAI;oBACF,kBAAkB,KAAK,KAAK,CAAC;gBAC/B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF;QACF;gCAAG,EAAE;IAEL,UAAU;IACV,MAAM,cAAc,CAAC;QACnB,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,MAAM,eAAe,YAAY,IAAI;QACrC,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,UAAU;YACV,MAAM,mBAAmB,WAAW,CAAC;gBAAE,OAAO;YAAa;YAE3D,WAAW;YACX,MAAM,cAAc;gBAAC;mBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;aAAc,CAAC,KAAK,CAAC,GAAG;YAC/F,YAAY;YAEZ,OAAO;YACP,IAAI,UAAU;gBACZ,SAAS;YACX,OAAO;gBACL,OAAO,IAAI,CAAC,AAAC,aAA6C,OAAjC,mBAAmB;YAC9C;YAEA,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,iBAAiB,CAAC;QAClB,UAAU,MAAM,MAAM,GAAG,KAAK;IAChC;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ;eACT,CAAC,eAAe,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;eACnC,cAAc,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,EAAE;eACrD;SACJ,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,MAAQ,IAAI,OAAO,CAAC,UAAU;QAErD,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;gBAClD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,CAAC,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM;gBACjE;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,GAAG;oBACtB,MAAM,eAAe,KAAK,CAAC,cAAc;oBACzC,SAAS;oBACT,aAAa;gBACf,OAAO;oBACL,aAAa;gBACf;gBACA;YACF,KAAK;oBAGH;gBAFA,UAAU;gBACV,iBAAiB,CAAC;iBAClB,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,IAAI;gBACtB;QACJ;IACF;IAEA,WAAW;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAChF,UAAU;wBACV,iBAAiB,CAAC;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;gCAAG,EAAE;IAEL,OAAO;IACP,MAAM,cAAc;YAOlB;QANA,SAAS;QACT,UAAU;QACV,iBAAiB,CAAC;QAClB,IAAI,SAAS;YACX;QACF;SACA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK;IACzB;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;QACrD,YAAY;IACd;IAEA,WAAW;IACX,MAAM,kBAAkB;QACtB,YAAY,EAAE;QACd,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,YAAY;IACZ,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAClB;IACF;IAEA,WAAW;IACX,MAAM,iBAAiB;QACrB,MAAM,QAAQ,EAAE;QAEhB,SAAS;QACT,IAAI,eAAe,MAAM,IAAI,IAAI;YAC/B,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO;gBACP,OAAO,YAAY,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC3B,MAAM,EAAE,KAAK;wBACb,OAAO,EAAE,KAAK;wBACd,MAAM;oBACR,CAAC;YACH;QACF;QAEA,SAAS;QACT,IAAI,eAAe,WAAW,QAAQ,MAAM,GAAG,GAAG;YAChD,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO;gBACP,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,CAAC;wBACvB,MAAM,EAAE,KAAK;wBACb,OAAO,EAAE,KAAK;wBACd,MAAM;oBACR,CAAC;YACH;QACF;QAEA,SAAS;QACT,IAAI,eAAe,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI;YAC9C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO;gBACP,OAAO,eAAe,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC9B,MAAM;wBACN,MAAM;oBACR,CAAC;YACH;QACF;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAc,WAAW,AAAC,YAAqB,OAAV;;0BAC7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,SAAS,IAAM,UAAU;4BACzB,aAAa;4BACb,WAAW,AAAC,wBAAsC,OAAf,gBAAe;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;8CAK3D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;8BAET,mCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;iFAGtD,6LAAC;wBAAI,WAAU;;4BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;oCAAuB,WAAW,eAAe,IAAI,6BAA6B;;sDAEjF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4C,QAAQ,KAAK;;;;;;gDACtE,QAAQ,IAAI,KAAK,YAAY,eAAe,MAAM,GAAG,mBACpD,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;wCAOJ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;4CACxB,MAAM,cAAc,iBACjB,KAAK,CAAC,GAAG,cACT,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK;4CAEjD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,YAAY;oDAAE,iBAAiB;gDAAiB;gDAChD,WAAW,AAAC,8DAEX,OADC,kBAAkB,cAAc,gBAAgB;gDAElD,SAAS;oDACP,SAAS,KAAK,IAAI;oDAClB,aAAa,KAAK,IAAI;gDACxB;;kEAEA,6LAAC;wDAAI,WAAU;;4DAEZ,KAAK,IAAI,KAAK,8BACb,6LAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;4DAKP,KAAK,IAAI,KAAK,2BACb,6LAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;4DAKP,KAAK,IAAI,KAAK,0BACb,6LAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;0EAKR,6LAAC;gEAAK,WAAU;0EAA6B,KAAK,IAAI;;;;;;;;;;;;kEAGxD,6LAAC;wDAAI,WAAU;;4DAEZ,KAAK,KAAK,kBACT,6LAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK;oEAAC;;;;;;;4DAKf,KAAK,IAAI,KAAK,0BACb,6LAAC;gEACC,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,kBAAkB,KAAK,IAAI;gEAC7B;gEACA,WAAU;0EAEV,cAAA,6LAAC;oEACC,OAAM;oEACN,MAAK;oEACL,SAAQ;oEACR,aAAa;oEACb,QAAO;oEACP,WAAU;8EAEV,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;+CA3FxD,AAAC,GAAkB,OAAhB,QAAQ,IAAI,EAAC,KAAa,OAAV,KAAK,IAAI;;;;;wCAkGvC;;mCAxHQ,QAAQ,IAAI;;;;;4BA6HvB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C;GAnba;;QASI,qIAAA,CAAA,YAAS;;;KATb", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/SearchHighlight.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useMemo } from 'react'\r\n\r\ninterface SearchHighlightProps {\r\n  text: string\r\n  query: string\r\n  className?: string\r\n  highlightClassName?: string\r\n}\r\n\r\nexport const SearchHighlight = ({\r\n  text,\r\n  query,\r\n  className = '',\r\n  highlightClassName = 'bg-yellow-200 text-yellow-800 px-1 rounded',\r\n}: SearchHighlightProps) => {\r\n  // 创建高亮文本\r\n  const highlightedText = useMemo(() => {\r\n    if (!query || !text) return text\r\n\r\n    // 清理查询字符串\r\n    const cleanQuery = query.trim()\r\n    if (!cleanQuery) return text\r\n\r\n    // 创建正则表达式，忽略大小写\r\n    const regex = new RegExp(`(${cleanQuery.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi')\r\n    \r\n    // 分割文本\r\n    const parts = text.split(regex)\r\n    \r\n    return parts.map((part, index) => {\r\n      // 检查是否匹配查询\r\n      const isMatch = regex.test(part)\r\n      regex.lastIndex = 0 // 重置正则表达式\r\n      \r\n      if (isMatch) {\r\n        return (\r\n          <mark key={index} className={highlightClassName}>\r\n            {part}\r\n          </mark>\r\n        )\r\n      }\r\n      return part\r\n    })\r\n  }, [text, query, highlightClassName])\r\n\r\n  return (\r\n    <span className={className}>\r\n      {highlightedText}\r\n    </span>\r\n  )\r\n}\r\n\r\n// 高亮多个关键词的组件\r\ninterface MultiSearchHighlightProps {\r\n  text: string\r\n  queries: string[]\r\n  className?: string\r\n  highlightClassName?: string\r\n}\r\n\r\nexport const MultiSearchHighlight = ({\r\n  text,\r\n  queries,\r\n  className = '',\r\n  highlightClassName = 'bg-yellow-200 text-yellow-800 px-1 rounded',\r\n}: MultiSearchHighlightProps) => {\r\n  // 创建多关键词高亮\r\n  const highlightedText = useMemo(() => {\r\n    if (!queries.length || !text) return text\r\n\r\n    // 过滤和清理查询字符串\r\n    const cleanQueries = queries\r\n      .map(q => q.trim())\r\n      .filter(q => q.length > 0)\r\n\r\n    if (!cleanQueries.length) return text\r\n\r\n    // 创建组合正则表达式\r\n    const escapedQueries = cleanQueries.map(q => q.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'))\r\n    const regex = new RegExp(`(${escapedQueries.join('|')})`, 'gi')\r\n    \r\n    // 分割文本\r\n    const parts = text.split(regex)\r\n    \r\n    return parts.map((part, index) => {\r\n      // 检查是否匹配任何查询\r\n      const isMatch = cleanQueries.some(q => \r\n        part.toLowerCase() === q.toLowerCase()\r\n      )\r\n      \r\n      if (isMatch) {\r\n        return (\r\n          <mark key={index} className={highlightClassName}>\r\n            {part}\r\n          </mark>\r\n        )\r\n      }\r\n      return part\r\n    })\r\n  }, [text, queries, highlightClassName])\r\n\r\n  return (\r\n    <span className={className}>\r\n      {highlightedText}\r\n    </span>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWO,MAAM,kBAAkB;QAAC,EAC9B,IAAI,EACJ,KAAK,EACL,YAAY,EAAE,EACd,qBAAqB,4CAA4C,EAC5C;;IACrB,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO;YAE5B,UAAU;YACV,MAAM,aAAa,MAAM,IAAI;YAC7B,IAAI,CAAC,YAAY,OAAO;YAExB,gBAAgB;YAChB,MAAM,QAAQ,IAAI,OAAO,AAAC,IAAqD,OAAlD,WAAW,OAAO,CAAC,uBAAuB,SAAQ,MAAI;YAEnF,OAAO;YACP,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,OAAO,MAAM,GAAG;4DAAC,CAAC,MAAM;oBACtB,WAAW;oBACX,MAAM,UAAU,MAAM,IAAI,CAAC;oBAC3B,MAAM,SAAS,GAAG,GAAE,UAAU;oBAE9B,IAAI,SAAS;wBACX,qBACE,6LAAC;4BAAiB,WAAW;sCAC1B;2BADQ;;;;;oBAIf;oBACA,OAAO;gBACT;;QACF;mDAAG;QAAC;QAAM;QAAO;KAAmB;IAEpC,qBACE,6LAAC;QAAK,WAAW;kBACd;;;;;;AAGP;GAzCa;KAAA;AAmDN,MAAM,uBAAuB;QAAC,EACnC,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,qBAAqB,4CAA4C,EACvC;;IAC1B,WAAW;IACX,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE;YAC9B,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,MAAM,OAAO;YAErC,aAAa;YACb,MAAM,eAAe,QAClB,GAAG;8EAAC,CAAA,IAAK,EAAE,IAAI;6EACf,MAAM;8EAAC,CAAA,IAAK,EAAE,MAAM,GAAG;;YAE1B,IAAI,CAAC,aAAa,MAAM,EAAE,OAAO;YAEjC,YAAY;YACZ,MAAM,iBAAiB,aAAa,GAAG;gFAAC,CAAA,IAAK,EAAE,OAAO,CAAC,uBAAuB;;YAC9E,MAAM,QAAQ,IAAI,OAAO,AAAC,IAA4B,OAAzB,eAAe,IAAI,CAAC,MAAK,MAAI;YAE1D,OAAO;YACP,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,OAAO,MAAM,GAAG;iEAAC,CAAC,MAAM;oBACtB,aAAa;oBACb,MAAM,UAAU,aAAa,IAAI;iFAAC,CAAA,IAChC,KAAK,WAAW,OAAO,EAAE,WAAW;;oBAGtC,IAAI,SAAS;wBACX,qBACE,6LAAC;4BAAiB,WAAW;sCAC1B;2BADQ;;;;;oBAIf;oBACA,OAAO;gBACT;;QACF;wDAAG;QAAC;QAAM;QAAS;KAAmB;IAEtC,qBACE,6LAAC;QAAK,WAAW;kBACd;;;;;;AAGP;IA9Ca;MAAA", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useAutoSave.ts"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface AutoSaveOptions {\r\n  delay?: number\r\n  enabled?: boolean\r\n  onSave?: (data: any) => Promise<void>\r\n  onError?: (error: Error) => void\r\n  storageKey?: string\r\n}\r\n\r\ninterface AutoSaveResult {\r\n  isSaving: boolean\r\n  lastSaved: Date | null\r\n  hasUnsavedChanges: boolean\r\n  save: () => Promise<void>\r\n  clearDraft: () => void\r\n  restoreDraft: () => any\r\n}\r\n\r\nexport function useAutoSave<T>(\r\n  data: T,\r\n  options: AutoSaveOptions = {}\r\n): AutoSaveResult {\r\n  const {\r\n    delay = 2000,\r\n    enabled = true,\r\n    onSave,\r\n    onError,\r\n    storageKey = 'draft',\r\n  } = options\r\n\r\n  const [isSaving, setIsSaving] = useState(false)\r\n  const [lastSaved, setLastSaved] = useState<Date | null>(null)\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)\r\n  const [initialData, setInitialData] = useState<T>(data)\r\n  \r\n  const timeoutRef = useRef<NodeJS.Timeout>()\r\n  const lastDataRef = useRef<T>(data)\r\n\r\n  // 保存到本地存储\r\n  const saveDraftToLocal = useCallback((draftData: T) => {\r\n    try {\r\n      localStorage.setItem(storageKey, JSON.stringify({\r\n        data: draftData,\r\n        timestamp: Date.now(),\r\n      }))\r\n    } catch (error) {\r\n      console.error('保存草稿到本地存储失败:', error)\r\n    }\r\n  }, [storageKey])\r\n\r\n  // 从本地存储恢复\r\n  const restoreDraft = useCallback(() => {\r\n    try {\r\n      const saved = localStorage.getItem(storageKey)\r\n      if (saved) {\r\n        const parsed = JSON.parse(saved)\r\n        return parsed.data\r\n      }\r\n    } catch (error) {\r\n      console.error('恢复草稿失败:', error)\r\n    }\r\n    return null\r\n  }, [storageKey])\r\n\r\n  // 清除草稿\r\n  const clearDraft = useCallback(() => {\r\n    try {\r\n      localStorage.removeItem(storageKey)\r\n      setHasUnsavedChanges(false)\r\n    } catch (error) {\r\n      console.error('清除草稿失败:', error)\r\n    }\r\n  }, [storageKey])\r\n\r\n  // 手动保存\r\n  const save = useCallback(async () => {\r\n    if (!onSave || isSaving) return\r\n\r\n    try {\r\n      setIsSaving(true)\r\n      await onSave(data)\r\n      setLastSaved(new Date())\r\n      setHasUnsavedChanges(false)\r\n      setInitialData(data)\r\n      clearDraft()\r\n      toast.success('保存成功')\r\n    } catch (error) {\r\n      const err = error instanceof Error ? error : new Error('保存失败')\r\n      if (onError) {\r\n        onError(err)\r\n      } else {\r\n        toast.error(err.message)\r\n      }\r\n    } finally {\r\n      setIsSaving(false)\r\n    }\r\n  }, [data, onSave, isSaving, onError, clearDraft])\r\n\r\n  // 自动保存逻辑\r\n  useEffect(() => {\r\n    if (!enabled || !onSave) return\r\n\r\n    // 检查数据是否发生变化\r\n    const hasChanged = JSON.stringify(data) !== JSON.stringify(lastDataRef.current)\r\n    \r\n    if (hasChanged) {\r\n      lastDataRef.current = data\r\n      setHasUnsavedChanges(true)\r\n      \r\n      // 保存到本地存储\r\n      saveDraftToLocal(data)\r\n      \r\n      // 清除之前的定时器\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current)\r\n      }\r\n      \r\n      // 设置新的定时器\r\n      timeoutRef.current = setTimeout(async () => {\r\n        if (isSaving) return\r\n        \r\n        try {\r\n          setIsSaving(true)\r\n          await onSave(data)\r\n          setLastSaved(new Date())\r\n          setHasUnsavedChanges(false)\r\n          setInitialData(data)\r\n          clearDraft()\r\n          toast.success('自动保存成功', { duration: 2000 })\r\n        } catch (error) {\r\n          const err = error instanceof Error ? error : new Error('自动保存失败')\r\n          if (onError) {\r\n            onError(err)\r\n          } else {\r\n            toast.error(err.message)\r\n          }\r\n        } finally {\r\n          setIsSaving(false)\r\n        }\r\n      }, delay)\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current)\r\n      }\r\n    }\r\n  }, [data, enabled, delay, onSave, onError, isSaving, saveDraftToLocal, clearDraft])\r\n\r\n  // 检查是否有未保存的更改\r\n  useEffect(() => {\r\n    const hasChanges = JSON.stringify(data) !== JSON.stringify(initialData)\r\n    setHasUnsavedChanges(hasChanges)\r\n  }, [data, initialData])\r\n\r\n  // 页面卸载时保存草稿\r\n  useEffect(() => {\r\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\r\n      if (hasUnsavedChanges) {\r\n        e.preventDefault()\r\n        e.returnValue = '有未保存的更改，确定要离开吗？'\r\n      }\r\n    }\r\n\r\n    window.addEventListener('beforeunload', handleBeforeUnload)\r\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload)\r\n  }, [hasUnsavedChanges])\r\n\r\n  return {\r\n    isSaving,\r\n    lastSaved,\r\n    hasUnsavedChanges,\r\n    save,\r\n    clearDraft,\r\n    restoreDraft,\r\n  }\r\n}\r\n\r\n// 专门用于提示词的自动保存 hook\r\nexport function usePromptAutoSave(\r\n  promptData: any,\r\n  options: {\r\n    enabled?: boolean\r\n    onSave?: (data: any) => Promise<void>\r\n    promptId?: string\r\n  } = {}\r\n) {\r\n  const { enabled = true, onSave, promptId } = options\r\n  \r\n  const storageKey = promptId ? `prompt-draft-${promptId}` : 'prompt-draft-new'\r\n  \r\n  return useAutoSave(promptData, {\r\n    enabled,\r\n    onSave,\r\n    storageKey,\r\n    delay: 3000, // 提示词自动保存延迟稍长\r\n  })\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAmBO,SAAS,YACd,IAAO;QACP,UAAA,iEAA2B,CAAC;;IAE5B,MAAM,EACJ,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,MAAM,EACN,OAAO,EACP,aAAa,OAAO,EACrB,GAAG;IAEJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAElD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAK;IAE9B,UAAU;IACV,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACpC,IAAI;gBACF,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;oBAC9C,MAAM;oBACN,WAAW,KAAK,GAAG;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;YAChC;QACF;oDAAG;QAAC;KAAW;IAEf,UAAU;IACV,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC/B,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,OAAO,OAAO,IAAI;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B;YACA,OAAO;QACT;gDAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC7B,IAAI;gBACF,aAAa,UAAU,CAAC;gBACxB,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,WAAW;YAC3B;QACF;8CAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE;YACvB,IAAI,CAAC,UAAU,UAAU;YAEzB,IAAI;gBACF,YAAY;gBACZ,MAAM,OAAO;gBACb,aAAa,IAAI;gBACjB,qBAAqB;gBACrB,eAAe;gBACf;gBACA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,MAAM,MAAM,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;gBACvD,IAAI,SAAS;oBACX,QAAQ;gBACV,OAAO;oBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;gBACzB;YACF,SAAU;gBACR,YAAY;YACd;QACF;wCAAG;QAAC;QAAM;QAAQ;QAAU;QAAS;KAAW;IAEhD,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,WAAW,CAAC,QAAQ;YAEzB,aAAa;YACb,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,OAAO;YAE9E,IAAI,YAAY;gBACd,YAAY,OAAO,GAAG;gBACtB,qBAAqB;gBAErB,UAAU;gBACV,iBAAiB;gBAEjB,WAAW;gBACX,IAAI,WAAW,OAAO,EAAE;oBACtB,aAAa,WAAW,OAAO;gBACjC;gBAEA,UAAU;gBACV,WAAW,OAAO,GAAG;6CAAW;wBAC9B,IAAI,UAAU;wBAEd,IAAI;4BACF,YAAY;4BACZ,MAAM,OAAO;4BACb,aAAa,IAAI;4BACjB,qBAAqB;4BACrB,eAAe;4BACf;4BACA,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU;gCAAE,UAAU;4BAAK;wBAC3C,EAAE,OAAO,OAAO;4BACd,MAAM,MAAM,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;4BACvD,IAAI,SAAS;gCACX,QAAQ;4BACV,OAAO;gCACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;4BACzB;wBACF,SAAU;4BACR,YAAY;wBACd;oBACF;4CAAG;YACL;YAEA;yCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,aAAa,WAAW,OAAO;oBACjC;gBACF;;QACF;gCAAG;QAAC;QAAM;QAAS;QAAO;QAAQ;QAAS;QAAU;QAAkB;KAAW;IAElF,cAAc;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC;YAC3D,qBAAqB;QACvB;gCAAG;QAAC;QAAM;KAAY;IAEtB,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,mBAAmB;wBACrB,EAAE,cAAc;wBAChB,EAAE,WAAW,GAAG;oBAClB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YACxC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,gBAAgB;;QAC1D;gCAAG;QAAC;KAAkB;IAEtB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA9JgB;AAiKT,SAAS,kBACd,UAAe;QACf,UAAA,iEAII,CAAC;;IAEL,MAAM,EAAE,UAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAE7C,MAAM,aAAa,WAAW,AAAC,gBAAwB,OAAT,YAAa;IAE3D,OAAO,YAAY,YAAY;QAC7B;QACA;QACA;QACA,OAAO;IACT;AACF;IAlBgB;;QAYP", "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useCopyToClipboard.ts"], "sourcesContent": ["import { useState, useCallback } from 'react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CopyOptions {\r\n  successMessage?: string\r\n  errorMessage?: string\r\n  showToast?: boolean\r\n  timeout?: number\r\n}\r\n\r\ninterface CopyResult {\r\n  isCopied: boolean\r\n  isLoading: boolean\r\n  copy: (text: string, options?: CopyOptions) => Promise<boolean>\r\n  reset: () => void\r\n}\r\n\r\nexport function useCopyToClipboard(): CopyResult {\r\n  const [isCopied, setIsCopied] = useState(false)\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  const copy = useCallback(async (text: string, options: CopyOptions = {}): Promise<boolean> => {\r\n    const {\r\n      successMessage = '已复制到剪贴板',\r\n      errorMessage = '复制失败',\r\n      showToast = true,\r\n      timeout = 2000,\r\n    } = options\r\n\r\n    if (!text || typeof text !== 'string') {\r\n      if (showToast) {\r\n        toast.error('没有内容可复制')\r\n      }\r\n      return false\r\n    }\r\n\r\n    setIsLoading(true)\r\n\r\n    try {\r\n      // 尝试使用现代 API\r\n      if (navigator.clipboard && window.isSecureContext) {\r\n        await navigator.clipboard.writeText(text)\r\n      } else {\r\n        // 降级到旧方法\r\n        const textArea = document.createElement('textarea')\r\n        textArea.value = text\r\n        textArea.style.position = 'fixed'\r\n        textArea.style.left = '-999999px'\r\n        textArea.style.top = '-999999px'\r\n        document.body.appendChild(textArea)\r\n        textArea.focus()\r\n        textArea.select()\r\n        \r\n        const successful = document.execCommand('copy')\r\n        document.body.removeChild(textArea)\r\n        \r\n        if (!successful) {\r\n          throw new Error('复制命令失败')\r\n        }\r\n      }\r\n\r\n      setIsCopied(true)\r\n      \r\n      if (showToast) {\r\n        toast.success(successMessage)\r\n      }\r\n\r\n      // 重置状态\r\n      setTimeout(() => {\r\n        setIsCopied(false)\r\n      }, timeout)\r\n\r\n      return true\r\n    } catch (error) {\r\n      console.error('复制失败:', error)\r\n      \r\n      if (showToast) {\r\n        toast.error(errorMessage)\r\n      }\r\n      \r\n      return false\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }, [])\r\n\r\n  const reset = useCallback(() => {\r\n    setIsCopied(false)\r\n    setIsLoading(false)\r\n  }, [])\r\n\r\n  return {\r\n    isCopied,\r\n    isLoading,\r\n    copy,\r\n    reset,\r\n  }\r\n}\r\n\r\n// 专门用于复制提示词的 hook\r\nexport function usePromptCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyPrompt = useCallback(async (prompt: { content: string; title: string }) => {\r\n    const success = await copy(prompt.content, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyPromptWithTitle = useCallback(async (prompt: { content: string; title: string }) => {\r\n    const textToCopy = `# ${prompt.title}\\n\\n${prompt.content}`\r\n    const success = await copy(textToCopy, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"（含标题）`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyPromptAsMarkdown = useCallback(async (prompt: { \r\n    content: string; \r\n    title: string; \r\n    description?: string;\r\n    tags?: { name: string }[];\r\n    category?: { name: string };\r\n  }) => {\r\n    let markdown = `# ${prompt.title}\\n\\n`\r\n    \r\n    if (prompt.description) {\r\n      markdown += `${prompt.description}\\n\\n`\r\n    }\r\n    \r\n    if (prompt.category) {\r\n      markdown += `**分类**: ${prompt.category.name}\\n\\n`\r\n    }\r\n    \r\n    if (prompt.tags && prompt.tags.length > 0) {\r\n      markdown += `**标签**: ${prompt.tags.map(t => t.name).join(', ')}\\n\\n`\r\n    }\r\n    \r\n    markdown += `## 内容\\n\\n\\`\\`\\`\\n${prompt.content}\\n\\`\\`\\``\r\n    \r\n    const success = await copy(markdown, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"（Markdown 格式）`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyPrompt,\r\n    copyPromptWithTitle,\r\n    copyPromptAsMarkdown,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}\r\n\r\n// 用于复制代码块的 hook\r\nexport function useCodeCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyCode = useCallback(async (code: string, language?: string) => {\r\n    const success = await copy(code, {\r\n      successMessage: `已复制${language ? ` ${language}` : ''} 代码`,\r\n      errorMessage: '复制代码失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyCode,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}\r\n\r\n// 用于复制链接的 hook\r\nexport function useLinkCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyLink = useCallback(async (url: string, title?: string) => {\r\n    const success = await copy(url, {\r\n      successMessage: title ? `已复制 \"${title}\" 链接` : '已复制链接',\r\n      errorMessage: '复制链接失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyCurrentUrl = useCallback(async () => {\r\n    const success = await copy(window.location.href, {\r\n      successMessage: '已复制当前页面链接',\r\n      errorMessage: '复制链接失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyLink,\r\n    copyCurrentUrl,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAgBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,eAAO;gBAAc,2EAAuB,CAAC;YACpE,MAAM,EACJ,iBAAiB,SAAS,EAC1B,eAAe,MAAM,EACrB,YAAY,IAAI,EAChB,UAAU,IAAI,EACf,GAAG;YAEJ,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,IAAI,WAAW;oBACb,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBACA,OAAO;YACT;YAEA,aAAa;YAEb,IAAI;gBACF,aAAa;gBACb,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;oBACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACtC,OAAO;oBACL,SAAS;oBACT,MAAM,WAAW,SAAS,aAAa,CAAC;oBACxC,SAAS,KAAK,GAAG;oBACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;oBAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;oBACtB,SAAS,KAAK,CAAC,GAAG,GAAG;oBACrB,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,SAAS,KAAK;oBACd,SAAS,MAAM;oBAEf,MAAM,aAAa,SAAS,WAAW,CAAC;oBACxC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAE1B,IAAI,CAAC,YAAY;wBACf,MAAM,IAAI,MAAM;oBAClB;gBACF;gBAEA,YAAY;gBAEZ,IAAI,WAAW;oBACb,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;gBAEA,OAAO;gBACP;4DAAW;wBACT,YAAY;oBACd;2DAAG;gBAEH,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,SAAS;gBAEvB,IAAI,WAAW;oBACb,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBAEA,OAAO;YACT,SAAU;gBACR,aAAa;YACf;QACF;+CAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,YAAY;YACZ,aAAa;QACf;gDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GAhFgB;AAmFT,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACpC,MAAM,UAAU,MAAM,KAAK,OAAO,OAAO,EAAE;gBACzC,gBAAgB,AAAC,WAAuB,OAAb,OAAO,KAAK,EAAC;gBACxC,cAAc;YAChB;YAEA,OAAO;QACT;gDAAG;QAAC;KAAK;IAET,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO;YAC7C,MAAM,aAAa,AAAC,KAAuB,OAAnB,OAAO,KAAK,EAAC,QAAqB,OAAf,OAAO,OAAO;YACzD,MAAM,UAAU,MAAM,KAAK,YAAY;gBACrC,gBAAgB,AAAC,WAAuB,OAAb,OAAO,KAAK,EAAC;gBACxC,cAAc;YAChB;YAEA,OAAO;QACT;yDAAG;QAAC;KAAK;IAET,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAO9C,IAAI,WAAW,AAAC,KAAiB,OAAb,OAAO,KAAK,EAAC;YAEjC,IAAI,OAAO,WAAW,EAAE;gBACtB,YAAY,AAAC,GAAqB,OAAnB,OAAO,WAAW,EAAC;YACpC;YAEA,IAAI,OAAO,QAAQ,EAAE;gBACnB,YAAY,AAAC,WAA+B,OAArB,OAAO,QAAQ,CAAC,IAAI,EAAC;YAC9C;YAEA,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBACzC,YAAY,AAAC,WAAkD,OAAxC,OAAO,IAAI,CAAC,GAAG;uEAAC,CAAA,IAAK,EAAE,IAAI;sEAAE,IAAI,CAAC,OAAM;YACjE;YAEA,YAAY,AAAC,iBAAkC,OAAf,OAAO,OAAO,EAAC;YAE/C,MAAM,UAAU,MAAM,KAAK,UAAU;gBACnC,gBAAgB,AAAC,WAAuB,OAAb,OAAO,KAAK,EAAC;gBACxC,cAAc;YAChB;YAEA,OAAO;QACT;0DAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IA5DgB;;QACwB;;;AA8DjC,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAAO,MAAc;YAChD,MAAM,UAAU,MAAM,KAAK,MAAM;gBAC/B,gBAAgB,AAAC,MAAoC,OAA/B,WAAW,AAAC,IAAY,OAAT,YAAa,IAAG;gBACrD,cAAc;YAChB;YAEA,OAAO;QACT;4CAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;IACF;AACF;IAjBgB;;QACwB;;;AAmBjC,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,OAAO,KAAa;YAC/C,MAAM,UAAU,MAAM,KAAK,KAAK;gBAC9B,gBAAgB,QAAQ,AAAC,QAAa,OAAN,OAAM,UAAQ;gBAC9C,cAAc;YAChB;YAEA,OAAO;QACT;4CAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACjC,MAAM,UAAU,MAAM,KAAK,OAAO,QAAQ,CAAC,IAAI,EAAE;gBAC/C,gBAAgB;gBAChB,cAAc;YAChB;YAEA,OAAO;QACT;kDAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IA3BgB;;QACwB", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/index.ts"], "sourcesContent": ["export { useAutoSave, usePromptAutoSave } from './useAutoSave'\nexport { \n  useCopyToClipboard, \n  usePromptCopy, \n  useCodeCopy, \n  useLinkCopy \n} from './useCopyToClipboard'"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptCard.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { Prompt } from '~/types'\r\nimport { api } from '~/trpc/react'\r\nimport { usePromptCopy } from '~/hooks'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface PromptCardProps {\r\n  prompt: Prompt\r\n  onEdit?: (prompt: Prompt) => void\r\n  onDelete?: (promptId: string) => void\r\n  onUse?: (prompt: Prompt) => void\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n}\r\n\r\nexport const PromptCard = ({\r\n  prompt,\r\n  onEdit,\r\n  onDelete,\r\n  onUse,\r\n  showCategory = true,\r\n  showActions = true,\r\n}: PromptCardProps) => {\r\n  const [showFullContent, setShowFullContent] = useState(false)\r\n  \r\n  // 复制功能\r\n  const { copyPrompt, isCopied, isLoading: isCopying } = usePromptCopy()\r\n\r\n  // 更新使用次数\r\n  const updateUsageMutation = api.prompts.updateUsage.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('使用次数已更新')\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 复制内容到剪贴板\r\n  const copyToClipboard = async () => {\r\n    const success = await copyPrompt(prompt)\r\n    \r\n    if (success) {\r\n      // 更新使用次数\r\n      updateUsageMutation.mutate({ id: prompt.id })\r\n      \r\n      // 触发使用回调\r\n      if (onUse) {\r\n        onUse(prompt)\r\n      }\r\n    }\r\n  }\r\n\r\n  // 处理编辑\r\n  const handleEdit = () => {\r\n    if (onEdit) {\r\n      onEdit(prompt)\r\n    }\r\n  }\r\n\r\n  // 处理删除\r\n  const handleDelete = () => {\r\n    if (onDelete) {\r\n      onDelete(prompt.id)\r\n    }\r\n  }\r\n\r\n  // 格式化时间\r\n  const formatDate = (date: Date) => {\r\n    return new Intl.DateTimeFormat('zh-CN', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    }).format(date)\r\n  }\r\n\r\n  // 截断内容\r\n  const truncateContent = (content: string, maxLength: number = 150) => {\r\n    if (content.length <= maxLength) return content\r\n    return content.substring(0, maxLength) + '...'\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      exit={{ opacity: 0, y: -20 }}\r\n      whileHover={{ y: -4 }}\r\n      transition={{ duration: 0.2 }}\r\n      className=\"card bg-base-100 shadow-md hover:shadow-xl transition-shadow duration-200 group\"\r\n    >\r\n      <div className=\"card-body p-4\">\r\n        {/* 头部信息 */}\r\n        <div className=\"flex items-start justify-between mb-3\">\r\n          <div className=\"flex-1 min-w-0\">\r\n            <h3 className=\"card-title text-lg font-semibold text-base-content truncate\">\r\n              {prompt.title}\r\n            </h3>\r\n            \r\n            {/* 分类和标签 */}\r\n            <div className=\"flex items-center gap-2 mt-1\">\r\n              {showCategory && prompt.category && (\r\n                <Link\r\n                  href={`/categories/${prompt.category.id}`}\r\n                  className=\"badge badge-sm hover:badge-primary transition-colors\"\r\n                  style={{ backgroundColor: prompt.category.color + '20', color: prompt.category.color }}\r\n                >\r\n                  {prompt.category.name}\r\n                </Link>\r\n              )}\r\n              \r\n              {prompt.tags?.slice(0, 3).map((tag) => (\r\n                <Link\r\n                  key={tag.id}\r\n                  href={`/tags/${encodeURIComponent(tag.name)}`}\r\n                  className=\"badge badge-sm badge-ghost hover:badge-primary transition-colors\"\r\n                >\r\n                  {tag.name}\r\n                </Link>\r\n              ))}\r\n              \r\n              {prompt.tags && prompt.tags.length > 3 && (\r\n                <span className=\"badge badge-sm badge-ghost\">\r\n                  +{prompt.tags.length - 3}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 收藏状态 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            {prompt.isFavorite && (\r\n              <div className=\"tooltip tooltip-left\" data-tip=\"已收藏\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  className=\"w-4 h-4 text-warning\"\r\n                >\r\n                  <path d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\" />\r\n                </svg>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 内容预览 */}\r\n        <div className=\"mb-3\">\r\n          <p className=\"text-sm text-base-content/80 leading-relaxed\">\r\n            {showFullContent ? prompt.content : truncateContent(prompt.content)}\r\n          </p>\r\n          \r\n          {prompt.content.length > 150 && (\r\n            <button\r\n              onClick={() => setShowFullContent(!showFullContent)}\r\n              className=\"text-xs text-primary hover:text-primary-focus mt-1\"\r\n            >\r\n              {showFullContent ? '收起' : '展开'}\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* 底部信息 */}\r\n        <div className=\"flex items-center justify-between text-xs text-base-content/60\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"flex items-center space-x-1\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                />\r\n              </svg>\r\n              <span>{prompt.usageCount || 0} 次使用</span>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-1\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                />\r\n              </svg>\r\n              <span>{formatDate(prompt.updatedAt)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <AnimatePresence>\r\n          {showActions && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: 10 }}\r\n              transition={{ duration: 0.2 }}\r\n              className=\"card-actions justify-end mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n            >\r\n              <div className=\"flex items-center space-x-2\">\r\n              {/* 复制按钮 */}\r\n              <motion.button\r\n                onClick={copyToClipboard}\r\n                disabled={isCopying}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className={`btn btn-sm btn-ghost ${isCopied ? 'btn-success' : 'hover:btn-primary'}`}\r\n                title={isCopied ? '已复制' : '复制内容'}\r\n              >\r\n                {isCopying ? (\r\n                  <span className=\"loading loading-spinner loading-xs\"></span>\r\n                ) : isCopied ? (\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                ) : (\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                    />\r\n                  </svg>\r\n                )}\r\n              </motion.button>\r\n\r\n              {/* 编辑按钮 */}\r\n              <motion.button\r\n                onClick={handleEdit}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost hover:btn-secondary\"\r\n                title=\"编辑\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              {/* 删除按钮 */}\r\n              <motion.button\r\n                onClick={handleDelete}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost hover:btn-error\"\r\n                title=\"删除\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              {/* 详情链接 */}\r\n              <motion.div\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                <Link\r\n                  href={`/prompts/${prompt.id}`}\r\n                  className=\"btn btn-sm btn-ghost hover:btn-info\"\r\n                  title=\"查看详情\"\r\n                >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z\"\r\n                  />\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                  />\r\n                </svg>\r\n              </Link>\r\n              </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AACA;;;AARA;;;;;;;AAmBO,MAAM,aAAa;QAAC,EACzB,MAAM,EACN,MAAM,EACN,QAAQ,EACR,KAAK,EACL,eAAe,IAAI,EACnB,cAAc,IAAI,EACF;QA2FH;;IA1Fb,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,OAAO;IACP,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAEnE,SAAS;IACT,MAAM,sBAAsB,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;QAC9D,SAAS;2DAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;2DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,WAAW;IACX,MAAM,kBAAkB;QACtB,MAAM,UAAU,MAAM,WAAW;QAEjC,IAAI,SAAS;YACX,SAAS;YACT,oBAAoB,MAAM,CAAC;gBAAE,IAAI,OAAO,EAAE;YAAC;YAE3C,SAAS;YACT,IAAI,OAAO;gBACT,MAAM;YACR;QACF;IACF;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,OAAO;IACP,MAAM,kBAAkB,SAAC;YAAiB,6EAAoB;QAC5D,IAAI,QAAQ,MAAM,IAAI,WAAW,OAAO;QACxC,OAAO,QAAQ,SAAS,CAAC,GAAG,aAAa;IAC3C;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,OAAO,KAAK;;;;;;8CAIf,6LAAC;oCAAI,WAAU;;wCACZ,gBAAgB,OAAO,QAAQ,kBAC9B,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,AAAC,eAAiC,OAAnB,OAAO,QAAQ,CAAC,EAAE;4CACvC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK,GAAG;gDAAM,OAAO,OAAO,QAAQ,CAAC,KAAK;4CAAC;sDAEpF,OAAO,QAAQ,CAAC,IAAI;;;;;;yCAIxB,eAAA,OAAO,IAAI,cAAX,mCAAA,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC7B,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,AAAC,SAAqC,OAA7B,mBAAmB,IAAI,IAAI;gDAC1C,WAAU;0DAET,IAAI,IAAI;+CAJJ,IAAI,EAAE;;;;;wCAQd,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,6LAAC;4CAAK,WAAU;;gDAA6B;gDACzC,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;sCAO/B,6LAAC;4BAAI,WAAU;sCACZ,OAAO,UAAU,kBAChB,6LAAC;gCAAI,WAAU;gCAAuB,YAAS;0CAC7C,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCACV,kBAAkB,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO;;;;;;wBAGnE,OAAO,OAAO,CAAC,MAAM,GAAG,qBACvB,6LAAC;4BACC,SAAS,IAAM,mBAAmB,CAAC;4BACnC,WAAU;sCAET,kBAAkB,OAAO;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;;4CAAM,OAAO,UAAU,IAAI;4CAAE;;;;;;;;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDAAM,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC,4LAAA,CAAA,kBAAe;8BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,UAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAW,AAAC,wBAAsE,OAA/C,WAAW,gBAAgB;oCAC9D,OAAO,WAAW,QAAQ;8CAEzB,0BACC,6LAAC;wCAAK,WAAU;;;;;mFACd,yBACF,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;iGAIN,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAOV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAMR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,AAAC,YAAqB,OAAV,OAAO,EAAE;wCAC3B,WAAU;wCACV,OAAM;kDAER,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;;8DAEV,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;8DAEJ,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYtB;GA3Ua;;QAW4C,qIAAA,CAAA,gBAAa;;;KAXzD", "debugId": null}}, {"offset": {"line": 1936, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptGrid.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { Prompt } from '~/types'\r\nimport { PromptCard } from './PromptCard'\r\n\r\ninterface PromptGridProps {\r\n  prompts: Prompt[]\r\n  isLoading?: boolean\r\n  onEdit?: (prompt: Prompt) => void\r\n  onDelete?: (promptId: string) => void\r\n  onUse?: (prompt: Prompt) => void\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n  emptyMessage?: string\r\n  columns?: 1 | 2 | 3 | 4 | 6\r\n}\r\n\r\nexport const PromptGrid = ({\r\n  prompts,\r\n  isLoading = false,\r\n  onEdit,\r\n  onDelete,\r\n  onUse,\r\n  showCategory = true,\r\n  showActions = true,\r\n  emptyMessage = '暂无提示词',\r\n  columns = 3,\r\n}: PromptGridProps) => {\r\n  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([])\r\n\r\n  // 响应式列数配置\r\n  const getGridCols = () => {\r\n    const colsMap = {\r\n      1: 'grid-cols-1',\r\n      2: 'grid-cols-1 md:grid-cols-2',\r\n      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\r\n      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',\r\n      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',\r\n    }\r\n    return colsMap[columns] || colsMap[3]\r\n  }\r\n\r\n  // 渲染加载骨架屏\r\n  const renderLoadingSkeleton = () => {\r\n    return (\r\n      <div className={`grid ${getGridCols()} gap-4`}>\r\n        {Array.from({ length: 6 }, (_, i) => (\r\n          <div key={i} className=\"card bg-base-100 shadow-md\">\r\n            <div className=\"card-body p-4\">\r\n              {/* 标题骨架 */}\r\n              <div className=\"h-6 bg-base-300 rounded animate-pulse mb-3\"></div>\r\n              \r\n              {/* 分类和标签骨架 */}\r\n              <div className=\"flex items-center gap-2 mb-3\">\r\n                <div className=\"h-4 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 w-12 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 w-14 bg-base-300 rounded animate-pulse\"></div>\r\n              </div>\r\n              \r\n              {/* 内容骨架 */}\r\n              <div className=\"space-y-2 mb-3\">\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse w-3/4\"></div>\r\n              </div>\r\n              \r\n              {/* 底部信息骨架 */}\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"h-3 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n                  <div className=\"h-3 w-20 bg-base-300 rounded animate-pulse\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染空状态\r\n  const renderEmptyState = () => {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex flex-col items-center justify-center py-16 text-center\"\r\n      >\r\n        <motion.div\r\n          initial={{ scale: 0 }}\r\n          animate={{ scale: 1 }}\r\n          transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\r\n          className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mb-4\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-12 h-12 text-base-content/40\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H9a2.25 2.25 0 01-2.25-2.25V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 002.25-2.25V9.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08M15.75 18v-2.625c0-.621.504-1.125 1.125-1.125h.375a1.125 1.125 0 011.125 1.125V18\"\r\n            />\r\n          </svg>\r\n        </motion.div>\r\n        <motion.h3\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"text-lg font-medium text-base-content mb-2\"\r\n        >\r\n          {emptyMessage}\r\n        </motion.h3>\r\n        <motion.p\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"text-base-content/70 mb-6\"\r\n        >\r\n          创建你的第一个提示词开始使用吧\r\n        </motion.p>\r\n        <motion.a\r\n          href=\"/prompts/new\"\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"btn btn-primary\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-5 h-5\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 4.5v15m7.5-7.5h-15\"\r\n            />\r\n          </svg>\r\n          新建提示词\r\n        </motion.a>\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  // 处理批量选择\r\n  const handleSelectPrompt = (promptId: string) => {\r\n    setSelectedPrompts(prev => \r\n      prev.includes(promptId)\r\n        ? prev.filter(id => id !== promptId)\r\n        : [...prev, promptId]\r\n    )\r\n  }\r\n\r\n  // 清空选择\r\n  const clearSelection = () => {\r\n    setSelectedPrompts([])\r\n  }\r\n\r\n  // 全选/取消全选\r\n  const toggleSelectAll = () => {\r\n    if (selectedPrompts.length === prompts.length) {\r\n      clearSelection()\r\n    } else {\r\n      setSelectedPrompts(prompts.map(p => p.id))\r\n    }\r\n  }\r\n\r\n  if (isLoading) {\r\n    return renderLoadingSkeleton()\r\n  }\r\n\r\n  if (!prompts || prompts.length === 0) {\r\n    return renderEmptyState()\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* 批量操作栏 */}\r\n      <AnimatePresence>\r\n        {selectedPrompts.length > 0 && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -20 }}\r\n            className=\"bg-base-200 rounded-lg p-4 flex items-center justify-between\"\r\n          >\r\n            <div className=\"flex items-center space-x-4\">\r\n              <span className=\"text-sm text-base-content\">\r\n                已选择 {selectedPrompts.length} 个提示词\r\n              </span>\r\n              <motion.button\r\n                onClick={clearSelection}\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost\"\r\n              >\r\n                取消选择\r\n              </motion.button>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-warning\"\r\n              >\r\n                批量编辑\r\n              </motion.button>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-error\"\r\n              >\r\n                批量删除\r\n              </motion.button>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 提示词网格 */}\r\n      <motion.div\r\n        layout\r\n        className={`grid ${getGridCols()} gap-4`}\r\n      >\r\n        <AnimatePresence mode=\"popLayout\">\r\n          {prompts.map((prompt, index) => (\r\n            <motion.div\r\n              key={prompt.id}\r\n              layout\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0.8 }}\r\n              transition={{ \r\n                duration: 0.2,\r\n                delay: index * 0.05,\r\n                layout: { duration: 0.2 }\r\n              }}\r\n              className=\"relative\"\r\n            >\r\n              {/* 选择框 */}\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"absolute top-2 left-2 z-10\"\r\n              >\r\n                <input\r\n                  type=\"checkbox\"\r\n                  className=\"checkbox checkbox-primary checkbox-sm\"\r\n                  checked={selectedPrompts.includes(prompt.id)}\r\n                  onChange={() => handleSelectPrompt(prompt.id)}\r\n                />\r\n              </motion.div>\r\n              \r\n              <PromptCard\r\n                prompt={prompt}\r\n                onEdit={onEdit}\r\n                onDelete={onDelete}\r\n                onUse={onUse}\r\n                showCategory={showCategory}\r\n                showActions={showActions}\r\n              />\r\n            </motion.div>\r\n          ))}\r\n        </AnimatePresence>\r\n      </motion.div>\r\n\r\n      {/* 加载更多按钮 */}\r\n      {prompts.length > 0 && (\r\n        <div className=\"flex justify-center pt-8\">\r\n          <button className=\"btn btn-outline\">\r\n            加载更多\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;;;AALA;;;;AAmBO,MAAM,aAAa;QAAC,EACzB,OAAO,EACP,YAAY,KAAK,EACjB,MAAM,EACN,QAAQ,EACR,KAAK,EACL,eAAe,IAAI,EACnB,cAAc,IAAI,EAClB,eAAe,OAAO,EACtB,UAAU,CAAC,EACK;;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,UAAU;IACV,MAAM,cAAc;QAClB,MAAM,UAAU;YACd,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,EAAE;IACvC;IAEA,UAAU;IACV,MAAM,wBAAwB;QAC5B,qBACE,6LAAC;YAAI,WAAW,AAAC,QAAqB,OAAd,eAAc;sBACnC,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mBAvBb;;;;;;;;;;IA+BlB;IAEA,QAAQ;IACR,MAAM,mBAAmB;QACvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;;8BAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;oBAAE;oBACpB,SAAS;wBAAE,OAAO;oBAAE;oBACpB,YAAY;wBAAE,OAAO;wBAAK,MAAM;wBAAU,WAAW;oBAAI;oBACzD,WAAU;8BAEV,cAAA,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;;;;;;8BAIR,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BAET;;;;;;8BAEH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BACX;;;;;;8BAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,MAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,WAAU;;sCAEV,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAEA;;;;;;;;;;;;;IAKd;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,YACzB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,mBAAmB,EAAE;IACvB;IAEA,UAAU;IACV,MAAM,kBAAkB;QACtB,IAAI,gBAAgB,MAAM,KAAK,QAAQ,MAAM,EAAE;YAC7C;QACF,OAAO;YACL,mBAAmB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC1C;IACF;IAEA,IAAI,WAAW;QACb,OAAO;IACT;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gBAAgB,MAAM,GAAG,mBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;wCAA4B;wCACrC,gBAAgB,MAAM;wCAAC;;;;;;;8CAE9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;sCAIH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,MAAM;gBACN,WAAW,AAAC,QAAqB,OAAd,eAAc;0BAEjC,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,MAAM;4BACN,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,QAAQ;oCAAE,UAAU;gCAAI;4BAC1B;4BACA,WAAU;;8CAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,WAAU;8CAEV,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;wCAC3C,UAAU,IAAM,mBAAmB,OAAO,EAAE;;;;;;;;;;;8CAIhD,6LAAC,8IAAA,CAAA,aAAU;oCACT,QAAQ;oCACR,QAAQ;oCACR,UAAU;oCACV,OAAO;oCACP,cAAc;oCACd,aAAa;;;;;;;2BAhCV,OAAO,EAAE;;;;;;;;;;;;;;;YAwCrB,QAAQ,MAAM,GAAG,mBAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAO,WAAU;8BAAkB;;;;;;;;;;;;;;;;;AAO9C;GA7Qa;KAAA", "debugId": null}}, {"offset": {"line": 2474, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/index.ts"], "sourcesContent": ["export { PromptCard } from './PromptCard'\r\nexport { PromptGrid } from './PromptGrid'"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 2496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/SearchResults.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { useRouter, useSearchParams } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\nimport { PromptGrid } from '~/components/prompts'\r\nimport { SearchHighlight } from './SearchHighlight'\r\nimport { Prompt } from '~/types'\r\n\r\ninterface SearchResultsProps {\r\n  initialQuery?: string\r\n  onQueryChange?: (query: string) => void\r\n}\r\n\r\nexport const SearchResults = ({ initialQuery = '', onQueryChange }: SearchResultsProps) => {\r\n  const router = useRouter()\r\n  const searchParams = useSearchParams()\r\n  const [query, setQuery] = useState(initialQuery || searchParams.get('q') || '')\r\n  const [sortBy, setSortBy] = useState<'relevance' | 'date' | 'usage'>('relevance')\r\n  const [filterCategory, setFilterCategory] = useState<string>('')\r\n  const [filterTags, setFilterTags] = useState<string[]>([])\r\n\r\n  // 搜索提示词\r\n  const { data: searchResults, isLoading, error } = api.search.searchPrompts.useQuery({\r\n    query: query.trim(),\r\n    sortBy,\r\n    categoryId: filterCategory || undefined,\r\n    tags: filterTags,\r\n    limit: 20,\r\n  }, {\r\n    enabled: query.trim().length > 0,\r\n  })\r\n\r\n  // 获取分类列表用于筛选\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 获取热门标签\r\n  const { data: popularTags } = api.tags.getPopular.useQuery({ limit: 20 })\r\n\r\n  // 更新 URL 参数\r\n  useEffect(() => {\r\n    if (query.trim()) {\r\n      const params = new URLSearchParams(searchParams)\r\n      params.set('q', query.trim())\r\n      if (sortBy !== 'relevance') params.set('sort', sortBy)\r\n      if (filterCategory) params.set('category', filterCategory)\r\n      if (filterTags.length > 0) params.set('tags', filterTags.join(','))\r\n      \r\n      router.replace(`/search?${params.toString()}`, { scroll: false })\r\n    }\r\n  }, [query, sortBy, filterCategory, filterTags, router, searchParams])\r\n\r\n  // 处理标签筛选\r\n  const handleTagFilter = (tag: string) => {\r\n    setFilterTags(prev => \r\n      prev.includes(tag) \r\n        ? prev.filter(t => t !== tag)\r\n        : [...prev, tag]\r\n    )\r\n  }\r\n\r\n  // 清空所有筛选\r\n  const clearFilters = () => {\r\n    setFilterCategory('')\r\n    setFilterTags([])\r\n    setSortBy('relevance')\r\n  }\r\n\r\n  // 处理查询变化\r\n  const handleQueryChange = (newQuery: string) => {\r\n    setQuery(newQuery)\r\n    if (onQueryChange) {\r\n      onQueryChange(newQuery)\r\n    }\r\n  }\r\n\r\n  if (!query.trim()) {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"text-center py-16\"\r\n      >\r\n        <div className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-12 h-12 text-base-content/40\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n            />\r\n          </svg>\r\n        </div>\r\n        <h3 className=\"text-lg font-medium text-base-content mb-2\">\r\n          输入关键词开始搜索\r\n        </h3>\r\n        <p className=\"text-base-content/70\">\r\n          搜索你需要的提示词，支持标题、内容、标签搜索\r\n        </p>\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* 搜索状态和统计 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div className=\"flex items-center space-x-4\">\r\n          <h2 className=\"text-xl font-semibold\">\r\n            <SearchHighlight\r\n              text={`搜索结果: \"${query}\"`}\r\n              query=\"\"\r\n              className=\"text-base-content\"\r\n            />\r\n          </h2>\r\n          {searchResults && (\r\n            <span className=\"text-sm text-base-content/70\">\r\n              找到 {searchResults.total} 个结果\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* 排序选项 */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <span className=\"text-sm text-base-content/70\">排序:</span>\r\n          <select\r\n            value={sortBy}\r\n            onChange={(e) => setSortBy(e.target.value as any)}\r\n            className=\"select select-sm select-bordered\"\r\n          >\r\n            <option value=\"relevance\">相关性</option>\r\n            <option value=\"date\">最新</option>\r\n            <option value=\"usage\">使用次数</option>\r\n          </select>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* 筛选器 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.1 }}\r\n        className=\"bg-base-100 rounded-lg p-4 shadow-sm\"\r\n      >\r\n        <div className=\"flex flex-wrap items-center gap-4\">\r\n          {/* 分类筛选 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <span className=\"text-sm font-medium text-base-content\">分类:</span>\r\n            <select\r\n              value={filterCategory}\r\n              onChange={(e) => setFilterCategory(e.target.value)}\r\n              className=\"select select-sm select-bordered\"\r\n            >\r\n              <option value=\"\">所有分类</option>\r\n              {categories?.map(category => (\r\n                <option key={category.id} value={category.id}>\r\n                  {category.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* 标签筛选 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <span className=\"text-sm font-medium text-base-content\">标签:</span>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {popularTags?.slice(0, 10).map(tag => (\r\n                <motion.button\r\n                  key={tag.name}\r\n                  onClick={() => handleTagFilter(tag.name)}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className={`badge badge-sm ${\r\n                    filterTags.includes(tag.name)\r\n                      ? 'badge-primary'\r\n                      : 'badge-ghost hover:badge-primary'\r\n                  }`}\r\n                >\r\n                  {tag.name}\r\n                </motion.button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 清空筛选 */}\r\n          {(filterCategory || filterTags.length > 0 || sortBy !== 'relevance') && (\r\n            <motion.button\r\n              onClick={clearFilters}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"btn btn-sm btn-ghost\"\r\n            >\r\n              清空筛选\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n\r\n        {/* 当前筛选状态 */}\r\n        {(filterCategory || filterTags.length > 0) && (\r\n          <div className=\"mt-3 pt-3 border-t border-base-200\">\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {filterCategory && categories && (\r\n                <span className=\"badge badge-outline\">\r\n                  分类: {categories.find(c => c.id === filterCategory)?.name}\r\n                </span>\r\n              )}\r\n              {filterTags.map(tag => (\r\n                <span key={tag} className=\"badge badge-outline\">\r\n                  标签: {tag}\r\n                  <button\r\n                    onClick={() => handleTagFilter(tag)}\r\n                    className=\"ml-1 text-xs\"\r\n                  >\r\n                    ×\r\n                  </button>\r\n                </span>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* 搜索结果 */}\r\n      <AnimatePresence mode=\"wait\">\r\n        {isLoading ? (\r\n          <motion.div\r\n            key=\"loading\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"text-center py-16\"\r\n          >\r\n            <div className=\"loading loading-spinner loading-lg\"></div>\r\n            <p className=\"mt-4 text-base-content/70\">搜索中...</p>\r\n          </motion.div>\r\n        ) : error ? (\r\n          <motion.div\r\n            key=\"error\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"text-center py-16\"\r\n          >\r\n            <div className=\"w-24 h-24 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-12 h-12 text-error\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-base-content mb-2\">\r\n              搜索出错了\r\n            </h3>\r\n            <p className=\"text-base-content/70\">\r\n              {error.message || '请稍后重试'}\r\n            </p>\r\n          </motion.div>\r\n        ) : (\r\n          <motion.div\r\n            key=\"results\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n          >\r\n            {searchResults && searchResults.results.length > 0 ? (\r\n              <PromptGrid\r\n                prompts={searchResults.results as Prompt[]}\r\n                emptyMessage={`没有找到包含 \"${query}\" 的提示词`}\r\n                columns={3}\r\n                showCategory={true}\r\n                showActions={true}\r\n              />\r\n            ) : (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"text-center py-16\"\r\n              >\r\n                <div className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-12 h-12 text-base-content/40\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"text-lg font-medium text-base-content mb-2\">\r\n                  没有找到相关结果\r\n                </h3>\r\n                <p className=\"text-base-content/70 mb-4\">\r\n                  尝试使用不同的关键词或调整筛选条件\r\n                </p>\r\n                <motion.button\r\n                  onClick={clearFilters}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className=\"btn btn-outline\"\r\n                >\r\n                  清空筛选条件\r\n                </motion.button>\r\n              </motion.div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAeO,MAAM,gBAAgB;QAAC,EAAE,eAAe,EAAE,EAAE,aAAa,EAAsB;QAuM/D;;IAtMrB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,aAAa,GAAG,CAAC,QAAQ;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEzD,QAAQ;IACR,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAClF,OAAO,MAAM,IAAI;QACjB;QACA,YAAY,kBAAkB;QAC9B,MAAM;QACN,OAAO;IACT,GAAG;QACD,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG;IACjC;IAEA,aAAa;IACb,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,SAAS;IACT,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAAE,OAAO;IAAG;IAEvE,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,MAAM,IAAI,IAAI;gBAChB,MAAM,SAAS,IAAI,gBAAgB;gBACnC,OAAO,GAAG,CAAC,KAAK,MAAM,IAAI;gBAC1B,IAAI,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ;gBAC/C,IAAI,gBAAgB,OAAO,GAAG,CAAC,YAAY;gBAC3C,IAAI,WAAW,MAAM,GAAG,GAAG,OAAO,GAAG,CAAC,QAAQ,WAAW,IAAI,CAAC;gBAE9D,OAAO,OAAO,CAAC,AAAC,WAA4B,OAAlB,OAAO,QAAQ,KAAM;oBAAE,QAAQ;gBAAM;YACjE;QACF;kCAAG;QAAC;QAAO;QAAQ;QAAgB;QAAY;QAAQ;KAAa;IAEpE,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,cAAc,CAAA,OACZ,KAAK,QAAQ,CAAC,OACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,OACvB;mBAAI;gBAAM;aAAI;IAEtB;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,kBAAkB;QAClB,cAAc,EAAE;QAChB,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;;;;;;8BAIR,6LAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAG3D,6LAAC;oBAAE,WAAU;8BAAuB;;;;;;;;;;;;IAK1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC,kJAAA,CAAA,kBAAe;oCACd,MAAM,AAAC,UAAe,OAAN,OAAM;oCACtB,OAAM;oCACN,WAAU;;;;;;;;;;;4BAGb,+BACC,6LAAC;gCAAK,WAAU;;oCAA+B;oCACzC,cAAc,KAAK;oCAAC;;;;;;;;;;;;;kCAM9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA+B;;;;;;0CAC/C,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;kDACxD,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,uBAAA,iCAAA,WAAY,GAAG,CAAC,CAAA,yBACf,6LAAC;oDAAyB,OAAO,SAAS,EAAE;8DACzC,SAAS,IAAI;mDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;0CAQ9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwC;;;;;;kDACxD,6LAAC;wCAAI,WAAU;kDACZ,wBAAA,kCAAA,YAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAA,oBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,gBAAgB,IAAI,IAAI;gDACvC,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAW,AAAC,kBAIX,OAHC,WAAW,QAAQ,CAAC,IAAI,IAAI,IACxB,kBACA;0DAGL,IAAI,IAAI;+CAVJ,IAAI,IAAI;;;;;;;;;;;;;;;;4BAiBpB,CAAC,kBAAkB,WAAW,MAAM,GAAG,KAAK,WAAW,WAAW,mBACjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;oBAOJ,CAAC,kBAAkB,WAAW,MAAM,GAAG,CAAC,mBACvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,kBAAkB,4BACjB,6LAAC;oCAAK,WAAU;;wCAAsB;yCAC/B,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,6BAA9B,uCAAA,iBAA+C,IAAI;;;;;;;gCAG3D,WAAW,GAAG,CAAC,CAAA,oBACd,6LAAC;wCAAe,WAAU;;4CAAsB;4CACzC;0DACL,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;;uCALQ;;;;;;;;;;;;;;;;;;;;;;0BAgBrB,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;;mBAPrC;;;;+DASJ,sBACF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;;;;;;sCAIR,6LAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,6LAAC;4BAAE,WAAU;sCACV,MAAM,OAAO,IAAI;;;;;;;mBA1BhB;;;;6EA8BN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAElB,iBAAiB,cAAc,OAAO,CAAC,MAAM,GAAG,kBAC/C,6LAAC,8IAAA,CAAA,aAAU;wBACT,SAAS,cAAc,OAAO;wBAC9B,cAAc,AAAC,WAAgB,OAAN,OAAM;wBAC/B,SAAS;wBACT,cAAc;wBACd,aAAa;;;;;iFAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAIR,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;0CAGzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;mBA9CD;;;;;;;;;;;;;;;;AAwDhB;GAhUa;;QACI,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFzB", "debugId": null}}, {"offset": {"line": 3157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/index.ts"], "sourcesContent": ["export { SearchInput } from './SearchInput'\r\nexport { SearchHighlight, MultiSearchHighlight } from './SearchHighlight'\r\nexport { SearchResults } from './SearchResults'"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3182, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useAuth } from '~/lib/auth/context'\r\nimport { useMainStore } from '~/stores'\r\nimport { SearchInput } from '~/components/search'\r\n\r\nexport const Header = () => {\r\n  const { user, signOut } = useAuth()\r\n  const { ui, toggleSidebar, setTheme } = useMainStore()\r\n  const [showUserMenu, setShowUserMenu] = useState(false)\r\n\r\n  const handleSignOut = async () => {\r\n    await signOut()\r\n  }\r\n\r\n  const handleThemeChange = () => {\r\n    const newTheme = ui.theme === 'light' ? 'dark' : 'light'\r\n    setTheme(newTheme)\r\n    document.documentElement.setAttribute('data-theme', newTheme)\r\n  }\r\n\r\n  return (\r\n    <header className=\"navbar bg-base-100 border-b border-base-200 sticky top-0 z-50\">\r\n      <div className=\"navbar-start\">\r\n        {/* 侧边栏切换按钮 */}\r\n        <button\r\n          className=\"btn btn-ghost btn-square\"\r\n          onClick={toggleSidebar}\r\n          aria-label=\"切换侧边栏\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            className=\"inline-block w-5 h-5 stroke-current\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth=\"2\"\r\n              d=\"M4 6h16M4 12h16M4 18h16\"\r\n            />\r\n          </svg>\r\n        </button>\r\n\r\n        {/* 应用标题 */}\r\n        <div className=\"flex items-center space-x-2 ml-2\">\r\n          <div className=\"avatar\">\r\n            <div className=\"w-8 h-8 rounded-lg bg-primary flex items-center justify-center\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5 text-primary-content\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          <h1 className=\"text-lg font-semibold text-base-content\">\r\n            提示词管理工具\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"navbar-center\">\r\n        {/* 搜索框 */}\r\n        <SearchInput\r\n          placeholder=\"搜索提示词...\"\r\n          className=\"w-96 max-w-xs\"\r\n          showHistory={true}\r\n          showSuggestions={true}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"navbar-end\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {/* 主题切换按钮 */}\r\n          <button\r\n            className=\"btn btn-ghost btn-square\"\r\n            onClick={handleThemeChange}\r\n            aria-label=\"切换主题\"\r\n          >\r\n            {ui.theme === 'light' ? (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z\"\r\n                />\r\n              </svg>\r\n            ) : (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\r\n                />\r\n              </svg>\r\n            )}\r\n          </button>\r\n\r\n          {/* 通知按钮 */}\r\n          <button className=\"btn btn-ghost btn-square\" aria-label=\"通知\">\r\n            <div className=\"indicator\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0\"\r\n                />\r\n              </svg>\r\n              <span className=\"badge badge-xs badge-primary indicator-item\"></span>\r\n            </div>\r\n          </button>\r\n\r\n          {/* 用户菜单 */}\r\n          <div className=\"dropdown dropdown-end\">\r\n            <div\r\n              tabIndex={0}\r\n              role=\"button\"\r\n              className=\"btn btn-ghost btn-circle avatar\"\r\n              onClick={() => setShowUserMenu(!showUserMenu)}\r\n            >\r\n              <div className=\"w-8 rounded-full\">\r\n                {user?.user_metadata?.avatar_url ? (\r\n                  <img\r\n                    alt=\"用户头像\"\r\n                    src={user.user_metadata.avatar_url}\r\n                    className=\"w-full h-full object-cover\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-full h-full bg-primary flex items-center justify-center text-primary-content font-medium\">\r\n                    {user?.email?.charAt(0).toUpperCase() || 'U'}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            {showUserMenu && (\r\n              <ul\r\n                tabIndex={0}\r\n                className=\"menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52\"\r\n              >\r\n                <li>\r\n                  <div className=\"flex flex-col items-start p-2\">\r\n                    <span className=\"font-medium text-base-content\">\r\n                      {user?.user_metadata?.full_name || '用户'}\r\n                    </span>\r\n                    <span className=\"text-xs text-base-content/70\">\r\n                      {user?.email}\r\n                    </span>\r\n                  </div>\r\n                </li>\r\n                <li>\r\n                  <hr className=\"my-1\" />\r\n                </li>\r\n                <li>\r\n                  <a className=\"flex items-center space-x-2\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z\"\r\n                      />\r\n                    </svg>\r\n                    <span>个人资料</span>\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a className=\"flex items-center space-x-2\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z\"\r\n                      />\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                      />\r\n                    </svg>\r\n                    <span>设置</span>\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a className=\"flex items-center space-x-2\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z\"\r\n                      />\r\n                    </svg>\r\n                    <span>帮助</span>\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <hr className=\"my-1\" />\r\n                </li>\r\n                <li>\r\n                  <a\r\n                    className=\"flex items-center space-x-2 text-error\"\r\n                    onClick={handleSignOut}\r\n                  >\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75\"\r\n                      />\r\n                    </svg>\r\n                    <span>退出登录</span>\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAOO,MAAM,SAAS;QAkJL,qBAQI,aAaE;;IAtKrB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,EAAE,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,oBAAoB;QACxB,MAAM,WAAW,GAAG,KAAK,KAAK,UAAU,SAAS;QACjD,SAAS;QACT,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;IACtD;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAW;kCAEX,cAAA,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,WAAU;sCAEV,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAY;gCACZ,GAAE;;;;;;;;;;;;;;;;kCAMR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;0BAM5D,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,8IAAA,CAAA,cAAW;oBACV,aAAY;oBACZ,WAAU;oBACV,aAAa;oBACb,iBAAiB;;;;;;;;;;;0BAIrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,GAAG,KAAK,KAAK,wBACZ,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;yFAIN,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAO,WAAU;4BAA2B,cAAW;sCACtD,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;wCAAK,WAAU;;;;;;;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,UAAU;oCACV,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,gBAAgB,CAAC;8CAEhC,cAAA,6LAAC;wCAAI,WAAU;kDACZ,CAAA,iBAAA,4BAAA,sBAAA,KAAM,aAAa,cAAnB,0CAAA,oBAAqB,UAAU,kBAC9B,6LAAC;4CACC,KAAI;4CACJ,KAAK,KAAK,aAAa,CAAC,UAAU;4CAClC,WAAU;;;;;qGAGZ,6LAAC;4CAAI,WAAU;sDACZ,CAAA,iBAAA,4BAAA,cAAA,KAAM,KAAK,cAAX,kCAAA,YAAa,MAAM,CAAC,GAAG,WAAW,OAAM;;;;;;;;;;;;;;;;gCAKhD,8BACC,6LAAC;oCACC,UAAU;oCACV,WAAU;;sDAEV,6LAAC;sDACC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,CAAA,iBAAA,4BAAA,uBAAA,KAAM,aAAa,cAAnB,2CAAA,qBAAqB,SAAS,KAAI;;;;;;kEAErC,6LAAC;wDAAK,WAAU;kEACb,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;;;;;;sDAIlB,6LAAC;sDACC,cAAA,6LAAC;gDAAG,WAAU;;;;;;;;;;;sDAEhB,6LAAC;sDACC,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;kEAGN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC;sDACC,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;;0EAEV,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;0EAEJ,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;kEAGN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC;sDACC,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;kEAGN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC;sDACC,cAAA,6LAAC;gDAAG,WAAU;;;;;;;;;;;sDAEhB,6LAAC;sDACC,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAS;;kEAET,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;kEAGN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;GA/Qa;;QACe,iIAAA,CAAA,UAAO;QACO,yHAAA,CAAA,eAAY;;;KAFzC", "debugId": null}}, {"offset": {"line": 3751, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/stores/categories.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\nimport { Category } from '~/types'\r\n\r\n// 分类 Store 接口\r\nexport interface CategoriesStore {\r\n  // 数据状态\r\n  categories: Category[]\r\n  selectedCategory: Category | null\r\n  \r\n  // 加载状态\r\n  loading: boolean\r\n  creating: boolean\r\n  updating: boolean\r\n  deleting: boolean\r\n  \r\n  // 错误状态\r\n  error: string | null\r\n  \r\n  // 状态管理方法\r\n  setCategories: (categories: Category[]) => void\r\n  addCategory: (category: Category) => void\r\n  updateCategory: (category: Category) => void\r\n  removeCategory: (id: string) => void\r\n  selectCategory: (category: Category | null) => void\r\n  setLoading: (loading: boolean) => void\r\n  setCreating: (creating: boolean) => void\r\n  setUpdating: (updating: boolean) => void\r\n  setDeleting: (deleting: boolean) => void\r\n  setError: (error: string | null) => void\r\n  clearError: () => void\r\n  \r\n  // 辅助方法\r\n  getCategoryById: (id: string) => Category | undefined\r\n  getCategoriesWithPromptCount: () => Category[]\r\n  getPopularCategories: () => Category[]\r\n}\r\n\r\n// 创建分类 Store\r\nexport const useCategoriesStore = create<CategoriesStore>()(\r\n  devtools(\r\n    immer((set, get) => ({\r\n      // 初始数据状态\r\n      categories: [],\r\n      selectedCategory: null,\r\n      \r\n      // 初始加载状态\r\n      loading: false,\r\n      creating: false,\r\n      updating: false,\r\n      deleting: false,\r\n      \r\n      // 初始错误状态\r\n      error: null,\r\n      \r\n      // 设置分类列表\r\n      setCategories: (categories: Category[]) => {\r\n        set((state) => {\r\n          state.categories = categories\r\n        })\r\n      },\r\n      \r\n      // 添加分类\r\n      addCategory: (category: Category) => {\r\n        set((state) => {\r\n          state.categories.push(category)\r\n        })\r\n      },\r\n      \r\n      // 更新分类\r\n      updateCategory: (category: Category) => {\r\n        set((state) => {\r\n          const index = state.categories.findIndex(c => c.id === category.id)\r\n          if (index !== -1) {\r\n            state.categories[index] = category\r\n          }\r\n          // 如果当前选中的分类被更新，也需要更新选中状态\r\n          if (state.selectedCategory?.id === category.id) {\r\n            state.selectedCategory = category\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 删除分类\r\n      removeCategory: (id: string) => {\r\n        set((state) => {\r\n          state.categories = state.categories.filter(c => c.id !== id)\r\n          // 如果删除的是当前选中的分类，清空选中状态\r\n          if (state.selectedCategory?.id === id) {\r\n            state.selectedCategory = null\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 选择分类\r\n      selectCategory: (category: Category | null) => {\r\n        set((state) => {\r\n          state.selectedCategory = category\r\n        })\r\n      },\r\n      \r\n      // 设置加载状态\r\n      setLoading: (loading: boolean) => {\r\n        set((state) => {\r\n          state.loading = loading\r\n        })\r\n      },\r\n      \r\n      // 设置创建状态\r\n      setCreating: (creating: boolean) => {\r\n        set((state) => {\r\n          state.creating = creating\r\n        })\r\n      },\r\n      \r\n      // 设置更新状态\r\n      setUpdating: (updating: boolean) => {\r\n        set((state) => {\r\n          state.updating = updating\r\n        })\r\n      },\r\n      \r\n      // 设置删除状态\r\n      setDeleting: (deleting: boolean) => {\r\n        set((state) => {\r\n          state.deleting = deleting\r\n        })\r\n      },\r\n      \r\n      // 设置错误\r\n      setError: (error: string | null) => {\r\n        set((state) => {\r\n          state.error = error\r\n        })\r\n      },\r\n      \r\n      // 清除错误\r\n      clearError: () => {\r\n        set((state) => {\r\n          state.error = null\r\n        })\r\n      },\r\n      \r\n      // 根据 ID 获取分类\r\n      getCategoryById: (id: string) => {\r\n        return get().categories.find(c => c.id === id)\r\n      },\r\n      \r\n      // 获取带有提示词数量的分类\r\n      getCategoriesWithPromptCount: () => {\r\n        return get().categories.filter(c => (c.promptCount ?? 0) > 0)\r\n      },\r\n      \r\n      // 获取热门分类（按提示词数量排序）\r\n      getPopularCategories: () => {\r\n        return get().categories\r\n          .filter(c => (c.promptCount ?? 0) > 0)\r\n          .sort((a, b) => (b.promptCount ?? 0) - (a.promptCount ?? 0))\r\n          .slice(0, 10)\r\n      },\r\n    })),\r\n    {\r\n      name: 'categories-store',\r\n    }\r\n  )\r\n)\r\n\r\n// 导出 Store 类型\r\nexport type { CategoriesStore }"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAsCO,MAAM,qBAAqB,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnB,SAAS;QACT,YAAY,EAAE;QACd,kBAAkB;QAElB,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QAEV,SAAS;QACT,OAAO;QAEP,SAAS;QACT,eAAe,CAAC;YACd,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG;YACrB;QACF;QAEA,OAAO;QACP,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,UAAU,CAAC,IAAI,CAAC;YACxB;QACF;QAEA,OAAO;QACP,gBAAgB,CAAC;YACf,IAAI,CAAC;oBAMC;gBALJ,MAAM,QAAQ,MAAM,UAAU,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;gBAClE,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,UAAU,CAAC,MAAM,GAAG;gBAC5B;gBACA,yBAAyB;gBACzB,IAAI,EAAA,0BAAA,MAAM,gBAAgB,cAAtB,8CAAA,wBAAwB,EAAE,MAAK,SAAS,EAAE,EAAE;oBAC9C,MAAM,gBAAgB,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO;QACP,gBAAgB,CAAC;YACf,IAAI,CAAC;oBAGC;gBAFJ,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACzD,uBAAuB;gBACvB,IAAI,EAAA,0BAAA,MAAM,gBAAgB,cAAtB,8CAAA,wBAAwB,EAAE,MAAK,IAAI;oBACrC,MAAM,gBAAgB,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO;QACP,gBAAgB,CAAC;YACf,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG;YAC3B;QACF;QAEA,SAAS;QACT,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG;YAClB;QACF;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,OAAO;QACP,UAAU,CAAC;YACT,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG;YAChB;QACF;QAEA,OAAO;QACP,YAAY;YACV,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG;YAChB;QACF;QAEA,aAAa;QACb,iBAAiB,CAAC;YAChB,OAAO,MAAM,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C;QAEA,eAAe;QACf,8BAA8B;YAC5B,OAAO,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA;oBAAM;uBAAD,CAAC,CAAA,iBAAA,EAAE,WAAW,cAAb,4BAAA,iBAAiB,CAAC,IAAI;;QAC7D;QAEA,mBAAmB;QACnB,sBAAsB;YACpB,OAAO,MAAM,UAAU,CACpB,MAAM,CAAC,CAAA;oBAAM;uBAAD,CAAC,CAAA,iBAAA,EAAE,WAAW,cAAb,4BAAA,iBAAiB,CAAC,IAAI;eACnC,IAAI,CAAC,CAAC,GAAG;oBAAO,gBAAuB;uBAAxB,CAAC,CAAA,iBAAA,EAAE,WAAW,cAAb,4BAAA,iBAAiB,CAAC,IAAI,CAAC,CAAA,iBAAA,EAAE,WAAW,cAAb,4BAAA,iBAAiB,CAAC;eACzD,KAAK,CAAC,GAAG;QACd;IACF,CAAC,IACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 3883, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/navigation/CategoryTree.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { useCategoriesStore } from '~/stores/categories'\r\nimport { Category } from '~/types'\r\n\r\ninterface CategoryTreeProps {\r\n  categories: Category[]\r\n  onCategoryClick?: (category: Category) => void\r\n}\r\n\r\nexport const CategoryTree = ({ categories, onCategoryClick }: CategoryTreeProps) => {\r\n  const pathname = usePathname()\r\n  const { selectedCategory } = useCategoriesStore()\r\n  const [expandedCategories, setExpandedCategories] = useState<string[]>([])\r\n\r\n  // 检查分类是否被选中\r\n  const isSelected = (category: Category) => {\r\n    return selectedCategory?.id === category.id || pathname === `/categories/${category.id}`\r\n  }\r\n\r\n  // 切换分类展开/收起状态\r\n  const toggleCategory = (categoryId: string) => {\r\n    setExpandedCategories(prev => \r\n      prev.includes(categoryId)\r\n        ? prev.filter(id => id !== categoryId)\r\n        : [...prev, categoryId]\r\n    )\r\n  }\r\n\r\n  // 处理分类点击\r\n  const handleCategoryClick = (category: Category) => {\r\n    if (onCategoryClick) {\r\n      onCategoryClick(category)\r\n    }\r\n  }\r\n\r\n  // 构建分类树结构\r\n  const buildCategoryTree = (categories: Category[], parentId: string | null = null): Category[] => {\r\n    return categories\r\n      .filter(cat => cat.parentId === parentId)\r\n      .sort((a, b) => a.name.localeCompare(b.name))\r\n  }\r\n\r\n  // 渲染分类项\r\n  const renderCategoryItem = (category: Category, level: number = 0) => {\r\n    const hasChildren = categories.some(cat => cat.parentId === category.id)\r\n    const isExpanded = expandedCategories.includes(category.id)\r\n    const children = hasChildren ? buildCategoryTree(categories, category.id) : []\r\n\r\n    return (\r\n      <div key={category.id} className=\"w-full\">\r\n        <div className=\"flex items-center w-full\">\r\n          {/* 缩进 */}\r\n          {level > 0 && (\r\n            <div className=\"flex items-center\">\r\n              {Array.from({ length: level }, (_, i) => (\r\n                <div key={i} className=\"w-4 flex justify-center\">\r\n                  <div className=\"w-px h-6 bg-base-300\"></div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n\r\n          {/* 展开/收起按钮 */}\r\n          <div className=\"flex items-center\">\r\n            {hasChildren ? (\r\n              <button\r\n                onClick={() => toggleCategory(category.id)}\r\n                className=\"p-1 hover:bg-base-200 rounded transition-colors\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-3 h-3 transition-transform ${\r\n                    isExpanded ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n            ) : (\r\n              <div className=\"w-5 h-5 flex items-center justify-center\">\r\n                <div className=\"w-1 h-1 bg-base-300 rounded-full\"></div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 分类链接 */}\r\n          <Link\r\n            href={`/categories/${category.id}`}\r\n            onClick={() => handleCategoryClick(category)}\r\n            className={`flex items-center justify-between flex-1 px-2 py-1.5 text-sm rounded-lg transition-colors group ${\r\n              isSelected(category)\r\n                ? 'bg-primary text-primary-content'\r\n                : 'text-base-content hover:bg-base-200'\r\n            }`}\r\n          >\r\n            <div className=\"flex items-center space-x-2 min-w-0\">\r\n              {/* 分类颜色指示器 */}\r\n              <div\r\n                className=\"w-3 h-3 rounded-full flex-shrink-0\"\r\n                style={{ backgroundColor: category.color }}\r\n              />\r\n              \r\n              {/* 分类名称 */}\r\n              <span className=\"truncate font-medium\">{category.name}</span>\r\n            </div>\r\n\r\n            {/* 提示词数量 */}\r\n            {category.promptCount !== undefined && category.promptCount > 0 && (\r\n              <span className={`text-xs px-2 py-0.5 rounded-full flex-shrink-0 ${\r\n                isSelected(category)\r\n                  ? 'bg-primary-content/20 text-primary-content'\r\n                  : 'bg-base-200 text-base-content/70 group-hover:bg-base-300'\r\n              }`}>\r\n                {category.promptCount}\r\n              </span>\r\n            )}\r\n          </Link>\r\n        </div>\r\n\r\n        {/* 子分类 */}\r\n        {hasChildren && isExpanded && (\r\n          <div className=\"ml-2\">\r\n            {children.map(child => renderCategoryItem(child, level + 1))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 获取根分类\r\n  const rootCategories = buildCategoryTree(categories)\r\n\r\n  if (rootCategories.length === 0) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-base-content/70 text-center\">\r\n        暂无分类\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-1\">\r\n      {rootCategories.map(category => renderCategoryItem(category))}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAaO,MAAM,eAAe;QAAC,EAAE,UAAU,EAAE,eAAe,EAAqB;;;IAC7E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;IAC9C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEzE,YAAY;IACZ,MAAM,aAAa,CAAC;QAClB,OAAO,CAAA,6BAAA,uCAAA,iBAAkB,EAAE,MAAK,SAAS,EAAE,IAAI,aAAa,AAAC,eAA0B,OAAZ,SAAS,EAAE;IACxF;IAEA,cAAc;IACd,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,cACzB;mBAAI;gBAAM;aAAW;IAE7B;IAEA,SAAS;IACT,MAAM,sBAAsB,CAAC;QAC3B,IAAI,iBAAiB;YACnB,gBAAgB;QAClB;IACF;IAEA,UAAU;IACV,MAAM,oBAAoB,SAAC;YAAwB,4EAA0B;QAC3E,OAAO,WACJ,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/C;IAEA,QAAQ;IACR,MAAM,qBAAqB,SAAC;YAAoB,yEAAgB;QAC9D,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,SAAS,EAAE;QACvE,MAAM,aAAa,mBAAmB,QAAQ,CAAC,SAAS,EAAE;QAC1D,MAAM,WAAW,cAAc,kBAAkB,YAAY,SAAS,EAAE,IAAI,EAAE;QAE9E,qBACE,6LAAC;YAAsB,WAAU;;8BAC/B,6LAAC;oBAAI,WAAU;;wBAEZ,QAAQ,mBACP,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAM,GAAG,CAAC,GAAG,kBACjC,6LAAC;oCAAY,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;;;;;mCADP;;;;;;;;;;sCAQhB,6LAAC;4BAAI,WAAU;sCACZ,4BACC,6LAAC;gCACC,SAAS,IAAM,eAAe,SAAS,EAAE;gCACzC,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAW,AAAC,gCAEX,OADC,aAAa,cAAc;8CAG7B,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;sDAKR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;sCAMrB,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,AAAC,eAA0B,OAAZ,SAAS,EAAE;4BAChC,SAAS,IAAM,oBAAoB;4BACnC,WAAW,AAAC,mGAIX,OAHC,WAAW,YACP,oCACA;;8CAGN,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,SAAS,KAAK;4CAAC;;;;;;sDAI3C,6LAAC;4CAAK,WAAU;sDAAwB,SAAS,IAAI;;;;;;;;;;;;gCAItD,SAAS,WAAW,KAAK,aAAa,SAAS,WAAW,GAAG,mBAC5D,6LAAC;oCAAK,WAAW,AAAC,kDAIjB,OAHC,WAAW,YACP,+CACA;8CAEH,SAAS,WAAW;;;;;;;;;;;;;;;;;;gBAO5B,eAAe,4BACd,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAA,QAAS,mBAAmB,OAAO,QAAQ;;;;;;;WAjFrD,SAAS,EAAE;;;;;IAsFzB;IAEA,QAAQ;IACR,MAAM,iBAAiB,kBAAkB;IAEzC,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,qBACE,6LAAC;YAAI,WAAU;sBAAqD;;;;;;IAIxE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,eAAe,GAAG,CAAC,CAAA,WAAY,mBAAmB;;;;;;AAGzD;GAhJa;;QACM,qIAAA,CAAA,cAAW;QACC,8HAAA,CAAA,qBAAkB;;;KAFpC", "debugId": null}}, {"offset": {"line": 4117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/navigation/PopularTags.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\n\r\ninterface PopularTagsProps {\r\n  limit?: number\r\n  onTagClick?: (tag: string) => void\r\n}\r\n\r\nexport const PopularTags = ({ limit = 10, onTagClick }: PopularTagsProps) => {\r\n  const pathname = usePathname()\r\n  const [selectedTag, setSelectedTag] = useState<string | null>(null)\r\n\r\n  // 获取热门标签\r\n  const { data: tags, isLoading, error } = api.tags.getPopular.useQuery({\r\n    limit,\r\n  })\r\n\r\n  // 处理标签点击\r\n  const handleTagClick = (tag: string) => {\r\n    setSelectedTag(tag)\r\n    if (onTagClick) {\r\n      onTagClick(tag)\r\n    }\r\n  }\r\n\r\n  // 检查标签是否被选中\r\n  const isSelected = (tag: string) => {\r\n    return selectedTag === tag || pathname === `/tags/${encodeURIComponent(tag)}`\r\n  }\r\n\r\n  // 加载状态\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-2\">\r\n        {Array.from({ length: 5 }, (_, i) => (\r\n          <div key={i} className=\"flex items-center space-x-2 px-3 py-2\">\r\n            <div className=\"w-2 h-2 bg-base-300 rounded-full animate-pulse\"></div>\r\n            <div className=\"h-3 bg-base-300 rounded animate-pulse flex-1\"></div>\r\n            <div className=\"w-6 h-4 bg-base-300 rounded animate-pulse\"></div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 错误状态\r\n  if (error) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-error\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-4 h-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n            />\r\n          </svg>\r\n          <span>加载标签失败</span>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 空状态\r\n  if (!tags || tags.length === 0) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-base-content/70 text-center\">\r\n        暂无热门标签\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染标签列表\r\n  return (\r\n    <div className=\"space-y-1\">\r\n      {tags.map((tag) => (\r\n        <Link\r\n          key={tag.name}\r\n          href={`/tags/${encodeURIComponent(tag.name)}`}\r\n          onClick={() => handleTagClick(tag.name)}\r\n          className={`flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors group ${\r\n            isSelected(tag.name)\r\n              ? 'bg-primary text-primary-content'\r\n              : 'text-base-content hover:bg-base-200'\r\n          }`}\r\n        >\r\n          <div className=\"flex items-center space-x-2 min-w-0\">\r\n            {/* 标签图标 */}\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-3 h-3 flex-shrink-0\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n              />\r\n            </svg>\r\n\r\n            {/* 标签名称 */}\r\n            <span className=\"truncate\">{tag.name}</span>\r\n          </div>\r\n\r\n          {/* 使用次数 */}\r\n          {tag.count > 0 && (\r\n            <span className={`text-xs px-2 py-0.5 rounded-full flex-shrink-0 ${\r\n              isSelected(tag.name)\r\n                ? 'bg-primary-content/20 text-primary-content'\r\n                : 'bg-base-200 text-base-content/70 group-hover:bg-base-300'\r\n            }`}>\r\n              {tag.count}\r\n            </span>\r\n          )}\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAE,EAAE,UAAU,EAAoB;;IACtE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,SAAS;IACT,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACpE;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,IAAI,YAAY;YACd,WAAW;QACb;IACF;IAEA,YAAY;IACZ,MAAM,aAAa,CAAC;QAClB,OAAO,gBAAgB,OAAO,aAAa,AAAC,SAAgC,OAAxB,mBAAmB;IACzE;IAEA,OAAO;IACP,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;oBAAY,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;mBAHP;;;;;;;;;;IAQlB;IAEA,OAAO;IACP,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,6LAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,MAAM;IACN,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC;YAAI,WAAU;sBAAqD;;;;;;IAIxE;IAEA,SAAS;IACT,qBACE,6LAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,+JAAA,CAAA,UAAI;gBAEH,MAAM,AAAC,SAAqC,OAA7B,mBAAmB,IAAI,IAAI;gBAC1C,SAAS,IAAM,eAAe,IAAI,IAAI;gBACtC,WAAW,AAAC,0FAIX,OAHC,WAAW,IAAI,IAAI,IACf,oCACA;;kCAGN,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;0CAKN,6LAAC;gCAAK,WAAU;0CAAY,IAAI,IAAI;;;;;;;;;;;;oBAIrC,IAAI,KAAK,GAAG,mBACX,6LAAC;wBAAK,WAAW,AAAC,kDAIjB,OAHC,WAAW,IAAI,IAAI,IACf,+CACA;kCAEH,IAAI,KAAK;;;;;;;eArCT,IAAI,IAAI;;;;;;;;;;AA4CvB;GAxHa;;QACM,qIAAA,CAAA,cAAW;;;KADjB", "debugId": null}}, {"offset": {"line": 4335, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/navigation/RecentPrompts.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\n\r\ninterface RecentPromptsProps {\r\n  limit?: number\r\n  onPromptClick?: (promptId: string) => void\r\n}\r\n\r\nexport const RecentPrompts = ({ limit = 10, onPromptClick }: RecentPromptsProps) => {\r\n  const pathname = usePathname()\r\n  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null)\r\n\r\n  // 获取最近使用的提示词\r\n  const { data: prompts, isLoading, error } = api.prompts.getRecent.useQuery({\r\n    limit,\r\n  })\r\n\r\n  // 处理提示词点击\r\n  const handlePromptClick = (promptId: string) => {\r\n    setSelectedPrompt(promptId)\r\n    if (onPromptClick) {\r\n      onPromptClick(promptId)\r\n    }\r\n  }\r\n\r\n  // 检查提示词是否被选中\r\n  const isSelected = (promptId: string) => {\r\n    return selectedPrompt === promptId || pathname === `/prompts/${promptId}`\r\n  }\r\n\r\n  // 格式化时间\r\n  const formatTime = (date: Date) => {\r\n    const now = new Date()\r\n    const diffMs = now.getTime() - date.getTime()\r\n    const diffMins = Math.floor(diffMs / (1000 * 60))\r\n    const diffHours = Math.floor(diffMins / 60)\r\n    const diffDays = Math.floor(diffHours / 24)\r\n\r\n    if (diffMins < 1) return '刚刚'\r\n    if (diffMins < 60) return `${diffMins}分钟前`\r\n    if (diffHours < 24) return `${diffHours}小时前`\r\n    if (diffDays < 7) return `${diffDays}天前`\r\n    return date.toLocaleDateString('zh-CN', {\r\n      month: 'short',\r\n      day: 'numeric',\r\n    })\r\n  }\r\n\r\n  // 加载状态\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-2\">\r\n        {Array.from({ length: 5 }, (_, i) => (\r\n          <div key={i} className=\"flex items-center space-x-2 px-3 py-2\">\r\n            <div className=\"w-2 h-2 bg-base-300 rounded-full animate-pulse\"></div>\r\n            <div className=\"flex-1 space-y-1\">\r\n              <div className=\"h-3 bg-base-300 rounded animate-pulse\"></div>\r\n              <div className=\"h-2 bg-base-300 rounded animate-pulse w-2/3\"></div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 错误状态\r\n  if (error) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-error\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-4 h-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n            />\r\n          </svg>\r\n          <span>加载失败</span>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 空状态\r\n  if (!prompts || prompts.length === 0) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-base-content/70 text-center\">\r\n        暂无最近使用\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染提示词列表\r\n  return (\r\n    <div className=\"space-y-1\">\r\n      {prompts.map((prompt) => (\r\n        <Link\r\n          key={prompt.id}\r\n          href={`/prompts/${prompt.id}`}\r\n          onClick={() => handlePromptClick(prompt.id)}\r\n          className={`block px-3 py-2 text-sm rounded-lg transition-colors group ${\r\n            isSelected(prompt.id)\r\n              ? 'bg-primary text-primary-content'\r\n              : 'text-base-content hover:bg-base-200'\r\n          }`}\r\n        >\r\n          <div className=\"flex items-start space-x-2\">\r\n            {/* 提示词图标 */}\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-3 h-3 flex-shrink-0 mt-0.5\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n              />\r\n            </svg>\r\n\r\n            <div className=\"flex-1 min-w-0\">\r\n              {/* 提示词标题 */}\r\n              <div className=\"font-medium truncate\">{prompt.title}</div>\r\n              \r\n              {/* 最后使用时间 */}\r\n              <div className={`text-xs mt-0.5 ${\r\n                isSelected(prompt.id)\r\n                  ? 'text-primary-content/70'\r\n                  : 'text-base-content/70'\r\n              }`}>\r\n                {formatTime(prompt.lastUsedAt!)}\r\n              </div>\r\n            </div>\r\n\r\n            {/* 使用次数 */}\r\n            {prompt.usageCount > 0 && (\r\n              <span className={`text-xs px-1.5 py-0.5 rounded-full flex-shrink-0 ${\r\n                isSelected(prompt.id)\r\n                  ? 'bg-primary-content/20 text-primary-content'\r\n                  : 'bg-base-200 text-base-content/70 group-hover:bg-base-300'\r\n              }`}>\r\n                {prompt.usageCount}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,MAAM,gBAAgB;QAAC,EAAE,QAAQ,EAAE,EAAE,aAAa,EAAsB;;IAC7E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,aAAa;IACb,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;QACzE;IACF;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,aAAa;IACb,MAAM,aAAa,CAAC;QAClB,OAAO,mBAAmB,YAAY,aAAa,AAAC,YAAoB,OAAT;IACjE;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QAC/C,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;QACxC,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QAExC,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,IAAI,OAAO,AAAC,GAAW,OAAT,UAAS;QACtC,IAAI,YAAY,IAAI,OAAO,AAAC,GAAY,OAAV,WAAU;QACxC,IAAI,WAAW,GAAG,OAAO,AAAC,GAAW,OAAT,UAAS;QACrC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,OAAO;YACP,KAAK;QACP;IACF;IAEA,OAAO;IACP,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;oBAAY,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;mBAJT;;;;;;;;;;IAUlB;IAEA,OAAO;IACP,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,6LAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,MAAM;IACN,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,qBACE,6LAAC;YAAI,WAAU;sBAAqD;;;;;;IAIxE;IAEA,UAAU;IACV,qBACE,6LAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,+JAAA,CAAA,UAAI;gBAEH,MAAM,AAAC,YAAqB,OAAV,OAAO,EAAE;gBAC3B,SAAS,IAAM,kBAAkB,OAAO,EAAE;gBAC1C,WAAW,AAAC,8DAIX,OAHC,WAAW,OAAO,EAAE,IAChB,oCACA;0BAGN,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;sCAIN,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CAAwB,OAAO,KAAK;;;;;;8CAGnD,6LAAC;oCAAI,WAAW,AAAC,kBAIhB,OAHC,WAAW,OAAO,EAAE,IAChB,4BACA;8CAEH,WAAW,OAAO,UAAU;;;;;;;;;;;;wBAKhC,OAAO,UAAU,GAAG,mBACnB,6LAAC;4BAAK,WAAW,AAAC,oDAIjB,OAHC,WAAW,OAAO,EAAE,IAChB,+CACA;sCAEH,OAAO,UAAU;;;;;;;;;;;;eA/CnB,OAAO,EAAE;;;;;;;;;;AAuDxB;GAvJa;;QACM,qIAAA,CAAA,cAAW;;;KADjB", "debugId": null}}, {"offset": {"line": 4593, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { useMainStore } from '~/stores'\r\nimport { useCategoriesStore } from '~/stores/categories'\r\nimport { CategoryTree } from '~/components/navigation/CategoryTree'\r\nimport { PopularTags } from '~/components/navigation/PopularTags'\r\nimport { RecentPrompts } from '~/components/navigation/RecentPrompts'\r\n\r\nexport const Sidebar = () => {\r\n  const pathname = usePathname()\r\n  const { ui } = useMainStore()\r\n  const { categories } = useCategoriesStore()\r\n  const [expandedSections, setExpandedSections] = useState({\r\n    categories: true,\r\n    tags: true,\r\n    recent: true,\r\n  })\r\n\r\n  const toggleSection = (section: keyof typeof expandedSections) => {\r\n    setExpandedSections(prev => ({\r\n      ...prev,\r\n      [section]: !prev[section],\r\n    }))\r\n  }\r\n\r\n  const navigationItems = [\r\n    {\r\n      href: '/',\r\n      label: '首页',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/prompts',\r\n      label: '所有提示词',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/favorites',\r\n      label: '收藏夹',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/tags',\r\n      label: '标签管理',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n          />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/stats',\r\n      label: '统计分析',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n  ]\r\n\r\n  const isActive = (href: string) => {\r\n    if (href === '/') {\r\n      return pathname === '/'\r\n    }\r\n    return pathname.startsWith(href)\r\n  }\r\n\r\n  return (\r\n    <aside\r\n      className={`fixed inset-y-0 left-0 z-40 w-64 bg-base-100 border-r border-base-200 transform transition-transform duration-300 ease-in-out ${\r\n        ui.sidebarOpen ? 'translate-x-0' : '-translate-x-full'\r\n      } lg:translate-x-0 lg:static lg:inset-0`}\r\n    >\r\n      <div className=\"flex flex-col h-full\">\r\n        {/* 侧边栏头部 */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-base-200\">\r\n          <h2 className=\"text-lg font-semibold text-base-content\">导航</h2>\r\n          <button\r\n            className=\"btn btn-ghost btn-sm lg:hidden\"\r\n            onClick={() => useMainStore.getState().toggleSidebar()}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M6 18L18 6M6 6l12 12\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* 滚动内容区域 */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-4 space-y-4\">\r\n            {/* 主导航 */}\r\n            <nav className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\r\n                    isActive(item.href)\r\n                      ? 'bg-primary text-primary-content'\r\n                      : 'text-base-content hover:bg-base-200'\r\n                  }`}\r\n                >\r\n                  {item.icon}\r\n                  <span>{item.label}</span>\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* 分类部分 */}\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => toggleSection('categories')}\r\n                className=\"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-base-content hover:bg-base-200 rounded-lg\"\r\n              >\r\n                <span className=\"flex items-center space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n                    />\r\n                  </svg>\r\n                  <span>分类</span>\r\n                </span>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-4 h-4 transform transition-transform ${\r\n                    expandedSections.categories ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              {expandedSections.categories && (\r\n                <div className=\"ml-4 space-y-1\">\r\n                  {/* 分类导航树 */}\r\n                  <CategoryTree categories={categories} />\r\n                  \r\n                  {/* 添加分类按钮 */}\r\n                  <div className=\"pt-2\">\r\n                    <Link\r\n                      href=\"/categories/new\"\r\n                      className=\"flex items-center space-x-2 px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded-lg\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        strokeWidth={1.5}\r\n                        stroke=\"currentColor\"\r\n                        className=\"w-4 h-4\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          d=\"M12 4.5v15m7.5-7.5h-15\"\r\n                        />\r\n                      </svg>\r\n                      <span>新增分类</span>\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 标签部分 */}\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => toggleSection('tags')}\r\n                className=\"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-base-content hover:bg-base-200 rounded-lg\"\r\n              >\r\n                <span className=\"flex items-center space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n                    />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n                  </svg>\r\n                  <span>常用标签</span>\r\n                </span>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-4 h-4 transform transition-transform ${\r\n                    expandedSections.tags ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              {expandedSections.tags && (\r\n                <div className=\"ml-4 space-y-1\">\r\n                  {/* 热门标签 */}\r\n                  <PopularTags limit={8} />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 最近使用部分 */}\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => toggleSection('recent')}\r\n                className=\"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-base-content hover:bg-base-200 rounded-lg\"\r\n              >\r\n                <span className=\"flex items-center space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                  <span>最近使用</span>\r\n                </span>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-4 h-4 transform transition-transform ${\r\n                    expandedSections.recent ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              {expandedSections.recent && (\r\n                <div className=\"ml-4 space-y-1\">\r\n                  {/* 最近使用的提示词 */}\r\n                  <RecentPrompts limit={6} />\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 侧边栏底部 */}\r\n        <div className=\"p-4 border-t border-base-200\">\r\n          <Link\r\n            href=\"/prompts/new\"\r\n            className=\"btn btn-primary btn-sm w-full\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M12 4.5v15m7.5-7.5h-15\"\r\n              />\r\n            </svg>\r\n            新建提示词\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 移动端遮罩层 */}\r\n      {ui.sidebarOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black/50 z-30 lg:hidden\"\r\n          onClick={() => useMainStore.getState().toggleSidebar()}\r\n        />\r\n      )}\r\n    </aside>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,MAAM,UAAU;;IACrB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAC1B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;IACxC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,YAAY;QACZ,MAAM;QACN,QAAQ;IACV;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,6LAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,6LAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,6LAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;;kCAEV,6LAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,GAAE;;;;;;kCAEJ,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,GAAE;;;;;;;;;;;;QAG3D;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,6LAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;KACD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,iIAEX,OADC,GAAG,WAAW,GAAG,kBAAkB,qBACpC;;0BAED,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;0CAEpD,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAOV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,AAAC,0FAIX,OAHC,SAAS,KAAK,IAAI,IACd,oCACA;;gDAGL,KAAK,IAAI;8DACV,6LAAC;8DAAM,KAAK,KAAK;;;;;;;2CATZ,KAAK,IAAI;;;;;;;;;;8CAepB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;sEAGN,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAW,AAAC,0CAEX,OADC,iBAAiB,UAAU,GAAG,cAAc;8DAG9C,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;wCAKP,iBAAiB,UAAU,kBAC1B,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC,mJAAA,CAAA,eAAY;oDAAC,YAAY;;;;;;8DAG1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,6LAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;0EAGN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;;8EAEV,6LAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;8EAEJ,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,GAAE;;;;;;;;;;;;sEAEvD,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAW,AAAC,0CAEX,OADC,iBAAiB,IAAI,GAAG,cAAc;8DAGxC,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;wCAKP,iBAAiB,IAAI,kBACpB,6LAAC;4CAAI,WAAU;sDAEb,cAAA,6LAAC,kJAAA,CAAA,cAAW;gDAAC,OAAO;;;;;;;;;;;;;;;;;8CAM1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;sEAGN,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAW,AAAC,0CAEX,OADC,iBAAiB,MAAM,GAAG,cAAc;8DAG1C,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;wCAKP,iBAAiB,MAAM,kBACtB,6LAAC;4CAAI,WAAU;sDAEb,cAAA,6LAAC,oJAAA,CAAA,gBAAa;gDAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;gCAEA;;;;;;;;;;;;;;;;;;YAOX,GAAG,WAAW,kBACb,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;;;;;;;;;;;;AAK9D;GApYa;;QACM,qIAAA,CAAA,cAAW;QACb,yHAAA,CAAA,eAAY;QACJ,8HAAA,CAAA,qBAAkB;;;KAH9B", "debugId": null}}, {"offset": {"line": 5279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/transitions/PageTransition.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { usePathname } from 'next/navigation'\r\nimport { useEffect, useState } from 'react'\r\n\r\ninterface PageTransitionProps {\r\n  children: React.ReactNode\r\n  className?: string\r\n}\r\n\r\n// 页面过渡动画配置\r\nconst pageVariants = {\r\n  initial: {\r\n    opacity: 0,\r\n    y: 20,\r\n    scale: 0.98,\r\n  },\r\n  in: {\r\n    opacity: 1,\r\n    y: 0,\r\n    scale: 1,\r\n  },\r\n  out: {\r\n    opacity: 0,\r\n    y: -20,\r\n    scale: 1.02,\r\n  },\r\n}\r\n\r\nconst pageTransition = {\r\n  type: 'tween',\r\n  ease: 'anticipate',\r\n  duration: 0.4,\r\n}\r\n\r\nexport const PageTransition = ({ children, className = '' }: PageTransitionProps) => {\r\n  const pathname = usePathname()\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  useEffect(() => {\r\n    // 页面切换时显示加载状态\r\n    setIsLoading(true)\r\n    const timer = setTimeout(() => setIsLoading(false), 100)\r\n    return () => clearTimeout(timer)\r\n  }, [pathname])\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      {!isLoading && (\r\n        <motion.div\r\n          key={pathname}\r\n          initial=\"initial\"\r\n          animate=\"in\"\r\n          exit=\"out\"\r\n          variants={pageVariants}\r\n          transition={pageTransition}\r\n          className={className}\r\n        >\r\n          {children}\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 路由切换加载指示器\r\nexport const RouteProgress = () => {\r\n  const pathname = usePathname()\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setIsLoading(true)\r\n    const timer = setTimeout(() => setIsLoading(false), 400)\r\n    return () => clearTimeout(timer)\r\n  }, [pathname])\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isLoading && (\r\n        <motion.div\r\n          initial={{ scaleX: 0 }}\r\n          animate={{ scaleX: 1 }}\r\n          exit={{ scaleX: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n          className=\"fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left\"\r\n        />\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 页面滑入动画\r\nexport const SlideInPage = ({ children, direction = 'right' }: {\r\n  children: React.ReactNode\r\n  direction?: 'left' | 'right' | 'up' | 'down'\r\n}) => {\r\n  const pathname = usePathname()\r\n\r\n  const getInitialPosition = () => {\r\n    switch (direction) {\r\n      case 'left': return { x: -100, opacity: 0 }\r\n      case 'right': return { x: 100, opacity: 0 }\r\n      case 'up': return { y: -100, opacity: 0 }\r\n      case 'down': return { y: 100, opacity: 0 }\r\n      default: return { x: 100, opacity: 0 }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={getInitialPosition()}\r\n        animate={{ x: 0, y: 0, opacity: 1 }}\r\n        exit={{ x: direction === 'left' ? 100 : -100, opacity: 0 }}\r\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 淡入淡出页面过渡\r\nexport const FadeTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 缩放页面过渡\r\nexport const ScaleTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ scale: 0.95, opacity: 0 }}\r\n        animate={{ scale: 1, opacity: 1 }}\r\n        exit={{ scale: 1.05, opacity: 0 }}\r\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 旋转页面过渡\r\nexport const RotateTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ rotate: -5, opacity: 0 }}\r\n        animate={{ rotate: 0, opacity: 1 }}\r\n        exit={{ rotate: 5, opacity: 0 }}\r\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 弹跳页面过渡\r\nexport const BounceTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ y: -100, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        exit={{ y: 100, opacity: 0 }}\r\n        transition={{ type: 'spring', bounce: 0.4, duration: 0.6 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 页面过渡容器\r\nexport const PageTransitionContainer = ({ \r\n  children, \r\n  variant = 'default',\r\n  className = '' \r\n}: {\r\n  children: React.ReactNode\r\n  variant?: 'default' | 'slide' | 'fade' | 'scale' | 'rotate' | 'bounce'\r\n  className?: string\r\n}) => {\r\n  const TransitionComponent = {\r\n    default: PageTransition,\r\n    slide: SlideInPage,\r\n    fade: FadeTransition,\r\n    scale: ScaleTransition,\r\n    rotate: RotateTransition,\r\n    bounce: BounceTransition,\r\n  }[variant]\r\n\r\n  return (\r\n    <div className={`min-h-screen ${className}`}>\r\n      <RouteProgress />\r\n      <TransitionComponent>\r\n        {children}\r\n      </TransitionComponent>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAWA,WAAW;AACX,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,IAAI;QACF,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,KAAK;QACH,SAAS;QACT,GAAG,CAAC;QACJ,OAAO;IACT;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,iBAAiB;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAuB;;IAC9E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,cAAc;YACd,aAAa;YACb,MAAM,QAAQ;kDAAW,IAAM,aAAa;iDAAQ;YACpD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACnB,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;YACZ,WAAW;sBAEV;WARI;;;;;;;;;;AAaf;GA5Ba;;QACM,qIAAA,CAAA,cAAW;;;KADjB;AA+BN,MAAM,gBAAgB;;IAC3B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,aAAa;YACb,MAAM,QAAQ;iDAAW,IAAM,aAAa;gDAAQ;YACpD;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,QAAQ;YAAE;YACrB,SAAS;gBAAE,QAAQ;YAAE;YACrB,MAAM;gBAAE,QAAQ;YAAE;YAClB,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;;;;;;;;;;AAKpB;IAvBa;;QACM,qIAAA,CAAA,cAAW;;;MADjB;AA0BN,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAE,YAAY,OAAO,EAG1D;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBAAQ,OAAO;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;YAC1C,KAAK;gBAAS,OAAO;oBAAE,GAAG;oBAAK,SAAS;gBAAE;YAC1C,KAAK;gBAAM,OAAO;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;YACxC,KAAK;gBAAQ,OAAO;oBAAE,GAAG;oBAAK,SAAS;gBAAE;YACzC;gBAAS,OAAO;oBAAE,GAAG;oBAAK,SAAS;gBAAE;QACvC;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;YACT,SAAS;gBAAE,GAAG;gBAAG,GAAG;gBAAG,SAAS;YAAE;YAClC,MAAM;gBAAE,GAAG,cAAc,SAAS,MAAM,CAAC;gBAAK,SAAS;YAAE;YACzD,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;sBAE7D;WANI;;;;;;;;;;AAUb;IA7Ba;;QAIM,qIAAA,CAAA,cAAW;;;MAJjB;AAgCN,MAAM,iBAAiB;QAAC,EAAE,QAAQ,EAAiC;;IACxE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;sBAE3B;WANI;;;;;;;;;;AAUb;IAhBa;;QACM,qIAAA,CAAA,cAAW;;;MADjB;AAmBN,MAAM,kBAAkB;QAAC,EAAE,QAAQ,EAAiC;;IACzE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,OAAO;gBAAM,SAAS;YAAE;YACnC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,MAAM;gBAAE,OAAO;gBAAM,SAAS;YAAE;YAChC,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;sBAE7D;WANI;;;;;;;;;;AAUb;IAhBa;;QACM,qIAAA,CAAA,cAAW;;;MADjB;AAmBN,MAAM,mBAAmB;QAAC,EAAE,QAAQ,EAAiC;;IAC1E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,QAAQ,CAAC;gBAAG,SAAS;YAAE;YAClC,SAAS;gBAAE,QAAQ;gBAAG,SAAS;YAAE;YACjC,MAAM;gBAAE,QAAQ;gBAAG,SAAS;YAAE;YAC9B,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;sBAE7D;WANI;;;;;;;;;;AAUb;IAhBa;;QACM,qIAAA,CAAA,cAAW;;;MADjB;AAmBN,MAAM,mBAAmB;QAAC,EAAE,QAAQ,EAAiC;;IAC1E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,GAAG,CAAC;gBAAK,SAAS;YAAE;YAC/B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,MAAM;gBAAE,GAAG;gBAAK,SAAS;YAAE;YAC3B,YAAY;gBAAE,MAAM;gBAAU,QAAQ;gBAAK,UAAU;YAAI;sBAExD;WANI;;;;;;;;;;AAUb;IAhBa;;QACM,qIAAA,CAAA,cAAW;;;MADjB;AAmBN,MAAM,0BAA0B;QAAC,EACtC,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EAKf;IACC,MAAM,sBAAsB;QAC1B,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV,CAAC,CAAC,QAAQ;IAEV,qBACE,6LAAC;QAAI,WAAW,AAAC,gBAAyB,OAAV;;0BAC9B,6LAAC;;;;;0BACD,6LAAC;0BACE;;;;;;;;;;;;AAIT;MA1Ba", "debugId": null}}, {"offset": {"line": 5701, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/transitions/index.ts"], "sourcesContent": ["export {\r\n  PageTransition,\r\n  RouteProgress,\r\n  SlideInPage,\r\n  FadeTransition,\r\n  ScaleTransition,\r\n  RotateTransition,\r\n  BounceTransition,\r\n  PageTransitionContainer,\r\n} from './PageTransition'"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 5720, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect } from 'react'\r\nimport { useAuth } from '~/lib/auth/context'\r\nimport { useMainStore } from '~/stores'\r\nimport { Header } from './Header'\r\nimport { Sidebar } from './Sidebar'\r\nimport { PageTransition } from '~/components/transitions'\r\n\r\ninterface MainLayoutProps {\r\n  children: React.ReactNode\r\n}\r\n\r\nexport const MainLayout = ({ children }: MainLayoutProps) => {\r\n  const { user, loading } = useAuth()\r\n  const { ui } = useMainStore()\r\n\r\n  // 初始化主题\r\n  useEffect(() => {\r\n    document.documentElement.setAttribute('data-theme', ui.theme)\r\n  }, [ui.theme])\r\n\r\n  // 如果正在加载认证状态，显示加载页面\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n        <div className=\"text-center\">\r\n          <div className=\"loading loading-spinner loading-lg\"></div>\r\n          <p className=\"mt-4 text-base-content/70\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 如果未登录，显示登录提示\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n        <div className=\"card w-96 bg-base-100 shadow-xl\">\r\n          <div className=\"card-body text-center\">\r\n            <h2 className=\"card-title justify-center\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-8 h-8 text-primary\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z\"\r\n                />\r\n              </svg>\r\n              需要登录\r\n            </h2>\r\n            <p className=\"text-base-content/70\">\r\n              请先登录以访问提示词管理工具\r\n            </p>\r\n            <div className=\"card-actions justify-center mt-4\">\r\n              <a href=\"/auth/login\" className=\"btn btn-primary\">\r\n                前往登录\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-base-200\">\r\n      {/* 头部导航 */}\r\n      <Header />\r\n\r\n      <div className=\"flex\">\r\n        {/* 侧边栏 */}\r\n        <Sidebar />\r\n\r\n        {/* 主内容区域 */}\r\n        <main\r\n          className={`flex-1 min-h-screen transition-all duration-300 ease-in-out ${\r\n            ui.sidebarOpen ? 'lg:ml-0' : 'lg:ml-0'\r\n          }`}\r\n        >\r\n          <div className=\"p-6\">\r\n            <PageTransition>\r\n              {children}\r\n            </PageTransition>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;;AAaO,MAAM,aAAa;QAAC,EAAE,QAAQ,EAAmB;;IACtD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAE1B,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc,GAAG,KAAK;QAC9D;+BAAG;QAAC,GAAG,KAAK;KAAC;IAEb,oBAAoB;IACpB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;;IAIjD;IAEA,eAAe;IACf,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;gCAEA;;;;;;;sCAGR,6LAAC;4BAAE,WAAU;sCAAuB;;;;;;sCAGpC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,MAAK;gCAAc,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ9D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,0IAAA,CAAA,UAAO;;;;;kCAGR,6LAAC;wBACC,WAAW,AAAC,+DAEX,OADC,GAAG,WAAW,GAAG,YAAY;kCAG/B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,sJAAA,CAAA,iBAAc;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlFa;;QACe,iIAAA,CAAA,UAAO;QAClB,yHAAA,CAAA,eAAY;;;KAFhB", "debugId": null}}, {"offset": {"line": 5936, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/CategoryFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\n\r\ninterface CategoryFilterProps {\r\n  selectedCategories: string[]\r\n  onCategoryChange: (categoryIds: string[]) => void\r\n  showCounts?: boolean\r\n  allowMultiple?: boolean\r\n  placeholder?: string\r\n  className?: string\r\n}\r\n\r\nexport const CategoryFilter = ({\r\n  selectedCategories,\r\n  onCategoryChange,\r\n  showCounts = true,\r\n  allowMultiple = true,\r\n  placeholder = \"选择分类...\",\r\n  className = '',\r\n}: CategoryFilterProps) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n\r\n  // 获取所有分类\r\n  const { data: categories, isLoading } = api.categories.getAll.useQuery()\r\n\r\n  // 过滤分类\r\n  const filteredCategories = categories?.filter(category =>\r\n    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))\r\n  ) || []\r\n\r\n  // 处理分类选择\r\n  const handleCategoryToggle = (categoryId: string) => {\r\n    if (allowMultiple) {\r\n      if (selectedCategories.includes(categoryId)) {\r\n        onCategoryChange(selectedCategories.filter(id => id !== categoryId))\r\n      } else {\r\n        onCategoryChange([...selectedCategories, categoryId])\r\n      }\r\n    } else {\r\n      if (selectedCategories.includes(categoryId)) {\r\n        onCategoryChange([])\r\n      } else {\r\n        onCategoryChange([categoryId])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 清除所有选择\r\n  const clearAll = () => {\r\n    onCategoryChange([])\r\n  }\r\n\r\n  // 获取选中的分类名称\r\n  const getSelectedCategoryNames = () => {\r\n    if (!categories) return []\r\n    return categories\r\n      .filter(cat => selectedCategories.includes(cat.id))\r\n      .map(cat => cat.name)\r\n  }\r\n\r\n  // 显示文本\r\n  const getDisplayText = () => {\r\n    const selectedNames = getSelectedCategoryNames()\r\n    if (selectedNames.length === 0) return placeholder\r\n    if (selectedNames.length === 1) return selectedNames[0]\r\n    return `${selectedNames.length} 个分类`\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 触发按钮 */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 text-sm border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n      >\r\n        <span className={`truncate ${selectedCategories.length === 0 ? 'text-base-content/50' : 'text-base-content'}`}>\r\n          {getDisplayText()}\r\n        </span>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedCategories.length > 0 && (\r\n            <motion.button\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                clearAll()\r\n              }}\r\n              whileHover={{ scale: 1.1 }}\r\n              whileTap={{ scale: 0.9 }}\r\n              className=\"flex items-center justify-center w-4 h-4 bg-base-content/20 rounded-full hover:bg-base-content/30\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n          \r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        </div>\r\n      </button>\r\n\r\n      {/* 下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden\"\r\n          >\r\n            {/* 搜索框 */}\r\n            <div className=\"p-3 border-b border-base-300\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"搜索分类...\"\r\n                  className=\"w-full pl-8 pr-4 py-2 text-sm border border-base-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n                />\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4 absolute left-2.5 top-2.5 text-base-content/50\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 分类列表 */}\r\n            <div className=\"max-h-64 overflow-y-auto\">\r\n              {isLoading ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  <span className=\"ml-2\">加载中...</span>\r\n                </div>\r\n              ) : filteredCategories.length === 0 ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  {searchTerm ? '未找到匹配的分类' : '暂无分类'}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-2 space-y-1\">\r\n                  {filteredCategories.map((category) => (\r\n                    <motion.label\r\n                      key={category.id}\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200 cursor-pointer\"\r\n                    >\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type={allowMultiple ? 'checkbox' : 'radio'}\r\n                          name=\"category\"\r\n                          checked={selectedCategories.includes(category.id)}\r\n                          onChange={() => handleCategoryToggle(category.id)}\r\n                          className=\"sr-only\"\r\n                        />\r\n                        <div\r\n                          className={`w-4 h-4 rounded ${\r\n                            allowMultiple ? 'rounded-sm' : 'rounded-full'\r\n                          } border-2 transition-colors ${\r\n                            selectedCategories.includes(category.id)\r\n                              ? 'bg-primary border-primary'\r\n                              : 'border-base-300'\r\n                          }`}\r\n                        >\r\n                          {selectedCategories.includes(category.id) && (\r\n                            <svg\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                              fill=\"none\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              strokeWidth={3}\r\n                              stroke=\"currentColor\"\r\n                              className=\"w-3 h-3 text-primary-content\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M4.5 12.75l6 6 9-13.5\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 分类图标 */}\r\n                      <div\r\n                        className=\"w-6 h-6 rounded flex items-center justify-center text-white text-xs font-semibold\"\r\n                        style={{ backgroundColor: category.color }}\r\n                      >\r\n                        {category.icon || category.name.charAt(0)}\r\n                      </div>\r\n\r\n                      {/* 分类信息 */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"text-sm font-medium text-base-content truncate\">\r\n                          {category.name}\r\n                        </p>\r\n                        {category.description && (\r\n                          <p className=\"text-xs text-base-content/70 truncate\">\r\n                            {category.description}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* 统计信息 */}\r\n                      {showCounts && (\r\n                        <div className=\"flex items-center space-x-2 text-xs text-base-content/50\">\r\n                          <span>{category.promptCount || 0}</span>\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth={1.5}\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-3 h-3\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                            />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </motion.label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 底部操作 */}\r\n            {selectedCategories.length > 0 && (\r\n              <div className=\"p-3 border-t border-base-300 bg-base-50\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectedCategories.length} 个分类\r\n                  </span>\r\n                  <button\r\n                    onClick={clearAll}\r\n                    className=\"text-sm text-primary hover:text-primary-focus\"\r\n                  >\r\n                    清除全部\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 点击外部关闭 */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAgBO,MAAM,iBAAiB;QAAC,EAC7B,kBAAkB,EAClB,gBAAgB,EAChB,aAAa,IAAI,EACjB,gBAAgB,IAAI,EACpB,cAAc,SAAS,EACvB,YAAY,EAAE,EACM;;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAEtE,OAAO;IACP,MAAM,qBAAqB,CAAA,uBAAA,iCAAA,WAAY,MAAM,CAAC,CAAA,WAC5C,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SACxF,EAAE;IAEP,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe;YACjB,IAAI,mBAAmB,QAAQ,CAAC,aAAa;gBAC3C,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO;YAC1D,OAAO;gBACL,iBAAiB;uBAAI;oBAAoB;iBAAW;YACtD;QACF,OAAO;YACL,IAAI,mBAAmB,QAAQ,CAAC,aAAa;gBAC3C,iBAAiB,EAAE;YACrB,OAAO;gBACL,iBAAiB;oBAAC;iBAAW;YAC/B;QACF;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,YAAY;IACZ,MAAM,2BAA2B;QAC/B,IAAI,CAAC,YAAY,OAAO,EAAE;QAC1B,OAAO,WACJ,MAAM,CAAC,CAAA,MAAO,mBAAmB,QAAQ,CAAC,IAAI,EAAE,GAChD,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IACxB;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,MAAM,gBAAgB;QACtB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,aAAa,CAAC,EAAE;QACvD,OAAO,AAAC,GAAuB,OAArB,cAAc,MAAM,EAAC;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BAE1B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAK,WAAW,AAAC,YAA0F,OAA/E,mBAAmB,MAAM,KAAK,IAAI,yBAAyB;kCACrF;;;;;;kCAGH,6LAAC;wBAAI,WAAU;;4BACZ,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;0CAK3D,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAW,AAAC,gCAA0D,OAA3B,SAAS,eAAe;0CAEnE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;2EAEvB,mBAAmB,MAAM,KAAK,kBAChC,6LAAC;gCAAI,WAAU;0CACZ,aAAa,aAAa;;;;;yFAG7B,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wCAEX,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAM,gBAAgB,aAAa;wDACnC,MAAK;wDACL,SAAS,mBAAmB,QAAQ,CAAC,SAAS,EAAE;wDAChD,UAAU,IAAM,qBAAqB,SAAS,EAAE;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;wDACC,WAAW,AAAC,mBAGV,OAFA,gBAAgB,eAAe,gBAChC,gCAIA,OAHC,mBAAmB,QAAQ,CAAC,SAAS,EAAE,IACnC,8BACA;kEAGL,mBAAmB,QAAQ,CAAC,SAAS,EAAE,mBACtC,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAQZ,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;0DAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,SAAS,IAAI;;;;;;oDAEf,SAAS,WAAW,kBACnB,6LAAC;wDAAE,WAAU;kEACV,SAAS,WAAW;;;;;;;;;;;;4CAM1B,4BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,SAAS,WAAW,IAAI;;;;;;kEAC/B,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;;;;;;;;uCA5EL,SAAS,EAAE;;;;;;;;;;;;;;;wBAwFzB,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAA+B;4CACxC,mBAAmB,MAAM;4CAAC;;;;;;;kDAEjC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GAlRa;KAAA", "debugId": null}}, {"offset": {"line": 6408, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/TagFilter.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Tag } from '~/types'\r\n\r\ninterface TagFilterProps {\r\n  selectedTags: string[]\r\n  onTagChange: (tagIds: string[]) => void\r\n  showCounts?: boolean\r\n  allowMultiple?: boolean\r\n  placeholder?: string\r\n  className?: string\r\n}\r\n\r\nexport const TagFilter = ({\r\n  selectedTags,\r\n  onTagChange,\r\n  showCounts = true,\r\n  allowMultiple = true,\r\n  placeholder = \"选择标签...\",\r\n  className = '',\r\n}: TagFilterProps) => {\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [searchTerm, setSearchTerm] = useState('')\r\n\r\n  // 获取所有标签\r\n  const { data: tags, isLoading } = api.tags.getAll.useQuery()\r\n\r\n  // 过滤标签\r\n  const filteredTags = tags?.filter(tag =>\r\n    tag.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  ) || []\r\n\r\n  // 处理标签选择\r\n  const handleTagToggle = (tagId: string) => {\r\n    if (allowMultiple) {\r\n      if (selectedTags.includes(tagId)) {\r\n        onTagChange(selectedTags.filter(id => id !== tagId))\r\n      } else {\r\n        onTagChange([...selectedTags, tagId])\r\n      }\r\n    } else {\r\n      if (selectedTags.includes(tagId)) {\r\n        onTagChange([])\r\n      } else {\r\n        onTagChange([tagId])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 清除所有选择\r\n  const clearAll = () => {\r\n    onTagChange([])\r\n  }\r\n\r\n  // 获取选中的标签名称\r\n  const getSelectedTagNames = () => {\r\n    if (!tags) return []\r\n    return tags\r\n      .filter(tag => selectedTags.includes(tag.id))\r\n      .map(tag => tag.name)\r\n  }\r\n\r\n  // 显示文本\r\n  const getDisplayText = () => {\r\n    const selectedNames = getSelectedTagNames()\r\n    if (selectedNames.length === 0) return placeholder\r\n    if (selectedNames.length === 1) return selectedNames[0]\r\n    return `${selectedNames.length} 个标签`\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* 触发按钮 */}\r\n      <button\r\n        onClick={() => setIsOpen(!isOpen)}\r\n        className=\"flex items-center justify-between w-full px-4 py-2 text-sm border border-base-300 rounded-lg bg-base-100 hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n      >\r\n        <span className={`truncate ${selectedTags.length === 0 ? 'text-base-content/50' : 'text-base-content'}`}>\r\n          {getDisplayText()}\r\n        </span>\r\n        \r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedTags.length > 0 && (\r\n            <motion.button\r\n              onClick={(e) => {\r\n                e.stopPropagation()\r\n                clearAll()\r\n              }}\r\n              whileHover={{ scale: 1.1 }}\r\n              whileTap={{ scale: 0.9 }}\r\n              className=\"flex items-center justify-center w-4 h-4 bg-base-content/20 rounded-full hover:bg-base-content/30\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </motion.button>\r\n          )}\r\n          \r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        </div>\r\n      </button>\r\n\r\n      {/* 下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"absolute top-full left-0 right-0 mt-2 bg-base-100 border border-base-300 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden\"\r\n          >\r\n            {/* 搜索框 */}\r\n            <div className=\"p-3 border-b border-base-300\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"搜索标签...\"\r\n                  className=\"w-full pl-8 pr-4 py-2 text-sm border border-base-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2\"\r\n                />\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4 absolute left-2.5 top-2.5 text-base-content/50\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 标签列表 */}\r\n            <div className=\"max-h-64 overflow-y-auto\">\r\n              {isLoading ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  <span className=\"ml-2\">加载中...</span>\r\n                </div>\r\n              ) : filteredTags.length === 0 ? (\r\n                <div className=\"p-4 text-center text-base-content/50\">\r\n                  {searchTerm ? '未找到匹配的标签' : '暂无标签'}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-2 space-y-1\">\r\n                  {filteredTags.map((tag) => (\r\n                    <motion.label\r\n                      key={tag.id}\r\n                      initial={{ opacity: 0 }}\r\n                      animate={{ opacity: 1 }}\r\n                      className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200 cursor-pointer\"\r\n                    >\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type={allowMultiple ? 'checkbox' : 'radio'}\r\n                          name=\"tag\"\r\n                          checked={selectedTags.includes(tag.id)}\r\n                          onChange={() => handleTagToggle(tag.id)}\r\n                          className=\"sr-only\"\r\n                        />\r\n                        <div\r\n                          className={`w-4 h-4 rounded ${\r\n                            allowMultiple ? 'rounded-sm' : 'rounded-full'\r\n                          } border-2 transition-colors ${\r\n                            selectedTags.includes(tag.id)\r\n                              ? 'bg-primary border-primary'\r\n                              : 'border-base-300'\r\n                          }`}\r\n                        >\r\n                          {selectedTags.includes(tag.id) && (\r\n                            <svg\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                              fill=\"none\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              strokeWidth={3}\r\n                              stroke=\"currentColor\"\r\n                              className=\"w-3 h-3 text-primary-content\"\r\n                            >\r\n                              <path\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                                d=\"M4.5 12.75l6 6 9-13.5\"\r\n                              />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* 标签图标 */}\r\n                      <div className=\"flex items-center justify-center w-6 h-6 bg-primary/10 rounded-full\">\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          fill=\"none\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          strokeWidth={1.5}\r\n                          stroke=\"currentColor\"\r\n                          className=\"w-3 h-3 text-primary\"\r\n                        >\r\n                          <path\r\n                            strokeLinecap=\"round\"\r\n                            strokeLinejoin=\"round\"\r\n                            d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n                          />\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n                        </svg>\r\n                      </div>\r\n\r\n                      {/* 标签信息 */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"text-sm font-medium text-base-content truncate\">\r\n                          {tag.name}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* 统计信息 */}\r\n                      {showCounts && (\r\n                        <div className=\"flex items-center space-x-1 text-xs text-base-content/50\">\r\n                          <span>{tag.promptCount || 0}</span>\r\n                          <svg\r\n                            xmlns=\"http://www.w3.org/2000/svg\"\r\n                            fill=\"none\"\r\n                            viewBox=\"0 0 24 24\"\r\n                            strokeWidth={1.5}\r\n                            stroke=\"currentColor\"\r\n                            className=\"w-3 h-3\"\r\n                          >\r\n                            <path\r\n                              strokeLinecap=\"round\"\r\n                              strokeLinejoin=\"round\"\r\n                              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                            />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </motion.label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 底部操作 */}\r\n            {selectedTags.length > 0 && (\r\n              <div className=\"p-3 border-t border-base-300 bg-base-50\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-sm text-base-content/70\">\r\n                    已选择 {selectedTags.length} 个标签\r\n                  </span>\r\n                  <button\r\n                    onClick={clearAll}\r\n                    className=\"text-sm text-primary hover:text-primary-focus\"\r\n                  >\r\n                    清除全部\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 点击外部关闭 */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40\"\r\n          onClick={() => setIsOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAgBO,MAAM,YAAY;QAAC,EACxB,YAAY,EACZ,WAAW,EACX,aAAa,IAAI,EACjB,gBAAgB,IAAI,EACpB,cAAc,SAAS,EACvB,YAAY,EAAE,EACC;;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAE1D,OAAO;IACP,MAAM,eAAe,CAAA,iBAAA,2BAAA,KAAM,MAAM,CAAC,CAAA,MAChC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SACnD,EAAE;IAEP,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe;YACjB,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,YAAY,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YAC/C,OAAO;gBACL,YAAY;uBAAI;oBAAc;iBAAM;YACtC;QACF,OAAO;YACL,IAAI,aAAa,QAAQ,CAAC,QAAQ;gBAChC,YAAY,EAAE;YAChB,OAAO;gBACL,YAAY;oBAAC;iBAAM;YACrB;QACF;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,YAAY,EAAE;IAChB;IAEA,YAAY;IACZ,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,OAAO,EAAE;QACpB,OAAO,KACJ,MAAM,CAAC,CAAA,MAAO,aAAa,QAAQ,CAAC,IAAI,EAAE,GAC1C,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;IACxB;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,MAAM,gBAAgB;QACtB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,aAAa,CAAC,EAAE;QACvD,OAAO,AAAC,GAAuB,OAArB,cAAc,MAAM,EAAC;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,YAAqB,OAAV;;0BAE1B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAK,WAAW,AAAC,YAAoF,OAAzE,aAAa,MAAM,KAAK,IAAI,yBAAyB;kCAC/E;;;;;;kCAGH,6LAAC;wBAAI,WAAU;;4BACZ,aAAa,MAAM,GAAG,mBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,GAAE;;;;;;;;;;;;;;;;0CAK3D,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAW,AAAC,gCAA0D,OAA3B,SAAS,eAAe;0CAEnE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;2EAEvB,aAAa,MAAM,KAAK,kBAC1B,6LAAC;gCAAI,WAAU;0CACZ,aAAa,aAAa;;;;;yFAG7B,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wCAEX,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAM,gBAAgB,aAAa;wDACnC,MAAK;wDACL,SAAS,aAAa,QAAQ,CAAC,IAAI,EAAE;wDACrC,UAAU,IAAM,gBAAgB,IAAI,EAAE;wDACtC,WAAU;;;;;;kEAEZ,6LAAC;wDACC,WAAW,AAAC,mBAGV,OAFA,gBAAgB,eAAe,gBAChC,gCAIA,OAHC,aAAa,QAAQ,CAAC,IAAI,EAAE,IACxB,8BACA;kEAGL,aAAa,QAAQ,CAAC,IAAI,EAAE,mBAC3B,6LAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0DAQZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAU;;sEAEV,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;sEAEJ,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,GAAE;;;;;;;;;;;;;;;;;0DAKzD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DACV,IAAI,IAAI;;;;;;;;;;;4CAKZ,4BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,IAAI,WAAW,IAAI;;;;;;kEAC1B,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,6LAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;;;;;;;;uCAlFL,IAAI,EAAE;;;;;;;;;;;;;;;wBA8FpB,aAAa,MAAM,GAAG,mBACrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAA+B;4CACxC,aAAa,MAAM;4CAAC;;;;;;;kDAE3B,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWZ,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GAvRa;KAAA", "debugId": null}}, {"offset": {"line": 6898, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/FilterBar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { CategoryFilter } from './CategoryFilter'\r\nimport { TagFilter } from './TagFilter'\r\n\r\ninterface FilterBarProps {\r\n  // 分类筛选\r\n  selectedCategories: string[]\r\n  onCategoryChange: (categoryIds: string[]) => void\r\n  \r\n  // 标签筛选\r\n  selectedTags: string[]\r\n  onTagChange: (tagIds: string[]) => void\r\n  \r\n  // 排序\r\n  sortBy: 'created' | 'updated' | 'usage' | 'title'\r\n  sortOrder: 'asc' | 'desc'\r\n  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void\r\n  \r\n  // 其他筛选\r\n  showFavorites?: boolean\r\n  onShowFavoritesChange?: (showFavorites: boolean) => void\r\n  \r\n  // 显示控制\r\n  showCategoryFilter?: boolean\r\n  showTagFilter?: boolean\r\n  showSortFilter?: boolean\r\n  showFavoriteFilter?: boolean\r\n  \r\n  className?: string\r\n}\r\n\r\nexport const FilterBar = ({\r\n  selectedCategories,\r\n  onCategoryChange,\r\n  selectedTags,\r\n  onTagChange,\r\n  sortBy,\r\n  sortOrder,\r\n  onSortChange,\r\n  showFavorites = false,\r\n  onShowFavoritesChange,\r\n  showCategoryFilter = true,\r\n  showTagFilter = true,\r\n  showSortFilter = true,\r\n  showFavoriteFilter = true,\r\n  className = '',\r\n}: FilterBarProps) => {\r\n  const [isExpanded, setIsExpanded] = useState(false)\r\n\r\n  // 排序选项\r\n  const sortOptions = [\r\n    { value: 'updated-desc', label: '最近更新', sortBy: 'updated', sortOrder: 'desc' },\r\n    { value: 'created-desc', label: '最新创建', sortBy: 'created', sortOrder: 'desc' },\r\n    { value: 'usage-desc', label: '使用最多', sortBy: 'usage', sortOrder: 'desc' },\r\n    { value: 'title-asc', label: '标题 A-Z', sortBy: 'title', sortOrder: 'asc' },\r\n    { value: 'title-desc', label: '标题 Z-A', sortBy: 'title', sortOrder: 'desc' },\r\n    { value: 'created-asc', label: '最早创建', sortBy: 'created', sortOrder: 'asc' },\r\n    { value: 'updated-asc', label: '最早更新', sortBy: 'updated', sortOrder: 'asc' },\r\n    { value: 'usage-asc', label: '使用最少', sortBy: 'usage', sortOrder: 'asc' },\r\n  ]\r\n\r\n  // 处理排序变化\r\n  const handleSortChange = (value: string) => {\r\n    const option = sortOptions.find(opt => opt.value === value)\r\n    if (option) {\r\n      onSortChange(option.sortBy, option.sortOrder as 'asc' | 'desc')\r\n    }\r\n  }\r\n\r\n  // 清除所有筛选\r\n  const clearAllFilters = () => {\r\n    onCategoryChange([])\r\n    onTagChange([])\r\n    if (onShowFavoritesChange) {\r\n      onShowFavoritesChange(false)\r\n    }\r\n  }\r\n\r\n  // 检查是否有活跃筛选\r\n  const hasActiveFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n      {/* 主筛选栏 */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 分类筛选 */}\r\n          {showCategoryFilter && (\r\n            <CategoryFilter\r\n              selectedCategories={selectedCategories}\r\n              onCategoryChange={onCategoryChange}\r\n              className=\"w-48\"\r\n            />\r\n          )}\r\n\r\n          {/* 标签筛选 */}\r\n          {showTagFilter && (\r\n            <TagFilter\r\n              selectedTags={selectedTags}\r\n              onTagChange={onTagChange}\r\n              className=\"w-48\"\r\n            />\r\n          )}\r\n\r\n          {/* 收藏筛选 */}\r\n          {showFavoriteFilter && onShowFavoritesChange && (\r\n            <motion.button\r\n              onClick={() => onShowFavoritesChange(!showFavorites)}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className={`flex items-center space-x-2 px-3 py-2 text-sm rounded-lg border transition-colors ${\r\n                showFavorites\r\n                  ? 'bg-warning text-warning-content border-warning'\r\n                  : 'bg-base-100 text-base-content border-base-300 hover:bg-base-200'\r\n              }`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill={showFavorites ? 'currentColor' : 'none'}\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\r\n                />\r\n              </svg>\r\n              <span>收藏</span>\r\n            </motion.button>\r\n          )}\r\n\r\n          {/* 展开/收起高级筛选 */}\r\n          <motion.button\r\n            onClick={() => setIsExpanded(!isExpanded)}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"flex items-center space-x-2 px-3 py-2 text-sm text-base-content/70 hover:text-base-content\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 12H7.5m0 6h9.75m-9.75 0a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 18H7.5\"\r\n              />\r\n            </svg>\r\n            <span>高级筛选</span>\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n            </svg>\r\n          </motion.button>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 排序选择 */}\r\n          {showSortFilter && (\r\n            <select\r\n              value={`${sortBy}-${sortOrder}`}\r\n              onChange={(e) => handleSortChange(e.target.value)}\r\n              className=\"select select-bordered select-sm\"\r\n            >\r\n              {sortOptions.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          )}\r\n\r\n          {/* 清除筛选 */}\r\n          {hasActiveFilters && (\r\n            <motion.button\r\n              onClick={clearAllFilters}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"flex items-center space-x-2 px-3 py-2 text-sm text-base-content/70 hover:text-base-content\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              <span>清除筛选</span>\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 高级筛选面板 */}\r\n      <AnimatePresence>\r\n        {isExpanded && (\r\n          <motion.div\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: 'auto' }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.2 }}\r\n            className=\"overflow-hidden\"\r\n          >\r\n            <div className=\"bg-base-100 border border-base-300 rounded-lg p-4 space-y-4\">\r\n              <h3 className=\"text-sm font-medium text-base-content\">高级筛选选项</h3>\r\n              \r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                {/* 更多筛选选项可以在这里添加 */}\r\n                \r\n                {/* 日期范围筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">创建日期</label>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"开始日期\"\r\n                    />\r\n                    <span className=\"text-base-content/50\">至</span>\r\n                    <input\r\n                      type=\"date\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"结束日期\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* 使用次数筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">使用次数</label>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"最少\"\r\n                    />\r\n                    <span className=\"text-base-content/50\">至</span>\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      className=\"input input-bordered input-sm flex-1\"\r\n                      placeholder=\"最多\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* 内容长度筛选 */}\r\n                <div className=\"space-y-2\">\r\n                  <label className=\"text-sm font-medium text-base-content\">内容长度</label>\r\n                  <select className=\"select select-bordered select-sm w-full\">\r\n                    <option value=\"\">全部</option>\r\n                    <option value=\"short\">短 (少于100字)</option>\r\n                    <option value=\"medium\">中 (100-500字)</option>\r\n                    <option value=\"long\">长 (超过500字)</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 应用/重置按钮 */}\r\n              <div className=\"flex justify-end space-x-2\">\r\n                <button className=\"btn btn-ghost btn-sm\">重置</button>\r\n                <button className=\"btn btn-primary btn-sm\">应用筛选</button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 活跃筛选标签 */}\r\n      {hasActiveFilters && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"flex items-center space-x-2 text-sm\"\r\n        >\r\n          <span className=\"text-base-content/70\">活跃筛选:</span>\r\n          \r\n          {/* 分类标签 */}\r\n          {selectedCategories.length > 0 && (\r\n            <span className=\"badge badge-primary badge-sm\">\r\n              {selectedCategories.length} 个分类\r\n            </span>\r\n          )}\r\n          \r\n          {/* 标签标签 */}\r\n          {selectedTags.length > 0 && (\r\n            <span className=\"badge badge-secondary badge-sm\">\r\n              {selectedTags.length} 个标签\r\n            </span>\r\n          )}\r\n          \r\n          {/* 收藏标签 */}\r\n          {showFavorites && (\r\n            <span className=\"badge badge-warning badge-sm\">\r\n              仅收藏\r\n            </span>\r\n          )}\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAkCO,MAAM,YAAY;QAAC,EACxB,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,MAAM,EACN,SAAS,EACT,YAAY,EACZ,gBAAgB,KAAK,EACrB,qBAAqB,EACrB,qBAAqB,IAAI,EACzB,gBAAgB,IAAI,EACpB,iBAAiB,IAAI,EACrB,qBAAqB,IAAI,EACzB,YAAY,EAAE,EACC;;IACf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,OAAO;IACP,MAAM,cAAc;QAClB;YAAE,OAAO;YAAgB,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAO;QAC7E;YAAE,OAAO;YAAgB,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAO;QAC7E;YAAE,OAAO;YAAc,OAAO;YAAQ,QAAQ;YAAS,WAAW;QAAO;QACzE;YAAE,OAAO;YAAa,OAAO;YAAU,QAAQ;YAAS,WAAW;QAAM;QACzE;YAAE,OAAO;YAAc,OAAO;YAAU,QAAQ;YAAS,WAAW;QAAO;QAC3E;YAAE,OAAO;YAAe,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAM;QAC3E;YAAE,OAAO;YAAe,OAAO;YAAQ,QAAQ;YAAW,WAAW;QAAM;QAC3E;YAAE,OAAO;YAAa,OAAO;YAAQ,QAAQ;YAAS,WAAW;QAAM;KACxE;IAED,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACrD,IAAI,QAAQ;YACV,aAAa,OAAO,MAAM,EAAE,OAAO,SAAS;QAC9C;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,iBAAiB,EAAE;QACnB,YAAY,EAAE;QACd,IAAI,uBAAuB;YACzB,sBAAsB;QACxB;IACF;IAEA,YAAY;IACZ,MAAM,mBAAmB,mBAAmB,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;IAErF,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAEZ,oCACC,6LAAC,kJAAA,CAAA,iBAAc;gCACb,oBAAoB;gCACpB,kBAAkB;gCAClB,WAAU;;;;;;4BAKb,+BACC,6LAAC,6IAAA,CAAA,YAAS;gCACR,cAAc;gCACd,aAAa;gCACb,WAAU;;;;;;4BAKb,sBAAsB,uCACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,sBAAsB,CAAC;gCACtC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAW,AAAC,qFAIX,OAHC,gBACI,mDACA;;kDAGN,6LAAC;wCACC,OAAM;wCACN,MAAM,gBAAgB,iBAAiB;wCACvC,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDAAK;;;;;;;;;;;;0CAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,cAAc,CAAC;gCAC9B,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAW,AAAC,gCAA8D,OAA/B,aAAa,eAAe;kDAEvE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAK3D,6LAAC;wBAAI,WAAU;;4BAEZ,gCACC,6LAAC;gCACC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;gCACpB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;0CAET,YAAY,GAAG,CAAC,CAAA,uBACf,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;4BAQ9B,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;kDAEvD,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDAIb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;kEACvC,6LAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;kEACvC,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAwC;;;;;;0DACzD,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAM3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAAuB;;;;;;kDACzC,6LAAC;wCAAO,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpD,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;oBAGtC,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;wBAAK,WAAU;;4BACb,mBAAmB,MAAM;4BAAC;;;;;;;oBAK9B,aAAa,MAAM,GAAG,mBACrB,6LAAC;wBAAK,WAAU;;4BACb,aAAa,MAAM;4BAAC;;;;;;;oBAKxB,+BACC,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;AAQ3D;GA/Ra;KAAA", "debugId": null}}, {"offset": {"line": 7522, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/filters/index.ts"], "sourcesContent": ["export { CategoryFilter } from './CategoryFilter'\r\nexport { TagFilter } from './TagFilter'\r\nexport { FilterBar } from './FilterBar'"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 7547, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MainLayout } from \"~/components/layout/MainLayout\";\nimport { PromptGrid } from \"~/components/prompts\";\nimport { FilterBar } from \"~/components/filters\";\nimport { api } from \"~/trpc/react\";\n\nexport default function Home() {\n  // 筛选状态\n  const [selectedCategories, setSelectedCategories] = useState<string[]>([])\n  const [selectedTags, setSelectedTags] = useState<string[]>([])\n  const [sortBy, setSortBy] = useState<'created' | 'updated' | 'usage' | 'title'>('updated')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')\n  const [showFavorites, setShowFavorites] = useState(false)\n\n  // 获取筛选后的提示词\n  const { data: prompts, isLoading } = api.prompts.getFiltered.useQuery({\n    categoryIds: selectedCategories.length > 0 ? selectedCategories : undefined,\n    tagIds: selectedTags.length > 0 ? selectedTags : undefined,\n    sortBy,\n    sortOrder,\n    favoritesOnly: showFavorites,\n    limit: 12,\n  })\n\n  // 获取最近使用的提示词（不受筛选影响）\n  const { data: recentPrompts, isLoading: recentLoading } = api.prompts.getRecent.useQuery({\n    limit: 6,\n  })\n\n  const { data: stats } = api.prompts.getStats.useQuery()\n\n  // 处理排序变化\n  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {\n    setSortBy(newSortBy as 'created' | 'updated' | 'usage' | 'title')\n    setSortOrder(newSortOrder)\n  }\n\n  // 检查是否有筛选条件\n  const hasFilters = selectedCategories.length > 0 || selectedTags.length > 0 || showFavorites\n\n  return (\n    <MainLayout>\n      <div className=\"space-y-6\">\n        {/* 欢迎横幅 */}\n        <div className=\"bg-gradient-to-r from-primary to-secondary text-primary-content rounded-lg p-6\">\n          <h1 className=\"text-3xl font-bold mb-2\">欢迎使用提示词管理工具</h1>\n          <p className=\"text-primary-content/90\">\n            管理你的 AI 提示词，提升工作效率\n          </p>\n        </div>\n\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-primary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">提示词总数</div>\n            <div className=\"stat-value text-primary\">{stats?.totalPrompts || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalPrompts ? '个人收藏' : '等待数据加载'}</div>\n          </div>\n\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-secondary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">分类数量</div>\n            <div className=\"stat-value text-secondary\">{stats?.totalCategories || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalCategories ? '个分类' : '等待数据加载'}</div>\n          </div>\n\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-accent\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">收藏数量</div>\n            <div className=\"stat-value text-accent\">{stats?.totalFavorites || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalFavorites ? '个收藏' : '等待数据加载'}</div>\n          </div>\n\n          <div className=\"stat bg-base-100 rounded-lg shadow\">\n            <div className=\"stat-figure text-warning\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                className=\"inline-block w-8 h-8 stroke-current\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                />\n              </svg>\n            </div>\n            <div className=\"stat-title\">总使用次数</div>\n            <div className=\"stat-value text-warning\">{stats?.totalUsage || 0}</div>\n            <div className=\"stat-desc\">{stats?.totalUsage ? '次使用' : '等待数据加载'}</div>\n          </div>\n        </div>\n\n        {/* 快速操作 */}\n        <div className=\"bg-base-100 rounded-lg p-6 shadow\">\n          <h2 className=\"text-xl font-semibold mb-4\">快速操作</h2>\n          <div className=\"flex flex-wrap gap-4\">\n            <a href=\"/prompts/new\" className=\"btn btn-primary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-5 h-5\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              新建提示词\n            </a>\n            <a href=\"/categories/new\" className=\"btn btn-secondary\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-5 h-5\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\n                />\n              </svg>\n              新建分类\n            </a>\n            <a href=\"/prompts/import\" className=\"btn btn-accent\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-5 h-5\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5\"\n                />\n              </svg>\n              批量导入\n            </a>\n          </div>\n        </div>\n\n        {/* 筛选和浏览 */}\n        <div className=\"bg-base-100 rounded-lg p-6 shadow\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-xl font-semibold\">\n              {hasFilters ? '筛选结果' : '浏览提示词'}\n            </h2>\n            <a href=\"/prompts\" className=\"text-sm text-primary hover:text-primary-focus\">\n              查看全部\n            </a>\n          </div>\n          \n          {/* 筛选栏 */}\n          <div className=\"mb-6\">\n            <FilterBar\n              selectedCategories={selectedCategories}\n              onCategoryChange={setSelectedCategories}\n              selectedTags={selectedTags}\n              onTagChange={setSelectedTags}\n              sortBy={sortBy}\n              sortOrder={sortOrder}\n              onSortChange={handleSortChange}\n              showFavorites={showFavorites}\n              onShowFavoritesChange={setShowFavorites}\n            />\n          </div>\n\n          {/* 提示词网格 */}\n          <PromptGrid\n            prompts={prompts || []}\n            loading={isLoading}\n            columns={3}\n            showCategory={true}\n            showActions={true}\n            emptyMessage={hasFilters ? \"未找到符合条件的提示词\" : \"暂无提示词\"}\n          />\n        </div>\n\n        {/* 最近使用 */}\n        {!hasFilters && (\n          <div className=\"bg-base-100 rounded-lg p-6 shadow\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h2 className=\"text-xl font-semibold\">最近使用</h2>\n              <a href=\"/prompts\" className=\"text-sm text-primary hover:text-primary-focus\">\n                查看全部\n              </a>\n            </div>\n            <PromptGrid\n              prompts={recentPrompts || []}\n              loading={recentLoading}\n              columns={3}\n              showCategory={true}\n              showActions={true}\n              emptyMessage=\"暂无最近使用的提示词\"\n            />\n          </div>\n        )}\n      </div>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,OAAO;IACP,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,YAAY;IACZ,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC;QACpE,aAAa,mBAAmB,MAAM,GAAG,IAAI,qBAAqB;QAClE,QAAQ,aAAa,MAAM,GAAG,IAAI,eAAe;QACjD;QACA;QACA,eAAe;QACf,OAAO;IACT;IAEA,qBAAqB;IACrB,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,aAAa,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvF,OAAO;IACT;IAEA,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ;IAErD,SAAS;IACT,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,UAAU;QACV,aAAa;IACf;IAEA,YAAY;IACZ,MAAM,aAAa,mBAAmB,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK;IAE/E,qBACE,6LAAC,6IAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAA0B;;;;;;;;;;;;8BAMzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;8CAAa;;;;;;8CAC5B,6LAAC;oCAAI,WAAU;8CAA2B,CAAA,kBAAA,4BAAA,MAAO,YAAY,KAAI;;;;;;8CACjE,6LAAC;oCAAI,WAAU;8CAAa,CAAA,kBAAA,4BAAA,MAAO,YAAY,IAAG,SAAS;;;;;;;;;;;;sCAG7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;8CAAa;;;;;;8CAC5B,6LAAC;oCAAI,WAAU;8CAA6B,CAAA,kBAAA,4BAAA,MAAO,eAAe,KAAI;;;;;;8CACtE,6LAAC;oCAAI,WAAU;8CAAa,CAAA,kBAAA,4BAAA,MAAO,eAAe,IAAG,QAAQ;;;;;;;;;;;;sCAG/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;8CAAa;;;;;;8CAC5B,6LAAC;oCAAI,WAAU;8CAA0B,CAAA,kBAAA,4BAAA,MAAO,cAAc,KAAI;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAa,CAAA,kBAAA,4BAAA,MAAO,cAAc,IAAG,QAAQ;;;;;;;;;;;;sCAG9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAI,WAAU;8CAAa;;;;;;8CAC5B,6LAAC;oCAAI,WAAU;8CAA2B,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;;;;;;8CAC/D,6LAAC;oCAAI,WAAU;8CAAa,CAAA,kBAAA,4BAAA,MAAO,UAAU,IAAG,QAAQ;;;;;;;;;;;;;;;;;;8BAK5D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAe,WAAU;;sDAC/B,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;wCAEA;;;;;;;8CAGR,6LAAC;oCAAE,MAAK;oCAAkB,WAAU;;sDAClC,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;wCAEA;;;;;;;8CAGR,6LAAC;oCAAE,MAAK;oCAAkB,WAAU;;sDAClC,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;wCAEA;;;;;;;;;;;;;;;;;;;8BAOZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,aAAa,SAAS;;;;;;8CAEzB,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAgD;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAA,CAAA,YAAS;gCACR,oBAAoB;gCACpB,kBAAkB;gCAClB,cAAc;gCACd,aAAa;gCACb,QAAQ;gCACR,WAAW;gCACX,cAAc;gCACd,eAAe;gCACf,uBAAuB;;;;;;;;;;;sCAK3B,6LAAC,8IAAA,CAAA,aAAU;4BACT,SAAS,WAAW,EAAE;4BACtB,SAAS;4BACT,SAAS;4BACT,cAAc;4BACd,aAAa;4BACb,cAAc,aAAa,gBAAgB;;;;;;;;;;;;gBAK9C,CAAC,4BACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAgD;;;;;;;;;;;;sCAI/E,6LAAC,8IAAA,CAAA,aAAU;4BACT,SAAS,iBAAiB,EAAE;4BAC5B,SAAS;4BACT,SAAS;4BACT,cAAc;4BACd,aAAa;4BACb,cAAa;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GAzPwB;KAAA", "debugId": null}}]}