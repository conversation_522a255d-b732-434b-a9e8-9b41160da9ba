# 部署指南

## Vercel 部署配置

### 1. 前期准备

#### 1.1 安装 Vercel CLI
```bash
npm install -g vercel
```

#### 1.2 登录 Vercel
```bash
vercel login
```

#### 1.3 链接项目
```bash
vercel link
```

### 2. 环境变量配置

#### 2.1 本地环境变量
复制 `.env.example` 为 `.env.local` 并填入实际值：

```bash
cp .env.example .env.local
```

#### 2.2 Vercel 环境变量设置
通过 Vercel Dashboard 或 CLI 设置环境变量：

**必需的环境变量：**
- `DATABASE_URL` - PostgreSQL 数据库连接字符串
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase 项目 URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase 匿名密钥
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase 服务角色密钥
- `NEXTAUTH_SECRET` - NextAuth.js 密钥
- `NEXTAUTH_URL` - 应用 URL

**CLI 设置方法：**
```bash
# 设置生产环境变量
vercel env add DATABASE_URL production

# 设置预览环境变量
vercel env add DATABASE_URL preview

# 设置开发环境变量
vercel env add DATABASE_URL development
```

**Dashboard 设置方法：**
1. 访问 Vercel Dashboard
2. 选择项目
3. 进入 Settings > Environment Variables
4. 添加环境变量

### 3. 部署流程

#### 3.1 预览部署
```bash
# 使用脚本（推荐）
./scripts/deploy.sh preview

# 或直接使用 Vercel CLI
vercel
```

#### 3.2 生产部署
```bash
# 使用脚本（推荐）
./scripts/deploy.sh production

# 或直接使用 Vercel CLI
vercel --prod
```

#### 3.3 自动部署
配置 GitHub 集成后，推送到以下分支会自动触发部署：
- `main` 分支 → 生产环境
- `develop` 分支 → 预览环境
- 其他分支 → 预览环境

### 4. 域名配置

#### 4.1 自定义域名
通过 Vercel Dashboard 添加自定义域名：
1. 进入 Settings > Domains
2. 添加域名
3. 配置 DNS 记录

#### 4.2 SSL 证书
Vercel 自动为所有域名提供 SSL 证书，无需手动配置。

### 5. 性能优化

#### 5.1 构建优化
```bash
# 分析构建产物
npm run build:analyze

# 检查构建大小
npm run build:size
```

#### 5.2 缓存策略
- 静态资源：长期缓存（1年）
- API 响应：短期缓存（1分钟）
- 页面：增量静态重新生成（ISR）

#### 5.3 边缘函数
- API 路由自动部署为边缘函数
- 响应时间 < 100ms
- 全球分布

### 6. 监控和日志

#### 6.1 访问日志
```bash
# 查看实时日志
vercel logs

# 查看特定部署的日志
vercel logs [deployment-url]
```

#### 6.2 性能监控
- 使用 Vercel Analytics
- 配置 Sentry 错误监控
- 设置 Uptime 监控

#### 6.3 健康检查
健康检查端点：`/api/health`
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "production",
  "uptime": 3600,
  "memory": {
    "used": 50,
    "total": 100,
    "percentage": 50
  },
  "database": {
    "status": "connected",
    "latency": 25
  },
  "services": {
    "api": "healthy",
    "database": "healthy"
  }
}
```

### 7. 故障排除

#### 7.1 常见问题

**构建失败**
```bash
# 本地构建测试
npm run build

# 检查类型错误
npm run type-check

# 检查语法错误
npm run lint
```

**环境变量问题**
```bash
# 检查环境变量
vercel env ls

# 拉取远程环境变量
vercel env pull .env.local
```

**数据库连接问题**
- 检查 DATABASE_URL 格式
- 确认数据库服务器可访问
- 检查防火墙设置

#### 7.2 调试技巧
1. 使用 `vercel dev` 本地模拟
2. 查看构建日志
3. 检查函数执行时间
4. 监控内存使用

### 8. 安全配置

#### 8.1 HTTP 头部
已配置的安全头部：
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Content-Security-Policy: ...`

#### 8.2 API 安全
- 认证中间件
- 速率限制
- CORS 配置
- 输入验证

#### 8.3 数据保护
- 数据库加密
- 敏感信息脱敏
- 审计日志

### 9. 最佳实践

#### 9.1 部署策略
1. 使用预览环境测试
2. 渐进式发布
3. 监控关键指标
4. 准备回滚计划

#### 9.2 代码质量
1. 代码审查
2. 自动化测试
3. 性能测试
4. 安全扫描

#### 9.3 团队协作
1. 环境隔离
2. 权限管理
3. 变更通知
4. 文档维护

### 10. 高级配置

#### 10.1 边缘配置
```javascript
// vercel.json
{
  "regions": ["hkg1", "sin1", "nrt1"],
  "functions": {
    "app/api/trpc/[trpc]/route.ts": {
      "maxDuration": 30
    }
  }
}
```

#### 10.2 重写和重定向
```javascript
// vercel.json
{
  "rewrites": [
    {
      "source": "/api/trpc/:path*",
      "destination": "/api/trpc/:path*"
    }
  ],
  "redirects": [
    {
      "source": "/login",
      "destination": "/auth/login",
      "permanent": true
    }
  ]
}
```

#### 10.3 构建配置
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client']
  }
}
```

### 11. 成本优化

#### 11.1 监控使用量
- 函数执行时间
- 带宽使用
- 构建时间
- 存储使用

#### 11.2 优化策略
1. 减少包大小
2. 优化图片
3. 使用 CDN
4. 缓存策略

### 12. 备份和恢复

#### 12.1 数据备份
- 数据库定期备份
- 文件存储备份
- 配置备份

#### 12.2 灾难恢复
- 多区域部署
- 故障转移
- 数据恢复

---

## 联系支持

如果遇到问题，请：
1. 查看 Vercel 文档
2. 检查 GitHub Issues
3. 联系团队成员
4. 提交支持请求