'use client'

import { Suspense } from 'react'
import { SearchInput, SearchResults } from '~/components/search'

function SearchPageContent() {
  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-base-content">搜索提示词</h1>
      </div>

      {/* 搜索输入框 */}
      <div className="max-w-2xl">
        <SearchInput
          placeholder="搜索提示词、标签或分类..."
          size="lg"
          showHistory={true}
          showSuggestions={true}
        />
      </div>

      {/* 搜索结果 */}
      <SearchResults />
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    }>
      <SearchPageContent />
    </Suspense>
  )
}