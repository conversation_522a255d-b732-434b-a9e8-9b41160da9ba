'use client'

import { useState } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { api } from '~/trpc/react'
import { PromptGrid } from '~/components/prompts/PromptGrid'
import { CategoryForm } from '~/components/categories/CategoryForm'
import { ConfirmDeleteModal } from '~/components/modals/ConfirmDeleteModal'
import { toast } from 'react-hot-toast'

export default function CategoryDetailPage() {
  const params = useParams()
  const router = useRouter()
  const categoryId = params.id as string

  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'usage'>('updated')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 获取分类详情
  const { data: category, isLoading, refetch } = api.categories.getById.useQuery({
    id: categoryId,
  })

  // 获取分类下的提示词
  const { data: prompts, isLoading: promptsLoading } = api.prompts.getByCategory.useQuery({
    categoryId,
    sortBy,
    sortOrder,
  })

  // 删除分类
  const deleteMutation = api.categories.delete.useMutation({
    onSuccess: () => {
      toast.success('分类删除成功')
      router.push('/categories')
    },
    onError: (error) => {
      toast.error('删除失败: ' + error.message)
    },
  })

  // 处理删除
  const handleDelete = () => {
    if (category) {
      deleteMutation.mutate({ id: category.id })
    }
  }

  // 处理表单关闭
  const handleFormClose = () => {
    setIsEditing(false)
    refetch()
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          {/* 返回按钮骨架 */}
          <div className="h-10 w-20 bg-base-300 rounded"></div>
          
          {/* 分类头部骨架 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-base-300 rounded-lg"></div>
              <div className="space-y-2">
                <div className="h-8 w-48 bg-base-300 rounded"></div>
                <div className="h-4 w-32 bg-base-300 rounded"></div>
              </div>
            </div>
            <div className="h-10 w-24 bg-base-300 rounded"></div>
          </div>
          
          {/* 统计信息骨架 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className="bg-base-100 rounded-lg p-4 space-y-3">
                <div className="h-4 w-20 bg-base-300 rounded"></div>
                <div className="h-8 w-16 bg-base-300 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!category) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-16 h-16 mx-auto text-base-content/50 mb-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
            />
          </svg>
          <h2 className="text-xl font-semibold text-base-content mb-2">分类不存在</h2>
          <p className="text-base-content/70 mb-4">该分类可能已被删除或不存在</p>
          <Link href="/categories" className="btn btn-primary">
            返回分类列表
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 返回按钮 */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
      >
        <Link href="/categories" className="btn btn-ghost btn-sm">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
          </svg>
          返回分类列表
        </Link>
      </motion.div>

      {/* 分类头部 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div className="flex items-center space-x-4">
          {/* 分类图标 */}
          <div
            className="w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-2xl shadow-lg"
            style={{ backgroundColor: category.color }}
          >
            {category.icon || category.name.charAt(0)}
          </div>

          {/* 分类信息 */}
          <div>
            <h1 className="text-3xl font-bold text-base-content">{category.name}</h1>
            {category.description && (
              <p className="text-base-content/70 mt-1">{category.description}</p>
            )}
            <div className="flex items-center space-x-4 mt-2 text-sm text-base-content/60">
              <span>{category.promptCount || 0} 个提示词</span>
              <span>使用 {category.usageCount || 0} 次</span>
              <span>更新于 {new Date(category.updatedAt).toLocaleDateString('zh-CN')}</span>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsEditing(true)}
            className="btn btn-ghost btn-sm"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
              />
            </svg>
            编辑
          </button>

          <button
            onClick={() => setIsDeleting(true)}
            className="btn btn-ghost btn-sm text-error hover:btn-error"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-4 h-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
              />
            </svg>
            删除
          </button>
        </div>
      </motion.div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="stat bg-base-100 rounded-lg shadow-md"
        >
          <div className="stat-title">提示词数量</div>
          <div className="stat-value text-primary">{category.promptCount || 0}</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="stat bg-base-100 rounded-lg shadow-md"
        >
          <div className="stat-title">使用次数</div>
          <div className="stat-value text-secondary">{category.usageCount || 0}</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="stat bg-base-100 rounded-lg shadow-md"
        >
          <div className="stat-title">平均使用率</div>
          <div className="stat-value text-accent">
            {category.promptCount ? Math.round((category.usageCount || 0) / category.promptCount) : 0}
          </div>
        </motion.div>
      </div>

      {/* 提示词列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-4"
      >
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-base-content">提示词列表</h2>
          
          <div className="flex items-center space-x-3">
            {/* 排序选择 */}
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [newSortBy, newSortOrder] = e.target.value.split('-')
                setSortBy(newSortBy as 'updated' | 'created' | 'usage')
                setSortOrder(newSortOrder as 'asc' | 'desc')
              }}
              className="select select-bordered select-sm"
            >
              <option value="updated-desc">最近更新</option>
              <option value="created-desc">最新创建</option>
              <option value="usage-desc">使用最多</option>
              <option value="updated-asc">最早更新</option>
              <option value="created-asc">最早创建</option>
              <option value="usage-asc">使用最少</option>
            </select>

            {/* 新建提示词按钮 */}
            <Link
              href={`/prompts/new?categoryId=${category.id}`}
              className="btn btn-primary btn-sm"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
              新建提示词
            </Link>
          </div>
        </div>

        {/* 提示词网格 */}
        <PromptGrid
          prompts={prompts || []}
          loading={promptsLoading}
          showCategory={false}
          emptyMessage="该分类下还没有提示词"
          emptyAction={
            <Link
              href={`/prompts/new?categoryId=${category.id}`}
              className="btn btn-primary"
            >
              创建第一个提示词
            </Link>
          }
        />
      </motion.div>

      {/* 编辑表单 */}
      <AnimatePresence>
        {isEditing && (
          <CategoryForm
            category={category}
            onClose={handleFormClose}
          />
        )}
      </AnimatePresence>

      {/* 删除确认对话框 */}
      <AnimatePresence>
        {isDeleting && (
          <ConfirmDeleteModal
            isOpen={isDeleting}
            onClose={() => setIsDeleting(false)}
            onConfirm={handleDelete}
            title="删除分类"
            message={`确定要删除分类 "${category.name}" 吗？删除后该分类下的所有提示词将移至"未分类"。`}
            type="warning"
            confirmText="删除"
            isLoading={deleteMutation.isLoading}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

