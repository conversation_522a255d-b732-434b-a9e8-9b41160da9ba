{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/tags/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { api } from '~/trpc/react'\nimport { motion } from 'framer-motion'\n\nexport default function TagsPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n\n  // 获取所有标签\n  const { data: tags, isLoading, refetch } = api.tags.getAll.useQuery()\n\n  // 过滤标签\n  const filteredTags = tags?.filter(tag =>\n    tag.name.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || []\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 页面标题 */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-base-content mb-2\">标签管理</h1>\n        <p className=\"text-base-content/70\">管理和组织你的所有标签</p>\n      </div>\n\n      {/* 搜索和操作栏 */}\n      <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n        {/* 搜索框 */}\n        <div className=\"flex-1\">\n          <div className=\"relative\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              strokeWidth={1.5}\n              stroke=\"currentColor\"\n              className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\n              />\n            </svg>\n            <input\n              type=\"text\"\n              placeholder=\"搜索标签...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"input input-bordered w-full pl-10\"\n            />\n          </div>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => refetch()}\n            className=\"btn btn-outline\"\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <span className=\"loading loading-spinner loading-sm\"></span>\n            ) : (\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n                />\n              </svg>\n            )}\n            刷新\n          </button>\n        </div>\n      </div>\n\n      {/* 标签列表 */}\n      <div className=\"grid gap-4\">\n        {isLoading ? (\n          <div className=\"text-center py-12\">\n            <span className=\"loading loading-spinner loading-lg\"></span>\n            <p className=\"mt-4 text-base-content/70\">加载标签中...</p>\n          </div>\n        ) : filteredTags.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              strokeWidth={1.5}\n              stroke=\"currentColor\"\n              className=\"w-16 h-16 mx-auto text-base-content/30 mb-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\n              />\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\n            </svg>\n            <h3 className=\"text-lg font-medium text-base-content/70 mb-2\">\n              {searchTerm ? '未找到匹配的标签' : '暂无标签'}\n            </h3>\n            <p className=\"text-base-content/50\">\n              {searchTerm ? '尝试使用其他关键词搜索' : '创建提示词时会自动生成标签'}\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n            {filteredTags.map((tag, index) => (\n              <motion.div\n                key={tag.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.05 }}\n                className=\"card bg-base-100 shadow-sm hover:shadow-md transition-shadow\"\n              >\n                <div className=\"card-body p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2 min-w-0\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        strokeWidth={1.5}\n                        stroke=\"currentColor\"\n                        className=\"w-4 h-4 text-primary flex-shrink-0\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\n                        />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\n                      </svg>\n                      <span className=\"font-medium text-base-content truncate\">\n                        {tag.name}\n                      </span>\n                    </div>\n                    <div className=\"badge badge-primary badge-sm\">\n                      {tag._count?.prompts || 0}\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-3\">\n                    <Link\n                      href={`/tags/${encodeURIComponent(tag.name)}`}\n                      className=\"btn btn-sm btn-outline w-full\"\n                    >\n                      查看提示词\n                    </Link>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* 统计信息 */}\n      {!isLoading && tags && tags.length > 0 && (\n        <div className=\"mt-8 text-center text-sm text-base-content/50\">\n          共 {tags.length} 个标签\n          {searchTerm && filteredTags.length !== tags.length && (\n            <span>，显示 {filteredTags.length} 个匹配结果</span>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,SAAS;IACT,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;IAEnE,OAAO;IACP,MAAM,eAAe,CAAA,iBAAA,2BAAA,KAAM,MAAM,CAAC,CAAA,MAChC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SACnD,EAAE;IAEP,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAC1D,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;8CAGN,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM;4BACf,WAAU;4BACV,UAAU;;gCAET,0BACC,6LAAC;oCAAK,WAAU;;;;;yDAEhB,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;gCAGN;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;0BACZ,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;;;;;sCAChB,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;2BAEzC,aAAa,MAAM,KAAK,kBAC1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;;8CAEV,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;8CAEJ,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;sCAEvD,6LAAC;4BAAG,WAAU;sCACX,aAAa,aAAa;;;;;;sCAE7B,6LAAC;4BAAE,WAAU;sCACV,aAAa,gBAAgB;;;;;;;;;;;yCAIlC,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,KAAK;4BA+Bb;6CA9BT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAK;4BAClC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;;0EAEV,6LAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;0EAEJ,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;kEAEvD,6LAAC;wDAAK,WAAU;kEACb,IAAI,IAAI;;;;;;;;;;;;0DAGb,6LAAC;gDAAI,WAAU;0DACZ,EAAA,cAAA,IAAI,MAAM,cAAV,kCAAA,YAAY,OAAO,KAAI;;;;;;;;;;;;kDAI5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,AAAC,SAAqC,OAA7B,mBAAmB,IAAI,IAAI;4CAC1C,WAAU;sDACX;;;;;;;;;;;;;;;;;2BArCA,IAAI,EAAE;;;;;;;;;;;;;;;;YAiDpB,CAAC,aAAa,QAAQ,KAAK,MAAM,GAAG,mBACnC,6LAAC;gBAAI,WAAU;;oBAAgD;oBAC1D,KAAK,MAAM;oBAAC;oBACd,cAAc,aAAa,MAAM,KAAK,KAAK,MAAM,kBAChD,6LAAC;;4BAAK;4BAAK,aAAa,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAM3C;GA5KwB;KAAA", "debugId": null}}]}