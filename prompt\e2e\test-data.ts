// E2E 测试数据
export const testData = {
  // 测试用户数据
  users: {
    testUser: {
      email: '<EMAIL>',
      password: 'password123',
      name: '测试用户'
    },
    adminUser: {
      email: '<EMAIL>',
      password: 'admin123',
      name: '管理员'
    }
  },

  // 测试分类数据
  categories: {
    work: {
      name: '工作',
      description: '工作相关的提示词',
      color: '#3b82f6',
      icon: '💼'
    },
    study: {
      name: '学习',
      description: '学习相关的提示词',
      color: '#10b981',
      icon: '📚'
    },
    life: {
      name: '生活',
      description: '生活相关的提示词',
      color: '#f59e0b',
      icon: '🏠'
    }
  },

  // 测试提示词数据
  prompts: {
    codeReview: {
      title: '代码审查提示',
      content: '请审查以下代码，重点关注：\\n1. 代码逻辑是否正确\\n2. 是否符合最佳实践\\n3. 是否存在潜在问题\\n\\n代码：\\n```\\n{code}\\n```',
      description: '用于代码审查的提示词模板',
      category: '工作',
      tags: ['代码', '审查', '开发'],
      isPublic: true
    },
    documentWriter: {
      title: '文档写作助手',
      content: '帮我写一份关于 {topic} 的技术文档，包括：\\n1. 概述\\n2. 详细说明\\n3. 示例代码\\n4. 注意事项',
      description: '技术文档写作助手',
      category: '工作',
      tags: ['文档', '写作', '技术'],
      isPublic: true
    },
    englishTutor: {
      title: '英语学习助手',
      content: '作为英语老师，帮我纠正以下文本的语法和表达：\\n\\n{text}\\n\\n请提供：\\n1. 修正后的文本\\n2. 具体的修改说明\\n3. 相关的语法知识点',
      description: '英语学习和语法纠正助手',
      category: '学习',
      tags: ['英语', '语法', '学习'],
      isPublic: true
    },
    mealPlanner: {
      title: '膳食计划助手',
      content: '请为我制定一周的健康膳食计划，要求：\\n1. 营养均衡\\n2. 考虑 {dietary_restrictions}\\n3. 包含早餐、午餐、晚餐\\n4. 提供简单的制作方法',
      description: '个人膳食计划制定助手',
      category: '生活',
      tags: ['膳食', '健康', '计划'],
      isPublic: false
    }
  },

  // 测试标签数据
  tags: [
    '代码', '审查', '开发', '文档', '写作', '技术',
    '英语', '语法', '学习', '膳食', '健康', '计划',
    '工作', '生活', '效率', '工具', '模板', '助手'
  ],

  // 测试搜索关键词
  searchKeywords: [
    '代码审查',
    '文档写作',
    '英语学习',
    '膳食计划',
    '工作效率',
    '代码',
    '学习',
    '助手'
  ],

  // 测试导入数据
  importData: {
    categories: [
      {
        name: '测试分类1',
        description: '这是第一个测试分类',
        color: '#ef4444',
        icon: '🔥'
      },
      {
        name: '测试分类2',
        description: '这是第二个测试分类',
        color: '#8b5cf6',
        icon: '⚡'
      }
    ],
    prompts: [
      {
        title: '测试提示词1',
        content: '这是测试提示词的内容1',
        description: '测试描述1',
        category: '测试分类1',
        tags: ['测试', '导入'],
        isPublic: true
      },
      {
        title: '测试提示词2',
        content: '这是测试提示词的内容2',
        description: '测试描述2',
        category: '测试分类2',
        tags: ['测试', '导入'],
        isPublic: false
      }
    ]
  },

  // 测试批量操作数据
  batchOperations: {
    prompts: [
      {
        title: '批量测试提示词1',
        content: '批量测试内容1',
        description: '批量测试描述1',
        category: '工作',
        tags: ['批量', '测试'],
        isPublic: true
      },
      {
        title: '批量测试提示词2',
        content: '批量测试内容2',
        description: '批量测试描述2',
        category: '学习',
        tags: ['批量', '测试'],
        isPublic: true
      },
      {
        title: '批量测试提示词3',
        content: '批量测试内容3',
        description: '批量测试描述3',
        category: '生活',
        tags: ['批量', '测试'],
        isPublic: false
      }
    ]
  },

  // 测试性能数据
  performance: {
    largePromptContent: 'A'.repeat(5000), // 大型提示词内容
    manyTags: Array.from({ length: 50 }, (_, i) => `标签${i + 1}`), // 多标签
    longTitle: '这是一个非常长的标题，用于测试标题长度限制和显示效果的处理情况'.repeat(3)
  },

  // 测试错误场景数据
  errorScenarios: {
    invalidPrompt: {
      title: '', // 空标题
      content: '', // 空内容
      description: 'A'.repeat(1000), // 过长描述
      category: '不存在的分类',
      tags: ['', '  ', 'A'.repeat(100)], // 无效标签
      isPublic: 'invalid' // 无效的公开状态
    },
    invalidCategory: {
      name: '', // 空名称
      description: 'A'.repeat(1000), // 过长描述
      color: 'invalid-color', // 无效颜色
      icon: '' // 空图标
    }
  },

  // 测试边界值数据
  boundaryValues: {
    prompt: {
      minTitle: 'A', // 最小标题
      maxTitle: 'A'.repeat(100), // 最大标题
      minContent: 'A', // 最小内容
      maxContent: 'A'.repeat(10000), // 最大内容
      minDescription: 'A', // 最小描述
      maxDescription: 'A'.repeat(500), // 最大描述
      maxTags: 20 // 最大标签数量
    },
    category: {
      minName: 'A', // 最小名称
      maxName: 'A'.repeat(50), // 最大名称
      minDescription: 'A', // 最小描述
      maxDescription: 'A'.repeat(200) // 最大描述
    }
  }
}

// 测试辅助函数
export const testHelpers = {
  // 生成随机字符串
  generateRandomString: (length: number) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  // 生成随机邮箱
  generateRandomEmail: () => {
    const randomString = testHelpers.generateRandomString(8)
    return `test${randomString}@example.com`
  },

  // 生成随机颜色
  generateRandomColor: () => {
    const colors = ['#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16', '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9', '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef', '#ec4899', '#f43f5e']
    return colors[Math.floor(Math.random() * colors.length)]
  },

  // 生成随机图标
  generateRandomIcon: () => {
    const icons = ['🔥', '⚡', '💡', '🚀', '🎯', '💼', '📚', '🏠', '🎨', '🔧', '⭐', '🌟', '✨', '🎪', '🎭', '🎨', '🎯', '🎪']
    return icons[Math.floor(Math.random() * icons.length)]
  },

  // 等待指定时间
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // 格式化日期
  formatDate: (date: Date) => {
    return date.toISOString().split('T')[0]
  },

  // 获取当前时间戳
  getCurrentTimestamp: () => {
    return Date.now()
  }
}

// 测试配置
export const testConfig = {
  // 超时设置
  timeouts: {
    short: 1000,
    medium: 5000,
    long: 10000,
    veryLong: 30000
  },

  // 重试设置
  retries: {
    flaky: 3,
    network: 2,
    default: 1
  },

  // 视口设置
  viewports: {
    mobile: { width: 375, height: 667 },
    tablet: { width: 768, height: 1024 },
    desktop: { width: 1920, height: 1080 },
    ultrawide: { width: 3440, height: 1440 }
  },

  // 测试用例标签
  tags: {
    smoke: 'smoke',
    regression: 'regression',
    performance: 'performance',
    accessibility: 'accessibility',
    integration: 'integration',
    api: 'api',
    ui: 'ui'
  },

  // 测试环境
  environments: {
    development: 'http://localhost:3000',
    staging: 'https://staging.example.com',
    production: 'https://production.example.com'
  }
}