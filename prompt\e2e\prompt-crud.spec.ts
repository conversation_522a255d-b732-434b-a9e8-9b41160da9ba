import { test, expect } from '@playwright/test'

test.describe('提示词 CRUD 操作', () => {
  test.beforeEach(async ({ page }) => {
    // 访问提示词列表页面
    await page.goto('/prompts')
  })

  test('应该能够创建新的提示词', async ({ page }) => {
    // 点击新建提示词按钮
    await page.click('[data-testid="new-prompt-button"]')
    
    // 检查是否跳转到新建页面
    await expect(page).toHaveURL('/prompts/new')
    
    // 填写表单
    await page.fill('[data-testid="prompt-title-input"]', '测试提示词')
    await page.fill('[data-testid="prompt-content-input"]', '这是一个测试提示词的内容')
    await page.fill('[data-testid="prompt-description-input"]', '这是测试描述')
    
    // 选择分类
    await page.click('[data-testid="category-select"]')
    await page.click('[data-testid="category-option-工作"]')
    
    // 添加标签
    await page.fill('[data-testid="tags-input"]', '测试')
    await page.keyboard.press('Enter')
    await page.fill('[data-testid="tags-input"]', '开发')
    await page.keyboard.press('Enter')
    
    // 提交表单
    await page.click('[data-testid="save-prompt-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=提示词创建成功')).toBeVisible()
    
    // 检查是否跳转到提示词列表页面
    await expect(page).toHaveURL('/prompts')
    
    // 检查新创建的提示词是否出现在列表中
    await expect(page.locator('[data-testid="prompt-card"]')).toContainText('测试提示词')
  })

  test('应该能够查看提示词详情', async ({ page }) => {
    // 点击第一个提示词卡片
    await page.click('[data-testid="prompt-card"]:first-child')
    
    // 检查是否打开详情模态框
    await expect(page.locator('[data-testid="prompt-detail-modal"]')).toBeVisible()
    
    // 检查详情内容
    await expect(page.locator('[data-testid="prompt-detail-title"]')).toBeVisible()
    await expect(page.locator('[data-testid="prompt-detail-content"]')).toBeVisible()
    await expect(page.locator('[data-testid="prompt-detail-category"]')).toBeVisible()
    await expect(page.locator('[data-testid="prompt-detail-tags"]')).toBeVisible()
    
    // 关闭模态框
    await page.click('[data-testid="close-modal-button"]')
    
    // 检查模态框是否关闭
    await expect(page.locator('[data-testid="prompt-detail-modal"]')).not.toBeVisible()
  })

  test('应该能够编辑提示词', async ({ page }) => {
    // 点击第一个提示词的编辑按钮
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="edit-prompt-button"]')
    
    // 检查是否打开编辑模态框
    await expect(page.locator('[data-testid="edit-prompt-modal"]')).toBeVisible()
    
    // 修改标题
    const titleInput = page.locator('[data-testid="prompt-title-input"]')
    await titleInput.clear()
    await titleInput.fill('修改后的提示词标题')
    
    // 修改内容
    const contentInput = page.locator('[data-testid="prompt-content-input"]')
    await contentInput.clear()
    await contentInput.fill('修改后的提示词内容')
    
    // 保存修改
    await page.click('[data-testid="save-prompt-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=提示词更新成功')).toBeVisible()
    
    // 检查模态框是否关闭
    await expect(page.locator('[data-testid="edit-prompt-modal"]')).not.toBeVisible()
    
    // 检查修改是否生效
    await expect(page.locator('[data-testid="prompt-card"]')).toContainText('修改后的提示词标题')
  })

  test('应该能够删除提示词', async ({ page }) => {
    // 点击第一个提示词的删除按钮
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="delete-prompt-button"]')
    
    // 检查是否出现确认删除对话框
    await expect(page.locator('[data-testid="confirm-delete-modal"]')).toBeVisible()
    await expect(page.locator('text=确认删除此提示词吗？')).toBeVisible()
    
    // 点击确认删除
    await page.click('[data-testid="confirm-delete-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=提示词删除成功')).toBeVisible()
    
    // 检查对话框是否关闭
    await expect(page.locator('[data-testid="confirm-delete-modal"]')).not.toBeVisible()
  })

  test('应该能够批量删除提示词', async ({ page }) => {
    // 选择多个提示词
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="select-checkbox"]')
    await page.click('[data-testid="prompt-card"]:nth-child(2) [data-testid="select-checkbox"]')
    
    // 检查是否出现批量操作按钮
    await expect(page.locator('[data-testid="batch-delete-button"]')).toBeVisible()
    await expect(page.locator('[data-testid="batch-delete-button"]')).toContainText('删除 (2)')
    
    // 点击批量删除
    await page.click('[data-testid="batch-delete-button"]')
    
    // 检查是否出现确认删除对话框
    await expect(page.locator('[data-testid="confirm-batch-delete-modal"]')).toBeVisible()
    await expect(page.locator('text=确认删除选中的 2 个提示词吗？')).toBeVisible()
    
    // 点击确认删除
    await page.click('[data-testid="confirm-batch-delete-button"]')
    
    // 检查是否显示成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=成功删除 2 个提示词')).toBeVisible()
  })

  test('应该能够复制提示词', async ({ page }) => {
    // 点击第一个提示词的复制按钮
    await page.click('[data-testid="prompt-card"]:first-child [data-testid="copy-prompt-button"]')
    
    // 检查是否显示复制成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=已复制到剪贴板')).toBeVisible()
    
    // 检查复制按钮状态变化
    await expect(page.locator('[data-testid="prompt-card"]:first-child [data-testid="copy-prompt-button"]')).toHaveClass(/copied/)
  })

  test('应该能够收藏/取消收藏提示词', async ({ page }) => {
    // 点击第一个提示词的收藏按钮
    const favoriteButton = page.locator('[data-testid="prompt-card"]:first-child [data-testid="favorite-button"]')
    await favoriteButton.click()
    
    // 检查是否显示收藏成功消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=已添加到收藏')).toBeVisible()
    
    // 检查收藏按钮状态变化
    await expect(favoriteButton).toHaveClass(/favorited/)
    
    // 再次点击取消收藏
    await favoriteButton.click()
    
    // 检查是否显示取消收藏消息
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    await expect(page.locator('text=已从收藏移除')).toBeVisible()
    
    // 检查收藏按钮状态恢复
    await expect(favoriteButton).not.toHaveClass(/favorited/)
  })

  test('应该能够筛选提示词', async ({ page }) => {
    // 点击筛选按钮
    await page.click('[data-testid="filter-button"]')
    
    // 检查筛选面板是否打开
    await expect(page.locator('[data-testid="filter-panel"]')).toBeVisible()
    
    // 选择分类筛选
    await page.click('[data-testid="category-filter-工作"]')
    
    // 应用筛选
    await page.click('[data-testid="apply-filter-button"]')
    
    // 检查筛选结果
    await expect(page.locator('[data-testid="filter-active-indicator"]')).toBeVisible()
    await expect(page.locator('[data-testid="filter-active-indicator"]')).toContainText('工作')
    
    // 清除筛选
    await page.click('[data-testid="clear-filter-button"]')
    
    // 检查筛选是否清除
    await expect(page.locator('[data-testid="filter-active-indicator"]')).not.toBeVisible()
  })

  test('应该能够搜索提示词', async ({ page }) => {
    // 在搜索框中输入关键词
    await page.fill('[data-testid="search-input"]', '代码审查')
    
    // 按回车搜索
    await page.keyboard.press('Enter')
    
    // 检查搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
    await expect(page.locator('[data-testid="prompt-card"]')).toContainText('代码审查')
    
    // 检查高亮显示
    await expect(page.locator('[data-testid="search-highlight"]')).toBeVisible()
    
    // 清除搜索
    await page.click('[data-testid="clear-search-button"]')
    
    // 检查搜索是否清除
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('')
  })

  test('应该能够排序提示词', async ({ page }) => {
    // 点击排序按钮
    await page.click('[data-testid="sort-button"]')
    
    // 检查排序选项
    await expect(page.locator('[data-testid="sort-options"]')).toBeVisible()
    
    // 选择按使用次数排序
    await page.click('[data-testid="sort-by-usage"]')
    
    // 检查排序是否生效
    await expect(page.locator('[data-testid="sort-indicator"]')).toContainText('使用次数')
    
    // 改变排序方向
    await page.click('[data-testid="sort-direction-button"]')
    
    // 检查排序方向是否改变
    await expect(page.locator('[data-testid="sort-direction-button"]')).toHaveClass(/desc/)
  })

  test('应该显示空状态', async ({ page }) => {
    // 拦截API请求，返回空数据
    await page.route('**/api/trpc/prompts.getAll**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ result: { data: [] } })
      })
    })
    
    // 重新加载页面
    await page.reload()
    
    // 检查空状态
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible()
    await expect(page.locator('text=暂无提示词')).toBeVisible()
    await expect(page.locator('text=开始创建您的第一个提示词')).toBeVisible()
    
    // 检查创建按钮
    await expect(page.locator('[data-testid="create-first-prompt-button"]')).toBeVisible()
  })

  test('应该处理表单验证错误', async ({ page }) => {
    // 点击新建提示词按钮
    await page.click('[data-testid="new-prompt-button"]')
    
    // 不填写任何内容直接提交
    await page.click('[data-testid="save-prompt-button"]')
    
    // 检查验证错误消息
    await expect(page.locator('[data-testid="title-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="title-error"]')).toContainText('标题不能为空')
    
    await expect(page.locator('[data-testid="content-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="content-error"]')).toContainText('内容不能为空')
    
    // 填写过长的标题
    await page.fill('[data-testid="prompt-title-input"]', 'a'.repeat(101))
    
    // 检查长度验证错误
    await expect(page.locator('[data-testid="title-error"]')).toContainText('标题长度不能超过100个字符')
  })

  test('应该支持无障碍性', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/提示词管理/)
    
    // 检查主要标题的语义
    await expect(page.locator('h1')).toBeVisible()
    
    // 检查键盘导航
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="new-prompt-button"]')).toBeFocused()
    
    // 检查 ARIA 标签
    await expect(page.locator('[data-testid="prompt-card"]')).toHaveAttribute('role', 'article')
    
    // 检查屏幕阅读器友好的文本
    await expect(page.locator('[data-testid="prompt-card"]:first-child [data-testid="usage-count"]')).toContainText('使用次数')
  })
})

test.describe('提示词表单测试', () => {
  test('应该支持自动保存', async ({ page }) => {
    // 前往新建提示词页面
    await page.goto('/prompts/new')
    
    // 输入标题
    await page.fill('[data-testid="prompt-title-input"]', '自动保存测试')
    
    // 等待自动保存
    await page.waitForTimeout(3000)
    
    // 检查自动保存指示器
    await expect(page.locator('[data-testid="auto-save-indicator"]')).toContainText('已保存')
    
    // 刷新页面
    await page.reload()
    
    // 检查内容是否恢复
    await expect(page.locator('[data-testid="prompt-title-input"]')).toHaveValue('自动保存测试')
  })

  test('应该支持预览模式', async ({ page }) => {
    // 前往新建提示词页面
    await page.goto('/prompts/new')
    
    // 输入 Markdown 内容
    await page.fill('[data-testid="prompt-content-input"]', '# 标题\n\n这是**粗体**内容')
    
    // 点击预览按钮
    await page.click('[data-testid="preview-button"]')
    
    // 检查预览模式
    await expect(page.locator('[data-testid="preview-mode"]')).toBeVisible()
    await expect(page.locator('[data-testid="preview-content"] h1')).toContainText('标题')
    await expect(page.locator('[data-testid="preview-content"] strong')).toContainText('粗体')
    
    // 切换回编辑模式
    await page.click('[data-testid="edit-button"]')
    
    // 检查是否切换回编辑模式
    await expect(page.locator('[data-testid="prompt-content-input"]')).toBeVisible()
  })

  test('应该支持模板插入', async ({ page }) => {
    // 前往新建提示词页面
    await page.goto('/prompts/new')
    
    // 点击模板按钮
    await page.click('[data-testid="template-button"]')
    
    // 检查模板选择器
    await expect(page.locator('[data-testid="template-selector"]')).toBeVisible()
    
    // 选择一个模板
    await page.click('[data-testid="template-code-review"]')
    
    // 检查模板内容是否插入
    await expect(page.locator('[data-testid="prompt-content-input"]')).toContainText('请审查以下代码')
  })

  test('应该支持快捷键', async ({ page }) => {
    // 前往新建提示词页面
    await page.goto('/prompts/new')
    
    // 填写表单
    await page.fill('[data-testid="prompt-title-input"]', '快捷键测试')
    await page.fill('[data-testid="prompt-content-input"]', '测试内容')
    
    // 使用 Ctrl+S 保存
    await page.keyboard.press('Control+s')
    
    // 检查是否保存成功
    await expect(page.locator('[data-testid="success-toast"]')).toBeVisible()
    
    // 使用 Ctrl+Z 撤销
    await page.keyboard.press('Control+z')
    
    // 检查内容是否撤销
    await expect(page.locator('[data-testid="prompt-content-input"]')).toHaveValue('')
  })
})