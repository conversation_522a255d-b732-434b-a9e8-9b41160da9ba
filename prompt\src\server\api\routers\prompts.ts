import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

const createPromptSchema = z.object({
  title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
  content: z.string().min(1, "内容不能为空"),
  description: z.string().optional(),
  categoryId: z.string().uuid("分类ID格式不正确").optional(),
  tags: z.array(z.string().min(1, "标签不能为空")).optional(),
  isFavorite: z.boolean().default(false),
  isPublic: z.boolean().default(false),
});

const updatePromptSchema = z.object({
  id: z.string().uuid("提示词ID格式不正确"),
  title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符").optional(),
  content: z.string().min(1, "内容不能为空").optional(),
  description: z.string().optional(),
  categoryId: z.string().uuid("分类ID格式不正确").optional().nullable(),
  tags: z.array(z.string().min(1, "标签不能为空")).optional(),
  isFavorite: z.boolean().optional(),
  isPublic: z.boolean().optional(),
});

const searchPromptsSchema = z.object({
  query: z.string().min(1, "搜索关键词不能为空").max(200, "搜索关键词不能超过200个字符"),
  categoryId: z.string().uuid("分类ID格式不正确").optional(),
  tags: z.array(z.string()).optional(),
  isFavorite: z.boolean().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

const getPromptsSchema = z.object({
  categoryId: z.string().uuid("分类ID格式不正确").optional(),
  tags: z.array(z.string()).optional(),
  isFavorite: z.boolean().optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "usageCount", "title"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

// 新增：筛选提示词的 schema
const getFilteredSchema = z.object({
  categoryIds: z.array(z.string().uuid()).optional(),
  tagIds: z.array(z.string().uuid()).optional(),
  favoritesOnly: z.boolean().optional(),
  sortBy: z.enum(["created", "updated", "usage", "title"]).default("updated"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

// 新增：获取最近提示词的 schema
const getRecentSchema = z.object({
  limit: z.number().min(1).max(100).default(10),
});

// 新增：获取统计信息的 schema
const getStatsSchema = z.object({
  timeRange: z.enum(["7d", "30d", "90d", "all"]).default("30d").optional(),
});

// 新增：获取使用统计的 schema
const getUsageStatsSchema = z.object({
  timeRange: z.enum(["7d", "30d", "90d", "all"]).default("30d"),
});

// 新增：获取热门提示词的 schema
const getTopUsedSchema = z.object({
  limit: z.number().min(1).max(100).default(10),
  timeRange: z.enum(["7d", "30d", "90d", "all"]).default("30d"),
});

// 新增：按分类获取提示词的 schema
const getByCategorySchema = z.object({
  categoryId: z.string().uuid("分类ID格式不正确"),
  sortBy: z.enum(["updated", "created", "usage", "title"]).default("updated"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

export const promptsRouter = createTRPCRouter({
  // 获取提示词列表
  getAll: protectedProcedure
    .input(getPromptsSchema)
    .query(async ({ ctx, input }) => {
      try {
        const {
          categoryId,
          tags,
          isFavorite,
          sortBy,
          sortOrder,
          limit,
          offset,
        } = input;

        const where: any = {
          userId: ctx.user.id,
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (isFavorite !== undefined) {
          where.isFavorite = isFavorite;
        }

        if (tags && tags.length > 0) {
          where.promptTags = {
            some: {
              tag: {
                name: { in: tags },
                userId: ctx.user.id,
              },
            },
          };
        }

        const orderBy: any = {};
        orderBy[sortBy] = sortOrder;

        const [prompts, total] = await Promise.all([
          ctx.db.prompt.findMany({
            where,
            include: {
              category: true,
              promptTags: {
                include: {
                  tag: true,
                },
              },
            },
            orderBy,
            skip: offset,
            take: limit,
          }),
          ctx.db.prompt.count({ where }),
        ]);

        return {
          prompts: prompts.map((prompt) => ({
            ...prompt,
            tags: prompt.promptTags.map((pt) => pt.tag),
          })),
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取提示词列表失败",
        });
      }
    }),

  // 根据ID获取提示词
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid("提示词ID格式不正确") }))
    .query(async ({ ctx, input }) => {
      try {
        const prompt = await ctx.db.prompt.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
          include: {
            category: true,
            promptTags: {
              include: {
                tag: true,
              },
            },
          },
        });

        if (!prompt) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "提示词不存在",
          });
        }

        return {
          ...prompt,
          tags: prompt.promptTags.map((pt) => pt.tag),
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取提示词详情失败",
        });
      }
    }),

  // 搜索提示词
  search: protectedProcedure
    .input(searchPromptsSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { query, categoryId, tags, isFavorite, limit, offset } = input;

        const where: any = {
          userId: ctx.user.id,
          OR: [
            { title: { contains: query, mode: "insensitive" } },
            { content: { contains: query, mode: "insensitive" } },
            { description: { contains: query, mode: "insensitive" } },
          ],
        };

        if (categoryId) {
          where.categoryId = categoryId;
        }

        if (isFavorite !== undefined) {
          where.isFavorite = isFavorite;
        }

        if (tags && tags.length > 0) {
          where.promptTags = {
            some: {
              tag: {
                name: { in: tags },
                userId: ctx.user.id,
              },
            },
          };
        }

        const [prompts, total] = await Promise.all([
          ctx.db.prompt.findMany({
            where,
            include: {
              category: true,
              promptTags: {
                include: {
                  tag: true,
                },
              },
            },
            orderBy: { updatedAt: "desc" },
            skip: offset,
            take: limit,
          }),
          ctx.db.prompt.count({ where }),
        ]);

        return {
          prompts: prompts.map((prompt) => ({
            ...prompt,
            tags: prompt.promptTags.map((pt) => pt.tag),
          })),
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "搜索提示词失败",
        });
      }
    }),

  // 创建提示词
  create: protectedProcedure
    .input(createPromptSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { tags, ...promptData } = input;

        // 验证分类是否存在且属于当前用户
        if (promptData.categoryId) {
          const category = await ctx.db.category.findFirst({
            where: {
              id: promptData.categoryId,
              userId: ctx.user.id,
            },
          });

          if (!category) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "分类不存在",
            });
          }
        }

        const result = await ctx.db.$transaction(async (tx) => {
          // 创建提示词
          const prompt = await tx.prompt.create({
            data: {
              ...promptData,
              userId: ctx.user.id,
            },
          });

          // 处理标签
          if (tags && tags.length > 0) {
            const tagPromises = tags.map(async (tagName) => {
              // 查找或创建标签
              const existingTag = await tx.tag.findFirst({
                where: {
                  name: tagName,
                  userId: ctx.user.id,
                },
              });

              const tag = existingTag || await tx.tag.create({
                data: {
                  name: tagName,
                  userId: ctx.user.id,
                },
              });

              // 创建关联关系
              await tx.promptTag.create({
                data: {
                  promptId: prompt.id,
                  tagId: tag.id,
                },
              });

              return tag;
            });

            await Promise.all(tagPromises);
          }

          return prompt;
        });

        return result;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "创建提示词失败",
        });
      }
    }),

  // 更新提示词
  update: protectedProcedure
    .input(updatePromptSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, tags, ...updateData } = input;

        // 检查提示词是否存在且属于当前用户
        const existingPrompt = await ctx.db.prompt.findFirst({
          where: {
            id,
            userId: ctx.user.id,
          },
        });

        if (!existingPrompt) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "提示词不存在",
          });
        }

        // 验证分类是否存在且属于当前用户
        if (updateData.categoryId) {
          const category = await ctx.db.category.findFirst({
            where: {
              id: updateData.categoryId,
              userId: ctx.user.id,
            },
          });

          if (!category) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "分类不存在",
            });
          }
        }

        const result = await ctx.db.$transaction(async (tx) => {
          // 更新提示词
          const updatedPrompt = await tx.prompt.update({
            where: { id },
            data: updateData,
          });

          // 处理标签更新
          if (tags !== undefined) {
            // 删除现有的标签关联
            await tx.promptTag.deleteMany({
              where: { promptId: id },
            });

            // 创建新的标签关联
            if (tags.length > 0) {
              const tagPromises = tags.map(async (tagName) => {
                // 查找或创建标签
                const existingTag = await tx.tag.findFirst({
                  where: {
                    name: tagName,
                    userId: ctx.user.id,
                  },
                });

                const tag = existingTag || await tx.tag.create({
                  data: {
                    name: tagName,
                    userId: ctx.user.id,
                  },
                });

                // 创建关联关系
                await tx.promptTag.create({
                  data: {
                    promptId: id,
                    tagId: tag.id,
                  },
                });

                return tag;
              });

              await Promise.all(tagPromises);
            }
          }

          return updatedPrompt;
        });

        return result;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "更新提示词失败",
        });
      }
    }),

  // 删除提示词
  delete: protectedProcedure
    .input(z.object({ id: z.string().uuid("提示词ID格式不正确") }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查提示词是否存在且属于当前用户
        const existingPrompt = await ctx.db.prompt.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
        });

        if (!existingPrompt) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "提示词不存在",
          });
        }

        await ctx.db.prompt.delete({
          where: { id: input.id },
        });

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "删除提示词失败",
        });
      }
    }),

  // 增加使用次数
  incrementUsage: protectedProcedure
    .input(z.object({ id: z.string().uuid("提示词ID格式不正确") }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查提示词是否存在且属于当前用户
        const existingPrompt = await ctx.db.prompt.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
        });

        if (!existingPrompt) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "提示词不存在",
          });
        }

        const updatedPrompt = await ctx.db.prompt.update({
          where: { id: input.id },
          data: {
            usageCount: { increment: 1 },
          },
        });

        return updatedPrompt;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "更新使用次数失败",
        });
      }
    }),

  // 批量导入提示词
  bulkImport: protectedProcedure
    .input(
      z.object({
        prompts: z.array(
          z.object({
            title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
            content: z.string().min(1, "内容不能为空"),
            description: z.string().optional(),
            categoryName: z.string().optional(),
            tags: z.array(z.string()).optional(),
          })
        ),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const results = await ctx.db.$transaction(async (tx) => {
          const importedPrompts = [];

          for (const promptData of input.prompts) {
            const { categoryName, tags, ...promptInfo } = promptData;

            // 处理分类
            let categoryId = null;
            if (categoryName) {
              let category = await tx.category.findFirst({
                where: {
                  name: categoryName,
                  userId: ctx.user.id,
                },
              });

              if (!category) {
                category = await tx.category.create({
                  data: {
                    name: categoryName,
                    userId: ctx.user.id,
                  },
                });
              }

              categoryId = category.id;
            }

            // 创建提示词
            const prompt = await tx.prompt.create({
              data: {
                ...promptInfo,
                categoryId,
                userId: ctx.user.id,
              },
            });

            // 处理标签
            if (tags && tags.length > 0) {
              const tagPromises = tags.map(async (tagName) => {
                let tag = await tx.tag.findFirst({
                  where: {
                    name: tagName,
                    userId: ctx.user.id,
                  },
                });

                if (!tag) {
                  tag = await tx.tag.create({
                    data: {
                      name: tagName,
                      userId: ctx.user.id,
                    },
                  });
                }

                await tx.promptTag.create({
                  data: {
                    promptId: prompt.id,
                    tagId: tag.id,
                  },
                });

                return tag;
              });

              await Promise.all(tagPromises);
            }

            importedPrompts.push(prompt);
          }

          return importedPrompts;
        });

        return {
          success: true,
          importedCount: results.length,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "批量导入提示词失败",
        });
      }
    }),

  // 获取筛选后的提示词（前端兼容性方法）
  getFiltered: protectedProcedure
    .input(getFilteredSchema)
    .query(async ({ ctx, input }) => {
      try {
        const {
          categoryIds,
          tagIds,
          favoritesOnly,
          sortBy,
          sortOrder,
          limit,
          offset,
        } = input;

        const where: any = {
          userId: ctx.user.id,
        };

        // 处理分类筛选（支持多个分类）
        if (categoryIds && categoryIds.length > 0) {
          where.categoryId = { in: categoryIds };
        }

        // 处理收藏筛选
        if (favoritesOnly !== undefined) {
          where.isFavorite = favoritesOnly;
        }

        // 处理标签筛选（通过标签ID）
        if (tagIds && tagIds.length > 0) {
          where.promptTags = {
            some: {
              tag: {
                id: { in: tagIds },
                userId: ctx.user.id,
              },
            },
          };
        }

        // 处理排序
        const orderBy: any = {};
        const sortField = sortBy === "created" ? "createdAt" :
                         sortBy === "updated" ? "updatedAt" :
                         sortBy === "usage" ? "usageCount" : "title";
        orderBy[sortField] = sortOrder;

        const [prompts, total] = await Promise.all([
          ctx.db.prompt.findMany({
            where,
            include: {
              category: true,
              promptTags: {
                include: {
                  tag: true,
                },
              },
            },
            orderBy,
            skip: offset,
            take: limit,
          }),
          ctx.db.prompt.count({ where }),
        ]);

        return {
          prompts: prompts.map((prompt) => ({
            ...prompt,
            tags: prompt.promptTags.map((pt) => pt.tag),
          })),
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取筛选提示词失败",
        });
      }
    }),

  // 获取最近使用的提示词
  getRecent: protectedProcedure
    .input(getRecentSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { limit } = input;

        const prompts = await ctx.db.prompt.findMany({
          where: {
            userId: ctx.user.id,
          },
          include: {
            category: true,
            promptTags: {
              include: {
                tag: true,
              },
            },
          },
          orderBy: {
            updatedAt: "desc",
          },
          take: limit,
        });

        return prompts.map((prompt) => ({
          ...prompt,
          tags: prompt.promptTags.map((pt) => pt.tag),
        }));
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取最近提示词失败",
        });
      }
    }),

  // 获取统计信息
  getStats: protectedProcedure
    .input(getStatsSchema.optional())
    .query(async ({ ctx, input }) => {
      try {
        const timeRange = input?.timeRange || "30d";

        // 计算时间范围
        let dateFilter = {};
        if (timeRange !== "all") {
          const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - days);
          dateFilter = {
            createdAt: {
              gte: startDate,
            },
          };
        }

        const [
          totalPrompts,
          totalCategories,
          totalTags,
          totalUsage,
          recentPrompts,
        ] = await Promise.all([
          // 总提示词数
          ctx.db.prompt.count({
            where: {
              userId: ctx.user.id,
              ...dateFilter,
            },
          }),
          // 总分类数
          ctx.db.category.count({
            where: { userId: ctx.user.id },
          }),
          // 总标签数
          ctx.db.tag.count({
            where: { userId: ctx.user.id },
          }),
          // 总使用次数
          ctx.db.prompt.aggregate({
            where: { userId: ctx.user.id },
            _sum: {
              usageCount: true,
            },
          }),
          // 最近创建的提示词数量（用于计算增长）
          ctx.db.prompt.count({
            where: {
              userId: ctx.user.id,
              createdAt: {
                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
              },
            },
          }),
        ]);

        return {
          totalPrompts,
          totalCategories,
          totalTags,
          totalUsage: totalUsage._sum.usageCount || 0,
          growth: recentPrompts,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取统计信息失败",
        });
      }
    }),

  // 获取使用统计
  getUsageStats: protectedProcedure
    .input(getUsageStatsSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { timeRange } = input;

        // 计算时间范围
        const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        // 获取使用趋势数据（简化版本，返回每日使用统计）
        const usageData = [];
        for (let i = days - 1; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          date.setHours(0, 0, 0, 0);

          const nextDate = new Date(date);
          nextDate.setDate(nextDate.getDate() + 1);

          const dayUsage = await ctx.db.prompt.aggregate({
            where: {
              userId: ctx.user.id,
              updatedAt: {
                gte: date,
                lt: nextDate,
              },
            },
            _sum: {
              usageCount: true,
            },
          });

          usageData.push({
            date: date.toISOString().split('T')[0],
            usage: dayUsage._sum.usageCount || 0,
          });
        }

        return usageData;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取使用统计失败",
        });
      }
    }),

  // 获取热门提示词
  getTopUsed: protectedProcedure
    .input(getTopUsedSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { limit, timeRange } = input;

        // 计算时间范围
        let dateFilter = {};
        if (timeRange !== "all") {
          const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - days);
          dateFilter = {
            updatedAt: {
              gte: startDate,
            },
          };
        }

        const prompts = await ctx.db.prompt.findMany({
          where: {
            userId: ctx.user.id,
            ...dateFilter,
          },
          include: {
            category: true,
            promptTags: {
              include: {
                tag: true,
              },
            },
          },
          orderBy: {
            usageCount: "desc",
          },
          take: limit,
        });

        return prompts.map((prompt) => ({
          ...prompt,
          tags: prompt.promptTags.map((pt) => pt.tag),
        }));
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取热门提示词失败",
        });
      }
    }),

  // 按分类获取提示词
  getByCategory: protectedProcedure
    .input(getByCategorySchema)
    .query(async ({ ctx, input }) => {
      try {
        const { categoryId, sortBy, sortOrder, limit, offset } = input;

        // 处理排序
        const orderBy: any = {};
        const sortField = sortBy === "created" ? "createdAt" :
                         sortBy === "updated" ? "updatedAt" :
                         sortBy === "usage" ? "usageCount" : "title";
        orderBy[sortField] = sortOrder;

        const [prompts, total] = await Promise.all([
          ctx.db.prompt.findMany({
            where: {
              userId: ctx.user.id,
              categoryId: categoryId,
            },
            include: {
              category: true,
              promptTags: {
                include: {
                  tag: true,
                },
              },
            },
            orderBy,
            skip: offset,
            take: limit,
          }),
          ctx.db.prompt.count({
            where: {
              userId: ctx.user.id,
              categoryId: categoryId,
            },
          }),
        ]);

        return {
          prompts: prompts.map((prompt) => ({
            ...prompt,
            tags: prompt.promptTags.map((pt) => pt.tag),
          })),
          total,
          hasMore: offset + limit < total,
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取分类提示词失败",
        });
      }
    }),
});