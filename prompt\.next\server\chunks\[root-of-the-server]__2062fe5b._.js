module.exports = {

"[project]/.next-internal/server/app/api/trpc/[trpc]/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[project]/src/env.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "env": ()=>env
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@t3-oss/env-nextjs/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/external.js [app-route] (ecmascript) <export * as z>");
;
;
const env = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$t3$2d$oss$2f$env$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createEnv"])({
    /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */ server: {
        DATABASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url(),
        NODE_ENV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            "development",
            "test",
            "production"
        ]).default("development")
    },
    /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */ client: {
        NEXT_PUBLIC_SUPABASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().url(),
        NEXT_PUBLIC_SUPABASE_ANON_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    },
    /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */ runtimeEnv: {
        DATABASE_URL: process.env.DATABASE_URL,
        NODE_ENV: ("TURBOPACK compile-time value", "development"),
        NEXT_PUBLIC_SUPABASE_URL: ("TURBOPACK compile-time value", "https://htdiodpknxwcgeeuwcny.supabase.co"),
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh0ZGlvZHBrbnh3Y2dlZXV3Y255Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDU0MzQsImV4cCI6MjA2ODQyMTQzNH0.HT6VQGgQHsLy-mlJrCoSqXWiwo1sfOSgARwgjOTnjDo")
    },
    /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */ skipValidation: !!process.env.SKIP_ENV_VALIDATION,
    /**
   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and
   * `SOME_VAR=''` will throw an error.
   */ emptyStringAsUndefined: true
});
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/server/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "db": ()=>db
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/env.js [app-route] (ecmascript)");
;
;
const createPrismaClient = ()=>new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
        log: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NODE_ENV === "development" ? [
            "query",
            "error",
            "warn"
        ] : [
            "error"
        ]
    });
const globalForPrisma = globalThis;
const db = globalForPrisma.prisma ?? createPrismaClient();
if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NODE_ENV !== "production") globalForPrisma.prisma = db;
}),
"[project]/src/server/api/trpc.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:
 * 1. You want to modify request context (see Part 1).
 * 2. You want to create a new middleware or type of procedure (see Part 3).
 *
 * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will
 * need to use are documented accordingly near the end.
 */ __turbopack_context__.s({
    "createCallerFactory": ()=>createCallerFactory,
    "createTRPCContext": ()=>createTRPCContext,
    "createTRPCRouter": ()=>createTRPCRouter,
    "protectedProcedure": ()=>protectedProcedure,
    "publicProcedure": ()=>publicProcedure
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$initTRPC$2d$IT_6ZYJd$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/initTRPC-IT_6ZYJd.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/superjson/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$ZodError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/ZodError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/auth-helpers-nextjs/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/db.ts [app-route] (ecmascript)");
;
;
;
;
;
;
const createTRPCContext = async (opts)=>{
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$auth$2d$helpers$2d$nextjs$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerComponentClient"])({
        cookies: ()=>cookieStore
    });
    const { data: { session } } = await supabase.auth.getSession();
    const { data: { user } } = await supabase.auth.getUser();
    return {
        db: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"],
        session,
        user,
        supabase,
        ...opts
    };
};
/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */ const t = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$initTRPC$2d$IT_6ZYJd$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initTRPC"].context().create({
    transformer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$superjson$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"],
    errorFormatter ({ shape, error }) {
        return {
            ...shape,
            data: {
                ...shape.data,
                zodError: error.cause instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$ZodError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ZodError"] ? error.cause.flatten() : null
            }
        };
    }
});
const createCallerFactory = t.createCallerFactory;
const createTRPCRouter = t.router;
/**
 * Middleware for timing procedure execution and adding an artificial delay in development.
 *
 * You can remove this if you don't like it, but it can help catch unwanted waterfalls by simulating
 * network latency that would occur in production but not in local development.
 */ const timingMiddleware = t.middleware(async ({ next, path })=>{
    const start = Date.now();
    if (t._config.isDev) {
        // artificial delay in dev
        const waitMs = Math.floor(Math.random() * 400) + 100;
        await new Promise((resolve)=>setTimeout(resolve, waitMs));
    }
    const result = await next();
    const end = Date.now();
    console.log(`[TRPC] ${path} took ${end - start}ms to execute`);
    return result;
});
const publicProcedure = t.procedure.use(timingMiddleware);
const protectedProcedure = t.procedure.use(timingMiddleware).use(({ ctx, next })=>{
    if (!ctx.user || !ctx.session) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
            code: "UNAUTHORIZED",
            message: "You must be logged in to perform this action"
        });
    }
    return next({
        ctx: {
            // infers the `user` and `session` as non-nullable
            ...ctx,
            user: ctx.user,
            session: ctx.session
        }
    });
});
}),
"[project]/src/server/api/routers/categories.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "categoriesRouter": ()=>categoriesRouter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-route] (ecmascript)");
;
;
;
const createCategorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "分类名称不能为空").max(100, "分类名称不能超过100个字符"),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").default("#3B82F6"),
    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "图标不能为空").max(50, "图标名称不能超过50个字符").default("folder")
});
const updateCategorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确"),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "分类名称不能为空").max(100, "分类名称不能超过100个字符").optional(),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").optional(),
    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "图标不能为空").max(50, "图标名称不能超过50个字符").optional()
});
const categoriesRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    // 获取用户的所有分类
    getAll: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ({ ctx })=>{
        try {
            const categories = await ctx.db.category.findMany({
                where: {
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            prompts: true
                        }
                    }
                },
                orderBy: {
                    createdAt: "desc"
                }
            });
            return categories.map((category)=>({
                    ...category,
                    promptCount: category._count.prompts
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取分类列表失败"
            });
        }
    }),
    // 根据ID获取分类
    getById: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确")
    })).query(async ({ ctx, input })=>{
        try {
            const category = await ctx.db.category.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            prompts: true
                        }
                    }
                }
            });
            if (!category) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "分类不存在"
                });
            }
            return {
                ...category,
                promptCount: category._count.prompts
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取分类详情失败"
            });
        }
    }),
    // 创建分类
    create: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(createCategorySchema).mutation(async ({ ctx, input })=>{
        try {
            // 检查同名分类是否存在
            const existingCategory = await ctx.db.category.findFirst({
                where: {
                    name: input.name,
                    userId: ctx.user.id
                }
            });
            if (existingCategory) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "CONFLICT",
                    message: "同名分类已存在"
                });
            }
            const category = await ctx.db.category.create({
                data: {
                    ...input,
                    userId: ctx.user.id
                }
            });
            return category;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "创建分类失败"
            });
        }
    }),
    // 更新分类
    update: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(updateCategorySchema).mutation(async ({ ctx, input })=>{
        try {
            const { id, ...updateData } = input;
            // 检查分类是否存在且属于当前用户
            const existingCategory = await ctx.db.category.findFirst({
                where: {
                    id,
                    userId: ctx.user.id
                }
            });
            if (!existingCategory) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "分类不存在"
                });
            }
            // 如果更新名称，检查是否与其他分类重名
            if (updateData.name && updateData.name !== existingCategory.name) {
                const duplicateCategory = await ctx.db.category.findFirst({
                    where: {
                        name: updateData.name,
                        userId: ctx.user.id,
                        id: {
                            not: id
                        }
                    }
                });
                if (duplicateCategory) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                        code: "CONFLICT",
                        message: "同名分类已存在"
                    });
                }
            }
            const updatedCategory = await ctx.db.category.update({
                where: {
                    id
                },
                data: updateData
            });
            return updatedCategory;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "更新分类失败"
            });
        }
    }),
    // 删除分类
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查分类是否存在且属于当前用户
            const existingCategory = await ctx.db.category.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            prompts: true
                        }
                    }
                }
            });
            if (!existingCategory) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "分类不存在"
                });
            }
            // 检查是否有关联的提示词
            if (existingCategory._count.prompts > 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "PRECONDITION_FAILED",
                    message: `该分类下还有 ${existingCategory._count.prompts} 个提示词，无法删除`
                });
            }
            await ctx.db.category.delete({
                where: {
                    id: input.id
                }
            });
            return {
                success: true
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "删除分类失败"
            });
        }
    }),
    // 强制删除分类（将关联的提示词分类设为null）
    forceDelete: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查分类是否存在且属于当前用户
            const existingCategory = await ctx.db.category.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                }
            });
            if (!existingCategory) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "分类不存在"
                });
            }
            // 使用事务确保数据一致性
            await ctx.db.$transaction(async (tx)=>{
                // 先将关联的提示词分类设为null
                await tx.prompt.updateMany({
                    where: {
                        categoryId: input.id,
                        userId: ctx.user.id
                    },
                    data: {
                        categoryId: null
                    }
                });
                // 然后删除分类
                await tx.category.delete({
                    where: {
                        id: input.id
                    }
                });
            });
            return {
                success: true
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "强制删除分类失败"
            });
        }
    }),
    // 获取分类统计信息
    getStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        timeRange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            "7d",
            "30d",
            "90d",
            "all"
        ]).default("30d").optional()
    }).optional()).query(async ({ ctx, input })=>{
        try {
            const timeRange = input?.timeRange || "30d";
            // 计算时间范围
            let dateFilter = {};
            if (timeRange !== "all") {
                const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - days);
                dateFilter = {
                    createdAt: {
                        gte: startDate
                    }
                };
            }
            // 获取分类统计
            const categoryStats = await ctx.db.category.findMany({
                where: {
                    userId: ctx.user.id,
                    ...dateFilter
                },
                include: {
                    _count: {
                        select: {
                            prompts: true
                        }
                    }
                },
                orderBy: {
                    prompts: {
                        _count: "desc"
                    }
                },
                take: 10
            });
            return categoryStats.map((category)=>({
                    id: category.id,
                    name: category.name,
                    color: category.color,
                    promptCount: category._count.prompts
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取分类统计失败"
            });
        }
    }),
    // 获取热门分类
    getPopular: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(10)
    }).optional()).query(async ({ ctx, input })=>{
        try {
            const limit = input?.limit || 10;
            const categories = await ctx.db.category.findMany({
                where: {
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            prompts: true
                        }
                    }
                },
                orderBy: {
                    prompts: {
                        _count: "desc"
                    }
                },
                take: limit
            });
            return categories.map((category)=>({
                    id: category.id,
                    name: category.name,
                    color: category.color,
                    count: category._count.prompts
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取热门分类失败"
            });
        }
    })
});
}),
"[project]/src/server/api/routers/prompts.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "promptsRouter": ()=>promptsRouter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-route] (ecmascript)");
;
;
;
const createPromptSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "内容不能为空"),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    categoryId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确").optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标签不能为空")).optional(),
    isFavorite: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(false),
    isPublic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(false)
});
const updatePromptSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("提示词ID格式不正确"),
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标题不能为空").max(200, "标题不能超过200个字符").optional(),
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "内容不能为空").optional(),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    categoryId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确").optional().nullable(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标签不能为空")).optional(),
    isFavorite: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    isPublic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional()
});
const searchPromptsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    query: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "搜索关键词不能为空").max(200, "搜索关键词不能超过200个字符"),
    categoryId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确").optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional(),
    isFavorite: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(20),
    offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).default(0)
});
const getPromptsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    categoryId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确").optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional(),
    isFavorite: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    sortBy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "createdAt",
        "updatedAt",
        "usageCount",
        "title"
    ]).default("createdAt"),
    sortOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "asc",
        "desc"
    ]).default("desc"),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(20),
    offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).default(0)
});
// 新增：筛选提示词的 schema
const getFilteredSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    categoryIds: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid()).optional(),
    tagIds: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid()).optional(),
    favoritesOnly: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    sortBy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "created",
        "updated",
        "usage",
        "title"
    ]).default("updated"),
    sortOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "asc",
        "desc"
    ]).default("desc"),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(20),
    offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).default(0)
});
// 新增：获取最近提示词的 schema
const getRecentSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(10)
});
// 新增：获取统计信息的 schema
const getStatsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    timeRange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "7d",
        "30d",
        "90d",
        "all"
    ]).default("30d").optional()
});
// 新增：获取使用统计的 schema
const getUsageStatsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    timeRange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "7d",
        "30d",
        "90d",
        "all"
    ]).default("30d")
});
// 新增：获取热门提示词的 schema
const getTopUsedSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(10),
    timeRange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "7d",
        "30d",
        "90d",
        "all"
    ]).default("30d")
});
// 新增：按分类获取提示词的 schema
const getByCategorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    categoryId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确"),
    sortBy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "updated",
        "created",
        "usage",
        "title"
    ]).default("updated"),
    sortOrder: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        "asc",
        "desc"
    ]).default("desc"),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(20),
    offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).default(0)
});
const promptsRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    // 获取提示词列表
    getAll: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getPromptsSchema).query(async ({ ctx, input })=>{
        try {
            const { categoryId, tags, isFavorite, sortBy, sortOrder, limit, offset } = input;
            const where = {
                userId: ctx.user.id
            };
            if (categoryId) {
                where.categoryId = categoryId;
            }
            if (isFavorite !== undefined) {
                where.isFavorite = isFavorite;
            }
            if (tags && tags.length > 0) {
                where.promptTags = {
                    some: {
                        tag: {
                            name: {
                                in: tags
                            },
                            userId: ctx.user.id
                        }
                    }
                };
            }
            const orderBy = {};
            orderBy[sortBy] = sortOrder;
            const [prompts, total] = await Promise.all([
                ctx.db.prompt.findMany({
                    where,
                    include: {
                        category: true,
                        promptTags: {
                            include: {
                                tag: true
                            }
                        }
                    },
                    orderBy,
                    skip: offset,
                    take: limit
                }),
                ctx.db.prompt.count({
                    where
                })
            ]);
            return {
                prompts: prompts.map((prompt)=>({
                        ...prompt,
                        tags: prompt.promptTags.map((pt)=>pt.tag)
                    })),
                total,
                hasMore: offset + limit < total
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取提示词列表失败"
            });
        }
    }),
    // 根据ID获取提示词
    getById: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("提示词ID格式不正确")
    })).query(async ({ ctx, input })=>{
        try {
            const prompt = await ctx.db.prompt.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                },
                include: {
                    category: true,
                    promptTags: {
                        include: {
                            tag: true
                        }
                    }
                }
            });
            if (!prompt) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "提示词不存在"
                });
            }
            return {
                ...prompt,
                tags: prompt.promptTags.map((pt)=>pt.tag)
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取提示词详情失败"
            });
        }
    }),
    // 搜索提示词
    search: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(searchPromptsSchema).query(async ({ ctx, input })=>{
        try {
            const { query, categoryId, tags, isFavorite, limit, offset } = input;
            const where = {
                userId: ctx.user.id,
                OR: [
                    {
                        title: {
                            contains: query,
                            mode: "insensitive"
                        }
                    },
                    {
                        content: {
                            contains: query,
                            mode: "insensitive"
                        }
                    },
                    {
                        description: {
                            contains: query,
                            mode: "insensitive"
                        }
                    }
                ]
            };
            if (categoryId) {
                where.categoryId = categoryId;
            }
            if (isFavorite !== undefined) {
                where.isFavorite = isFavorite;
            }
            if (tags && tags.length > 0) {
                where.promptTags = {
                    some: {
                        tag: {
                            name: {
                                in: tags
                            },
                            userId: ctx.user.id
                        }
                    }
                };
            }
            const [prompts, total] = await Promise.all([
                ctx.db.prompt.findMany({
                    where,
                    include: {
                        category: true,
                        promptTags: {
                            include: {
                                tag: true
                            }
                        }
                    },
                    orderBy: {
                        updatedAt: "desc"
                    },
                    skip: offset,
                    take: limit
                }),
                ctx.db.prompt.count({
                    where
                })
            ]);
            return {
                prompts: prompts.map((prompt)=>({
                        ...prompt,
                        tags: prompt.promptTags.map((pt)=>pt.tag)
                    })),
                total,
                hasMore: offset + limit < total
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "搜索提示词失败"
            });
        }
    }),
    // 创建提示词
    create: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(createPromptSchema).mutation(async ({ ctx, input })=>{
        try {
            const { tags, ...promptData } = input;
            // 验证分类是否存在且属于当前用户
            if (promptData.categoryId) {
                const category = await ctx.db.category.findFirst({
                    where: {
                        id: promptData.categoryId,
                        userId: ctx.user.id
                    }
                });
                if (!category) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                        code: "NOT_FOUND",
                        message: "分类不存在"
                    });
                }
            }
            const result = await ctx.db.$transaction(async (tx)=>{
                // 创建提示词
                const prompt = await tx.prompt.create({
                    data: {
                        ...promptData,
                        userId: ctx.user.id
                    }
                });
                // 处理标签
                if (tags && tags.length > 0) {
                    const tagPromises = tags.map(async (tagName)=>{
                        // 查找或创建标签
                        const existingTag = await tx.tag.findFirst({
                            where: {
                                name: tagName,
                                userId: ctx.user.id
                            }
                        });
                        const tag = existingTag || await tx.tag.create({
                            data: {
                                name: tagName,
                                userId: ctx.user.id
                            }
                        });
                        // 创建关联关系
                        await tx.promptTag.create({
                            data: {
                                promptId: prompt.id,
                                tagId: tag.id
                            }
                        });
                        return tag;
                    });
                    await Promise.all(tagPromises);
                }
                return prompt;
            });
            return result;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "创建提示词失败"
            });
        }
    }),
    // 更新提示词
    update: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(updatePromptSchema).mutation(async ({ ctx, input })=>{
        try {
            const { id, tags, ...updateData } = input;
            // 检查提示词是否存在且属于当前用户
            const existingPrompt = await ctx.db.prompt.findFirst({
                where: {
                    id,
                    userId: ctx.user.id
                }
            });
            if (!existingPrompt) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "提示词不存在"
                });
            }
            // 验证分类是否存在且属于当前用户
            if (updateData.categoryId) {
                const category = await ctx.db.category.findFirst({
                    where: {
                        id: updateData.categoryId,
                        userId: ctx.user.id
                    }
                });
                if (!category) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                        code: "NOT_FOUND",
                        message: "分类不存在"
                    });
                }
            }
            const result = await ctx.db.$transaction(async (tx)=>{
                // 更新提示词
                const updatedPrompt = await tx.prompt.update({
                    where: {
                        id
                    },
                    data: updateData
                });
                // 处理标签更新
                if (tags !== undefined) {
                    // 删除现有的标签关联
                    await tx.promptTag.deleteMany({
                        where: {
                            promptId: id
                        }
                    });
                    // 创建新的标签关联
                    if (tags.length > 0) {
                        const tagPromises = tags.map(async (tagName)=>{
                            // 查找或创建标签
                            const existingTag = await tx.tag.findFirst({
                                where: {
                                    name: tagName,
                                    userId: ctx.user.id
                                }
                            });
                            const tag = existingTag || await tx.tag.create({
                                data: {
                                    name: tagName,
                                    userId: ctx.user.id
                                }
                            });
                            // 创建关联关系
                            await tx.promptTag.create({
                                data: {
                                    promptId: id,
                                    tagId: tag.id
                                }
                            });
                            return tag;
                        });
                        await Promise.all(tagPromises);
                    }
                }
                return updatedPrompt;
            });
            return result;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "更新提示词失败"
            });
        }
    }),
    // 删除提示词
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("提示词ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查提示词是否存在且属于当前用户
            const existingPrompt = await ctx.db.prompt.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                }
            });
            if (!existingPrompt) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "提示词不存在"
                });
            }
            await ctx.db.prompt.delete({
                where: {
                    id: input.id
                }
            });
            return {
                success: true
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "删除提示词失败"
            });
        }
    }),
    // 增加使用次数
    incrementUsage: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("提示词ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查提示词是否存在且属于当前用户
            const existingPrompt = await ctx.db.prompt.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                }
            });
            if (!existingPrompt) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "提示词不存在"
                });
            }
            const updatedPrompt = await ctx.db.prompt.update({
                where: {
                    id: input.id
                },
                data: {
                    usageCount: {
                        increment: 1
                    }
                }
            });
            return updatedPrompt;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "更新使用次数失败"
            });
        }
    }),
    // 批量导入提示词
    bulkImport: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        prompts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
            content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "内容不能为空"),
            description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            categoryName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional()
        }))
    })).mutation(async ({ ctx, input })=>{
        try {
            const results = await ctx.db.$transaction(async (tx)=>{
                const importedPrompts = [];
                for (const promptData of input.prompts){
                    const { categoryName, tags, ...promptInfo } = promptData;
                    // 处理分类
                    let categoryId = null;
                    if (categoryName) {
                        let category = await tx.category.findFirst({
                            where: {
                                name: categoryName,
                                userId: ctx.user.id
                            }
                        });
                        if (!category) {
                            category = await tx.category.create({
                                data: {
                                    name: categoryName,
                                    userId: ctx.user.id
                                }
                            });
                        }
                        categoryId = category.id;
                    }
                    // 创建提示词
                    const prompt = await tx.prompt.create({
                        data: {
                            ...promptInfo,
                            categoryId,
                            userId: ctx.user.id
                        }
                    });
                    // 处理标签
                    if (tags && tags.length > 0) {
                        const tagPromises = tags.map(async (tagName)=>{
                            let tag = await tx.tag.findFirst({
                                where: {
                                    name: tagName,
                                    userId: ctx.user.id
                                }
                            });
                            if (!tag) {
                                tag = await tx.tag.create({
                                    data: {
                                        name: tagName,
                                        userId: ctx.user.id
                                    }
                                });
                            }
                            await tx.promptTag.create({
                                data: {
                                    promptId: prompt.id,
                                    tagId: tag.id
                                }
                            });
                            return tag;
                        });
                        await Promise.all(tagPromises);
                    }
                    importedPrompts.push(prompt);
                }
                return importedPrompts;
            });
            return {
                success: true,
                importedCount: results.length
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "批量导入提示词失败"
            });
        }
    }),
    // 获取筛选后的提示词（前端兼容性方法）
    getFiltered: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getFilteredSchema).query(async ({ ctx, input })=>{
        try {
            const { categoryIds, tagIds, favoritesOnly, sortBy, sortOrder, limit, offset } = input;
            const where = {
                userId: ctx.user.id
            };
            // 处理分类筛选（支持多个分类）
            if (categoryIds && categoryIds.length > 0) {
                where.categoryId = {
                    in: categoryIds
                };
            }
            // 处理收藏筛选
            if (favoritesOnly !== undefined) {
                where.isFavorite = favoritesOnly;
            }
            // 处理标签筛选（通过标签ID）
            if (tagIds && tagIds.length > 0) {
                where.promptTags = {
                    some: {
                        tag: {
                            id: {
                                in: tagIds
                            },
                            userId: ctx.user.id
                        }
                    }
                };
            }
            // 处理排序
            const orderBy = {};
            const sortField = sortBy === "created" ? "createdAt" : sortBy === "updated" ? "updatedAt" : sortBy === "usage" ? "usageCount" : "title";
            orderBy[sortField] = sortOrder;
            const [prompts, total] = await Promise.all([
                ctx.db.prompt.findMany({
                    where,
                    include: {
                        category: true,
                        promptTags: {
                            include: {
                                tag: true
                            }
                        }
                    },
                    orderBy,
                    skip: offset,
                    take: limit
                }),
                ctx.db.prompt.count({
                    where
                })
            ]);
            return {
                prompts: prompts.map((prompt)=>({
                        ...prompt,
                        tags: prompt.promptTags.map((pt)=>pt.tag)
                    })),
                total,
                hasMore: offset + limit < total
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取筛选提示词失败"
            });
        }
    }),
    // 获取最近使用的提示词
    getRecent: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getRecentSchema).query(async ({ ctx, input })=>{
        try {
            const { limit } = input;
            const prompts = await ctx.db.prompt.findMany({
                where: {
                    userId: ctx.user.id
                },
                include: {
                    category: true,
                    promptTags: {
                        include: {
                            tag: true
                        }
                    }
                },
                orderBy: {
                    updatedAt: "desc"
                },
                take: limit
            });
            return prompts.map((prompt)=>({
                    ...prompt,
                    tags: prompt.promptTags.map((pt)=>pt.tag)
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取最近提示词失败"
            });
        }
    }),
    // 获取统计信息
    getStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getStatsSchema.optional()).query(async ({ ctx, input })=>{
        try {
            const timeRange = input?.timeRange || "30d";
            // 计算时间范围
            let dateFilter = {};
            if (timeRange !== "all") {
                const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - days);
                dateFilter = {
                    createdAt: {
                        gte: startDate
                    }
                };
            }
            const [totalPrompts, totalCategories, totalTags, totalUsage, recentPrompts] = await Promise.all([
                // 总提示词数
                ctx.db.prompt.count({
                    where: {
                        userId: ctx.user.id,
                        ...dateFilter
                    }
                }),
                // 总分类数
                ctx.db.category.count({
                    where: {
                        userId: ctx.user.id
                    }
                }),
                // 总标签数
                ctx.db.tag.count({
                    where: {
                        userId: ctx.user.id
                    }
                }),
                // 总使用次数
                ctx.db.prompt.aggregate({
                    where: {
                        userId: ctx.user.id
                    },
                    _sum: {
                        usageCount: true
                    }
                }),
                // 最近创建的提示词数量（用于计算增长）
                ctx.db.prompt.count({
                    where: {
                        userId: ctx.user.id,
                        createdAt: {
                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                        }
                    }
                })
            ]);
            return {
                totalPrompts,
                totalCategories,
                totalTags,
                totalUsage: totalUsage._sum.usageCount || 0,
                growth: recentPrompts
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取统计信息失败"
            });
        }
    }),
    // 获取使用统计
    getUsageStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getUsageStatsSchema).query(async ({ ctx, input })=>{
        try {
            const { timeRange } = input;
            // 计算时间范围
            const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            // 获取使用趋势数据（简化版本，返回每日使用统计）
            const usageData = [];
            for(let i = days - 1; i >= 0; i--){
                const date = new Date();
                date.setDate(date.getDate() - i);
                date.setHours(0, 0, 0, 0);
                const nextDate = new Date(date);
                nextDate.setDate(nextDate.getDate() + 1);
                const dayUsage = await ctx.db.prompt.aggregate({
                    where: {
                        userId: ctx.user.id,
                        updatedAt: {
                            gte: date,
                            lt: nextDate
                        }
                    },
                    _sum: {
                        usageCount: true
                    }
                });
                usageData.push({
                    date: date.toISOString().split('T')[0],
                    usage: dayUsage._sum.usageCount || 0
                });
            }
            return usageData;
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取使用统计失败"
            });
        }
    }),
    // 获取热门提示词
    getTopUsed: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getTopUsedSchema).query(async ({ ctx, input })=>{
        try {
            const { limit, timeRange } = input;
            // 计算时间范围
            let dateFilter = {};
            if (timeRange !== "all") {
                const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - days);
                dateFilter = {
                    updatedAt: {
                        gte: startDate
                    }
                };
            }
            const prompts = await ctx.db.prompt.findMany({
                where: {
                    userId: ctx.user.id,
                    ...dateFilter
                },
                include: {
                    category: true,
                    promptTags: {
                        include: {
                            tag: true
                        }
                    }
                },
                orderBy: {
                    usageCount: "desc"
                },
                take: limit
            });
            return prompts.map((prompt)=>({
                    ...prompt,
                    tags: prompt.promptTags.map((pt)=>pt.tag)
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取热门提示词失败"
            });
        }
    }),
    // 按分类获取提示词
    getByCategory: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(getByCategorySchema).query(async ({ ctx, input })=>{
        try {
            const { categoryId, sortBy, sortOrder, limit, offset } = input;
            // 处理排序
            const orderBy = {};
            const sortField = sortBy === "created" ? "createdAt" : sortBy === "updated" ? "updatedAt" : sortBy === "usage" ? "usageCount" : "title";
            orderBy[sortField] = sortOrder;
            const [prompts, total] = await Promise.all([
                ctx.db.prompt.findMany({
                    where: {
                        userId: ctx.user.id,
                        categoryId: categoryId
                    },
                    include: {
                        category: true,
                        promptTags: {
                            include: {
                                tag: true
                            }
                        }
                    },
                    orderBy,
                    skip: offset,
                    take: limit
                }),
                ctx.db.prompt.count({
                    where: {
                        userId: ctx.user.id,
                        categoryId: categoryId
                    }
                })
            ]);
            return {
                prompts: prompts.map((prompt)=>({
                        ...prompt,
                        tags: prompt.promptTags.map((pt)=>pt.tag)
                    })),
                total,
                hasMore: offset + limit < total
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取分类提示词失败"
            });
        }
    })
});
}),
"[project]/src/server/api/routers/tags.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "tagsRouter": ()=>tagsRouter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-route] (ecmascript)");
;
;
;
const createTagSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标签名称不能为空").max(50, "标签名称不能超过50个字符"),
    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").default("#6B7280")
});
const updateTagSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("标签ID格式不正确"),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标签名称不能为空").max(50, "标签名称不能超过50个字符").optional(),
    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").optional()
});
const tagsRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    // 获取用户的所有标签
    getAll: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ({ ctx })=>{
        try {
            const tags = await ctx.db.tag.findMany({
                where: {
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            promptTags: true
                        }
                    }
                },
                orderBy: {
                    createdAt: "desc"
                }
            });
            return tags.map((tag)=>({
                    ...tag,
                    promptCount: tag._count.promptTags
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取标签列表失败"
            });
        }
    }),
    // 根据ID获取标签
    getById: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("标签ID格式不正确")
    })).query(async ({ ctx, input })=>{
        try {
            const tag = await ctx.db.tag.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            promptTags: true
                        }
                    }
                }
            });
            if (!tag) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "标签不存在"
                });
            }
            return {
                ...tag,
                promptCount: tag._count.promptTags
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取标签详情失败"
            });
        }
    }),
    // 根据名称搜索标签
    search: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        query: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "搜索关键词不能为空").max(50, "搜索关键词不能超过50个字符"),
        limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(50).default(10)
    })).query(async ({ ctx, input })=>{
        try {
            const tags = await ctx.db.tag.findMany({
                where: {
                    userId: ctx.user.id,
                    name: {
                        contains: input.query,
                        mode: "insensitive"
                    }
                },
                include: {
                    _count: {
                        select: {
                            promptTags: true
                        }
                    }
                },
                take: input.limit,
                orderBy: {
                    createdAt: "desc"
                }
            });
            return tags.map((tag)=>({
                    ...tag,
                    promptCount: tag._count.promptTags
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "搜索标签失败"
            });
        }
    }),
    // 创建标签
    create: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(createTagSchema).mutation(async ({ ctx, input })=>{
        try {
            // 检查同名标签是否存在
            const existingTag = await ctx.db.tag.findFirst({
                where: {
                    name: input.name,
                    userId: ctx.user.id
                }
            });
            if (existingTag) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "CONFLICT",
                    message: "同名标签已存在"
                });
            }
            const tag = await ctx.db.tag.create({
                data: {
                    ...input,
                    userId: ctx.user.id
                }
            });
            return tag;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "创建标签失败"
            });
        }
    }),
    // 更新标签
    update: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(updateTagSchema).mutation(async ({ ctx, input })=>{
        try {
            const { id, ...updateData } = input;
            // 检查标签是否存在且属于当前用户
            const existingTag = await ctx.db.tag.findFirst({
                where: {
                    id,
                    userId: ctx.user.id
                }
            });
            if (!existingTag) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "标签不存在"
                });
            }
            // 如果更新名称，检查是否与其他标签重名
            if (updateData.name && updateData.name !== existingTag.name) {
                const duplicateTag = await ctx.db.tag.findFirst({
                    where: {
                        name: updateData.name,
                        userId: ctx.user.id,
                        id: {
                            not: id
                        }
                    }
                });
                if (duplicateTag) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                        code: "CONFLICT",
                        message: "同名标签已存在"
                    });
                }
            }
            const updatedTag = await ctx.db.tag.update({
                where: {
                    id
                },
                data: updateData
            });
            return updatedTag;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "更新标签失败"
            });
        }
    }),
    // 删除标签
    delete: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("标签ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查标签是否存在且属于当前用户
            const existingTag = await ctx.db.tag.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            promptTags: true
                        }
                    }
                }
            });
            if (!existingTag) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "标签不存在"
                });
            }
            // 检查是否有关联的提示词
            if (existingTag._count.promptTags > 0) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "PRECONDITION_FAILED",
                    message: `该标签关联了 ${existingTag._count.promptTags} 个提示词，无法删除`
                });
            }
            await ctx.db.tag.delete({
                where: {
                    id: input.id
                }
            });
            return {
                success: true
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "删除标签失败"
            });
        }
    }),
    // 强制删除标签（同时删除所有关联关系）
    forceDelete: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("标签ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查标签是否存在且属于当前用户
            const existingTag = await ctx.db.tag.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                }
            });
            if (!existingTag) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "标签不存在"
                });
            }
            // 使用事务确保数据一致性
            await ctx.db.$transaction(async (tx)=>{
                // 先删除所有关联关系
                await tx.promptTag.deleteMany({
                    where: {
                        tagId: input.id
                    }
                });
                // 然后删除标签
                await tx.tag.delete({
                    where: {
                        id: input.id
                    }
                });
            });
            return {
                success: true
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "强制删除标签失败"
            });
        }
    }),
    // 获取热门标签
    getPopular: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(50).default(10)
    })).query(async ({ ctx, input })=>{
        try {
            const tags = await ctx.db.tag.findMany({
                where: {
                    userId: ctx.user.id
                },
                include: {
                    _count: {
                        select: {
                            promptTags: true
                        }
                    }
                },
                orderBy: {
                    promptTags: {
                        _count: "desc"
                    }
                },
                take: input.limit
            });
            return tags.filter((tag)=>tag._count.promptTags > 0).map((tag)=>({
                    ...tag,
                    promptCount: tag._count.promptTags
                }));
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取热门标签失败"
            });
        }
    }),
    // 批量创建标签
    bulkCreate: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "标签名称不能为空").max(50, "标签名称不能超过50个字符"),
            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").default("#6B7280")
        }))
    })).mutation(async ({ ctx, input })=>{
        try {
            const results = await ctx.db.$transaction(async (tx)=>{
                const createdTags = [];
                const skippedTags = [];
                for (const tagData of input.tags){
                    // 检查是否已存在
                    const existingTag = await tx.tag.findFirst({
                        where: {
                            name: tagData.name,
                            userId: ctx.user.id
                        }
                    });
                    if (existingTag) {
                        skippedTags.push({
                            name: tagData.name,
                            reason: "已存在"
                        });
                        continue;
                    }
                    // 创建标签
                    const tag = await tx.tag.create({
                        data: {
                            ...tagData,
                            userId: ctx.user.id
                        }
                    });
                    createdTags.push(tag);
                }
                return {
                    createdTags,
                    skippedTags
                };
            });
            return {
                success: true,
                createdCount: results.createdTags.length,
                skippedCount: results.skippedTags.length,
                createdTags: results.createdTags,
                skippedTags: results.skippedTags
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "批量创建标签失败"
            });
        }
    })
});
}),
"[project]/src/server/api/routers/search.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "searchRouter": ()=>searchRouter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/trpc.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/tracked-gU3ttYjg.mjs [app-route] (ecmascript)");
;
;
;
const searchSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    query: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "搜索关键词不能为空").max(200, "搜索关键词不能超过200个字符"),
    categoryId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("分类ID格式不正确").optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).optional(),
    isFavorite: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(100).default(20),
    offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).default(0)
});
const addSearchHistorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    query: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "搜索关键词不能为空").max(200, "搜索关键词不能超过200个字符")
});
const searchRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    // 全文搜索提示词
    searchPrompts: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(searchSchema).query(async ({ ctx, input })=>{
        try {
            const { query, categoryId, tags, isFavorite, limit, offset } = input;
            // 构建搜索条件
            const where = {
                userId: ctx.user.id,
                OR: [
                    {
                        title: {
                            contains: query,
                            mode: "insensitive"
                        }
                    },
                    {
                        content: {
                            contains: query,
                            mode: "insensitive"
                        }
                    },
                    {
                        description: {
                            contains: query,
                            mode: "insensitive"
                        }
                    }
                ]
            };
            if (categoryId) {
                where.categoryId = categoryId;
            }
            if (isFavorite !== undefined) {
                where.isFavorite = isFavorite;
            }
            if (tags && tags.length > 0) {
                where.promptTags = {
                    some: {
                        tag: {
                            name: {
                                in: tags
                            },
                            userId: ctx.user.id
                        }
                    }
                };
            }
            // 执行搜索
            const [prompts, total] = await Promise.all([
                ctx.db.prompt.findMany({
                    where,
                    include: {
                        category: true,
                        promptTags: {
                            include: {
                                tag: true
                            }
                        }
                    },
                    orderBy: [
                        // 优先显示标题匹配的结果
                        {
                            title: {
                                _relevance: {
                                    fields: [
                                        "title"
                                    ],
                                    search: query,
                                    sort: "desc"
                                }
                            }
                        },
                        // 然后按更新时间排序
                        {
                            updatedAt: "desc"
                        }
                    ],
                    skip: offset,
                    take: limit
                }),
                ctx.db.prompt.count({
                    where
                })
            ]);
            // 生成搜索结果高亮
            const processedPrompts = prompts.map((prompt)=>{
                const highlightText = (text, query)=>{
                    if (!text) return text;
                    const regex = new RegExp(`(${query})`, "gi");
                    return text.replace(regex, "<mark>$1</mark>");
                };
                return {
                    ...prompt,
                    title: highlightText(prompt.title, query),
                    content: highlightText(prompt.content.slice(0, 200), query),
                    description: prompt.description ? highlightText(prompt.description, query) : null,
                    tags: prompt.promptTags.map((pt)=>pt.tag)
                };
            });
            return {
                prompts: processedPrompts,
                total,
                hasMore: offset + limit < total,
                query
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "搜索提示词失败"
            });
        }
    }),
    // 智能搜索建议
    getSuggestions: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        query: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1).max(200),
        limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(10).default(5)
    })).query(async ({ ctx, input })=>{
        try {
            const { query, limit } = input;
            // 搜索相关的提示词标题
            const titleSuggestions = await ctx.db.prompt.findMany({
                where: {
                    userId: ctx.user.id,
                    title: {
                        contains: query,
                        mode: "insensitive"
                    }
                },
                select: {
                    title: true
                },
                take: limit,
                orderBy: {
                    usageCount: "desc"
                }
            });
            // 搜索相关的标签
            const tagSuggestions = await ctx.db.tag.findMany({
                where: {
                    userId: ctx.user.id,
                    name: {
                        contains: query,
                        mode: "insensitive"
                    }
                },
                select: {
                    name: true
                },
                take: limit,
                orderBy: {
                    promptTags: {
                        _count: "desc"
                    }
                }
            });
            // 搜索相关的分类
            const categorySuggestions = await ctx.db.category.findMany({
                where: {
                    userId: ctx.user.id,
                    name: {
                        contains: query,
                        mode: "insensitive"
                    }
                },
                select: {
                    name: true
                },
                take: limit,
                orderBy: {
                    prompts: {
                        _count: "desc"
                    }
                }
            });
            return {
                titles: titleSuggestions.map((p)=>p.title),
                tags: tagSuggestions.map((t)=>t.name),
                categories: categorySuggestions.map((c)=>c.name)
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取搜索建议失败"
            });
        }
    }),
    // 添加搜索历史
    addSearchHistory: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(addSearchHistorySchema).mutation(async ({ ctx, input })=>{
        try {
            // 检查是否已存在相同的搜索记录
            const existingRecord = await ctx.db.searchHistory.findFirst({
                where: {
                    query: input.query,
                    userId: ctx.user.id
                }
            });
            if (existingRecord) {
                // 如果已存在，更新时间
                await ctx.db.searchHistory.update({
                    where: {
                        id: existingRecord.id
                    },
                    data: {
                        createdAt: new Date()
                    }
                });
                return existingRecord;
            }
            // 创建新的搜索记录
            const searchRecord = await ctx.db.searchHistory.create({
                data: {
                    query: input.query,
                    userId: ctx.user.id
                }
            });
            // 保持搜索历史记录数量（最多100条）
            const historyCount = await ctx.db.searchHistory.count({
                where: {
                    userId: ctx.user.id
                }
            });
            if (historyCount > 100) {
                // 删除最老的记录
                const oldestRecords = await ctx.db.searchHistory.findMany({
                    where: {
                        userId: ctx.user.id
                    },
                    orderBy: {
                        createdAt: "asc"
                    },
                    take: historyCount - 100
                });
                await ctx.db.searchHistory.deleteMany({
                    where: {
                        id: {
                            in: oldestRecords.map((r)=>r.id)
                        }
                    }
                });
            }
            return searchRecord;
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "添加搜索历史失败"
            });
        }
    }),
    // 获取搜索历史
    getSearchHistory: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1).max(50).default(20)
    })).query(async ({ ctx, input })=>{
        try {
            const history = await ctx.db.searchHistory.findMany({
                where: {
                    userId: ctx.user.id
                },
                orderBy: {
                    createdAt: "desc"
                },
                take: input.limit
            });
            return history;
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取搜索历史失败"
            });
        }
    }),
    // 清空搜索历史
    clearSearchHistory: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].mutation(async ({ ctx })=>{
        try {
            await ctx.db.searchHistory.deleteMany({
                where: {
                    userId: ctx.user.id
                }
            });
            return {
                success: true
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "清空搜索历史失败"
            });
        }
    }),
    // 删除单个搜索历史记录
    deleteSearchHistory: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].input(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().uuid("搜索历史ID格式不正确")
    })).mutation(async ({ ctx, input })=>{
        try {
            // 检查记录是否存在且属于当前用户
            const existingRecord = await ctx.db.searchHistory.findFirst({
                where: {
                    id: input.id,
                    userId: ctx.user.id
                }
            });
            if (!existingRecord) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                    code: "NOT_FOUND",
                    message: "搜索历史记录不存在"
                });
            }
            await ctx.db.searchHistory.delete({
                where: {
                    id: input.id
                }
            });
            return {
                success: true
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]) {
                throw error;
            }
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "删除搜索历史失败"
            });
        }
    }),
    // 获取搜索统计
    getSearchStats: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["protectedProcedure"].query(async ({ ctx })=>{
        try {
            const [totalPrompts, totalCategories, totalTags, totalSearches, recentSearches, popularTags] = await Promise.all([
                // 总提示词数
                ctx.db.prompt.count({
                    where: {
                        userId: ctx.user.id
                    }
                }),
                // 总分类数
                ctx.db.category.count({
                    where: {
                        userId: ctx.user.id
                    }
                }),
                // 总标签数
                ctx.db.tag.count({
                    where: {
                        userId: ctx.user.id
                    }
                }),
                // 总搜索次数
                ctx.db.searchHistory.count({
                    where: {
                        userId: ctx.user.id
                    }
                }),
                // 最近7天的搜索次数
                ctx.db.searchHistory.count({
                    where: {
                        userId: ctx.user.id,
                        createdAt: {
                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                        }
                    }
                }),
                // 最受欢迎的标签
                ctx.db.tag.findMany({
                    where: {
                        userId: ctx.user.id
                    },
                    include: {
                        _count: {
                            select: {
                                promptTags: true
                            }
                        }
                    },
                    orderBy: {
                        promptTags: {
                            _count: "desc"
                        }
                    },
                    take: 5
                })
            ]);
            return {
                totalPrompts,
                totalCategories,
                totalTags,
                totalSearches,
                recentSearches,
                popularTags: popularTags.map((tag)=>({
                        name: tag.name,
                        count: tag._count.promptTags
                    }))
            };
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$tracked$2d$gU3ttYjg$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TRPCError"]({
                code: "INTERNAL_SERVER_ERROR",
                message: "获取搜索统计失败"
            });
        }
    })
});
}),
"[project]/src/server/api/root.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "appRouter": ()=>appRouter,
    "createCaller": ()=>createCaller
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/routers/categories.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/routers/prompts.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$tags$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/routers/tags.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/routers/search.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
;
const appRouter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCRouter"])({
    categories: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$categories$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["categoriesRouter"],
    prompts: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$prompts$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptsRouter"],
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$tags$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["tagsRouter"],
    search: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$routers$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchRouter"]
});
const createCaller = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createCallerFactory"])(appRouter);
}),
"[project]/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>handler,
    "POST": ()=>handler
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$adapters$2f$fetch$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@trpc/server/dist/adapters/fetch/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/env.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$root$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/root.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/server/api/trpc.ts [app-route] (ecmascript)");
;
;
;
;
/**
 * This wraps the `createTRPCContext` helper and provides the required context for the tRPC API when
 * handling a HTTP request (e.g. when you make requests from Client Components).
 */ const createContext = async (req)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$trpc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTRPCContext"])({
        headers: req.headers
    });
};
const handler = (req)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$trpc$2f$server$2f$dist$2f$adapters$2f$fetch$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fetchRequestHandler"])({
        endpoint: "/api/trpc",
        req,
        router: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$server$2f$api$2f$root$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["appRouter"],
        createContext: ()=>createContext(req),
        onError: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$env$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["env"].NODE_ENV === "development" ? ({ path, error })=>{
            console.error(`❌ tRPC failed on ${path ?? "<no-path>"}: ${error.message}`);
        } : undefined
    });
;
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2062fe5b._.js.map