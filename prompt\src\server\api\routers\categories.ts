import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { TRPCError } from "@trpc/server";

const createCategorySchema = z.object({
  name: z.string().min(1, "分类名称不能为空").max(100, "分类名称不能超过100个字符"),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").default("#3B82F6"),
  icon: z.string().min(1, "图标不能为空").max(50, "图标名称不能超过50个字符").default("folder"),
});

const updateCategorySchema = z.object({
  id: z.string().uuid("分类ID格式不正确"),
  name: z.string().min(1, "分类名称不能为空").max(100, "分类名称不能超过100个字符").optional(),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "颜色格式不正确").optional(),
  icon: z.string().min(1, "图标不能为空").max(50, "图标名称不能超过50个字符").optional(),
});

export const categoriesRouter = createTRPCRouter({
  // 获取用户的所有分类
  getAll: protectedProcedure.query(async ({ ctx }) => {
    try {
      const categories = await ctx.db.category.findMany({
        where: {
          userId: ctx.user.id,
        },
        include: {
          _count: {
            select: {
              prompts: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return categories.map((category) => ({
        ...category,
        promptCount: category._count.prompts,
      }));
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取分类列表失败",
      });
    }
  }),

  // 根据ID获取分类
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid("分类ID格式不正确") }))
    .query(async ({ ctx, input }) => {
      try {
        const category = await ctx.db.category.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
          include: {
            _count: {
              select: {
                prompts: true,
              },
            },
          },
        });

        if (!category) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "分类不存在",
          });
        }

        return {
          ...category,
          promptCount: category._count.prompts,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取分类详情失败",
        });
      }
    }),

  // 创建分类
  create: protectedProcedure
    .input(createCategorySchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查同名分类是否存在
        const existingCategory = await ctx.db.category.findFirst({
          where: {
            name: input.name,
            userId: ctx.user.id,
          },
        });

        if (existingCategory) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "同名分类已存在",
          });
        }

        const category = await ctx.db.category.create({
          data: {
            ...input,
            userId: ctx.user.id,
          },
        });

        return category;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "创建分类失败",
        });
      }
    }),

  // 更新分类
  update: protectedProcedure
    .input(updateCategorySchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...updateData } = input;

        // 检查分类是否存在且属于当前用户
        const existingCategory = await ctx.db.category.findFirst({
          where: {
            id,
            userId: ctx.user.id,
          },
        });

        if (!existingCategory) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "分类不存在",
          });
        }

        // 如果更新名称，检查是否与其他分类重名
        if (updateData.name && updateData.name !== existingCategory.name) {
          const duplicateCategory = await ctx.db.category.findFirst({
            where: {
              name: updateData.name,
              userId: ctx.user.id,
              id: { not: id },
            },
          });

          if (duplicateCategory) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "同名分类已存在",
            });
          }
        }

        const updatedCategory = await ctx.db.category.update({
          where: { id },
          data: updateData,
        });

        return updatedCategory;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "更新分类失败",
        });
      }
    }),

  // 删除分类
  delete: protectedProcedure
    .input(z.object({ id: z.string().uuid("分类ID格式不正确") }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查分类是否存在且属于当前用户
        const existingCategory = await ctx.db.category.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
          include: {
            _count: {
              select: {
                prompts: true,
              },
            },
          },
        });

        if (!existingCategory) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "分类不存在",
          });
        }

        // 检查是否有关联的提示词
        if (existingCategory._count.prompts > 0) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            message: `该分类下还有 ${existingCategory._count.prompts} 个提示词，无法删除`,
          });
        }

        await ctx.db.category.delete({
          where: { id: input.id },
        });

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "删除分类失败",
        });
      }
    }),

  // 强制删除分类（将关联的提示词分类设为null）
  forceDelete: protectedProcedure
    .input(z.object({ id: z.string().uuid("分类ID格式不正确") }))
    .mutation(async ({ ctx, input }) => {
      try {
        // 检查分类是否存在且属于当前用户
        const existingCategory = await ctx.db.category.findFirst({
          where: {
            id: input.id,
            userId: ctx.user.id,
          },
        });

        if (!existingCategory) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "分类不存在",
          });
        }

        // 使用事务确保数据一致性
        await ctx.db.$transaction(async (tx) => {
          // 先将关联的提示词分类设为null
          await tx.prompt.updateMany({
            where: {
              categoryId: input.id,
              userId: ctx.user.id,
            },
            data: {
              categoryId: null,
            },
          });

          // 然后删除分类
          await tx.category.delete({
            where: { id: input.id },
          });
        });

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "强制删除分类失败",
        });
      }
    }),

  // 获取分类统计信息
  getStats: protectedProcedure
    .input(z.object({
      timeRange: z.enum(["7d", "30d", "90d", "all"]).default("30d").optional(),
    }).optional())
    .query(async ({ ctx, input }) => {
      try {
        const timeRange = input?.timeRange || "30d";

        // 计算时间范围
        let dateFilter = {};
        if (timeRange !== "all") {
          const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - days);
          dateFilter = {
            createdAt: {
              gte: startDate,
            },
          };
        }

        // 获取分类统计
        const categoryStats = await ctx.db.category.findMany({
          where: {
            userId: ctx.user.id,
            ...dateFilter,
          },
          include: {
            _count: {
              select: {
                prompts: true,
              },
            },
          },
          orderBy: {
            prompts: {
              _count: "desc",
            },
          },
          take: 10,
        });

        return categoryStats.map((category) => ({
          id: category.id,
          name: category.name,
          color: category.color,
          promptCount: category._count.prompts,
        }));
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取分类统计失败",
        });
      }
    }),

  // 获取热门分类
  getPopular: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(10),
    }).optional())
    .query(async ({ ctx, input }) => {
      try {
        const limit = input?.limit || 10;

        const categories = await ctx.db.category.findMany({
          where: {
            userId: ctx.user.id,
          },
          include: {
            _count: {
              select: {
                prompts: true,
              },
            },
          },
          orderBy: {
            prompts: {
              _count: "desc",
            },
          },
          take: limit,
        });

        return categories.map((category) => ({
          id: category.id,
          name: category.name,
          color: category.color,
          count: category._count.prompts,
        }));
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "获取热门分类失败",
        });
      }
    }),
});