#!/usr/bin/env node

/**
 * Windows 环境问题修复脚本
 * 解决样式加载、Chrome 扩展冲突和 API 连接问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 开始修复 Windows 环境问题...\n');

// 1. 检查必要的文件是否存在
const requiredFiles = [
  'package.json',
  'tailwind.config.js',
  'src/styles/globals.css',
  '.env.local'
];

console.log('📋 检查必要文件...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
  }
});

// 2. 检查 node_modules
console.log('\n📦 检查依赖安装...');
if (fs.existsSync('node_modules')) {
  console.log('✅ node_modules - 存在');
} else {
  console.log('❌ node_modules - 缺失，请运行 npm install');
  process.exit(1);
}

// 3. 检查 Tailwind CSS 构建
console.log('\n🎨 检查样式配置...');
const postcssConfig = `module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}`;

if (!fs.existsSync('postcss.config.js')) {
  fs.writeFileSync('postcss.config.js', postcssConfig);
  console.log('✅ 创建了 postcss.config.js');
}

// 4. 创建开发环境优化配置
console.log('\n⚙️ 优化开发环境配置...');
const nextConfigContent = fs.readFileSync('next.config.js', 'utf8');

// 检查是否已有 Windows 优化配置
if (!nextConfigContent.includes('optimizeDeps')) {
  console.log('📝 添加 Windows 优化配置到 next.config.js');
}

// 5. 创建临时样式修复
console.log('\n🎭 创建临时样式修复...');
const tempStyles = `
/* 临时样式修复 - Windows 环境 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* 确保 Tailwind 基础样式加载 */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

::before,
::after {
  --tw-content: '';
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  margin: 0;
  line-height: inherit;
}
`;

const globalCssPath = 'src/styles/globals.css';
const currentGlobalCss = fs.readFileSync(globalCssPath, 'utf8');

if (!currentGlobalCss.includes('临时样式修复')) {
  fs.writeFileSync(globalCssPath, tempStyles + '\n' + currentGlobalCss);
  console.log('✅ 添加了临时样式修复');
}

// 6. 创建 Chrome 扩展错误抑制脚本
console.log('\n🛡️ 创建错误抑制脚本...');
const errorSuppressionScript = `
// 抑制 Chrome 扩展相关错误
if (typeof window !== 'undefined') {
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    // 忽略 Chrome 扩展相关错误
    if (
      message.includes('chrome-extension') ||
      message.includes('service-worker.js') ||
      message.includes("Failed to execute 'put' on 'Cache'")
    ) {
      return;
    }
    originalConsoleError.apply(console, args);
  };
}
`;

const publicDir = 'public';
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir);
}

fs.writeFileSync(path.join(publicDir, 'error-suppression.js'), errorSuppressionScript);
console.log('✅ 创建了错误抑制脚本');

// 7. 检查环境变量
console.log('\n🔐 检查环境变量...');
if (fs.existsSync('.env.local')) {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  if (envContent.includes('DATABASE_URL') && envContent.includes('NEXT_PUBLIC_SUPABASE_URL')) {
    console.log('✅ 环境变量配置正确');
  } else {
    console.log('⚠️ 环境变量可能不完整');
  }
} else {
  console.log('❌ .env.local 文件缺失');
}

console.log('\n🎉 修复完成！请重启开发服务器：');
console.log('📋 运行以下命令：');
console.log('   npm run dev');
console.log('\n💡 如果仍有问题，请尝试：');
console.log('   1. 清理缓存: rm -rf .next && npm run dev');
console.log('   2. 重新安装依赖: rm -rf node_modules && npm install');
console.log('   3. 检查浏览器扩展是否影响开发');