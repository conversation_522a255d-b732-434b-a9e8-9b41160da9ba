import { describe, it, expect } from 'vitest'
import {
  promptSchema,
  categorySchema,
  tagSchema,
  searchSchema,
  batchOperationSchema,
  batchImportSchema,
  filterSchema,
} from '~/lib/validation/schemas'

describe('Validation Schemas', () => {
  describe('promptSchema', () => {
    it('应该验证有效的提示词数据', () => {
      const validPrompt = {
        title: '测试提示词',
        content: '这是一个测试提示词内容',
        description: '测试描述',
        categoryId: '123e4567-e89b-12d3-a456-426614174000',
        tags: ['测试', '开发'],
        isFavorite: true,
      }

      const result = promptSchema.safeParse(validPrompt)
      expect(result.success).toBe(true)
    })

    it('应该拒绝空标题', () => {
      const invalidPrompt = {
        title: '',
        content: '内容',
        tags: [],
        isFavorite: true,
      }

      const result = promptSchema.safeParse(invalidPrompt)
      expect(result.success).toBe(false)
    })

    it('应该拒绝过长的标题', () => {
      const invalidPrompt = {
        title: 'a'.repeat(101),
        content: '内容',
      }

      const result = promptSchema.safeParse(invalidPrompt)
      expect(result.success).toBe(false)
    })

    it('应该拒绝空内容', () => {
      const invalidPrompt = {
        title: '标题',
        content: '',
      }

      const result = promptSchema.safeParse(invalidPrompt)
      expect(result.success).toBe(false)
    })

    it('应该拒绝过长的内容', () => {
      const invalidPrompt = {
        title: '标题',
        content: 'a'.repeat(10001),
      }

      const result = promptSchema.safeParse(invalidPrompt)
      expect(result.success).toBe(false)
    })

    it('应该接受可选字段', () => {
      const minimalPrompt = {
        title: '标题',
        content: '内容',
      }

      const result = promptSchema.safeParse(minimalPrompt)
      expect(result.success).toBe(true)
    })
  })

  describe('categorySchema', () => {
    it('应该验证有效的分类数据', () => {
      const validCategory = {
        name: '工作',
        description: '工作相关的提示词',
        color: '#FF5733',
        icon: '💼',
      }

      const result = categorySchema.safeParse(validCategory)
      expect(result.success).toBe(true)
    })

    it('应该拒绝空名称', () => {
      const invalidCategory = {
        name: '',
        description: '描述',
        color: '#FF5733',
        icon: '💼',
      }

      const result = categorySchema.safeParse(invalidCategory)
      expect(result.success).toBe(false)
    })

    it('应该拒绝过长的名称', () => {
      const invalidCategory = {
        name: 'a'.repeat(51),
        description: '描述',
        color: '#FF5733',
        icon: '💼',
      }

      const result = categorySchema.safeParse(invalidCategory)
      expect(result.success).toBe(false)
    })

    it('应该拒绝无效的颜色格式', () => {
      const invalidCategory = {
        name: '工作',
        description: '描述',
        color: 'invalid-color',
        icon: '💼',
      }

      const result = categorySchema.safeParse(invalidCategory)
      expect(result.success).toBe(false)
    })

    it('应该接受有效的颜色格式', () => {
      const validColors = ['#FF5733', '#ffffff', '#000000', '#AbCdEf']
      
      validColors.forEach(color => {
        const category = {
          name: '工作',
          description: '描述',
          color,
          icon: '💼',
        }

        const result = categorySchema.safeParse(category)
        expect(result.success).toBe(true)
      })
    })
  })

  describe('tagSchema', () => {
    it('应该验证有效的标签数据', () => {
      const validTag = {
        name: '测试',
        color: '#FF5733',
      }

      const result = tagSchema.safeParse(validTag)
      expect(result.success).toBe(true)
    })

    it('应该拒绝空名称', () => {
      const invalidTag = {
        name: '',
        color: '#FF5733',
      }

      const result = tagSchema.safeParse(invalidTag)
      expect(result.success).toBe(false)
    })

    it('应该拒绝过长的名称', () => {
      const invalidTag = {
        name: 'a'.repeat(31),
        color: '#FF5733',
      }

      const result = tagSchema.safeParse(invalidTag)
      expect(result.success).toBe(false)
    })
  })

  describe('searchSchema', () => {
    it('应该验证有效的搜索数据', () => {
      const validSearch = {
        query: '测试搜索',
        categories: ['工作', '学习'],
        tags: ['测试', '开发'],
        sortBy: 'createdAt',
        sortOrder: 'desc',
        limit: 10,
        offset: 0,
      }

      const result = searchSchema.safeParse(validSearch)
      expect(result.success).toBe(true)
    })

    it('应该拒绝无效的排序字段', () => {
      const invalidSearch = {
        query: '测试搜索',
        sortBy: 'invalid-field',
        sortOrder: 'desc',
      }

      const result = searchSchema.safeParse(invalidSearch)
      expect(result.success).toBe(false)
    })

    it('应该拒绝无效的排序顺序', () => {
      const invalidSearch = {
        query: '测试搜索',
        sortBy: 'createdAt',
        sortOrder: 'invalid-order',
      }

      const result = searchSchema.safeParse(invalidSearch)
      expect(result.success).toBe(false)
    })

    it('应该拒绝负数的限制和偏移', () => {
      const invalidSearch = {
        query: '测试搜索',
        limit: -1,
        offset: -1,
      }

      const result = searchSchema.safeParse(invalidSearch)
      expect(result.success).toBe(false)
    })
  })

  describe('batchOperationSchema', () => {
    it('应该验证有效的批量操作数据', () => {
      const validBatch = {
        promptIds: ['1', '2', '3'],
        operation: 'delete',
        metadata: {
          categoryId: '1',
        },
      }

      const result = batchOperationSchema.safeParse(validBatch)
      expect(result.success).toBe(true)
    })

    it('应该拒绝空的 ID 列表', () => {
      const invalidBatch = {
        promptIds: [],
        operation: 'delete',
      }

      const result = batchOperationSchema.safeParse(invalidBatch)
      expect(result.success).toBe(false)
    })

    it('应该拒绝无效的操作类型', () => {
      const invalidBatch = {
        promptIds: ['1', '2'],
        operation: 'invalid-operation',
      }

      const result = batchOperationSchema.safeParse(invalidBatch)
      expect(result.success).toBe(false)
    })

    it('应该接受所有有效的操作类型', () => {
      const validOperations = ['delete', 'categorize', 'favorite', 'unfavorite', 'pin', 'unpin']
      
      validOperations.forEach(operation => {
        const batch = {
          promptIds: ['1', '2'],
          operation,
        }

        const result = batchOperationSchema.safeParse(batch)
        expect(result.success).toBe(true)
      })
    })
  })

  describe('importPromptSchema', () => {
    it('应该验证有效的导入提示词数据', () => {
      const validImport = {
        title: '导入的提示词',
        content: '导入的内容',
        category: '工作',
        tags: ['导入', '测试'],
        isPublic: true,
      }

      const result = importPromptSchema.safeParse(validImport)
      expect(result.success).toBe(true)
    })

    it('应该拒绝无效的导入数据', () => {
      const invalidImport = {
        title: '',
        content: '内容',
      }

      const result = importPromptSchema.safeParse(invalidImport)
      expect(result.success).toBe(false)
    })
  })

  describe('filterSchema', () => {
    it('应该验证有效的筛选数据', () => {
      const validFilter = {
        categories: ['工作', '学习'],
        tags: ['测试', '开发'],
        isFavorite: true,
        isPinned: false,
        isPublic: true,
        dateRange: {
          from: new Date('2023-01-01'),
          to: new Date('2023-12-31'),
        },
        usageCount: {
          min: 0,
          max: 100,
        },
      }

      const result = filterSchema.safeParse(validFilter)
      expect(result.success).toBe(true)
    })

    it('应该接受空的筛选条件', () => {
      const emptyFilter = {}

      const result = filterSchema.safeParse(emptyFilter)
      expect(result.success).toBe(true)
    })

    it('应该拒绝无效的日期范围', () => {
      const invalidFilter = {
        dateRange: {
          from: 'invalid-date',
          to: new Date(),
        },
      }

      const result = filterSchema.safeParse(invalidFilter)
      expect(result.success).toBe(false)
    })

    it('应该拒绝负数的使用次数', () => {
      const invalidFilter = {
        usageCount: {
          min: -1,
          max: 100,
        },
      }

      const result = filterSchema.safeParse(invalidFilter)
      expect(result.success).toBe(false)
    })
  })
})