{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/trpc/query-client.ts"], "sourcesContent": ["import {\n  defaultShouldDehydrateQuery,\n  QueryClient,\n} from \"@tanstack/react-query\";\nimport SuperJSON from \"superjson\";\n\nexport const createQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        // With SSR, we usually want to set some default staleTime\n        // above 0 to avoid refetching immediately on the client\n        staleTime: 30 * 1000,\n      },\n      dehydrate: {\n        serializeData: SuperJSON.serialize,\n        shouldDehydrateQuery: (query) =>\n          defaultShouldDehydrateQuery(query) ||\n          query.state.status === \"pending\",\n      },\n      hydrate: {\n        deserializeData: SuperJSON.deserialize,\n      },\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAIA;;;AAEO,MAAM,oBAAoB,IAC/B,IAAI,6KAAA,CAAA,cAAW,CAAC;QACd,gBAAgB;YACd,SAAS;gBACP,0DAA0D;gBAC1D,wDAAwD;gBACxD,WAAW,KAAK;YAClB;YACA,WAAW;gBACT,eAAe,0IAAA,CAAA,UAAS,CAAC,SAAS;gBAClC,sBAAsB,CAAC,QACrB,CAAA,GAAA,2KAAA,CAAA,8BAA2B,AAAD,EAAE,UAC5B,MAAM,KAAK,CAAC,MAAM,KAAK;YAC3B;YACA,SAAS;gBACP,iBAAiB,0IAAA,CAAA,UAAS,CAAC,WAAW;YACxC;QACF;IACF", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/trpc/react.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClientProvider, type QueryClient } from \"@tanstack/react-query\";\nimport { httpBatchStreamLink, loggerLink } from \"@trpc/client\";\nimport { createTRPCReact } from \"@trpc/react-query\";\nimport { type inferRouterInputs, type inferRouterOutputs } from \"@trpc/server\";\nimport { useState } from \"react\";\nimport SuperJSON from \"superjson\";\n\nimport { type AppRouter } from \"~/server/api/root\";\nimport { createQueryClient } from \"./query-client\";\n\nlet clientQueryClientSingleton: QueryClient | undefined = undefined;\nconst getQueryClient = () => {\n  if (typeof window === \"undefined\") {\n    // Server: always make a new query client\n    return createQueryClient();\n  }\n  // Browser: use singleton pattern to keep the same query client\n  clientQueryClientSingleton ??= createQueryClient();\n\n  return clientQueryClientSingleton;\n};\n\nexport const api = createTRPCReact<AppRouter>();\n\n/**\n * Inference helper for inputs.\n *\n * @example type HelloInput = RouterInputs['example']['hello']\n */\nexport type RouterInputs = inferRouterInputs<AppRouter>;\n\n/**\n * Inference helper for outputs.\n *\n * @example type HelloOutput = RouterOutputs['example']['hello']\n */\nexport type RouterOutputs = inferRouterOutputs<AppRouter>;\n\nexport function TRPCReactProvider(props: { children: React.ReactNode }) {\n  const queryClient = getQueryClient();\n\n  const [trpcClient] = useState(() =>\n    api.createClient({\n      links: [\n        loggerLink({\n          enabled: (op) =>\n            process.env.NODE_ENV === \"development\" ||\n            (op.direction === \"down\" && op.result instanceof Error),\n        }),\n        httpBatchStreamLink({\n          transformer: SuperJSON,\n          url: getBaseUrl() + \"/api/trpc\",\n          headers: () => {\n            const headers = new Headers();\n            headers.set(\"x-trpc-source\", \"nextjs-react\");\n            return headers;\n          },\n        }),\n      ],\n    }),\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <api.Provider client={trpcClient} queryClient={queryClient}>\n        {props.children}\n      </api.Provider>\n    </QueryClientProvider>\n  );\n}\n\nfunction getBaseUrl() {\n  if (typeof window !== \"undefined\") return window.location.origin;\n  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\n  return `http://localhost:${process.env.PORT ?? 3000}`;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AACA;AAGA;AAVA;;;;;;;;AAYA,IAAI,6BAAsD;AAC1D,MAAM,iBAAiB;IACrB,wCAAmC;QACjC,yCAAyC;QACzC,OAAO,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IACzB;;;AAKF;AAEO,MAAM,MAAM,CAAA,GAAA,0KAAA,CAAA,kBAAe,AAAD;AAgB1B,SAAS,kBAAkB,KAAoC;IACpE,MAAM,cAAc;IAEpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAC5B,IAAI,YAAY,CAAC;YACf,OAAO;gBACL,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE;oBACT,SAAS,CAAC,KACR,oDAAyB,iBACxB,GAAG,SAAS,KAAK,UAAU,GAAG,MAAM,YAAY;gBACrD;gBACA,CAAA,GAAA,kKAAA,CAAA,sBAAmB,AAAD,EAAE;oBAClB,aAAa,0IAAA,CAAA,UAAS;oBACtB,KAAK,eAAe;oBACpB,SAAS;wBACP,MAAM,UAAU,IAAI;wBACpB,QAAQ,GAAG,CAAC,iBAAiB;wBAC7B,OAAO;oBACT;gBACF;aACD;QACH;IAGF,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,8OAAC,IAAI,QAAQ;YAAC,QAAQ;YAAY,aAAa;sBAC5C,MAAM,QAAQ;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP;;IACA,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;IACtE,OAAO,CAAC,iBAAiB,EAAE,QAAQ,GAAG,CAAC,IAAI,IAAI,MAAM;AACvD", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/env.js"], "sourcesContent": ["import { createEnv } from \"@t3-oss/env-nextjs\";\nimport { z } from \"zod\";\n\nexport const env = createEnv({\n  /**\n   * Specify your server-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars.\n   */\n  server: {\n    DATABASE_URL: z.string().url(),\n    NODE_ENV: z\n      .enum([\"development\", \"test\", \"production\"])\n      .default(\"development\"),\n  },\n\n  /**\n   * Specify your client-side environment variables schema here. This way you can ensure the app\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\n   * `NEXT_PUBLIC_`.\n   */\n  client: {\n    NEXT_PUBLIC_SUPABASE_URL: z.string().url(),\n    NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),\n  },\n\n  /**\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\n   * middlewares) or client-side so we need to destruct manually.\n   */\n  runtimeEnv: {\n    DATABASE_URL: process.env.DATABASE_URL,\n    NODE_ENV: process.env.NODE_ENV,\n    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,\n    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  },\n  /**\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\n   * useful for Docker builds.\n   */\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\n  /**\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\n   * `SOME_VAR=''` will throw an error.\n   */\n  emptyStringAsUndefined: true,\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,cAAc,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QAC5B,UAAU,kKAAA,CAAA,IAAC,CACR,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa,EAC1C,OAAO,CAAC;IACb;IAEA;;;;GAIC,GACD,QAAQ;QACN,0BAA0B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;QACxC,+BAA+B,kKAAA,CAAA,IAAC,CAAC,MAAM;IACzC;IAEA;;;GAGC,GACD,YAAY;QACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACtC,QAAQ;QACR,wBAAwB;QACxB,6BAA6B;IAC/B;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\r\nimport { env } from '~/env'\r\n\r\nexport const supabase = createClientComponentClient({\r\n  supabaseUrl: env.NEXT_PUBLIC_SUPABASE_URL,\r\n  supabaseKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\r\n})\r\n\r\nexport type Database = {\r\n  public: {\r\n    Tables: {\r\n      categories: {\r\n        Row: {\r\n          id: string\r\n          name: string\r\n          description: string | null\r\n          color: string\r\n          icon: string\r\n          user_id: string\r\n          created_at: string\r\n          updated_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          name: string\r\n          description?: string | null\r\n          color?: string\r\n          icon?: string\r\n          user_id: string\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          name?: string\r\n          description?: string | null\r\n          color?: string\r\n          icon?: string\r\n          user_id?: string\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n      }\r\n      prompts: {\r\n        Row: {\r\n          id: string\r\n          title: string\r\n          content: string\r\n          description: string | null\r\n          category_id: string | null\r\n          user_id: string\r\n          usage_count: number\r\n          is_favorite: boolean\r\n          is_public: boolean\r\n          created_at: string\r\n          updated_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          title: string\r\n          content: string\r\n          description?: string | null\r\n          category_id?: string | null\r\n          user_id: string\r\n          usage_count?: number\r\n          is_favorite?: boolean\r\n          is_public?: boolean\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          title?: string\r\n          content?: string\r\n          description?: string | null\r\n          category_id?: string | null\r\n          user_id?: string\r\n          usage_count?: number\r\n          is_favorite?: boolean\r\n          is_public?: boolean\r\n          created_at?: string\r\n          updated_at?: string\r\n        }\r\n      }\r\n      tags: {\r\n        Row: {\r\n          id: string\r\n          name: string\r\n          color: string\r\n          user_id: string\r\n          created_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          name: string\r\n          color?: string\r\n          user_id: string\r\n          created_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          name?: string\r\n          color?: string\r\n          user_id?: string\r\n          created_at?: string\r\n        }\r\n      }\r\n      prompt_tags: {\r\n        Row: {\r\n          prompt_id: string\r\n          tag_id: string\r\n          created_at: string\r\n        }\r\n        Insert: {\r\n          prompt_id: string\r\n          tag_id: string\r\n          created_at?: string\r\n        }\r\n        Update: {\r\n          prompt_id?: string\r\n          tag_id?: string\r\n          created_at?: string\r\n        }\r\n      }\r\n      search_history: {\r\n        Row: {\r\n          id: string\r\n          query: string\r\n          user_id: string\r\n          created_at: string\r\n        }\r\n        Insert: {\r\n          id?: string\r\n          query: string\r\n          user_id: string\r\n          created_at?: string\r\n        }\r\n        Update: {\r\n          id?: string\r\n          query?: string\r\n          user_id?: string\r\n          created_at?: string\r\n        }\r\n      }\r\n    }\r\n    Views: {\r\n      [_ in never]: never\r\n    }\r\n    Functions: {\r\n      increment_prompt_usage: {\r\n        Args: {\r\n          prompt_uuid: string\r\n        }\r\n        Returns: undefined\r\n      }\r\n    }\r\n    Enums: {\r\n      [_ in never]: never\r\n    }\r\n    CompositeTypes: {\r\n      [_ in never]: never\r\n    }\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,WAAW,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD,EAAE;IAClD,aAAa,0GAAA,CAAA,MAAG,CAAC,wBAAwB;IACzC,aAAa,0GAAA,CAAA,MAAG,CAAC,6BAA6B;AAChD", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/lib/auth/context.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react'\r\nimport { User, Session } from '@supabase/supabase-js'\r\nimport { supabase } from '~/lib/supabase/client'\r\n\r\ninterface AuthContextType {\r\n  user: User | null\r\n  session: Session | null\r\n  loading: boolean\r\n  signIn: (email: string, password: string) => Promise<{ error: any }>\r\n  signUp: (email: string, password: string) => Promise<{ error: any }>\r\n  signOut: () => Promise<void>\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext)\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider')\r\n  }\r\n  return context\r\n}\r\n\r\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\r\n  const [user, setUser] = useState<User | null>(null)\r\n  const [session, setSession] = useState<Session | null>(null)\r\n  const [loading, setLoading] = useState(true)\r\n\r\n  useEffect(() => {\r\n    // 获取初始会话\r\n    const getInitialSession = async () => {\r\n      const { data: { session } } = await supabase.auth.getSession()\r\n      setSession(session)\r\n      setUser(session?.user ?? null)\r\n      setLoading(false)\r\n    }\r\n\r\n    getInitialSession()\r\n\r\n    // 监听认证状态变化\r\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\r\n      async (event, session) => {\r\n        setSession(session)\r\n        setUser(session?.user ?? null)\r\n        setLoading(false)\r\n      }\r\n    )\r\n\r\n    return () => subscription.unsubscribe()\r\n  }, [])\r\n\r\n  const signIn = async (email: string, password: string) => {\r\n    const { error } = await supabase.auth.signInWithPassword({\r\n      email,\r\n      password,\r\n    })\r\n    return { error }\r\n  }\r\n\r\n  const signUp = async (email: string, password: string) => {\r\n    const { error } = await supabase.auth.signUp({\r\n      email,\r\n      password,\r\n    })\r\n    return { error }\r\n  }\r\n\r\n  const signOut = async () => {\r\n    await supabase.auth.signOut()\r\n  }\r\n\r\n  const value = {\r\n    user,\r\n    session,\r\n    loading,\r\n    signIn,\r\n    signUp,\r\n    signOut,\r\n  }\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA;QAEA,WAAW;QACX,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,gIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/lib/errors/index.ts"], "sourcesContent": ["import { ZodError } from 'zod'\r\n\r\n// 自定义错误类型\r\nexport class AppError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public code: string,\r\n    public statusCode: number = 500,\r\n    public details?: any\r\n  ) {\r\n    super(message)\r\n    this.name = 'AppError'\r\n  }\r\n}\r\n\r\n// 验证错误\r\nexport class ValidationError extends AppError {\r\n  constructor(message: string, details?: any) {\r\n    super(message, 'VALIDATION_ERROR', 400, details)\r\n    this.name = 'ValidationError'\r\n  }\r\n}\r\n\r\n// 权限错误\r\nexport class AuthorizationError extends AppError {\r\n  constructor(message: string = '权限不足') {\r\n    super(message, 'AUTHORIZATION_ERROR', 403)\r\n    this.name = 'AuthorizationError'\r\n  }\r\n}\r\n\r\n// 认证错误\r\nexport class AuthenticationError extends AppError {\r\n  constructor(message: string = '认证失败') {\r\n    super(message, 'AUTHENTICATION_ERROR', 401)\r\n    this.name = 'AuthenticationError'\r\n  }\r\n}\r\n\r\n// 资源未找到错误\r\nexport class NotFoundError extends AppError {\r\n  constructor(message: string = '资源不存在') {\r\n    super(message, 'NOT_FOUND_ERROR', 404)\r\n    this.name = 'NotFoundError'\r\n  }\r\n}\r\n\r\n// 业务逻辑错误\r\nexport class BusinessError extends AppError {\r\n  constructor(message: string, code: string = 'BUSINESS_ERROR') {\r\n    super(message, code, 400)\r\n    this.name = 'BusinessError'\r\n  }\r\n}\r\n\r\n// 错误处理器\r\nexport class ErrorHandler {\r\n  static handle(error: unknown): {\r\n    message: string\r\n    code: string\r\n    statusCode: number\r\n    details?: any\r\n  } {\r\n    // ZodError 处理\r\n    if (error instanceof ZodError) {\r\n      return {\r\n        message: '数据验证失败',\r\n        code: 'VALIDATION_ERROR',\r\n        statusCode: 400,\r\n        details: error.errors.map(err => ({\r\n          field: err.path.join('.'),\r\n          message: err.message,\r\n          value: err.input,\r\n        })),\r\n      }\r\n    }\r\n\r\n    // 自定义应用错误\r\n    if (error instanceof AppError) {\r\n      return {\r\n        message: error.message,\r\n        code: error.code,\r\n        statusCode: error.statusCode,\r\n        details: error.details,\r\n      }\r\n    }\r\n\r\n    // 数据库错误\r\n    if (error && typeof error === 'object' && 'code' in error) {\r\n      const dbError = error as { code: string; message: string }\r\n      \r\n      switch (dbError.code) {\r\n        case 'P2002':\r\n          return {\r\n            message: '数据已存在，请检查唯一性约束',\r\n            code: 'UNIQUE_CONSTRAINT_ERROR',\r\n            statusCode: 409,\r\n            details: dbError.message,\r\n          }\r\n        case 'P2025':\r\n          return {\r\n            message: '要操作的记录不存在',\r\n            code: 'RECORD_NOT_FOUND',\r\n            statusCode: 404,\r\n            details: dbError.message,\r\n          }\r\n        case 'P2003':\r\n          return {\r\n            message: '外键约束失败',\r\n            code: 'FOREIGN_KEY_CONSTRAINT_ERROR',\r\n            statusCode: 400,\r\n            details: dbError.message,\r\n          }\r\n        default:\r\n          return {\r\n            message: '数据库操作失败',\r\n            code: 'DATABASE_ERROR',\r\n            statusCode: 500,\r\n            details: dbError.message,\r\n          }\r\n      }\r\n    }\r\n\r\n    // 网络错误\r\n    if (error instanceof TypeError && error.message.includes('fetch')) {\r\n      return {\r\n        message: '网络请求失败，请检查网络连接',\r\n        code: 'NETWORK_ERROR',\r\n        statusCode: 502,\r\n      }\r\n    }\r\n\r\n    // 默认错误\r\n    return {\r\n      message: error instanceof Error ? error.message : '未知错误',\r\n      code: 'UNKNOWN_ERROR',\r\n      statusCode: 500,\r\n    }\r\n  }\r\n\r\n  static getErrorMessage(error: unknown): string {\r\n    const handled = this.handle(error)\r\n    return handled.message\r\n  }\r\n\r\n  static getValidationErrors(error: unknown): Array<{\r\n    field: string\r\n    message: string\r\n    value?: any\r\n  }> {\r\n    if (error instanceof ZodError) {\r\n      return error.errors.map(err => ({\r\n        field: err.path.join('.'),\r\n        message: err.message,\r\n        value: err.input,\r\n      }))\r\n    }\r\n\r\n    if (error instanceof ValidationError && error.details) {\r\n      return Array.isArray(error.details) ? error.details : [error.details]\r\n    }\r\n\r\n    return []\r\n  }\r\n}\r\n\r\n// 错误边界组件的错误处理\r\nexport const handleComponentError = (error: Error, errorInfo: { componentStack: string }) => {\r\n  console.error('组件错误:', error)\r\n  console.error('错误信息:', errorInfo)\r\n  \r\n  // 这里可以发送错误报告到监控服务\r\n  // reportError(error, errorInfo)\r\n}\r\n\r\n// 异步操作错误处理\r\nexport const handleAsyncError = (error: unknown, context?: string) => {\r\n  const handled = ErrorHandler.handle(error)\r\n  \r\n  console.error(`异步操作错误${context ? ` (${context})` : ''}:`, handled)\r\n  \r\n  // 这里可以发送错误报告到监控服务\r\n  // reportError(handled, context)\r\n  \r\n  return handled\r\n}\r\n\r\n// 表单验证错误处理\r\nexport const handleFormError = (error: unknown): Record<string, string> => {\r\n  const validationErrors = ErrorHandler.getValidationErrors(error)\r\n  \r\n  return validationErrors.reduce((acc, err) => {\r\n    acc[err.field] = err.message\r\n    return acc\r\n  }, {} as Record<string, string>)\r\n}\r\n\r\n// 错误重试机制\r\nexport const withRetry = async <T>(\r\n  fn: () => Promise<T>,\r\n  maxRetries: number = 3,\r\n  delay: number = 1000\r\n): Promise<T> => {\r\n  let lastError: unknown\r\n  \r\n  for (let i = 0; i < maxRetries; i++) {\r\n    try {\r\n      return await fn()\r\n    } catch (error) {\r\n      lastError = error\r\n      \r\n      // 如果是客户端错误（4xx），不重试\r\n      const handled = ErrorHandler.handle(error)\r\n      if (handled.statusCode >= 400 && handled.statusCode < 500) {\r\n        throw error\r\n      }\r\n      \r\n      // 如果不是最后一次，等待后重试\r\n      if (i < maxRetries - 1) {\r\n        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))\r\n      }\r\n    }\r\n  }\r\n  \r\n  throw lastError\r\n}\r\n\r\n// 错误日志记录\r\nexport const logError = (error: unknown, context?: string) => {\r\n  const handled = ErrorHandler.handle(error)\r\n  \r\n  const logData = {\r\n    timestamp: new Date().toISOString(),\r\n    context,\r\n    error: handled,\r\n    stack: error instanceof Error ? error.stack : undefined,\r\n  }\r\n  \r\n  // 开发环境直接输出到控制台\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.error('应用错误:', logData)\r\n  }\r\n  \r\n  // 生产环境发送到监控服务\r\n  // if (process.env.NODE_ENV === 'production') {\r\n  //   sendToMonitoring(logData)\r\n  // }\r\n}\r\n\r\n// 错误常量\r\nexport const ERROR_MESSAGES = {\r\n  VALIDATION: {\r\n    REQUIRED: '此字段为必填项',\r\n    INVALID_EMAIL: '邮箱格式不正确',\r\n    INVALID_URL: 'URL格式不正确',\r\n    TOO_SHORT: '内容太短',\r\n    TOO_LONG: '内容太长',\r\n    INVALID_FORMAT: '格式不正确',\r\n  },\r\n  AUTH: {\r\n    UNAUTHORIZED: '请先登录',\r\n    FORBIDDEN: '权限不足',\r\n    INVALID_TOKEN: '无效的访问令牌',\r\n    EXPIRED_TOKEN: '访问令牌已过期',\r\n  },\r\n  RESOURCE: {\r\n    NOT_FOUND: '资源不存在',\r\n    ALREADY_EXISTS: '资源已存在',\r\n    CANNOT_DELETE: '无法删除此资源',\r\n    CANNOT_UPDATE: '无法更新此资源',\r\n  },\r\n  SYSTEM: {\r\n    SERVER_ERROR: '服务器内部错误',\r\n    NETWORK_ERROR: '网络连接失败',\r\n    TIMEOUT: '请求超时',\r\n    UNKNOWN: '未知错误',\r\n  },\r\n} as const"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,iBAAiB;;;;IAC5B,YACE,OAAe,EACf,AAAO,IAAY,EACnB,AAAO,aAAqB,GAAG,EAC/B,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAJC,OAAA,WACA,aAAA,iBACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,wBAAwB;IACnC,YAAY,OAAe,EAAE,OAAa,CAAE;QAC1C,KAAK,CAAC,SAAS,oBAAoB,KAAK;QACxC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,2BAA2B;IACtC,YAAY,UAAkB,MAAM,CAAE;QACpC,KAAK,CAAC,SAAS,uBAAuB;QACtC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,4BAA4B;IACvC,YAAY,UAAkB,MAAM,CAAE;QACpC,KAAK,CAAC,SAAS,wBAAwB;QACvC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,YAAY,UAAkB,OAAO,CAAE;QACrC,KAAK,CAAC,SAAS,mBAAmB;QAClC,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,sBAAsB;IACjC,YAAY,OAAe,EAAE,OAAe,gBAAgB,CAAE;QAC5D,KAAK,CAAC,SAAS,MAAM;QACrB,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM;IACX,OAAO,OAAO,KAAc,EAK1B;QACA,cAAc;QACd,IAAI,iBAAiB,qIAAA,CAAA,WAAQ,EAAE;YAC7B,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,YAAY;gBACZ,SAAS,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAChC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;wBACrB,SAAS,IAAI,OAAO;wBACpB,OAAO,IAAI,KAAK;oBAClB,CAAC;YACH;QACF;QAEA,UAAU;QACV,IAAI,iBAAiB,UAAU;YAC7B,OAAO;gBACL,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,YAAY,MAAM,UAAU;gBAC5B,SAAS,MAAM,OAAO;YACxB;QACF;QAEA,QAAQ;QACR,IAAI,SAAS,OAAO,UAAU,YAAY,UAAU,OAAO;YACzD,MAAM,UAAU;YAEhB,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;gBACF,KAAK;oBACH,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;gBACF,KAAK;oBACH,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;gBACF;oBACE,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,YAAY;wBACZ,SAAS,QAAQ,OAAO;oBAC1B;YACJ;QACF;QAEA,OAAO;QACP,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;YACjE,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,YAAY;YACd;QACF;QAEA,OAAO;QACP,OAAO;YACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,MAAM;YACN,YAAY;QACd;IACF;IAEA,OAAO,gBAAgB,KAAc,EAAU;QAC7C,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC;QAC5B,OAAO,QAAQ,OAAO;IACxB;IAEA,OAAO,oBAAoB,KAAc,EAItC;QACD,IAAI,iBAAiB,qIAAA,CAAA,WAAQ,EAAE;YAC7B,OAAO,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC9B,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;oBACrB,SAAS,IAAI,OAAO;oBACpB,OAAO,IAAI,KAAK;gBAClB,CAAC;QACH;QAEA,IAAI,iBAAiB,mBAAmB,MAAM,OAAO,EAAE;YACrD,OAAO,MAAM,OAAO,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAAG;gBAAC,MAAM,OAAO;aAAC;QACvE;QAEA,OAAO,EAAE;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAc;IACjD,QAAQ,KAAK,CAAC,SAAS;IACvB,QAAQ,KAAK,CAAC,SAAS;AAEvB,kBAAkB;AAClB,gCAAgC;AAClC;AAGO,MAAM,mBAAmB,CAAC,OAAgB;IAC/C,MAAM,UAAU,aAAa,MAAM,CAAC;IAEpC,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;IAE1D,kBAAkB;IAClB,gCAAgC;IAEhC,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,mBAAmB,aAAa,mBAAmB,CAAC;IAE1D,OAAO,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACnC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO;QAC5B,OAAO;IACT,GAAG,CAAC;AACN;AAGO,MAAM,YAAY,OACvB,IACA,aAAqB,CAAC,EACtB,QAAgB,IAAI;IAEpB,IAAI;IAEJ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,oBAAoB;YACpB,MAAM,UAAU,aAAa,MAAM,CAAC;YACpC,IAAI,QAAQ,UAAU,IAAI,OAAO,QAAQ,UAAU,GAAG,KAAK;gBACzD,MAAM;YACR;YAEA,iBAAiB;YACjB,IAAI,IAAI,aAAa,GAAG;gBACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,CAAC,IAAI,CAAC;YACjE;QACF;IACF;IAEA,MAAM;AACR;AAGO,MAAM,WAAW,CAAC,OAAgB;IACvC,MAAM,UAAU,aAAa,MAAM,CAAC;IAEpC,MAAM,UAAU;QACd,WAAW,IAAI,OAAO,WAAW;QACjC;QACA,OAAO;QACP,OAAO,iBAAiB,QAAQ,MAAM,KAAK,GAAG;IAChD;IAEA,eAAe;IACf,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,SAAS;IACzB;AAEA,cAAc;AACd,+CAA+C;AAC/C,8BAA8B;AAC9B,IAAI;AACN;AAGO,MAAM,iBAAiB;IAC5B,YAAY;QACV,UAAU;QACV,eAAe;QACf,aAAa;QACb,WAAW;QACX,UAAU;QACV,gBAAgB;IAClB;IACA,MAAM;QACJ,cAAc;QACd,WAAW;QACX,eAAe;QACf,eAAe;IACjB;IACA,UAAU;QACR,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,eAAe;IACjB;IACA,QAAQ;QACN,cAAc;QACd,eAAe;QACf,SAAS;QACT,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/error/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { handleComponentError } from '~/lib/errors'\r\n\r\ninterface ErrorBoundaryState {\r\n  hasError: boolean\r\n  error?: Error\r\n  errorInfo?: React.ErrorInfo\r\n}\r\n\r\ninterface ErrorBoundaryProps {\r\n  children: React.ReactNode\r\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void\r\n}\r\n\r\nclass ErrorBoundaryClass extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\r\n  constructor(props: ErrorBoundaryProps) {\r\n    super(props)\r\n    this.state = { hasError: false }\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\r\n    return {\r\n      hasError: true,\r\n      error,\r\n    }\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\r\n    this.setState({\r\n      error,\r\n      errorInfo,\r\n    })\r\n\r\n    // 处理错误\r\n    handleComponentError(error, errorInfo)\r\n    \r\n    // 调用自定义错误处理\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo)\r\n    }\r\n  }\r\n\r\n  retry = () => {\r\n    this.setState({\r\n      hasError: false,\r\n      error: undefined,\r\n      errorInfo: undefined,\r\n    })\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError && this.state.error) {\r\n      // 使用自定义错误组件\r\n      if (this.props.fallback) {\r\n        const FallbackComponent = this.props.fallback\r\n        return <FallbackComponent error={this.state.error} retry={this.retry} />\r\n      }\r\n\r\n      // 默认错误页面\r\n      return <DefaultErrorFallback error={this.state.error} retry={this.retry} />\r\n    }\r\n\r\n    return this.props.children\r\n  }\r\n}\r\n\r\n// 默认错误回退组件\r\nconst DefaultErrorFallback = ({ error, retry }: { error: Error; retry: () => void }) => {\r\n  const isNetworkError = error.message.includes('fetch') || error.message.includes('network')\r\n  const isChunkError = error.message.includes('Loading chunk')\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      className=\"min-h-screen flex items-center justify-center bg-base-200\"\r\n    >\r\n      <div className=\"max-w-md w-full mx-4\">\r\n        <div className=\"bg-base-100 rounded-lg shadow-lg p-8 text-center\">\r\n          {/* 错误图标 */}\r\n          <div className=\"mb-6\">\r\n            {isNetworkError ? (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-16 h-16 mx-auto text-warning\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z\"\r\n                />\r\n              </svg>\r\n            ) : (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-16 h-16 mx-auto text-error\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                />\r\n              </svg>\r\n            )}\r\n          </div>\r\n\r\n          {/* 错误标题 */}\r\n          <h1 className=\"text-2xl font-bold text-base-content mb-4\">\r\n            {isNetworkError ? '网络连接失败' : \r\n             isChunkError ? '页面加载失败' : \r\n             '页面出现错误'}\r\n          </h1>\r\n\r\n          {/* 错误描述 */}\r\n          <p className=\"text-base-content/70 mb-6\">\r\n            {isNetworkError ? '请检查您的网络连接，然后重试。' :\r\n             isChunkError ? '页面资源加载失败，请刷新页面重试。' :\r\n             '页面遇到了意外错误，我们正在努力修复。'}\r\n          </p>\r\n\r\n          {/* 错误详情（开发环境） */}\r\n          {process.env.NODE_ENV === 'development' && (\r\n            <details className=\"mb-6 text-left\">\r\n              <summary className=\"cursor-pointer text-sm font-medium text-base-content/60 mb-2\">\r\n                错误详情\r\n              </summary>\r\n              <pre className=\"text-xs bg-base-200 p-4 rounded overflow-auto max-h-32\">\r\n                {error.stack}\r\n              </pre>\r\n            </details>\r\n          )}\r\n\r\n          {/* 操作按钮 */}\r\n          <div className=\"space-y-3\">\r\n            <button\r\n              onClick={retry}\r\n              className=\"btn btn-primary w-full\"\r\n            >\r\n              重试\r\n            </button>\r\n            \r\n            <button\r\n              onClick={() => window.location.reload()}\r\n              className=\"btn btn-ghost w-full\"\r\n            >\r\n              刷新页面\r\n            </button>\r\n            \r\n            <button\r\n              onClick={() => window.location.href = '/'}\r\n              className=\"btn btn-ghost w-full\"\r\n            >\r\n              返回首页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\n// 简单的错误回退组件\r\nexport const SimpleErrorFallback = ({ error, retry }: { error: Error; retry: () => void }) => (\r\n  <div className=\"bg-error/10 border border-error/20 rounded-lg p-6 text-center\">\r\n    <h3 className=\"text-lg font-semibold text-error mb-2\">加载失败</h3>\r\n    <p className=\"text-base-content/70 mb-4\">\r\n      {error.message || '遇到了未知错误'}\r\n    </p>\r\n    <button onClick={retry} className=\"btn btn-error btn-sm\">\r\n      重试\r\n    </button>\r\n  </div>\r\n)\r\n\r\n// 导出错误边界\r\nexport const ErrorBoundary = ErrorBoundaryClass\r\n\r\n// 错误边界 HOC\r\nexport const withErrorBoundary = <P extends object>(\r\n  Component: React.ComponentType<P>,\r\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\r\n) => {\r\n  const WrappedComponent = (props: P) => (\r\n    <ErrorBoundary fallback={fallback}>\r\n      <Component {...props} />\r\n    </ErrorBoundary>\r\n  )\r\n  \r\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`\r\n  \r\n  return WrappedComponent\r\n}\r\n\r\n// 异步错误边界\r\nexport const AsyncErrorBoundary = ({ children, fallback }: {\r\n  children: React.ReactNode\r\n  fallback?: React.ComponentType<{ error: Error; retry: () => void }>\r\n}) => {\r\n  const [asyncError, setAsyncError] = React.useState<Error | null>(null)\r\n\r\n  // 处理异步错误\r\n  React.useEffect(() => {\r\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\r\n      setAsyncError(new Error(event.reason))\r\n    }\r\n\r\n    window.addEventListener('unhandledrejection', handleUnhandledRejection)\r\n    \r\n    return () => {\r\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection)\r\n    }\r\n  }, [])\r\n\r\n  if (asyncError) {\r\n    const retry = () => setAsyncError(null)\r\n    \r\n    if (fallback) {\r\n      const FallbackComponent = fallback\r\n      return <FallbackComponent error={asyncError} retry={retry} />\r\n    }\r\n    \r\n    return <DefaultErrorFallback error={asyncError} retry={retry} />\r\n  }\r\n\r\n  return (\r\n    <ErrorBoundary fallback={fallback}>\r\n      {children}\r\n    </ErrorBoundary>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,2BAA2B,qMAAA,CAAA,UAAK,CAAC,SAAS;IAC9C,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;QAEA,OAAO;QACP,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;QAE5B,YAAY;QACZ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAEA,QAAQ;QACN,IAAI,CAAC,QAAQ,CAAC;YACZ,UAAU;YACV,OAAO;YACP,WAAW;QACb;IACF,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAC3C,YAAY;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAE,OAAO,IAAI,CAAC,KAAK;;;;;;YACtE;YAEA,SAAS;YACT,qBAAO,8OAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,KAAK;;;;;;QACzE;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,WAAW;AACX,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAuC;IACjF,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,MAAM,OAAO,CAAC,QAAQ,CAAC;IACjF,MAAM,eAAe,MAAM,OAAO,CAAC,QAAQ,CAAC;IAE5C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,+BACC,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;qFAIN,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;;;;;;kCAOV,8OAAC;wBAAG,WAAU;kCACX,iBAAiB,WACjB,eAAe,WACf;;;;;;kCAIH,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,oBACjB,eAAe,sBACf;;;;;;oBAIF,oDAAyB,+BACxB,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAQ,WAAU;0CAA+D;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,8OAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;0CAID,8OAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACtC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAGO,MAAM,sBAAsB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAuC,iBACvF,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BACV,MAAM,OAAO,IAAI;;;;;;0BAEpB,8OAAC;gBAAO,SAAS;gBAAO,WAAU;0BAAuB;;;;;;;;;;;;AAOtD,MAAM,gBAAgB;AAGtB,MAAM,oBAAoB,CAC/B,WACA;IAEA,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;YAAc,UAAU;sBACvB,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAGO,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAGtD;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEjE,SAAS;IACT,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,2BAA2B,CAAC;YAChC,cAAc,IAAI,MAAM,MAAM,MAAM;QACtC;QAEA,OAAO,gBAAgB,CAAC,sBAAsB;QAE9C,OAAO;YACL,OAAO,mBAAmB,CAAC,sBAAsB;QACnD;IACF,GAAG,EAAE;IAEL,IAAI,YAAY;QACd,MAAM,QAAQ,IAAM,cAAc;QAElC,IAAI,UAAU;YACZ,MAAM,oBAAoB;YAC1B,qBAAO,8OAAC;gBAAkB,OAAO;gBAAY,OAAO;;;;;;QACtD;QAEA,qBAAO,8OAAC;YAAqB,OAAO;YAAY,OAAO;;;;;;IACzD;IAEA,qBACE,8OAAC;QAAc,UAAU;kBACtB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/common/NetworkStatus.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface NetworkStatusProps {\r\n  className?: string\r\n}\r\n\r\nexport const NetworkStatus = ({ className = '' }: NetworkStatusProps) => {\r\n  const [isOnline, setIsOnline] = useState(true)\r\n  const [showNotification, setShowNotification] = useState(false)\r\n\r\n  useEffect(() => {\r\n    const handleOnline = () => {\r\n      setIsOnline(true)\r\n      setShowNotification(true)\r\n      setTimeout(() => setShowNotification(false), 3000)\r\n    }\r\n\r\n    const handleOffline = () => {\r\n      setIsOnline(false)\r\n      setShowNotification(true)\r\n    }\r\n\r\n    // 初始状态\r\n    setIsOnline(navigator.onLine)\r\n\r\n    // 监听网络状态变化\r\n    window.addEventListener('online', handleOnline)\r\n    window.addEventListener('offline', handleOffline)\r\n\r\n    return () => {\r\n      window.removeEventListener('online', handleOnline)\r\n      window.removeEventListener('offline', handleOffline)\r\n    }\r\n  }, [])\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {showNotification && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -50 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -50 }}\r\n          className={`fixed top-4 right-4 z-50 ${className}`}\r\n        >\r\n          <div className={`alert ${isOnline ? 'alert-success' : 'alert-error'} shadow-lg`}>\r\n            <div className=\"flex items-center space-x-2\">\r\n              {isOnline ? (\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-6 h-6\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z\"\r\n                  />\r\n                </svg>\r\n              ) : (\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-6 h-6\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 01-.923 1.785A5.969 5.969 0 006 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337z\"\r\n                  />\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5\"\r\n                  />\r\n                </svg>\r\n              )}\r\n              <span>\r\n                {isOnline ? '网络连接已恢复' : '网络连接已断开'}\r\n              </span>\r\n            </div>\r\n\r\n            <button\r\n              onClick={() => setShowNotification(false)}\r\n              className=\"btn btn-sm btn-ghost\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 网络状态钩子\r\nexport const useNetworkStatus = () => {\r\n  const [isOnline, setIsOnline] = useState(true)\r\n\r\n  useEffect(() => {\r\n    const handleOnline = () => setIsOnline(true)\r\n    const handleOffline = () => setIsOnline(false)\r\n\r\n    // 初始状态\r\n    setIsOnline(navigator.onLine)\r\n\r\n    // 监听网络状态变化\r\n    window.addEventListener('online', handleOnline)\r\n    window.addEventListener('offline', handleOffline)\r\n\r\n    return () => {\r\n      window.removeEventListener('online', handleOnline)\r\n      window.removeEventListener('offline', handleOffline)\r\n    }\r\n  }, [])\r\n\r\n  return { isOnline }\r\n}\r\n\r\n// 网络状态指示器\r\nexport const NetworkIndicator = ({ className = '' }: { className?: string }) => {\r\n  const { isOnline } = useNetworkStatus()\r\n\r\n  if (isOnline) return null\r\n\r\n  return (\r\n    <div className={`fixed bottom-4 left-4 z-50 ${className}`}>\r\n      <div className=\"flex items-center space-x-2 bg-error text-error-content px-4 py-2 rounded-lg shadow-lg\">\r\n        <div className=\"animate-pulse w-2 h-2 bg-current rounded-full\"></div>\r\n        <span className=\"text-sm\">离线模式</span>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAHA;;;;AASO,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE,EAAsB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY;YACZ,oBAAoB;YACpB,WAAW,IAAM,oBAAoB,QAAQ;QAC/C;QAEA,MAAM,gBAAgB;YACpB,YAAY;YACZ,oBAAoB;QACtB;QAEA,OAAO;QACP,YAAY,UAAU,MAAM;QAE5B,WAAW;QACX,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,WAAW,CAAC,yBAAyB,EAAE,WAAW;sBAElD,cAAA,8OAAC;gBAAI,WAAW,CAAC,MAAM,EAAE,WAAW,kBAAkB,cAAc,UAAU,CAAC;;kCAC7E,8OAAC;wBAAI,WAAU;;4BACZ,yBACC,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;yFAIN,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;;kDAEV,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;kDAEJ,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;0CAIR,8OAAC;0CACE,WAAW,YAAY;;;;;;;;;;;;kCAI5B,8OAAC;wBACC,SAAS,IAAM,oBAAoB;wBACnC,WAAU;kCAEV,cAAA,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrE;AAGO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,IAAM,YAAY;QACvC,MAAM,gBAAgB,IAAM,YAAY;QAExC,OAAO;QACP,YAAY,UAAU,MAAM;QAE5B,WAAW;QACX,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG,EAAE;IAEL,OAAO;QAAE;IAAS;AACpB;AAGO,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAE,EAA0B;IACzE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,IAAI,UAAU,OAAO;IAErB,qBACE,8OAAC;QAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;kBACvD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;;;;;;AAIlC", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/common/LoadingManager.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface LoadingManagerProps {\r\n  isLoading: boolean\r\n  message?: string\r\n  timeout?: number\r\n  onTimeout?: () => void\r\n  className?: string\r\n}\r\n\r\nexport const LoadingManager = ({\r\n  isLoading,\r\n  message = '加载中...',\r\n  timeout = 30000, // 30秒超时\r\n  onTimeout,\r\n  className = '',\r\n}: LoadingManagerProps) => {\r\n  const [hasTimedOut, setHasTimedOut] = useState(false)\r\n\r\n  useEffect(() => {\r\n    if (!isLoading) {\r\n      setHasTimedOut(false)\r\n      return\r\n    }\r\n\r\n    const timer = setTimeout(() => {\r\n      setHasTimedOut(true)\r\n      onTimeout?.()\r\n    }, timeout)\r\n\r\n    return () => clearTimeout(timer)\r\n  }, [isLoading, timeout, onTimeout])\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isLoading && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${className}`}\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            exit={{ scale: 0.9, opacity: 0 }}\r\n            className=\"bg-base-100 rounded-lg p-8 max-w-sm w-full mx-4\"\r\n          >\r\n            <div className=\"text-center\">\r\n              {!hasTimedOut ? (\r\n                <>\r\n                  {/* 加载动画 */}\r\n                  <div className=\"mb-4\">\r\n                    <span className=\"loading loading-spinner loading-lg text-primary\"></span>\r\n                  </div>\r\n                  \r\n                  {/* 加载消息 */}\r\n                  <p className=\"text-base-content font-medium mb-2\">{message}</p>\r\n                  <p className=\"text-base-content/60 text-sm\">请稍候...</p>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {/* 超时图标 */}\r\n                  <div className=\"mb-4\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-16 h-16 mx-auto text-warning\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  \r\n                  {/* 超时消息 */}\r\n                  <p className=\"text-base-content font-medium mb-2\">请求超时</p>\r\n                  <p className=\"text-base-content/60 text-sm mb-4\">\r\n                    请检查网络连接，然后重试\r\n                  </p>\r\n                  \r\n                  {/* 重试按钮 */}\r\n                  <button\r\n                    onClick={() => window.location.reload()}\r\n                    className=\"btn btn-primary btn-sm\"\r\n                  >\r\n                    重试\r\n                  </button>\r\n                </>\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 页面级加载组件\r\nexport const PageLoading = ({ message = '页面加载中...' }: { message?: string }) => {\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n      <div className=\"text-center\">\r\n        <div className=\"mb-4\">\r\n          <span className=\"loading loading-spinner loading-lg text-primary\"></span>\r\n        </div>\r\n        <p className=\"text-base-content font-medium\">{message}</p>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 内容加载组件\r\nexport const ContentLoading = ({ \r\n  message = '加载中...',\r\n  className = '' \r\n}: { \r\n  message?: string\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>\r\n      <div className=\"mb-4\">\r\n        <span className=\"loading loading-spinner loading-md text-primary\"></span>\r\n      </div>\r\n      <p className=\"text-base-content/70 text-sm\">{message}</p>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 骨架屏组件\r\nexport const SkeletonLoader = ({ \r\n  lines = 3, \r\n  className = '' \r\n}: { \r\n  lines?: number\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`animate-pulse space-y-3 ${className}`}>\r\n      {Array.from({ length: lines }, (_, i) => (\r\n        <div key={i} className=\"flex space-x-4\">\r\n          <div className=\"rounded-full bg-base-300 h-10 w-10\"></div>\r\n          <div className=\"flex-1 space-y-2 py-1\">\r\n            <div className=\"h-4 bg-base-300 rounded w-3/4\"></div>\r\n            <div className=\"h-4 bg-base-300 rounded w-1/2\"></div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n\r\n// 表格骨架屏\r\nexport const TableSkeleton = ({ \r\n  rows = 5, \r\n  cols = 4, \r\n  className = '' \r\n}: { \r\n  rows?: number\r\n  cols?: number\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`animate-pulse ${className}`}>\r\n      <div className=\"overflow-x-auto\">\r\n        <table className=\"table w-full\">\r\n          <thead>\r\n            <tr>\r\n              {Array.from({ length: cols }, (_, i) => (\r\n                <th key={i}>\r\n                  <div className=\"h-4 bg-base-300 rounded w-full\"></div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            {Array.from({ length: rows }, (_, i) => (\r\n              <tr key={i}>\r\n                {Array.from({ length: cols }, (_, j) => (\r\n                  <td key={j}>\r\n                    <div className=\"h-4 bg-base-300 rounded w-full\"></div>\r\n                  </td>\r\n                ))}\r\n              </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\n// 卡片骨架屏\r\nexport const CardSkeleton = ({ \r\n  count = 3, \r\n  className = '' \r\n}: { \r\n  count?: number\r\n  className?: string \r\n}) => {\r\n  return (\r\n    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>\r\n      {Array.from({ length: count }, (_, i) => (\r\n        <div key={i} className=\"animate-pulse\">\r\n          <div className=\"bg-base-100 rounded-lg p-6 shadow\">\r\n            <div className=\"flex items-center space-x-4 mb-4\">\r\n              <div className=\"w-12 h-12 bg-base-300 rounded-full\"></div>\r\n              <div className=\"space-y-2 flex-1\">\r\n                <div className=\"h-4 bg-base-300 rounded w-3/4\"></div>\r\n                <div className=\"h-3 bg-base-300 rounded w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"h-4 bg-base-300 rounded\"></div>\r\n              <div className=\"h-4 bg-base-300 rounded\"></div>\r\n              <div className=\"h-4 bg-base-300 rounded w-2/3\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  )\r\n}\r\n\r\n// 加载状态钩子\r\nexport const useLoadingState = (initialLoading = false) => {\r\n  const [isLoading, setIsLoading] = useState(initialLoading)\r\n  const [error, setError] = useState<Error | null>(null)\r\n\r\n  const startLoading = () => {\r\n    setIsLoading(true)\r\n    setError(null)\r\n  }\r\n\r\n  const stopLoading = () => {\r\n    setIsLoading(false)\r\n  }\r\n\r\n  const setLoadingError = (error: Error) => {\r\n    setError(error)\r\n    setIsLoading(false)\r\n  }\r\n\r\n  return {\r\n    isLoading,\r\n    error,\r\n    startLoading,\r\n    stopLoading,\r\n    setLoadingError,\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AAHA;;;;AAaO,MAAM,iBAAiB,CAAC,EAC7B,SAAS,EACT,UAAU,QAAQ,EAClB,UAAU,KAAK,EACf,SAAS,EACT,YAAY,EAAE,EACM;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,eAAe;YACf;QACF;QAEA,MAAM,QAAQ,WAAW;YACvB,eAAe;YACf;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAW;QAAS;KAAU;IAElC,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAW,CAAC,gEAAgE,EAAE,WAAW;sBAEzF,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACZ,CAAC,4BACA;;0CAEE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;;;;;;;;;;0CAIlB,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;;qDAG9C;;0CAEE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAMR,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CAKjD,8OAAC;gCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;AAGO,MAAM,cAAc,CAAC,EAAE,UAAU,UAAU,EAAwB;IACxE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;;;;;;;;;;;8BAElB,8OAAC;oBAAE,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAItD;AAGO,MAAM,iBAAiB,CAAC,EAC7B,UAAU,QAAQ,EAClB,YAAY,EAAE,EAIf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,gDAAgD,EAAE,WAAW;;0BAC5E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;;;;;;;AAGnD;AAGO,MAAM,iBAAiB,CAAC,EAC7B,QAAQ,CAAC,EACT,YAAY,EAAE,EAIf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;kBACnD,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;eAJT;;;;;;;;;;AAUlB;AAGO,MAAM,gBAAgB,CAAC,EAC5B,OAAO,CAAC,EACR,OAAO,CAAC,EACR,YAAY,EAAE,EAKf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;kBAC1C,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;kCACC,cAAA,8OAAC;sCACE,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAK,GAAG,CAAC,GAAG,kBAChC,8OAAC;8CACC,cAAA,8OAAC;wCAAI,WAAU;;;;;;mCADR;;;;;;;;;;;;;;;kCAMf,8OAAC;kCACE,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAK,GAAG,CAAC,GAAG,kBAChC,8OAAC;0CACE,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAK,GAAG,CAAC,GAAG,kBAChC,8OAAC;kDACC,cAAA,8OAAC;4CAAI,WAAU;;;;;;uCADR;;;;;+BAFJ;;;;;;;;;;;;;;;;;;;;;;;;;;AAavB;AAGO,MAAM,eAAe,CAAC,EAC3B,QAAQ,CAAC,EACT,YAAY,EAAE,EAIf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;kBAChF,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;eAZX;;;;;;;;;;AAmBlB;AAGO,MAAM,kBAAkB,CAAC,iBAAiB,KAAK;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,eAAe;QACnB,aAAa;QACb,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa;IACf;IAEA,MAAM,kBAAkB,CAAC;QACvB,SAAS;QACT,aAAa;IACf;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1650, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/stores/index.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\nimport { StoreState, UIState, LoadingState, ErrorState } from '~/types'\r\n\r\n// 初始状态\r\nconst initialUIState: UIState = {\r\n  sidebarOpen: true,\r\n  currentView: 'grid',\r\n  theme: 'light',\r\n  selectedCategory: null,\r\n  selectedTags: [],\r\n  searchQuery: '',\r\n}\r\n\r\nconst initialLoadingState: LoadingState = {\r\n  categories: false,\r\n  prompts: false,\r\n  tags: false,\r\n  search: false,\r\n  creating: false,\r\n  updating: false,\r\n  deleting: false,\r\n}\r\n\r\nconst initialErrorState: ErrorState = {\r\n  categories: null,\r\n  prompts: null,\r\n  tags: null,\r\n  search: null,\r\n  general: null,\r\n}\r\n\r\n// 主 Store 接口\r\nexport interface MainStore extends StoreState {\r\n  // UI 操作\r\n  toggleSidebar: () => void\r\n  setCurrentView: (view: 'grid' | 'list') => void\r\n  setTheme: (theme: 'light' | 'dark') => void\r\n  setSelectedCategory: (categoryId: string | null) => void\r\n  setSelectedTags: (tags: string[]) => void\r\n  setSearchQuery: (query: string) => void\r\n  \r\n  // 加载状态管理\r\n  setLoading: (key: keyof LoadingState, value: boolean) => void\r\n  \r\n  // 错误状态管理\r\n  setError: (key: keyof ErrorState, error: string | null) => void\r\n  clearErrors: () => void\r\n  \r\n  // 重置状态\r\n  resetState: () => void\r\n}\r\n\r\n// 创建主 Store\r\nexport const useMainStore = create<MainStore>()(\r\n  devtools(\r\n    persist(\r\n      immer((set, get) => ({\r\n        // 初始数据状态\r\n        categories: [],\r\n        prompts: [],\r\n        tags: [],\r\n        searchHistory: [],\r\n        searchResults: null,\r\n        searchSuggestions: null,\r\n        \r\n        // 初始 UI 状态\r\n        ui: initialUIState,\r\n        \r\n        // 初始加载状态\r\n        loading: initialLoadingState,\r\n        \r\n        // 初始错误状态\r\n        errors: initialErrorState,\r\n        \r\n        // 初始分页状态\r\n        pagination: {\r\n          prompts: {\r\n            offset: 0,\r\n            limit: 20,\r\n            total: 0,\r\n            hasMore: false,\r\n          },\r\n          search: {\r\n            offset: 0,\r\n            limit: 20,\r\n            total: 0,\r\n            hasMore: false,\r\n          },\r\n        },\r\n        \r\n        // UI 操作\r\n        toggleSidebar: () => set((state) => {\r\n          state.ui.sidebarOpen = !state.ui.sidebarOpen\r\n        }),\r\n        \r\n        setCurrentView: (view) => set((state) => {\r\n          state.ui.currentView = view\r\n        }),\r\n        \r\n        setTheme: (theme) => set((state) => {\r\n          state.ui.theme = theme\r\n        }),\r\n        \r\n        setSelectedCategory: (categoryId) => set((state) => {\r\n          state.ui.selectedCategory = categoryId\r\n        }),\r\n        \r\n        setSelectedTags: (tags) => set((state) => {\r\n          state.ui.selectedTags = tags\r\n        }),\r\n        \r\n        setSearchQuery: (query) => set((state) => {\r\n          state.ui.searchQuery = query\r\n        }),\r\n        \r\n        // 加载状态管理\r\n        setLoading: (key, value) => set((state) => {\r\n          state.loading[key] = value\r\n        }),\r\n        \r\n        // 错误状态管理\r\n        setError: (key, error) => set((state) => {\r\n          state.errors[key] = error\r\n        }),\r\n        \r\n        clearErrors: () => set((state) => {\r\n          state.errors = initialErrorState\r\n        }),\r\n        \r\n        // 重置状态\r\n        resetState: () => set((state) => {\r\n          state.categories = []\r\n          state.prompts = []\r\n          state.tags = []\r\n          state.searchHistory = []\r\n          state.searchResults = null\r\n          state.searchSuggestions = null\r\n          state.ui = initialUIState\r\n          state.loading = initialLoadingState\r\n          state.errors = initialErrorState\r\n          state.pagination = {\r\n            prompts: {\r\n              offset: 0,\r\n              limit: 20,\r\n              total: 0,\r\n              hasMore: false,\r\n            },\r\n            search: {\r\n              offset: 0,\r\n              limit: 20,\r\n              total: 0,\r\n              hasMore: false,\r\n            },\r\n          }\r\n        }),\r\n      })),\r\n      {\r\n        name: 'prompt-manager-store',\r\n        // 只持久化 UI 状态\r\n        partialize: (state) => ({\r\n          ui: {\r\n            sidebarOpen: state.ui.sidebarOpen,\r\n            currentView: state.ui.currentView,\r\n            theme: state.ui.theme,\r\n          },\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'prompt-manager-store',\r\n    }\r\n  )\r\n)\r\n\r\n// 导出 Store 类型\r\nexport type { MainStore }"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGA,OAAO;AACP,MAAM,iBAA0B;IAC9B,aAAa;IACb,aAAa;IACb,OAAO;IACP,kBAAkB;IAClB,cAAc,EAAE;IAChB,aAAa;AACf;AAEA,MAAM,sBAAoC;IACxC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;AACZ;AAEA,MAAM,oBAAgC;IACpC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,QAAQ;IACR,SAAS;AACX;AAwBO,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnB,SAAS;QACT,YAAY,EAAE;QACd,SAAS,EAAE;QACX,MAAM,EAAE;QACR,eAAe,EAAE;QACjB,eAAe;QACf,mBAAmB;QAEnB,WAAW;QACX,IAAI;QAEJ,SAAS;QACT,SAAS;QAET,SAAS;QACT,QAAQ;QAER,SAAS;QACT,YAAY;YACV,SAAS;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;YACA,QAAQ;gBACN,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;QACF;QAEA,QAAQ;QACR,eAAe,IAAM,IAAI,CAAC;gBACxB,MAAM,EAAE,CAAC,WAAW,GAAG,CAAC,MAAM,EAAE,CAAC,WAAW;YAC9C;QAEA,gBAAgB,CAAC,OAAS,IAAI,CAAC;gBAC7B,MAAM,EAAE,CAAC,WAAW,GAAG;YACzB;QAEA,UAAU,CAAC,QAAU,IAAI,CAAC;gBACxB,MAAM,EAAE,CAAC,KAAK,GAAG;YACnB;QAEA,qBAAqB,CAAC,aAAe,IAAI,CAAC;gBACxC,MAAM,EAAE,CAAC,gBAAgB,GAAG;YAC9B;QAEA,iBAAiB,CAAC,OAAS,IAAI,CAAC;gBAC9B,MAAM,EAAE,CAAC,YAAY,GAAG;YAC1B;QAEA,gBAAgB,CAAC,QAAU,IAAI,CAAC;gBAC9B,MAAM,EAAE,CAAC,WAAW,GAAG;YACzB;QAEA,SAAS;QACT,YAAY,CAAC,KAAK,QAAU,IAAI,CAAC;gBAC/B,MAAM,OAAO,CAAC,IAAI,GAAG;YACvB;QAEA,SAAS;QACT,UAAU,CAAC,KAAK,QAAU,IAAI,CAAC;gBAC7B,MAAM,MAAM,CAAC,IAAI,GAAG;YACtB;QAEA,aAAa,IAAM,IAAI,CAAC;gBACtB,MAAM,MAAM,GAAG;YACjB;QAEA,OAAO;QACP,YAAY,IAAM,IAAI,CAAC;gBACrB,MAAM,UAAU,GAAG,EAAE;gBACrB,MAAM,OAAO,GAAG,EAAE;gBAClB,MAAM,IAAI,GAAG,EAAE;gBACf,MAAM,aAAa,GAAG,EAAE;gBACxB,MAAM,aAAa,GAAG;gBACtB,MAAM,iBAAiB,GAAG;gBAC1B,MAAM,EAAE,GAAG;gBACX,MAAM,OAAO,GAAG;gBAChB,MAAM,MAAM,GAAG;gBACf,MAAM,UAAU,GAAG;oBACjB,SAAS;wBACP,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,SAAS;oBACX;oBACA,QAAQ;wBACN,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,SAAS;oBACX;gBACF;YACF;IACF,CAAC,IACD;IACE,MAAM;IACN,aAAa;IACb,YAAY,CAAC,QAAU,CAAC;YACtB,IAAI;gBACF,aAAa,MAAM,EAAE,CAAC,WAAW;gBACjC,aAAa,MAAM,EAAE,CAAC,WAAW;gBACjC,OAAO,MAAM,EAAE,CAAC,KAAK;YACvB;QACF,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/SearchInput.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect, useRef } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { useRouter } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface SearchInputProps {\r\n  placeholder?: string\r\n  onSearch?: (query: string) => void\r\n  onClear?: () => void\r\n  showHistory?: boolean\r\n  showSuggestions?: boolean\r\n  size?: 'sm' | 'md' | 'lg'\r\n  className?: string\r\n}\r\n\r\nexport const SearchInput = ({\r\n  placeholder = '搜索提示词...',\r\n  onSearch,\r\n  onClear,\r\n  showHistory = true,\r\n  showSuggestions = true,\r\n  size = 'md',\r\n  className = '',\r\n}: SearchInputProps) => {\r\n  const router = useRouter()\r\n  const [query, setQuery] = useState('')\r\n  const [isOpen, setIsOpen] = useState(false)\r\n  const [selectedIndex, setSelectedIndex] = useState(-1)\r\n  const [recentSearches, setRecentSearches] = useState<string[]>([])\r\n  const inputRef = useRef<HTMLInputElement>(null)\r\n  const containerRef = useRef<HTMLDivElement>(null)\r\n\r\n  // 获取搜索建议\r\n  const { data: suggestions, isLoading: suggestionsLoading } = api.search.getSuggestions.useQuery({\r\n    query: query.trim(),\r\n    limit: 5,\r\n  }, {\r\n    enabled: showSuggestions && query.trim().length > 0,\r\n  })\r\n\r\n  // 获取搜索历史\r\n  const { data: history } = api.search.getHistory.useQuery({\r\n    limit: 10,\r\n  }, {\r\n    enabled: showHistory,\r\n  })\r\n\r\n  // 保存搜索历史\r\n  const saveSearchMutation = api.search.saveToHistory.useMutation()\r\n\r\n  // 从本地存储加载最近搜索\r\n  useEffect(() => {\r\n    const saved = localStorage.getItem('recent-searches')\r\n    if (saved) {\r\n      try {\r\n        setRecentSearches(JSON.parse(saved))\r\n      } catch (error) {\r\n        console.error('Failed to parse recent searches:', error)\r\n      }\r\n    }\r\n  }, [])\r\n\r\n  // 保存到本地存储\r\n  const saveToLocal = (searches: string[]) => {\r\n    try {\r\n      localStorage.setItem('recent-searches', JSON.stringify(searches))\r\n      setRecentSearches(searches)\r\n    } catch (error) {\r\n      console.error('Failed to save recent searches:', error)\r\n    }\r\n  }\r\n\r\n  // 处理搜索\r\n  const handleSearch = async (searchQuery: string) => {\r\n    const trimmedQuery = searchQuery.trim()\r\n    if (!trimmedQuery) return\r\n\r\n    try {\r\n      // 保存到搜索历史\r\n      await saveSearchMutation.mutateAsync({ query: trimmedQuery })\r\n      \r\n      // 更新本地历史记录\r\n      const newSearches = [trimmedQuery, ...recentSearches.filter(s => s !== trimmedQuery)].slice(0, 10)\r\n      saveToLocal(newSearches)\r\n\r\n      // 执行搜索\r\n      if (onSearch) {\r\n        onSearch(trimmedQuery)\r\n      } else {\r\n        router.push(`/search?q=${encodeURIComponent(trimmedQuery)}`)\r\n      }\r\n\r\n      setIsOpen(false)\r\n    } catch (error) {\r\n      toast.error('搜索失败，请稍后重试')\r\n    }\r\n  }\r\n\r\n  // 处理输入变化\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value\r\n    setQuery(value)\r\n    setSelectedIndex(-1)\r\n    setIsOpen(value.length > 0 || showHistory)\r\n  }\r\n\r\n  // 处理键盘事件\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    const items = [\r\n      ...(suggestions || []).map(s => s.query),\r\n      ...(showHistory ? (history || []).map(h => h.query) : []),\r\n      ...recentSearches,\r\n    ].filter((item, index, arr) => arr.indexOf(item) === index)\r\n\r\n    switch (e.key) {\r\n      case 'ArrowDown':\r\n        e.preventDefault()\r\n        setSelectedIndex(prev => (prev + 1) % items.length)\r\n        break\r\n      case 'ArrowUp':\r\n        e.preventDefault()\r\n        setSelectedIndex(prev => (prev - 1 + items.length) % items.length)\r\n        break\r\n      case 'Enter':\r\n        e.preventDefault()\r\n        if (selectedIndex >= 0) {\r\n          const selectedItem = items[selectedIndex]\r\n          setQuery(selectedItem)\r\n          handleSearch(selectedItem)\r\n        } else {\r\n          handleSearch(query)\r\n        }\r\n        break\r\n      case 'Escape':\r\n        setIsOpen(false)\r\n        setSelectedIndex(-1)\r\n        inputRef.current?.blur()\r\n        break\r\n    }\r\n  }\r\n\r\n  // 处理点击外部关闭\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\r\n        setIsOpen(false)\r\n        setSelectedIndex(-1)\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside)\r\n    return () => document.removeEventListener('mousedown', handleClickOutside)\r\n  }, [])\r\n\r\n  // 清空搜索\r\n  const handleClear = () => {\r\n    setQuery('')\r\n    setIsOpen(false)\r\n    setSelectedIndex(-1)\r\n    if (onClear) {\r\n      onClear()\r\n    }\r\n    inputRef.current?.focus()\r\n  }\r\n\r\n  // 删除历史记录项\r\n  const removeFromHistory = (item: string) => {\r\n    const newSearches = recentSearches.filter(s => s !== item)\r\n    saveToLocal(newSearches)\r\n  }\r\n\r\n  // 清空所有历史记录\r\n  const clearAllHistory = () => {\r\n    saveToLocal([])\r\n    toast.success('历史记录已清空')\r\n  }\r\n\r\n  // 获取输入框尺寸样式\r\n  const getSizeClass = () => {\r\n    switch (size) {\r\n      case 'sm': return 'input-sm'\r\n      case 'lg': return 'input-lg'\r\n      default: return 'input-md'\r\n    }\r\n  }\r\n\r\n  // 合并和去重搜索项\r\n  const getSearchItems = () => {\r\n    const items = []\r\n    \r\n    // 添加搜索建议\r\n    if (suggestions && query.trim()) {\r\n      items.push({\r\n        type: 'suggestion',\r\n        title: '搜索建议',\r\n        items: suggestions.map(s => ({\r\n          text: s.query,\r\n          count: s.count,\r\n          type: 'suggestion' as const,\r\n        }))\r\n      })\r\n    }\r\n\r\n    // 添加搜索历史\r\n    if (showHistory && history && history.length > 0) {\r\n      items.push({\r\n        type: 'history',\r\n        title: '搜索历史',\r\n        items: history.map(h => ({\r\n          text: h.query,\r\n          count: h.count,\r\n          type: 'history' as const,\r\n        }))\r\n      })\r\n    }\r\n\r\n    // 添加本地历史\r\n    if (recentSearches.length > 0 && !query.trim()) {\r\n      items.push({\r\n        type: 'recent',\r\n        title: '最近搜索',\r\n        items: recentSearches.map(r => ({\r\n          text: r,\r\n          type: 'recent' as const,\r\n        }))\r\n      })\r\n    }\r\n\r\n    return items\r\n  }\r\n\r\n  return (\r\n    <div ref={containerRef} className={`relative ${className}`}>\r\n      <div className=\"form-control\">\r\n        <div className=\"input-group\">\r\n          <input\r\n            ref={inputRef}\r\n            type=\"text\"\r\n            value={query}\r\n            onChange={handleInputChange}\r\n            onKeyDown={handleKeyDown}\r\n            onFocus={() => setIsOpen(true)}\r\n            placeholder={placeholder}\r\n            className={`input input-bordered ${getSizeClass()} w-full pr-20`}\r\n          />\r\n          \r\n          {/* 搜索和清空按钮 */}\r\n          <div className=\"flex items-center absolute right-2 top-1/2 transform -translate-y-1/2 space-x-1\">\r\n            {query && (\r\n              <motion.button\r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                exit={{ opacity: 0, scale: 0.8 }}\r\n                onClick={handleClear}\r\n                className=\"btn btn-ghost btn-sm btn-square\"\r\n                title=\"清空\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                </svg>\r\n              </motion.button>\r\n            )}\r\n            \r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              onClick={() => handleSearch(query)}\r\n              className=\"btn btn-primary btn-sm btn-square\"\r\n              title=\"搜索\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                />\r\n              </svg>\r\n            </motion.button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 搜索下拉菜单 */}\r\n      <AnimatePresence>\r\n        {isOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            className=\"absolute top-full left-0 right-0 z-50 mt-2 bg-base-100 rounded-lg shadow-lg border border-base-200 max-h-96 overflow-y-auto\"\r\n          >\r\n            {suggestionsLoading ? (\r\n              <div className=\"p-4 text-center\">\r\n                <div className=\"loading loading-spinner loading-sm\"></div>\r\n                <span className=\"ml-2 text-sm text-base-content/70\">搜索中...</span>\r\n              </div>\r\n            ) : (\r\n              <div className=\"py-2\">\r\n                {getSearchItems().map((section, sectionIndex) => (\r\n                  <div key={section.type} className={sectionIndex > 0 ? 'border-t border-base-200' : ''}>\r\n                    {/* 分组标题 */}\r\n                    <div className=\"px-4 py-2 flex items-center justify-between\">\r\n                      <h3 className=\"text-sm font-medium text-base-content/70\">{section.title}</h3>\r\n                      {section.type === 'recent' && recentSearches.length > 0 && (\r\n                        <button\r\n                          onClick={clearAllHistory}\r\n                          className=\"text-xs text-base-content/50 hover:text-base-content/70\"\r\n                        >\r\n                          清空\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* 搜索项 */}\r\n                    {section.items.map((item, itemIndex) => {\r\n                      const globalIndex = getSearchItems()\r\n                        .slice(0, sectionIndex)\r\n                        .reduce((acc, s) => acc + s.items.length, 0) + itemIndex\r\n                      \r\n                      return (\r\n                        <motion.div\r\n                          key={`${section.type}-${item.text}`}\r\n                          whileHover={{ backgroundColor: 'hsl(var(--b2))' }}\r\n                          className={`px-4 py-2 cursor-pointer flex items-center justify-between ${\r\n                            selectedIndex === globalIndex ? 'bg-base-200' : ''\r\n                          }`}\r\n                          onClick={() => {\r\n                            setQuery(item.text)\r\n                            handleSearch(item.text)\r\n                          }}\r\n                        >\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            {/* 图标 */}\r\n                            {item.type === 'suggestion' && (\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                strokeWidth={1.5}\r\n                                stroke=\"currentColor\"\r\n                                className=\"w-4 h-4 text-base-content/50\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n                            \r\n                            {item.type === 'history' && (\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                strokeWidth={1.5}\r\n                                stroke=\"currentColor\"\r\n                                className=\"w-4 h-4 text-base-content/50\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n                            \r\n                            {item.type === 'recent' && (\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                strokeWidth={1.5}\r\n                                stroke=\"currentColor\"\r\n                                className=\"w-4 h-4 text-base-content/50\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                                />\r\n                              </svg>\r\n                            )}\r\n\r\n                            <span className=\"text-sm text-base-content\">{item.text}</span>\r\n                          </div>\r\n\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            {/* 搜索次数 */}\r\n                            {item.count && (\r\n                              <span className=\"text-xs text-base-content/50\">\r\n                                {item.count}次\r\n                              </span>\r\n                            )}\r\n\r\n                            {/* 删除按钮 */}\r\n                            {item.type === 'recent' && (\r\n                              <button\r\n                                onClick={(e) => {\r\n                                  e.stopPropagation()\r\n                                  removeFromHistory(item.text)\r\n                                }}\r\n                                className=\"p-1 hover:bg-base-300 rounded\"\r\n                              >\r\n                                <svg\r\n                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                  fill=\"none\"\r\n                                  viewBox=\"0 0 24 24\"\r\n                                  strokeWidth={1.5}\r\n                                  stroke=\"currentColor\"\r\n                                  className=\"w-3 h-3\"\r\n                                >\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                                </svg>\r\n                              </button>\r\n                            )}\r\n                          </div>\r\n                        </motion.div>\r\n                      )\r\n                    })}\r\n                  </div>\r\n                ))}\r\n\r\n                {/* 空状态 */}\r\n                {getSearchItems().length === 0 && (\r\n                  <div className=\"px-4 py-8 text-center text-base-content/50\">\r\n                    <p className=\"text-sm\">暂无搜索记录</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAkBO,MAAM,cAAc,CAAC,EAC1B,cAAc,UAAU,EACxB,QAAQ,EACR,OAAO,EACP,cAAc,IAAI,EAClB,kBAAkB,IAAI,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACG;IACjB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,SAAS;IACT,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,kBAAkB,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;QAC9F,OAAO,MAAM,IAAI;QACjB,OAAO;IACT,GAAG;QACD,SAAS,mBAAmB,MAAM,IAAI,GAAG,MAAM,GAAG;IACpD;IAEA,SAAS;IACT,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QACvD,OAAO;IACT,GAAG;QACD,SAAS;IACX;IAEA,SAAS;IACT,MAAM,qBAAqB,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW;IAE/D,cAAc;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,IAAI;gBACF,kBAAkB,KAAK,KAAK,CAAC;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;IACF,GAAG,EAAE;IAEL,UAAU;IACV,MAAM,cAAc,CAAC;QACnB,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,MAAM,eAAe,YAAY,IAAI;QACrC,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,UAAU;YACV,MAAM,mBAAmB,WAAW,CAAC;gBAAE,OAAO;YAAa;YAE3D,WAAW;YACX,MAAM,cAAc;gBAAC;mBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;aAAc,CAAC,KAAK,CAAC,GAAG;YAC/F,YAAY;YAEZ,OAAO;YACP,IAAI,UAAU;gBACZ,SAAS;YACX,OAAO;gBACL,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,eAAe;YAC7D;YAEA,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,iBAAiB,CAAC;QAClB,UAAU,MAAM,MAAM,GAAG,KAAK;IAChC;IAEA,SAAS;IACT,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ;eACT,CAAC,eAAe,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;eACnC,cAAc,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,EAAE;eACrD;SACJ,CAAC,MAAM,CAAC,CAAC,MAAM,OAAO,MAAQ,IAAI,OAAO,CAAC,UAAU;QAErD,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;gBAClD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,CAAC,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM;gBACjE;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,GAAG;oBACtB,MAAM,eAAe,KAAK,CAAC,cAAc;oBACzC,SAAS;oBACT,aAAa;gBACf,OAAO;oBACL,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAChF,UAAU;gBACV,iBAAiB,CAAC;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;QACV,iBAAiB,CAAC;QAClB,IAAI,SAAS;YACX;QACF;QACA,SAAS,OAAO,EAAE;IACpB;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;QACrD,YAAY;IACd;IAEA,WAAW;IACX,MAAM,kBAAkB;QACtB,YAAY,EAAE;QACd,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,YAAY;IACZ,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAClB;IACF;IAEA,WAAW;IACX,MAAM,iBAAiB;QACrB,MAAM,QAAQ,EAAE;QAEhB,SAAS;QACT,IAAI,eAAe,MAAM,IAAI,IAAI;YAC/B,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO;gBACP,OAAO,YAAY,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC3B,MAAM,EAAE,KAAK;wBACb,OAAO,EAAE,KAAK;wBACd,MAAM;oBACR,CAAC;YACH;QACF;QAEA,SAAS;QACT,IAAI,eAAe,WAAW,QAAQ,MAAM,GAAG,GAAG;YAChD,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO;gBACP,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,CAAC;wBACvB,MAAM,EAAE,KAAK;wBACb,OAAO,EAAE,KAAK;wBACd,MAAM;oBACR,CAAC;YACH;QACF;QAEA,SAAS;QACT,IAAI,eAAe,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,IAAI;YAC9C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO;gBACP,OAAO,eAAe,GAAG,CAAC,CAAA,IAAK,CAAC;wBAC9B,MAAM;wBACN,MAAM;oBACR,CAAC;YACH;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,SAAS,IAAM,UAAU;4BACzB,aAAa;4BACb,WAAW,CAAC,qBAAqB,EAAE,eAAe,aAAa,CAAC;;;;;;sCAIlE,8OAAC;4BAAI,WAAU;;gCACZ,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;;;;;;8CAK3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;8BAET,mCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;iFAGtD,8OAAC;wBAAI,WAAU;;4BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;oCAAuB,WAAW,eAAe,IAAI,6BAA6B;;sDAEjF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4C,QAAQ,KAAK;;;;;;gDACtE,QAAQ,IAAI,KAAK,YAAY,eAAe,MAAM,GAAG,mBACpD,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;wCAOJ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;4CACxB,MAAM,cAAc,iBACjB,KAAK,CAAC,GAAG,cACT,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK;4CAEjD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,YAAY;oDAAE,iBAAiB;gDAAiB;gDAChD,WAAW,CAAC,2DAA2D,EACrE,kBAAkB,cAAc,gBAAgB,IAChD;gDACF,SAAS;oDACP,SAAS,KAAK,IAAI;oDAClB,aAAa,KAAK,IAAI;gDACxB;;kEAEA,8OAAC;wDAAI,WAAU;;4DAEZ,KAAK,IAAI,KAAK,8BACb,8OAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;4DAKP,KAAK,IAAI,KAAK,2BACb,8OAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;4DAKP,KAAK,IAAI,KAAK,0BACb,8OAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;0EAKR,8OAAC;gEAAK,WAAU;0EAA6B,KAAK,IAAI;;;;;;;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;;4DAEZ,KAAK,KAAK,kBACT,8OAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK;oEAAC;;;;;;;4DAKf,KAAK,IAAI,KAAK,0BACb,8OAAC;gEACC,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,kBAAkB,KAAK,IAAI;gEAC7B;gEACA,WAAU;0EAEV,cAAA,8OAAC;oEACC,OAAM;oEACN,MAAK;oEACL,SAAQ;oEACR,aAAa;oEACb,QAAO;oEACP,WAAU;8EAEV,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;;+CA3FxD,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;;;;;wCAkGzC;;mCAxHQ,QAAQ,IAAI;;;;;4BA6HvB,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C", "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/SearchHighlight.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useMemo } from 'react'\r\n\r\ninterface SearchHighlightProps {\r\n  text: string\r\n  query: string\r\n  className?: string\r\n  highlightClassName?: string\r\n}\r\n\r\nexport const SearchHighlight = ({\r\n  text,\r\n  query,\r\n  className = '',\r\n  highlightClassName = 'bg-yellow-200 text-yellow-800 px-1 rounded',\r\n}: SearchHighlightProps) => {\r\n  // 创建高亮文本\r\n  const highlightedText = useMemo(() => {\r\n    if (!query || !text) return text\r\n\r\n    // 清理查询字符串\r\n    const cleanQuery = query.trim()\r\n    if (!cleanQuery) return text\r\n\r\n    // 创建正则表达式，忽略大小写\r\n    const regex = new RegExp(`(${cleanQuery.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi')\r\n    \r\n    // 分割文本\r\n    const parts = text.split(regex)\r\n    \r\n    return parts.map((part, index) => {\r\n      // 检查是否匹配查询\r\n      const isMatch = regex.test(part)\r\n      regex.lastIndex = 0 // 重置正则表达式\r\n      \r\n      if (isMatch) {\r\n        return (\r\n          <mark key={index} className={highlightClassName}>\r\n            {part}\r\n          </mark>\r\n        )\r\n      }\r\n      return part\r\n    })\r\n  }, [text, query, highlightClassName])\r\n\r\n  return (\r\n    <span className={className}>\r\n      {highlightedText}\r\n    </span>\r\n  )\r\n}\r\n\r\n// 高亮多个关键词的组件\r\ninterface MultiSearchHighlightProps {\r\n  text: string\r\n  queries: string[]\r\n  className?: string\r\n  highlightClassName?: string\r\n}\r\n\r\nexport const MultiSearchHighlight = ({\r\n  text,\r\n  queries,\r\n  className = '',\r\n  highlightClassName = 'bg-yellow-200 text-yellow-800 px-1 rounded',\r\n}: MultiSearchHighlightProps) => {\r\n  // 创建多关键词高亮\r\n  const highlightedText = useMemo(() => {\r\n    if (!queries.length || !text) return text\r\n\r\n    // 过滤和清理查询字符串\r\n    const cleanQueries = queries\r\n      .map(q => q.trim())\r\n      .filter(q => q.length > 0)\r\n\r\n    if (!cleanQueries.length) return text\r\n\r\n    // 创建组合正则表达式\r\n    const escapedQueries = cleanQueries.map(q => q.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'))\r\n    const regex = new RegExp(`(${escapedQueries.join('|')})`, 'gi')\r\n    \r\n    // 分割文本\r\n    const parts = text.split(regex)\r\n    \r\n    return parts.map((part, index) => {\r\n      // 检查是否匹配任何查询\r\n      const isMatch = cleanQueries.some(q => \r\n        part.toLowerCase() === q.toLowerCase()\r\n      )\r\n      \r\n      if (isMatch) {\r\n        return (\r\n          <mark key={index} className={highlightClassName}>\r\n            {part}\r\n          </mark>\r\n        )\r\n      }\r\n      return part\r\n    })\r\n  }, [text, queries, highlightClassName])\r\n\r\n  return (\r\n    <span className={className}>\r\n      {highlightedText}\r\n    </span>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWO,MAAM,kBAAkB,CAAC,EAC9B,IAAI,EACJ,KAAK,EACL,YAAY,EAAE,EACd,qBAAqB,4CAA4C,EAC5C;IACrB,SAAS;IACT,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO;QAE5B,UAAU;QACV,MAAM,aAAa,MAAM,IAAI;QAC7B,IAAI,CAAC,YAAY,OAAO;QAExB,gBAAgB;QAChB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;QAEnF,OAAO;QACP,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;YACtB,WAAW;YACX,MAAM,UAAU,MAAM,IAAI,CAAC;YAC3B,MAAM,SAAS,GAAG,GAAE,UAAU;YAE9B,IAAI,SAAS;gBACX,qBACE,8OAAC;oBAAiB,WAAW;8BAC1B;mBADQ;;;;;YAIf;YACA,OAAO;QACT;IACF,GAAG;QAAC;QAAM;QAAO;KAAmB;IAEpC,qBACE,8OAAC;QAAK,WAAW;kBACd;;;;;;AAGP;AAUO,MAAM,uBAAuB,CAAC,EACnC,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,qBAAqB,4CAA4C,EACvC;IAC1B,WAAW;IACX,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,MAAM,OAAO;QAErC,aAAa;QACb,MAAM,eAAe,QAClB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IACf,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,GAAG;QAE1B,IAAI,CAAC,aAAa,MAAM,EAAE,OAAO;QAEjC,YAAY;QACZ,MAAM,iBAAiB,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,uBAAuB;QAC9E,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAE1D,OAAO;QACP,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;YACtB,aAAa;YACb,MAAM,UAAU,aAAa,IAAI,CAAC,CAAA,IAChC,KAAK,WAAW,OAAO,EAAE,WAAW;YAGtC,IAAI,SAAS;gBACX,qBACE,8OAAC;oBAAiB,WAAW;8BAC1B;mBADQ;;;;;YAIf;YACA,OAAO;QACT;IACF,GAAG;QAAC;QAAM;QAAS;KAAmB;IAEtC,qBACE,8OAAC;QAAK,WAAW;kBACd;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2503, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useAutoSave.ts"], "sourcesContent": ["import { useState, useEffect, useRef, useCallback } from 'react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface AutoSaveOptions {\r\n  delay?: number\r\n  enabled?: boolean\r\n  onSave?: (data: any) => Promise<void>\r\n  onError?: (error: Error) => void\r\n  storageKey?: string\r\n}\r\n\r\ninterface AutoSaveResult {\r\n  isSaving: boolean\r\n  lastSaved: Date | null\r\n  hasUnsavedChanges: boolean\r\n  save: () => Promise<void>\r\n  clearDraft: () => void\r\n  restoreDraft: () => any\r\n}\r\n\r\nexport function useAutoSave<T>(\r\n  data: T,\r\n  options: AutoSaveOptions = {}\r\n): AutoSaveResult {\r\n  const {\r\n    delay = 2000,\r\n    enabled = true,\r\n    onSave,\r\n    onError,\r\n    storageKey = 'draft',\r\n  } = options\r\n\r\n  const [isSaving, setIsSaving] = useState(false)\r\n  const [lastSaved, setLastSaved] = useState<Date | null>(null)\r\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)\r\n  const [initialData, setInitialData] = useState<T>(data)\r\n  \r\n  const timeoutRef = useRef<NodeJS.Timeout>()\r\n  const lastDataRef = useRef<T>(data)\r\n\r\n  // 保存到本地存储\r\n  const saveDraftToLocal = useCallback((draftData: T) => {\r\n    try {\r\n      localStorage.setItem(storageKey, JSON.stringify({\r\n        data: draftData,\r\n        timestamp: Date.now(),\r\n      }))\r\n    } catch (error) {\r\n      console.error('保存草稿到本地存储失败:', error)\r\n    }\r\n  }, [storageKey])\r\n\r\n  // 从本地存储恢复\r\n  const restoreDraft = useCallback(() => {\r\n    try {\r\n      const saved = localStorage.getItem(storageKey)\r\n      if (saved) {\r\n        const parsed = JSON.parse(saved)\r\n        return parsed.data\r\n      }\r\n    } catch (error) {\r\n      console.error('恢复草稿失败:', error)\r\n    }\r\n    return null\r\n  }, [storageKey])\r\n\r\n  // 清除草稿\r\n  const clearDraft = useCallback(() => {\r\n    try {\r\n      localStorage.removeItem(storageKey)\r\n      setHasUnsavedChanges(false)\r\n    } catch (error) {\r\n      console.error('清除草稿失败:', error)\r\n    }\r\n  }, [storageKey])\r\n\r\n  // 手动保存\r\n  const save = useCallback(async () => {\r\n    if (!onSave || isSaving) return\r\n\r\n    try {\r\n      setIsSaving(true)\r\n      await onSave(data)\r\n      setLastSaved(new Date())\r\n      setHasUnsavedChanges(false)\r\n      setInitialData(data)\r\n      clearDraft()\r\n      toast.success('保存成功')\r\n    } catch (error) {\r\n      const err = error instanceof Error ? error : new Error('保存失败')\r\n      if (onError) {\r\n        onError(err)\r\n      } else {\r\n        toast.error(err.message)\r\n      }\r\n    } finally {\r\n      setIsSaving(false)\r\n    }\r\n  }, [data, onSave, isSaving, onError, clearDraft])\r\n\r\n  // 自动保存逻辑\r\n  useEffect(() => {\r\n    if (!enabled || !onSave) return\r\n\r\n    // 检查数据是否发生变化\r\n    const hasChanged = JSON.stringify(data) !== JSON.stringify(lastDataRef.current)\r\n    \r\n    if (hasChanged) {\r\n      lastDataRef.current = data\r\n      setHasUnsavedChanges(true)\r\n      \r\n      // 保存到本地存储\r\n      saveDraftToLocal(data)\r\n      \r\n      // 清除之前的定时器\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current)\r\n      }\r\n      \r\n      // 设置新的定时器\r\n      timeoutRef.current = setTimeout(async () => {\r\n        if (isSaving) return\r\n        \r\n        try {\r\n          setIsSaving(true)\r\n          await onSave(data)\r\n          setLastSaved(new Date())\r\n          setHasUnsavedChanges(false)\r\n          setInitialData(data)\r\n          clearDraft()\r\n          toast.success('自动保存成功', { duration: 2000 })\r\n        } catch (error) {\r\n          const err = error instanceof Error ? error : new Error('自动保存失败')\r\n          if (onError) {\r\n            onError(err)\r\n          } else {\r\n            toast.error(err.message)\r\n          }\r\n        } finally {\r\n          setIsSaving(false)\r\n        }\r\n      }, delay)\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutRef.current) {\r\n        clearTimeout(timeoutRef.current)\r\n      }\r\n    }\r\n  }, [data, enabled, delay, onSave, onError, isSaving, saveDraftToLocal, clearDraft])\r\n\r\n  // 检查是否有未保存的更改\r\n  useEffect(() => {\r\n    const hasChanges = JSON.stringify(data) !== JSON.stringify(initialData)\r\n    setHasUnsavedChanges(hasChanges)\r\n  }, [data, initialData])\r\n\r\n  // 页面卸载时保存草稿\r\n  useEffect(() => {\r\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\r\n      if (hasUnsavedChanges) {\r\n        e.preventDefault()\r\n        e.returnValue = '有未保存的更改，确定要离开吗？'\r\n      }\r\n    }\r\n\r\n    window.addEventListener('beforeunload', handleBeforeUnload)\r\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload)\r\n  }, [hasUnsavedChanges])\r\n\r\n  return {\r\n    isSaving,\r\n    lastSaved,\r\n    hasUnsavedChanges,\r\n    save,\r\n    clearDraft,\r\n    restoreDraft,\r\n  }\r\n}\r\n\r\n// 专门用于提示词的自动保存 hook\r\nexport function usePromptAutoSave(\r\n  promptData: any,\r\n  options: {\r\n    enabled?: boolean\r\n    onSave?: (data: any) => Promise<void>\r\n    promptId?: string\r\n  } = {}\r\n) {\r\n  const { enabled = true, onSave, promptId } = options\r\n  \r\n  const storageKey = promptId ? `prompt-draft-${promptId}` : 'prompt-draft-new'\r\n  \r\n  return useAutoSave(promptData, {\r\n    enabled,\r\n    onSave,\r\n    storageKey,\r\n    delay: 3000, // 提示词自动保存延迟稍长\r\n  })\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAmBO,SAAS,YACd,IAAO,EACP,UAA2B,CAAC,CAAC;IAE7B,MAAM,EACJ,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,MAAM,EACN,OAAO,EACP,aAAa,OAAO,EACrB,GAAG;IAEJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAElD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAK;IAE9B,UAAU;IACV,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI;YACF,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAC9C,MAAM;gBACN,WAAW,KAAK,GAAG;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC;IACF,GAAG;QAAC;KAAW;IAEf,UAAU;IACV,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,OAAO,OAAO,IAAI;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;QACA,OAAO;IACT,GAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;IACP,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvB,IAAI,CAAC,UAAU,UAAU;QAEzB,IAAI;YACF,YAAY;YACZ,MAAM,OAAO;YACb,aAAa,IAAI;YACjB,qBAAqB;YACrB,eAAe;YACf;YACA,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,MAAM,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;YACvD,IAAI,SAAS;gBACX,QAAQ;YACV,OAAO;gBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;YACzB;QACF,SAAU;YACR,YAAY;QACd;IACF,GAAG;QAAC;QAAM;QAAQ;QAAU;QAAS;KAAW;IAEhD,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,QAAQ;QAEzB,aAAa;QACb,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,OAAO;QAE9E,IAAI,YAAY;YACd,YAAY,OAAO,GAAG;YACtB,qBAAqB;YAErB,UAAU;YACV,iBAAiB;YAEjB,WAAW;YACX,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;YAEA,UAAU;YACV,WAAW,OAAO,GAAG,WAAW;gBAC9B,IAAI,UAAU;gBAEd,IAAI;oBACF,YAAY;oBACZ,MAAM,OAAO;oBACb,aAAa,IAAI;oBACjB,qBAAqB;oBACrB,eAAe;oBACf;oBACA,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,UAAU;wBAAE,UAAU;oBAAK;gBAC3C,EAAE,OAAO,OAAO;oBACd,MAAM,MAAM,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;oBACvD,IAAI,SAAS;wBACX,QAAQ;oBACV,OAAO;wBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;oBACzB;gBACF,SAAU;oBACR,YAAY;gBACd;YACF,GAAG;QACL;QAEA,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;QACF;IACF,GAAG;QAAC;QAAM;QAAS;QAAO;QAAQ;QAAS;QAAU;QAAkB;KAAW;IAElF,cAAc;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC;QAC3D,qBAAqB;IACvB,GAAG;QAAC;QAAM;KAAY;IAEtB,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,mBAAmB;gBACrB,EAAE,cAAc;gBAChB,EAAE,WAAW,GAAG;YAClB;QACF;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QACxC,OAAO,IAAM,OAAO,mBAAmB,CAAC,gBAAgB;IAC1D,GAAG;QAAC;KAAkB;IAEtB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,kBACd,UAAe,EACf,UAII,CAAC,CAAC;IAEN,MAAM,EAAE,UAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;IAE7C,MAAM,aAAa,WAAW,CAAC,aAAa,EAAE,UAAU,GAAG;IAE3D,OAAO,YAAY,YAAY;QAC7B;QACA;QACA;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2684, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/useCopyToClipboard.ts"], "sourcesContent": ["import { useState, useCallback } from 'react'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CopyOptions {\r\n  successMessage?: string\r\n  errorMessage?: string\r\n  showToast?: boolean\r\n  timeout?: number\r\n}\r\n\r\ninterface CopyResult {\r\n  isCopied: boolean\r\n  isLoading: boolean\r\n  copy: (text: string, options?: CopyOptions) => Promise<boolean>\r\n  reset: () => void\r\n}\r\n\r\nexport function useCopyToClipboard(): CopyResult {\r\n  const [isCopied, setIsCopied] = useState(false)\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  const copy = useCallback(async (text: string, options: CopyOptions = {}): Promise<boolean> => {\r\n    const {\r\n      successMessage = '已复制到剪贴板',\r\n      errorMessage = '复制失败',\r\n      showToast = true,\r\n      timeout = 2000,\r\n    } = options\r\n\r\n    if (!text || typeof text !== 'string') {\r\n      if (showToast) {\r\n        toast.error('没有内容可复制')\r\n      }\r\n      return false\r\n    }\r\n\r\n    setIsLoading(true)\r\n\r\n    try {\r\n      // 尝试使用现代 API\r\n      if (navigator.clipboard && window.isSecureContext) {\r\n        await navigator.clipboard.writeText(text)\r\n      } else {\r\n        // 降级到旧方法\r\n        const textArea = document.createElement('textarea')\r\n        textArea.value = text\r\n        textArea.style.position = 'fixed'\r\n        textArea.style.left = '-999999px'\r\n        textArea.style.top = '-999999px'\r\n        document.body.appendChild(textArea)\r\n        textArea.focus()\r\n        textArea.select()\r\n        \r\n        const successful = document.execCommand('copy')\r\n        document.body.removeChild(textArea)\r\n        \r\n        if (!successful) {\r\n          throw new Error('复制命令失败')\r\n        }\r\n      }\r\n\r\n      setIsCopied(true)\r\n      \r\n      if (showToast) {\r\n        toast.success(successMessage)\r\n      }\r\n\r\n      // 重置状态\r\n      setTimeout(() => {\r\n        setIsCopied(false)\r\n      }, timeout)\r\n\r\n      return true\r\n    } catch (error) {\r\n      console.error('复制失败:', error)\r\n      \r\n      if (showToast) {\r\n        toast.error(errorMessage)\r\n      }\r\n      \r\n      return false\r\n    } finally {\r\n      setIsLoading(false)\r\n    }\r\n  }, [])\r\n\r\n  const reset = useCallback(() => {\r\n    setIsCopied(false)\r\n    setIsLoading(false)\r\n  }, [])\r\n\r\n  return {\r\n    isCopied,\r\n    isLoading,\r\n    copy,\r\n    reset,\r\n  }\r\n}\r\n\r\n// 专门用于复制提示词的 hook\r\nexport function usePromptCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyPrompt = useCallback(async (prompt: { content: string; title: string }) => {\r\n    const success = await copy(prompt.content, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyPromptWithTitle = useCallback(async (prompt: { content: string; title: string }) => {\r\n    const textToCopy = `# ${prompt.title}\\n\\n${prompt.content}`\r\n    const success = await copy(textToCopy, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"（含标题）`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyPromptAsMarkdown = useCallback(async (prompt: { \r\n    content: string; \r\n    title: string; \r\n    description?: string;\r\n    tags?: { name: string }[];\r\n    category?: { name: string };\r\n  }) => {\r\n    let markdown = `# ${prompt.title}\\n\\n`\r\n    \r\n    if (prompt.description) {\r\n      markdown += `${prompt.description}\\n\\n`\r\n    }\r\n    \r\n    if (prompt.category) {\r\n      markdown += `**分类**: ${prompt.category.name}\\n\\n`\r\n    }\r\n    \r\n    if (prompt.tags && prompt.tags.length > 0) {\r\n      markdown += `**标签**: ${prompt.tags.map(t => t.name).join(', ')}\\n\\n`\r\n    }\r\n    \r\n    markdown += `## 内容\\n\\n\\`\\`\\`\\n${prompt.content}\\n\\`\\`\\``\r\n    \r\n    const success = await copy(markdown, {\r\n      successMessage: `已复制提示词 \"${prompt.title}\"（Markdown 格式）`,\r\n      errorMessage: '复制提示词失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyPrompt,\r\n    copyPromptWithTitle,\r\n    copyPromptAsMarkdown,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}\r\n\r\n// 用于复制代码块的 hook\r\nexport function useCodeCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyCode = useCallback(async (code: string, language?: string) => {\r\n    const success = await copy(code, {\r\n      successMessage: `已复制${language ? ` ${language}` : ''} 代码`,\r\n      errorMessage: '复制代码失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyCode,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}\r\n\r\n// 用于复制链接的 hook\r\nexport function useLinkCopy() {\r\n  const { copy, isCopied, isLoading } = useCopyToClipboard()\r\n\r\n  const copyLink = useCallback(async (url: string, title?: string) => {\r\n    const success = await copy(url, {\r\n      successMessage: title ? `已复制 \"${title}\" 链接` : '已复制链接',\r\n      errorMessage: '复制链接失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  const copyCurrentUrl = useCallback(async () => {\r\n    const success = await copy(window.location.href, {\r\n      successMessage: '已复制当前页面链接',\r\n      errorMessage: '复制链接失败',\r\n    })\r\n    \r\n    return success\r\n  }, [copy])\r\n\r\n  return {\r\n    copyLink,\r\n    copyCurrentUrl,\r\n    isCopied,\r\n    isLoading,\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAgBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAc,UAAuB,CAAC,CAAC;QACrE,MAAM,EACJ,iBAAiB,SAAS,EAC1B,eAAe,MAAM,EACrB,YAAY,IAAI,EAChB,UAAU,IAAI,EACf,GAAG;QAEJ,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,IAAI,WAAW;gBACb,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YACA,OAAO;QACT;QAEA,aAAa;QAEb,IAAI;YACF,aAAa;YACb,IAAI,UAAU,SAAS,IAAI,OAAO,eAAe,EAAE;gBACjD,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACtC,OAAO;gBACL,SAAS;gBACT,MAAM,WAAW,SAAS,aAAa,CAAC;gBACxC,SAAS,KAAK,GAAG;gBACjB,SAAS,KAAK,CAAC,QAAQ,GAAG;gBAC1B,SAAS,KAAK,CAAC,IAAI,GAAG;gBACtB,SAAS,KAAK,CAAC,GAAG,GAAG;gBACrB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,SAAS,KAAK;gBACd,SAAS,MAAM;gBAEf,MAAM,aAAa,SAAS,WAAW,CAAC;gBACxC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,IAAI,CAAC,YAAY;oBACf,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,YAAY;YAEZ,IAAI,WAAW;gBACb,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,OAAO;YACP,WAAW;gBACT,YAAY;YACd,GAAG;YAEH,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YAEvB,IAAI,WAAW;gBACb,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,YAAY;QACZ,aAAa;IACf,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,MAAM,UAAU,MAAM,KAAK,OAAO,OAAO,EAAE;YACzC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC;YAC1C,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,MAAM,aAAa,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE;QAC3D,MAAM,UAAU,MAAM,KAAK,YAAY;YACrC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC;YAC/C,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAO9C,IAAI,WAAW,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC;QAEtC,IAAI,OAAO,WAAW,EAAE;YACtB,YAAY,GAAG,OAAO,WAAW,CAAC,IAAI,CAAC;QACzC;QAEA,IAAI,OAAO,QAAQ,EAAE;YACnB,YAAY,CAAC,QAAQ,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QACnD;QAEA,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;YACzC,YAAY,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;QACtE;QAEA,YAAY,CAAC,iBAAiB,EAAE,OAAO,OAAO,CAAC,QAAQ,CAAC;QAExD,MAAM,UAAU,MAAM,KAAK,UAAU;YACnC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC;YACvD,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAc;QAChD,MAAM,UAAU,MAAM,KAAK,MAAM;YAC/B,gBAAgB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,EAAE,UAAU,GAAG,GAAG,GAAG,CAAC;YACzD,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAEtC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAa;QAC/C,MAAM,UAAU,MAAM,KAAK,KAAK;YAC9B,gBAAgB,QAAQ,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,GAAG;YAC9C,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,MAAM,UAAU,MAAM,KAAK,OAAO,QAAQ,CAAC,IAAI,EAAE;YAC/C,gBAAgB;YAChB,cAAc;QAChB;QAEA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2853, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/hooks/index.ts"], "sourcesContent": ["export { useAutoSave, usePromptAutoSave } from './useAutoSave'\nexport { \n  useCopyToClipboard, \n  usePromptCopy, \n  useCodeCopy, \n  useLinkCopy \n} from './useCopyToClipboard'"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptCard.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { Prompt } from '~/types'\r\nimport { api } from '~/trpc/react'\r\nimport { usePromptCopy } from '~/hooks'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface PromptCardProps {\r\n  prompt: Prompt\r\n  onEdit?: (prompt: Prompt) => void\r\n  onDelete?: (promptId: string) => void\r\n  onUse?: (prompt: Prompt) => void\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n}\r\n\r\nexport const PromptCard = ({\r\n  prompt,\r\n  onEdit,\r\n  onDelete,\r\n  onUse,\r\n  showCategory = true,\r\n  showActions = true,\r\n}: PromptCardProps) => {\r\n  const [showFullContent, setShowFullContent] = useState(false)\r\n  \r\n  // 复制功能\r\n  const { copyPrompt, isCopied, isLoading: isCopying } = usePromptCopy()\r\n\r\n  // 更新使用次数\r\n  const updateUsageMutation = api.prompts.updateUsage.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('使用次数已更新')\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 复制内容到剪贴板\r\n  const copyToClipboard = async () => {\r\n    const success = await copyPrompt(prompt)\r\n    \r\n    if (success) {\r\n      // 更新使用次数\r\n      updateUsageMutation.mutate({ id: prompt.id })\r\n      \r\n      // 触发使用回调\r\n      if (onUse) {\r\n        onUse(prompt)\r\n      }\r\n    }\r\n  }\r\n\r\n  // 处理编辑\r\n  const handleEdit = () => {\r\n    if (onEdit) {\r\n      onEdit(prompt)\r\n    }\r\n  }\r\n\r\n  // 处理删除\r\n  const handleDelete = () => {\r\n    if (onDelete) {\r\n      onDelete(prompt.id)\r\n    }\r\n  }\r\n\r\n  // 格式化时间\r\n  const formatDate = (date: Date) => {\r\n    return new Intl.DateTimeFormat('zh-CN', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    }).format(date)\r\n  }\r\n\r\n  // 截断内容\r\n  const truncateContent = (content: string, maxLength: number = 150) => {\r\n    if (content.length <= maxLength) return content\r\n    return content.substring(0, maxLength) + '...'\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      exit={{ opacity: 0, y: -20 }}\r\n      whileHover={{ y: -4 }}\r\n      transition={{ duration: 0.2 }}\r\n      className=\"card bg-base-100 shadow-md hover:shadow-xl transition-shadow duration-200 group\"\r\n    >\r\n      <div className=\"card-body p-4\">\r\n        {/* 头部信息 */}\r\n        <div className=\"flex items-start justify-between mb-3\">\r\n          <div className=\"flex-1 min-w-0\">\r\n            <h3 className=\"card-title text-lg font-semibold text-base-content truncate\">\r\n              {prompt.title}\r\n            </h3>\r\n            \r\n            {/* 分类和标签 */}\r\n            <div className=\"flex items-center gap-2 mt-1\">\r\n              {showCategory && prompt.category && (\r\n                <Link\r\n                  href={`/categories/${prompt.category.id}`}\r\n                  className=\"badge badge-sm hover:badge-primary transition-colors\"\r\n                  style={{ backgroundColor: prompt.category.color + '20', color: prompt.category.color }}\r\n                >\r\n                  {prompt.category.name}\r\n                </Link>\r\n              )}\r\n              \r\n              {prompt.tags?.slice(0, 3).map((tag) => (\r\n                <Link\r\n                  key={tag.id}\r\n                  href={`/tags/${encodeURIComponent(tag.name)}`}\r\n                  className=\"badge badge-sm badge-ghost hover:badge-primary transition-colors\"\r\n                >\r\n                  {tag.name}\r\n                </Link>\r\n              ))}\r\n              \r\n              {prompt.tags && prompt.tags.length > 3 && (\r\n                <span className=\"badge badge-sm badge-ghost\">\r\n                  +{prompt.tags.length - 3}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 收藏状态 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            {prompt.isFavorite && (\r\n              <div className=\"tooltip tooltip-left\" data-tip=\"已收藏\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"currentColor\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  className=\"w-4 h-4 text-warning\"\r\n                >\r\n                  <path d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\" />\r\n                </svg>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* 内容预览 */}\r\n        <div className=\"mb-3\">\r\n          <p className=\"text-sm text-base-content/80 leading-relaxed\">\r\n            {showFullContent ? prompt.content : truncateContent(prompt.content)}\r\n          </p>\r\n          \r\n          {prompt.content.length > 150 && (\r\n            <button\r\n              onClick={() => setShowFullContent(!showFullContent)}\r\n              className=\"text-xs text-primary hover:text-primary-focus mt-1\"\r\n            >\r\n              {showFullContent ? '收起' : '展开'}\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* 底部信息 */}\r\n        <div className=\"flex items-center justify-between text-xs text-base-content/60\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            <div className=\"flex items-center space-x-1\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                />\r\n              </svg>\r\n              <span>{prompt.usageCount || 0} 次使用</span>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-1\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-3 h-3\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                />\r\n              </svg>\r\n              <span>{formatDate(prompt.updatedAt)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <AnimatePresence>\r\n          {showActions && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: 10 }}\r\n              transition={{ duration: 0.2 }}\r\n              className=\"card-actions justify-end mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\r\n            >\r\n              <div className=\"flex items-center space-x-2\">\r\n              {/* 复制按钮 */}\r\n              <motion.button\r\n                onClick={copyToClipboard}\r\n                disabled={isCopying}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className={`btn btn-sm btn-ghost ${isCopied ? 'btn-success' : 'hover:btn-primary'}`}\r\n                title={isCopied ? '已复制' : '复制内容'}\r\n              >\r\n                {isCopying ? (\r\n                  <span className=\"loading loading-spinner loading-xs\"></span>\r\n                ) : isCopied ? (\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                ) : (\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                    />\r\n                  </svg>\r\n                )}\r\n              </motion.button>\r\n\r\n              {/* 编辑按钮 */}\r\n              <motion.button\r\n                onClick={handleEdit}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost hover:btn-secondary\"\r\n                title=\"编辑\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              {/* 删除按钮 */}\r\n              <motion.button\r\n                onClick={handleDelete}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost hover:btn-error\"\r\n                title=\"删除\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              {/* 详情链接 */}\r\n              <motion.div\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                <Link\r\n                  href={`/prompts/${prompt.id}`}\r\n                  className=\"btn btn-sm btn-ghost hover:btn-info\"\r\n                  title=\"查看详情\"\r\n                >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z\"\r\n                  />\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                  />\r\n                </svg>\r\n              </Link>\r\n              </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AACA;AARA;;;;;;;;AAmBO,MAAM,aAAa,CAAC,EACzB,MAAM,EACN,MAAM,EACN,QAAQ,EACR,KAAK,EACL,eAAe,IAAI,EACnB,cAAc,IAAI,EACF;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,OAAO;IACP,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAEnE,SAAS;IACT,MAAM,sBAAsB,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;QAC9D,WAAW;YACT,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;QACtC;IACF;IAEA,WAAW;IACX,MAAM,kBAAkB;QACtB,MAAM,UAAU,MAAM,WAAW;QAEjC,IAAI,SAAS;YACX,SAAS;YACT,oBAAoB,MAAM,CAAC;gBAAE,IAAI,OAAO,EAAE;YAAC;YAE3C,SAAS;YACT,IAAI,OAAO;gBACT,MAAM;YACR;QACF;IACF;IAEA,OAAO;IACP,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,OAAO;IACP,MAAM,kBAAkB,CAAC,SAAiB,YAAoB,GAAG;QAC/D,IAAI,QAAQ,MAAM,IAAI,WAAW,OAAO;QACxC,OAAO,QAAQ,SAAS,CAAC,GAAG,aAAa;IAC3C;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,OAAO,KAAK;;;;;;8CAIf,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,OAAO,QAAQ,kBAC9B,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,EAAE,EAAE;4CACzC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,OAAO,QAAQ,CAAC,KAAK,GAAG;gDAAM,OAAO,OAAO,QAAQ,CAAC,KAAK;4CAAC;sDAEpF,OAAO,QAAQ,CAAC,IAAI;;;;;;wCAIxB,OAAO,IAAI,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,oBAC7B,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,CAAC,MAAM,EAAE,mBAAmB,IAAI,IAAI,GAAG;gDAC7C,WAAU;0DAET,IAAI,IAAI;+CAJJ,IAAI,EAAE;;;;;wCAQd,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,mBACnC,8OAAC;4CAAK,WAAU;;gDAA6B;gDACzC,OAAO,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;sCAO/B,8OAAC;4BAAI,WAAU;sCACZ,OAAO,UAAU,kBAChB,8OAAC;gCAAI,WAAU;gCAAuB,YAAS;0CAC7C,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCACV,kBAAkB,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO;;;;;;wBAGnE,OAAO,OAAO,CAAC,MAAM,GAAG,qBACvB,8OAAC;4BACC,SAAS,IAAM,mBAAmB,CAAC;4BACnC,WAAU;sCAET,kBAAkB,OAAO;;;;;;;;;;;;8BAMhC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,8OAAC;;4CAAM,OAAO,UAAU,IAAI;4CAAE;;;;;;;;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,8OAAC;kDAAM,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,8OAAC,yLAAA,CAAA,kBAAe;8BACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,UAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAW,CAAC,qBAAqB,EAAE,WAAW,gBAAgB,qBAAqB;oCACnF,OAAO,WAAW,QAAQ;8CAEzB,0BACC,8OAAC;wCAAK,WAAU;;;;;mFACd,yBACF,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;iGAIN,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAOV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAMR,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAMR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;wCAC7B,WAAU;wCACV,OAAM;kDAER,cAAA,8OAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;;8DAEV,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;8DAEJ,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYtB", "debugId": null}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/PromptGrid.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { Prompt } from '~/types'\r\nimport { PromptCard } from './PromptCard'\r\n\r\ninterface PromptGridProps {\r\n  prompts: Prompt[]\r\n  isLoading?: boolean\r\n  onEdit?: (prompt: Prompt) => void\r\n  onDelete?: (promptId: string) => void\r\n  onUse?: (prompt: Prompt) => void\r\n  showCategory?: boolean\r\n  showActions?: boolean\r\n  emptyMessage?: string\r\n  columns?: 1 | 2 | 3 | 4 | 6\r\n}\r\n\r\nexport const PromptGrid = ({\r\n  prompts,\r\n  isLoading = false,\r\n  onEdit,\r\n  onDelete,\r\n  onUse,\r\n  showCategory = true,\r\n  showActions = true,\r\n  emptyMessage = '暂无提示词',\r\n  columns = 3,\r\n}: PromptGridProps) => {\r\n  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([])\r\n\r\n  // 响应式列数配置\r\n  const getGridCols = () => {\r\n    const colsMap = {\r\n      1: 'grid-cols-1',\r\n      2: 'grid-cols-1 md:grid-cols-2',\r\n      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\r\n      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',\r\n      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',\r\n    }\r\n    return colsMap[columns] || colsMap[3]\r\n  }\r\n\r\n  // 渲染加载骨架屏\r\n  const renderLoadingSkeleton = () => {\r\n    return (\r\n      <div className={`grid ${getGridCols()} gap-4`}>\r\n        {Array.from({ length: 6 }, (_, i) => (\r\n          <div key={i} className=\"card bg-base-100 shadow-md\">\r\n            <div className=\"card-body p-4\">\r\n              {/* 标题骨架 */}\r\n              <div className=\"h-6 bg-base-300 rounded animate-pulse mb-3\"></div>\r\n              \r\n              {/* 分类和标签骨架 */}\r\n              <div className=\"flex items-center gap-2 mb-3\">\r\n                <div className=\"h-4 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 w-12 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 w-14 bg-base-300 rounded animate-pulse\"></div>\r\n              </div>\r\n              \r\n              {/* 内容骨架 */}\r\n              <div className=\"space-y-2 mb-3\">\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse w-3/4\"></div>\r\n              </div>\r\n              \r\n              {/* 底部信息骨架 */}\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"h-3 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n                  <div className=\"h-3 w-20 bg-base-300 rounded animate-pulse\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染空状态\r\n  const renderEmptyState = () => {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex flex-col items-center justify-center py-16 text-center\"\r\n      >\r\n        <motion.div\r\n          initial={{ scale: 0 }}\r\n          animate={{ scale: 1 }}\r\n          transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\r\n          className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mb-4\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-12 h-12 text-base-content/40\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 18H9a2.25 2.25 0 01-2.25-2.25V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 002.25-2.25V9.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08M15.75 18v-2.625c0-.621.504-1.125 1.125-1.125h.375a1.125 1.125 0 011.125 1.125V18\"\r\n            />\r\n          </svg>\r\n        </motion.div>\r\n        <motion.h3\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"text-lg font-medium text-base-content mb-2\"\r\n        >\r\n          {emptyMessage}\r\n        </motion.h3>\r\n        <motion.p\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"text-base-content/70 mb-6\"\r\n        >\r\n          创建你的第一个提示词开始使用吧\r\n        </motion.p>\r\n        <motion.a\r\n          href=\"/prompts/new\"\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"btn btn-primary\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-5 h-5\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 4.5v15m7.5-7.5h-15\"\r\n            />\r\n          </svg>\r\n          新建提示词\r\n        </motion.a>\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  // 处理批量选择\r\n  const handleSelectPrompt = (promptId: string) => {\r\n    setSelectedPrompts(prev => \r\n      prev.includes(promptId)\r\n        ? prev.filter(id => id !== promptId)\r\n        : [...prev, promptId]\r\n    )\r\n  }\r\n\r\n  // 清空选择\r\n  const clearSelection = () => {\r\n    setSelectedPrompts([])\r\n  }\r\n\r\n  // 全选/取消全选\r\n  const toggleSelectAll = () => {\r\n    if (selectedPrompts.length === prompts.length) {\r\n      clearSelection()\r\n    } else {\r\n      setSelectedPrompts(prompts.map(p => p.id))\r\n    }\r\n  }\r\n\r\n  if (isLoading) {\r\n    return renderLoadingSkeleton()\r\n  }\r\n\r\n  if (!prompts || prompts.length === 0) {\r\n    return renderEmptyState()\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* 批量操作栏 */}\r\n      <AnimatePresence>\r\n        {selectedPrompts.length > 0 && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -20 }}\r\n            className=\"bg-base-200 rounded-lg p-4 flex items-center justify-between\"\r\n          >\r\n            <div className=\"flex items-center space-x-4\">\r\n              <span className=\"text-sm text-base-content\">\r\n                已选择 {selectedPrompts.length} 个提示词\r\n              </span>\r\n              <motion.button\r\n                onClick={clearSelection}\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-ghost\"\r\n              >\r\n                取消选择\r\n              </motion.button>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-warning\"\r\n              >\r\n                批量编辑\r\n              </motion.button>\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-sm btn-error\"\r\n              >\r\n                批量删除\r\n              </motion.button>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 提示词网格 */}\r\n      <motion.div\r\n        layout\r\n        className={`grid ${getGridCols()} gap-4`}\r\n      >\r\n        <AnimatePresence mode=\"popLayout\">\r\n          {prompts.map((prompt, index) => (\r\n            <motion.div\r\n              key={prompt.id}\r\n              layout\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0.8 }}\r\n              transition={{ \r\n                duration: 0.2,\r\n                delay: index * 0.05,\r\n                layout: { duration: 0.2 }\r\n              }}\r\n              className=\"relative\"\r\n            >\r\n              {/* 选择框 */}\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"absolute top-2 left-2 z-10\"\r\n              >\r\n                <input\r\n                  type=\"checkbox\"\r\n                  className=\"checkbox checkbox-primary checkbox-sm\"\r\n                  checked={selectedPrompts.includes(prompt.id)}\r\n                  onChange={() => handleSelectPrompt(prompt.id)}\r\n                />\r\n              </motion.div>\r\n              \r\n              <PromptCard\r\n                prompt={prompt}\r\n                onEdit={onEdit}\r\n                onDelete={onDelete}\r\n                onUse={onUse}\r\n                showCategory={showCategory}\r\n                showActions={showActions}\r\n              />\r\n            </motion.div>\r\n          ))}\r\n        </AnimatePresence>\r\n      </motion.div>\r\n\r\n      {/* 加载更多按钮 */}\r\n      {prompts.length > 0 && (\r\n        <div className=\"flex justify-center pt-8\">\r\n          <button className=\"btn btn-outline\">\r\n            加载更多\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AALA;;;;;AAmBO,MAAM,aAAa,CAAC,EACzB,OAAO,EACP,YAAY,KAAK,EACjB,MAAM,EACN,QAAQ,EACR,KAAK,EACL,eAAe,IAAI,EACnB,cAAc,IAAI,EAClB,eAAe,OAAO,EACtB,UAAU,CAAC,EACK;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,UAAU;IACV,MAAM,cAAc;QAClB,MAAM,UAAU;YACd,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;QACL;QACA,OAAO,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,EAAE;IACvC;IAEA,UAAU;IACV,MAAM,wBAAwB;QAC5B,qBACE,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,cAAc,MAAM,CAAC;sBAC1C,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mBAvBb;;;;;;;;;;IA+BlB;IAEA,QAAQ;IACR,MAAM,mBAAmB;QACvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;;8BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;oBAAE;oBACpB,SAAS;wBAAE,OAAO;oBAAE;oBACpB,YAAY;wBAAE,OAAO;wBAAK,MAAM;wBAAU,WAAW;oBAAI;oBACzD,WAAU;8BAEV,cAAA,8OAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;;;;;;8BAIR,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BAET;;;;;;8BAEH,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;oBAAI;oBACzB,WAAU;8BACX;;;;;;8BAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,MAAK;oBACL,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;oBACzB,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,WAAU;;sCAEV,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAEA;;;;;;;;;;;;;IAKd;IAEA,SAAS;IACT,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,YACzB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,mBAAmB,EAAE;IACvB;IAEA,UAAU;IACV,MAAM,kBAAkB;QACtB,IAAI,gBAAgB,MAAM,KAAK,QAAQ,MAAM,EAAE;YAC7C;QACF,OAAO;YACL,mBAAmB,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC1C;IACF;IAEA,IAAI,WAAW;QACb,OAAO;IACT;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAA4B;wCACrC,gBAAgB,MAAM;wCAAC;;;;;;;8CAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;sCAIH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,MAAM;gBACN,WAAW,CAAC,KAAK,EAAE,cAAc,MAAM,CAAC;0BAExC,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,MAAM;4BACN,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,YAAY;gCACV,UAAU;gCACV,OAAO,QAAQ;gCACf,QAAQ;oCAAE,UAAU;gCAAI;4BAC1B;4BACA,WAAU;;8CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,WAAU;8CAEV,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;wCAC3C,UAAU,IAAM,mBAAmB,OAAO,EAAE;;;;;;;;;;;8CAIhD,8OAAC,2IAAA,CAAA,aAAU;oCACT,QAAQ;oCACR,QAAQ;oCACR,UAAU;oCACV,OAAO;oCACP,cAAc;oCACd,aAAa;;;;;;;2BAhCV,OAAO,EAAE;;;;;;;;;;;;;;;YAwCrB,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAO,WAAU;8BAAkB;;;;;;;;;;;;;;;;;AAO9C", "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/prompts/index.ts"], "sourcesContent": ["export { PromptCard } from './PromptCard'\r\nexport { PromptGrid } from './PromptGrid'"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 3970, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/SearchResults.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { useRouter, useSearchParams } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\nimport { PromptGrid } from '~/components/prompts'\r\nimport { SearchHighlight } from './SearchHighlight'\r\nimport { Prompt } from '~/types'\r\n\r\ninterface SearchResultsProps {\r\n  initialQuery?: string\r\n  onQueryChange?: (query: string) => void\r\n}\r\n\r\nexport const SearchResults = ({ initialQuery = '', onQueryChange }: SearchResultsProps) => {\r\n  const router = useRouter()\r\n  const searchParams = useSearchParams()\r\n  const [query, setQuery] = useState(initialQuery || searchParams.get('q') || '')\r\n  const [sortBy, setSortBy] = useState<'relevance' | 'date' | 'usage'>('relevance')\r\n  const [filterCategory, setFilterCategory] = useState<string>('')\r\n  const [filterTags, setFilterTags] = useState<string[]>([])\r\n\r\n  // 搜索提示词\r\n  const { data: searchResults, isLoading, error } = api.search.searchPrompts.useQuery({\r\n    query: query.trim(),\r\n    sortBy,\r\n    categoryId: filterCategory || undefined,\r\n    tags: filterTags,\r\n    limit: 20,\r\n  }, {\r\n    enabled: query.trim().length > 0,\r\n  })\r\n\r\n  // 获取分类列表用于筛选\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 获取热门标签\r\n  const { data: popularTags } = api.tags.getPopular.useQuery({ limit: 20 })\r\n\r\n  // 更新 URL 参数\r\n  useEffect(() => {\r\n    if (query.trim()) {\r\n      const params = new URLSearchParams(searchParams)\r\n      params.set('q', query.trim())\r\n      if (sortBy !== 'relevance') params.set('sort', sortBy)\r\n      if (filterCategory) params.set('category', filterCategory)\r\n      if (filterTags.length > 0) params.set('tags', filterTags.join(','))\r\n      \r\n      router.replace(`/search?${params.toString()}`, { scroll: false })\r\n    }\r\n  }, [query, sortBy, filterCategory, filterTags, router, searchParams])\r\n\r\n  // 处理标签筛选\r\n  const handleTagFilter = (tag: string) => {\r\n    setFilterTags(prev => \r\n      prev.includes(tag) \r\n        ? prev.filter(t => t !== tag)\r\n        : [...prev, tag]\r\n    )\r\n  }\r\n\r\n  // 清空所有筛选\r\n  const clearFilters = () => {\r\n    setFilterCategory('')\r\n    setFilterTags([])\r\n    setSortBy('relevance')\r\n  }\r\n\r\n  // 处理查询变化\r\n  const handleQueryChange = (newQuery: string) => {\r\n    setQuery(newQuery)\r\n    if (onQueryChange) {\r\n      onQueryChange(newQuery)\r\n    }\r\n  }\r\n\r\n  if (!query.trim()) {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"text-center py-16\"\r\n      >\r\n        <div className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-12 h-12 text-base-content/40\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n            />\r\n          </svg>\r\n        </div>\r\n        <h3 className=\"text-lg font-medium text-base-content mb-2\">\r\n          输入关键词开始搜索\r\n        </h3>\r\n        <p className=\"text-base-content/70\">\r\n          搜索你需要的提示词，支持标题、内容、标签搜索\r\n        </p>\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* 搜索状态和统计 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"flex items-center justify-between\"\r\n      >\r\n        <div className=\"flex items-center space-x-4\">\r\n          <h2 className=\"text-xl font-semibold\">\r\n            <SearchHighlight\r\n              text={`搜索结果: \"${query}\"`}\r\n              query=\"\"\r\n              className=\"text-base-content\"\r\n            />\r\n          </h2>\r\n          {searchResults && (\r\n            <span className=\"text-sm text-base-content/70\">\r\n              找到 {searchResults.total} 个结果\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* 排序选项 */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <span className=\"text-sm text-base-content/70\">排序:</span>\r\n          <select\r\n            value={sortBy}\r\n            onChange={(e) => setSortBy(e.target.value as any)}\r\n            className=\"select select-sm select-bordered\"\r\n          >\r\n            <option value=\"relevance\">相关性</option>\r\n            <option value=\"date\">最新</option>\r\n            <option value=\"usage\">使用次数</option>\r\n          </select>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* 筛选器 */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.1 }}\r\n        className=\"bg-base-100 rounded-lg p-4 shadow-sm\"\r\n      >\r\n        <div className=\"flex flex-wrap items-center gap-4\">\r\n          {/* 分类筛选 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <span className=\"text-sm font-medium text-base-content\">分类:</span>\r\n            <select\r\n              value={filterCategory}\r\n              onChange={(e) => setFilterCategory(e.target.value)}\r\n              className=\"select select-sm select-bordered\"\r\n            >\r\n              <option value=\"\">所有分类</option>\r\n              {categories?.map(category => (\r\n                <option key={category.id} value={category.id}>\r\n                  {category.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* 标签筛选 */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <span className=\"text-sm font-medium text-base-content\">标签:</span>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {popularTags?.slice(0, 10).map(tag => (\r\n                <motion.button\r\n                  key={tag.name}\r\n                  onClick={() => handleTagFilter(tag.name)}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className={`badge badge-sm ${\r\n                    filterTags.includes(tag.name)\r\n                      ? 'badge-primary'\r\n                      : 'badge-ghost hover:badge-primary'\r\n                  }`}\r\n                >\r\n                  {tag.name}\r\n                </motion.button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 清空筛选 */}\r\n          {(filterCategory || filterTags.length > 0 || sortBy !== 'relevance') && (\r\n            <motion.button\r\n              onClick={clearFilters}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"btn btn-sm btn-ghost\"\r\n            >\r\n              清空筛选\r\n            </motion.button>\r\n          )}\r\n        </div>\r\n\r\n        {/* 当前筛选状态 */}\r\n        {(filterCategory || filterTags.length > 0) && (\r\n          <div className=\"mt-3 pt-3 border-t border-base-200\">\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {filterCategory && categories && (\r\n                <span className=\"badge badge-outline\">\r\n                  分类: {categories.find(c => c.id === filterCategory)?.name}\r\n                </span>\r\n              )}\r\n              {filterTags.map(tag => (\r\n                <span key={tag} className=\"badge badge-outline\">\r\n                  标签: {tag}\r\n                  <button\r\n                    onClick={() => handleTagFilter(tag)}\r\n                    className=\"ml-1 text-xs\"\r\n                  >\r\n                    ×\r\n                  </button>\r\n                </span>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* 搜索结果 */}\r\n      <AnimatePresence mode=\"wait\">\r\n        {isLoading ? (\r\n          <motion.div\r\n            key=\"loading\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"text-center py-16\"\r\n          >\r\n            <div className=\"loading loading-spinner loading-lg\"></div>\r\n            <p className=\"mt-4 text-base-content/70\">搜索中...</p>\r\n          </motion.div>\r\n        ) : error ? (\r\n          <motion.div\r\n            key=\"error\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"text-center py-16\"\r\n          >\r\n            <div className=\"w-24 h-24 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-12 h-12 text-error\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-base-content mb-2\">\r\n              搜索出错了\r\n            </h3>\r\n            <p className=\"text-base-content/70\">\r\n              {error.message || '请稍后重试'}\r\n            </p>\r\n          </motion.div>\r\n        ) : (\r\n          <motion.div\r\n            key=\"results\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n          >\r\n            {searchResults && searchResults.results.length > 0 ? (\r\n              <PromptGrid\r\n                prompts={searchResults.results as Prompt[]}\r\n                emptyMessage={`没有找到包含 \"${query}\" 的提示词`}\r\n                columns={3}\r\n                showCategory={true}\r\n                showActions={true}\r\n              />\r\n            ) : (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"text-center py-16\"\r\n              >\r\n                <div className=\"w-24 h-24 bg-base-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-12 h-12 text-base-content/40\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n                <h3 className=\"text-lg font-medium text-base-content mb-2\">\r\n                  没有找到相关结果\r\n                </h3>\r\n                <p className=\"text-base-content/70 mb-4\">\r\n                  尝试使用不同的关键词或调整筛选条件\r\n                </p>\r\n                <motion.button\r\n                  onClick={clearFilters}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                  className=\"btn btn-outline\"\r\n                >\r\n                  清空筛选条件\r\n                </motion.button>\r\n              </motion.div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAeO,MAAM,gBAAgB,CAAC,EAAE,eAAe,EAAE,EAAE,aAAa,EAAsB;IACpF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,aAAa,GAAG,CAAC,QAAQ;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEzD,QAAQ;IACR,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAClF,OAAO,MAAM,IAAI;QACjB;QACA,YAAY,kBAAkB;QAC9B,MAAM;QACN,OAAO;IACT,GAAG;QACD,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG;IACjC;IAEA,aAAa;IACb,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,SAAS;IACT,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAAE,OAAO;IAAG;IAEvE,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,SAAS,IAAI,gBAAgB;YACnC,OAAO,GAAG,CAAC,KAAK,MAAM,IAAI;YAC1B,IAAI,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ;YAC/C,IAAI,gBAAgB,OAAO,GAAG,CAAC,YAAY;YAC3C,IAAI,WAAW,MAAM,GAAG,GAAG,OAAO,GAAG,CAAC,QAAQ,WAAW,IAAI,CAAC;YAE9D,OAAO,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,QAAQ,IAAI,EAAE;gBAAE,QAAQ;YAAM;QACjE;IACF,GAAG;QAAC;QAAO;QAAQ;QAAgB;QAAY;QAAQ;KAAa;IAEpE,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,cAAc,CAAA,OACZ,KAAK,QAAQ,CAAC,OACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,OACvB;mBAAI;gBAAM;aAAI;IAEtB;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,kBAAkB;QAClB,cAAc,EAAE;QAChB,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI;QACjB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;;;;;;8BAIR,8OAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAG3D,8OAAC;oBAAE,WAAU;8BAAuB;;;;;;;;;;;;IAK1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,+IAAA,CAAA,kBAAe;oCACd,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;oCACxB,OAAM;oCACN,WAAU;;;;;;;;;;;4BAGb,+BACC,8OAAC;gCAAK,WAAU;;oCAA+B;oCACzC,cAAc,KAAK;oCAAC;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;0CAC/C,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;kDACxD,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,YAAY,IAAI,CAAA,yBACf,8OAAC;oDAAyB,OAAO,SAAS,EAAE;8DACzC,SAAS,IAAI;mDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;0CAQ9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwC;;;;;;kDACxD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM,GAAG,IAAI,IAAI,CAAA,oBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,gBAAgB,IAAI,IAAI;gDACvC,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAW,CAAC,eAAe,EACzB,WAAW,QAAQ,CAAC,IAAI,IAAI,IACxB,kBACA,mCACJ;0DAED,IAAI,IAAI;+CAVJ,IAAI,IAAI;;;;;;;;;;;;;;;;4BAiBpB,CAAC,kBAAkB,WAAW,MAAM,GAAG,KAAK,WAAW,WAAW,mBACjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;oBAOJ,CAAC,kBAAkB,WAAW,MAAM,GAAG,CAAC,mBACvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,kBAAkB,4BACjB,8OAAC;oCAAK,WAAU;;wCAAsB;wCAC/B,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;;;;;;;gCAGvD,WAAW,GAAG,CAAC,CAAA,oBACd,8OAAC;wCAAe,WAAU;;4CAAsB;4CACzC;0DACL,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;;uCALQ;;;;;;;;;;;;;;;;;;;;;;0BAgBrB,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;mBAPrC;;;;+DASJ,sBACF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;;;;;;sCAIR,8OAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,8OAAC;4BAAE,WAAU;sCACV,MAAM,OAAO,IAAI;;;;;;;mBA1BhB;;;;6EA8BN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAElB,iBAAiB,cAAc,OAAO,CAAC,MAAM,GAAG,kBAC/C,8OAAC,2IAAA,CAAA,aAAU;wBACT,SAAS,cAAc,OAAO;wBAC9B,cAAc,CAAC,QAAQ,EAAE,MAAM,MAAM,CAAC;wBACtC,SAAS;wBACT,cAAc;wBACd,aAAa;;;;;iFAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAIR,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;0CAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;mBA9CD;;;;;;;;;;;;;;;;AAwDhB", "debugId": null}}, {"offset": {"line": 4611, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/search/index.ts"], "sourcesContent": ["export { SearchInput } from './SearchInput'\r\nexport { SearchHighlight, MultiSearchHighlight } from './SearchHighlight'\r\nexport { SearchResults } from './SearchResults'"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4631, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { useAuth } from '~/lib/auth/context'\r\nimport { useMainStore } from '~/stores'\r\nimport { SearchInput } from '~/components/search'\r\n\r\nexport const Header = () => {\r\n  const { user, signOut } = useAuth()\r\n  const { ui, toggleSidebar, setTheme } = useMainStore()\r\n  const [showUserMenu, setShowUserMenu] = useState(false)\r\n\r\n  const handleSignOut = async () => {\r\n    await signOut()\r\n  }\r\n\r\n  const handleThemeChange = () => {\r\n    const newTheme = ui.theme === 'light' ? 'dark' : 'light'\r\n    setTheme(newTheme)\r\n    document.documentElement.setAttribute('data-theme', newTheme)\r\n  }\r\n\r\n  return (\r\n    <header className=\"navbar bg-base-100 border-b border-base-200 sticky top-0 z-50\">\r\n      <div className=\"navbar-start\">\r\n        {/* 侧边栏切换按钮 */}\r\n        <button\r\n          className=\"btn btn-ghost btn-square\"\r\n          onClick={toggleSidebar}\r\n          aria-label=\"切换侧边栏\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            className=\"inline-block w-5 h-5 stroke-current\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth=\"2\"\r\n              d=\"M4 6h16M4 12h16M4 18h16\"\r\n            />\r\n          </svg>\r\n        </button>\r\n\r\n        {/* 应用标题 */}\r\n        <div className=\"flex items-center space-x-2 ml-2\">\r\n          <div className=\"avatar\">\r\n            <div className=\"w-8 h-8 rounded-lg bg-primary flex items-center justify-center\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={2}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5 text-primary-content\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          <h1 className=\"text-lg font-semibold text-base-content\">\r\n            提示词管理工具\r\n          </h1>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"navbar-center\">\r\n        {/* 搜索框 */}\r\n        <SearchInput\r\n          placeholder=\"搜索提示词...\"\r\n          className=\"w-96 max-w-xs\"\r\n          showHistory={true}\r\n          showSuggestions={true}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"navbar-end\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {/* 主题切换按钮 */}\r\n          <button\r\n            className=\"btn btn-ghost btn-square\"\r\n            onClick={handleThemeChange}\r\n            aria-label=\"切换主题\"\r\n          >\r\n            {ui.theme === 'light' ? (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z\"\r\n                />\r\n              </svg>\r\n            ) : (\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"\r\n                />\r\n              </svg>\r\n            )}\r\n          </button>\r\n\r\n          {/* 通知按钮 */}\r\n          <button className=\"btn btn-ghost btn-square\" aria-label=\"通知\">\r\n            <div className=\"indicator\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-5 h-5\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0\"\r\n                />\r\n              </svg>\r\n              <span className=\"badge badge-xs badge-primary indicator-item\"></span>\r\n            </div>\r\n          </button>\r\n\r\n          {/* 用户菜单 */}\r\n          <div className=\"dropdown dropdown-end\">\r\n            <div\r\n              tabIndex={0}\r\n              role=\"button\"\r\n              className=\"btn btn-ghost btn-circle avatar\"\r\n              onClick={() => setShowUserMenu(!showUserMenu)}\r\n            >\r\n              <div className=\"w-8 rounded-full\">\r\n                {user?.user_metadata?.avatar_url ? (\r\n                  <img\r\n                    alt=\"用户头像\"\r\n                    src={user.user_metadata.avatar_url}\r\n                    className=\"w-full h-full object-cover\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-full h-full bg-primary flex items-center justify-center text-primary-content font-medium\">\r\n                    {user?.email?.charAt(0).toUpperCase() || 'U'}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            {showUserMenu && (\r\n              <ul\r\n                tabIndex={0}\r\n                className=\"menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52\"\r\n              >\r\n                <li>\r\n                  <div className=\"flex flex-col items-start p-2\">\r\n                    <span className=\"font-medium text-base-content\">\r\n                      {user?.user_metadata?.full_name || '用户'}\r\n                    </span>\r\n                    <span className=\"text-xs text-base-content/70\">\r\n                      {user?.email}\r\n                    </span>\r\n                  </div>\r\n                </li>\r\n                <li>\r\n                  <hr className=\"my-1\" />\r\n                </li>\r\n                <li>\r\n                  <a className=\"flex items-center space-x-2\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z\"\r\n                      />\r\n                    </svg>\r\n                    <span>个人资料</span>\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a className=\"flex items-center space-x-2\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z\"\r\n                      />\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\r\n                      />\r\n                    </svg>\r\n                    <span>设置</span>\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <a className=\"flex items-center space-x-2\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z\"\r\n                      />\r\n                    </svg>\r\n                    <span>帮助</span>\r\n                  </a>\r\n                </li>\r\n                <li>\r\n                  <hr className=\"my-1\" />\r\n                </li>\r\n                <li>\r\n                  <a\r\n                    className=\"flex items-center space-x-2 text-error\"\r\n                    onClick={handleSignOut}\r\n                  >\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-4 h-4\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75\"\r\n                      />\r\n                    </svg>\r\n                    <span>退出登录</span>\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOO,MAAM,SAAS;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,EAAE,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,oBAAoB;QACxB,MAAM,WAAW,GAAG,KAAK,KAAK,UAAU,SAAS;QACjD,SAAS;QACT,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc;IACtD;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAU;wBACV,SAAS;wBACT,cAAW;kCAEX,cAAA,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAY;gCACZ,GAAE;;;;;;;;;;;;;;;;kCAMR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;0CAKV,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC,2IAAA,CAAA,cAAW;oBACV,aAAY;oBACZ,WAAU;oBACV,aAAa;oBACb,iBAAiB;;;;;;;;;;;0BAIrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,GAAG,KAAK,KAAK,wBACZ,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;yFAIN,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;;;;;;sCAOV,8OAAC;4BAAO,WAAU;4BAA2B,cAAW;sCACtD,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,8OAAC;wCAAK,WAAU;;;;;;;;;;;;;;;;;sCAKpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,UAAU;oCACV,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,gBAAgB,CAAC;8CAEhC,cAAA,8OAAC;wCAAI,WAAU;kDACZ,MAAM,eAAe,2BACpB,8OAAC;4CACC,KAAI;4CACJ,KAAK,KAAK,aAAa,CAAC,UAAU;4CAClC,WAAU;;;;;qGAGZ,8OAAC;4CAAI,WAAU;sDACZ,MAAM,OAAO,OAAO,GAAG,iBAAiB;;;;;;;;;;;;;;;;gCAKhD,8BACC,8OAAC;oCACC,UAAU;oCACV,WAAU;;sDAEV,8OAAC;sDACC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,MAAM,eAAe,aAAa;;;;;;kEAErC,8OAAC;wDAAK,WAAU;kEACb,MAAM;;;;;;;;;;;;;;;;;sDAIb,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;;;;;;;;;;sDAEhB,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;kEAGN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;;0EAEV,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;0EAEJ,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;;kEAGN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;kEAGN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;;;;;;;;;;sDAEhB,8OAAC;sDACC,cAAA,8OAAC;gDACC,WAAU;gDACV,SAAS;;kEAET,8OAAC;wDACC,OAAM;wDACN,MAAK;wDACL,SAAQ;wDACR,aAAa;wDACb,QAAO;wDACP,WAAU;kEAEV,cAAA,8OAAC;4DACC,eAAc;4DACd,gBAAe;4DACf,GAAE;;;;;;;;;;;kEAGN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B", "debugId": null}}, {"offset": {"line": 5183, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/stores/categories.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools } from 'zustand/middleware'\r\nimport { immer } from 'zustand/middleware/immer'\r\nimport { Category } from '~/types'\r\n\r\n// 分类 Store 接口\r\nexport interface CategoriesStore {\r\n  // 数据状态\r\n  categories: Category[]\r\n  selectedCategory: Category | null\r\n  \r\n  // 加载状态\r\n  loading: boolean\r\n  creating: boolean\r\n  updating: boolean\r\n  deleting: boolean\r\n  \r\n  // 错误状态\r\n  error: string | null\r\n  \r\n  // 状态管理方法\r\n  setCategories: (categories: Category[]) => void\r\n  addCategory: (category: Category) => void\r\n  updateCategory: (category: Category) => void\r\n  removeCategory: (id: string) => void\r\n  selectCategory: (category: Category | null) => void\r\n  setLoading: (loading: boolean) => void\r\n  setCreating: (creating: boolean) => void\r\n  setUpdating: (updating: boolean) => void\r\n  setDeleting: (deleting: boolean) => void\r\n  setError: (error: string | null) => void\r\n  clearError: () => void\r\n  \r\n  // 辅助方法\r\n  getCategoryById: (id: string) => Category | undefined\r\n  getCategoriesWithPromptCount: () => Category[]\r\n  getPopularCategories: () => Category[]\r\n}\r\n\r\n// 创建分类 Store\r\nexport const useCategoriesStore = create<CategoriesStore>()(\r\n  devtools(\r\n    immer((set, get) => ({\r\n      // 初始数据状态\r\n      categories: [],\r\n      selectedCategory: null,\r\n      \r\n      // 初始加载状态\r\n      loading: false,\r\n      creating: false,\r\n      updating: false,\r\n      deleting: false,\r\n      \r\n      // 初始错误状态\r\n      error: null,\r\n      \r\n      // 设置分类列表\r\n      setCategories: (categories: Category[]) => {\r\n        set((state) => {\r\n          state.categories = categories\r\n        })\r\n      },\r\n      \r\n      // 添加分类\r\n      addCategory: (category: Category) => {\r\n        set((state) => {\r\n          state.categories.push(category)\r\n        })\r\n      },\r\n      \r\n      // 更新分类\r\n      updateCategory: (category: Category) => {\r\n        set((state) => {\r\n          const index = state.categories.findIndex(c => c.id === category.id)\r\n          if (index !== -1) {\r\n            state.categories[index] = category\r\n          }\r\n          // 如果当前选中的分类被更新，也需要更新选中状态\r\n          if (state.selectedCategory?.id === category.id) {\r\n            state.selectedCategory = category\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 删除分类\r\n      removeCategory: (id: string) => {\r\n        set((state) => {\r\n          state.categories = state.categories.filter(c => c.id !== id)\r\n          // 如果删除的是当前选中的分类，清空选中状态\r\n          if (state.selectedCategory?.id === id) {\r\n            state.selectedCategory = null\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 选择分类\r\n      selectCategory: (category: Category | null) => {\r\n        set((state) => {\r\n          state.selectedCategory = category\r\n        })\r\n      },\r\n      \r\n      // 设置加载状态\r\n      setLoading: (loading: boolean) => {\r\n        set((state) => {\r\n          state.loading = loading\r\n        })\r\n      },\r\n      \r\n      // 设置创建状态\r\n      setCreating: (creating: boolean) => {\r\n        set((state) => {\r\n          state.creating = creating\r\n        })\r\n      },\r\n      \r\n      // 设置更新状态\r\n      setUpdating: (updating: boolean) => {\r\n        set((state) => {\r\n          state.updating = updating\r\n        })\r\n      },\r\n      \r\n      // 设置删除状态\r\n      setDeleting: (deleting: boolean) => {\r\n        set((state) => {\r\n          state.deleting = deleting\r\n        })\r\n      },\r\n      \r\n      // 设置错误\r\n      setError: (error: string | null) => {\r\n        set((state) => {\r\n          state.error = error\r\n        })\r\n      },\r\n      \r\n      // 清除错误\r\n      clearError: () => {\r\n        set((state) => {\r\n          state.error = null\r\n        })\r\n      },\r\n      \r\n      // 根据 ID 获取分类\r\n      getCategoryById: (id: string) => {\r\n        return get().categories.find(c => c.id === id)\r\n      },\r\n      \r\n      // 获取带有提示词数量的分类\r\n      getCategoriesWithPromptCount: () => {\r\n        return get().categories.filter(c => (c.promptCount ?? 0) > 0)\r\n      },\r\n      \r\n      // 获取热门分类（按提示词数量排序）\r\n      getPopularCategories: () => {\r\n        return get().categories\r\n          .filter(c => (c.promptCount ?? 0) > 0)\r\n          .sort((a, b) => (b.promptCount ?? 0) - (a.promptCount ?? 0))\r\n          .slice(0, 10)\r\n      },\r\n    })),\r\n    {\r\n      name: 'categories-store',\r\n    }\r\n  )\r\n)\r\n\r\n// 导出 Store 类型\r\nexport type { CategoriesStore }"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAsCO,MAAM,qBAAqB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnB,SAAS;QACT,YAAY,EAAE;QACd,kBAAkB;QAElB,SAAS;QACT,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QAEV,SAAS;QACT,OAAO;QAEP,SAAS;QACT,eAAe,CAAC;YACd,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG;YACrB;QACF;QAEA,OAAO;QACP,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,UAAU,CAAC,IAAI,CAAC;YACxB;QACF;QAEA,OAAO;QACP,gBAAgB,CAAC;YACf,IAAI,CAAC;gBACH,MAAM,QAAQ,MAAM,UAAU,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;gBAClE,IAAI,UAAU,CAAC,GAAG;oBAChB,MAAM,UAAU,CAAC,MAAM,GAAG;gBAC5B;gBACA,yBAAyB;gBACzB,IAAI,MAAM,gBAAgB,EAAE,OAAO,SAAS,EAAE,EAAE;oBAC9C,MAAM,gBAAgB,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO;QACP,gBAAgB,CAAC;YACf,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACzD,uBAAuB;gBACvB,IAAI,MAAM,gBAAgB,EAAE,OAAO,IAAI;oBACrC,MAAM,gBAAgB,GAAG;gBAC3B;YACF;QACF;QAEA,OAAO;QACP,gBAAgB,CAAC;YACf,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG;YAC3B;QACF;QAEA,SAAS;QACT,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG;YAClB;QACF;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,SAAS;QACT,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG;YACnB;QACF;QAEA,OAAO;QACP,UAAU,CAAC;YACT,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG;YAChB;QACF;QAEA,OAAO;QACP,YAAY;YACV,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG;YAChB;QACF;QAEA,aAAa;QACb,iBAAiB,CAAC;YAChB,OAAO,MAAM,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C;QAEA,eAAe;QACf,8BAA8B;YAC5B,OAAO,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,WAAW,IAAI,CAAC,IAAI;QAC7D;QAEA,mBAAmB;QACnB,sBAAsB;YACpB,OAAO,MAAM,UAAU,CACpB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,WAAW,IAAI,CAAC,IAAI,GACnC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,GACzD,KAAK,CAAC,GAAG;QACd;IACF,CAAC,IACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 5299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/navigation/CategoryTree.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { useCategoriesStore } from '~/stores/categories'\r\nimport { Category } from '~/types'\r\n\r\ninterface CategoryTreeProps {\r\n  categories: Category[]\r\n  onCategoryClick?: (category: Category) => void\r\n}\r\n\r\nexport const CategoryTree = ({ categories, onCategoryClick }: CategoryTreeProps) => {\r\n  const pathname = usePathname()\r\n  const { selectedCategory } = useCategoriesStore()\r\n  const [expandedCategories, setExpandedCategories] = useState<string[]>([])\r\n\r\n  // 检查分类是否被选中\r\n  const isSelected = (category: Category) => {\r\n    return selectedCategory?.id === category.id || pathname === `/categories/${category.id}`\r\n  }\r\n\r\n  // 切换分类展开/收起状态\r\n  const toggleCategory = (categoryId: string) => {\r\n    setExpandedCategories(prev => \r\n      prev.includes(categoryId)\r\n        ? prev.filter(id => id !== categoryId)\r\n        : [...prev, categoryId]\r\n    )\r\n  }\r\n\r\n  // 处理分类点击\r\n  const handleCategoryClick = (category: Category) => {\r\n    if (onCategoryClick) {\r\n      onCategoryClick(category)\r\n    }\r\n  }\r\n\r\n  // 构建分类树结构\r\n  const buildCategoryTree = (categories: Category[], parentId: string | null = null): Category[] => {\r\n    return categories\r\n      .filter(cat => cat.parentId === parentId)\r\n      .sort((a, b) => a.name.localeCompare(b.name))\r\n  }\r\n\r\n  // 渲染分类项\r\n  const renderCategoryItem = (category: Category, level: number = 0) => {\r\n    const hasChildren = categories.some(cat => cat.parentId === category.id)\r\n    const isExpanded = expandedCategories.includes(category.id)\r\n    const children = hasChildren ? buildCategoryTree(categories, category.id) : []\r\n\r\n    return (\r\n      <div key={category.id} className=\"w-full\">\r\n        <div className=\"flex items-center w-full\">\r\n          {/* 缩进 */}\r\n          {level > 0 && (\r\n            <div className=\"flex items-center\">\r\n              {Array.from({ length: level }, (_, i) => (\r\n                <div key={i} className=\"w-4 flex justify-center\">\r\n                  <div className=\"w-px h-6 bg-base-300\"></div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n\r\n          {/* 展开/收起按钮 */}\r\n          <div className=\"flex items-center\">\r\n            {hasChildren ? (\r\n              <button\r\n                onClick={() => toggleCategory(category.id)}\r\n                className=\"p-1 hover:bg-base-200 rounded transition-colors\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-3 h-3 transition-transform ${\r\n                    isExpanded ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n            ) : (\r\n              <div className=\"w-5 h-5 flex items-center justify-center\">\r\n                <div className=\"w-1 h-1 bg-base-300 rounded-full\"></div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 分类链接 */}\r\n          <Link\r\n            href={`/categories/${category.id}`}\r\n            onClick={() => handleCategoryClick(category)}\r\n            className={`flex items-center justify-between flex-1 px-2 py-1.5 text-sm rounded-lg transition-colors group ${\r\n              isSelected(category)\r\n                ? 'bg-primary text-primary-content'\r\n                : 'text-base-content hover:bg-base-200'\r\n            }`}\r\n          >\r\n            <div className=\"flex items-center space-x-2 min-w-0\">\r\n              {/* 分类颜色指示器 */}\r\n              <div\r\n                className=\"w-3 h-3 rounded-full flex-shrink-0\"\r\n                style={{ backgroundColor: category.color }}\r\n              />\r\n              \r\n              {/* 分类名称 */}\r\n              <span className=\"truncate font-medium\">{category.name}</span>\r\n            </div>\r\n\r\n            {/* 提示词数量 */}\r\n            {category.promptCount !== undefined && category.promptCount > 0 && (\r\n              <span className={`text-xs px-2 py-0.5 rounded-full flex-shrink-0 ${\r\n                isSelected(category)\r\n                  ? 'bg-primary-content/20 text-primary-content'\r\n                  : 'bg-base-200 text-base-content/70 group-hover:bg-base-300'\r\n              }`}>\r\n                {category.promptCount}\r\n              </span>\r\n            )}\r\n          </Link>\r\n        </div>\r\n\r\n        {/* 子分类 */}\r\n        {hasChildren && isExpanded && (\r\n          <div className=\"ml-2\">\r\n            {children.map(child => renderCategoryItem(child, level + 1))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 获取根分类\r\n  const rootCategories = buildCategoryTree(categories)\r\n\r\n  if (rootCategories.length === 0) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-base-content/70 text-center\">\r\n        暂无分类\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-1\">\r\n      {rootCategories.map(category => renderCategoryItem(category))}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,MAAM,eAAe,CAAC,EAAE,UAAU,EAAE,eAAe,EAAqB;IAC7E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD;IAC9C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEzE,YAAY;IACZ,MAAM,aAAa,CAAC;QAClB,OAAO,kBAAkB,OAAO,SAAS,EAAE,IAAI,aAAa,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;IAC1F;IAEA,cAAc;IACd,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,cACzB;mBAAI;gBAAM;aAAW;IAE7B;IAEA,SAAS;IACT,MAAM,sBAAsB,CAAC;QAC3B,IAAI,iBAAiB;YACnB,gBAAgB;QAClB;IACF;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC,YAAwB,WAA0B,IAAI;QAC/E,OAAO,WACJ,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,UAC/B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;IAC/C;IAEA,QAAQ;IACR,MAAM,qBAAqB,CAAC,UAAoB,QAAgB,CAAC;QAC/D,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,SAAS,EAAE;QACvE,MAAM,aAAa,mBAAmB,QAAQ,CAAC,SAAS,EAAE;QAC1D,MAAM,WAAW,cAAc,kBAAkB,YAAY,SAAS,EAAE,IAAI,EAAE;QAE9E,qBACE,8OAAC;YAAsB,WAAU;;8BAC/B,8OAAC;oBAAI,WAAU;;wBAEZ,QAAQ,mBACP,8OAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAM,GAAG,CAAC,GAAG,kBACjC,8OAAC;oCAAY,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;;;;;mCADP;;;;;;;;;;sCAQhB,8OAAC;4BAAI,WAAU;sCACZ,4BACC,8OAAC;gCACC,SAAS,IAAM,eAAe,SAAS,EAAE;gCACzC,WAAU;0CAEV,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAW,CAAC,6BAA6B,EACvC,aAAa,cAAc,IAC3B;8CAEF,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;yFAKR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;sCAMrB,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;4BAClC,SAAS,IAAM,oBAAoB;4BACnC,WAAW,CAAC,gGAAgG,EAC1G,WAAW,YACP,oCACA,uCACJ;;8CAEF,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,SAAS,KAAK;4CAAC;;;;;;sDAI3C,8OAAC;4CAAK,WAAU;sDAAwB,SAAS,IAAI;;;;;;;;;;;;gCAItD,SAAS,WAAW,KAAK,aAAa,SAAS,WAAW,GAAG,mBAC5D,8OAAC;oCAAK,WAAW,CAAC,+CAA+C,EAC/D,WAAW,YACP,+CACA,4DACJ;8CACC,SAAS,WAAW;;;;;;;;;;;;;;;;;;gBAO5B,eAAe,4BACd,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAA,QAAS,mBAAmB,OAAO,QAAQ;;;;;;;WAjFrD,SAAS,EAAE;;;;;IAsFzB;IAEA,QAAQ;IACR,MAAM,iBAAiB,kBAAkB;IAEzC,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,qBACE,8OAAC;YAAI,WAAU;sBAAqD;;;;;;IAIxE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,eAAe,GAAG,CAAC,CAAA,WAAY,mBAAmB;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 5513, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/navigation/PopularTags.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\n\r\ninterface PopularTagsProps {\r\n  limit?: number\r\n  onTagClick?: (tag: string) => void\r\n}\r\n\r\nexport const PopularTags = ({ limit = 10, onTagClick }: PopularTagsProps) => {\r\n  const pathname = usePathname()\r\n  const [selectedTag, setSelectedTag] = useState<string | null>(null)\r\n\r\n  // 获取热门标签\r\n  const { data: tags, isLoading, error } = api.tags.getPopular.useQuery({\r\n    limit,\r\n  })\r\n\r\n  // 处理标签点击\r\n  const handleTagClick = (tag: string) => {\r\n    setSelectedTag(tag)\r\n    if (onTagClick) {\r\n      onTagClick(tag)\r\n    }\r\n  }\r\n\r\n  // 检查标签是否被选中\r\n  const isSelected = (tag: string) => {\r\n    return selectedTag === tag || pathname === `/tags/${encodeURIComponent(tag)}`\r\n  }\r\n\r\n  // 加载状态\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-2\">\r\n        {Array.from({ length: 5 }, (_, i) => (\r\n          <div key={i} className=\"flex items-center space-x-2 px-3 py-2\">\r\n            <div className=\"w-2 h-2 bg-base-300 rounded-full animate-pulse\"></div>\r\n            <div className=\"h-3 bg-base-300 rounded animate-pulse flex-1\"></div>\r\n            <div className=\"w-6 h-4 bg-base-300 rounded animate-pulse\"></div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 错误状态\r\n  if (error) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-error\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-4 h-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n            />\r\n          </svg>\r\n          <span>加载标签失败</span>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 空状态\r\n  if (!tags || tags.length === 0) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-base-content/70 text-center\">\r\n        暂无热门标签\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染标签列表\r\n  return (\r\n    <div className=\"space-y-1\">\r\n      {tags.map((tag) => (\r\n        <Link\r\n          key={tag.name}\r\n          href={`/tags/${encodeURIComponent(tag.name)}`}\r\n          onClick={() => handleTagClick(tag.name)}\r\n          className={`flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors group ${\r\n            isSelected(tag.name)\r\n              ? 'bg-primary text-primary-content'\r\n              : 'text-base-content hover:bg-base-200'\r\n          }`}\r\n        >\r\n          <div className=\"flex items-center space-x-2 min-w-0\">\r\n            {/* 标签图标 */}\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-3 h-3 flex-shrink-0\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n              />\r\n            </svg>\r\n\r\n            {/* 标签名称 */}\r\n            <span className=\"truncate\">{tag.name}</span>\r\n          </div>\r\n\r\n          {/* 使用次数 */}\r\n          {tag.count > 0 && (\r\n            <span className={`text-xs px-2 py-0.5 rounded-full flex-shrink-0 ${\r\n              isSelected(tag.name)\r\n                ? 'bg-primary-content/20 text-primary-content'\r\n                : 'bg-base-200 text-base-content/70 group-hover:bg-base-300'\r\n            }`}>\r\n              {tag.count}\r\n            </span>\r\n          )}\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,EAAE,UAAU,EAAoB;IACtE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,SAAS;IACT,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACpE;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,IAAI,YAAY;YACd,WAAW;QACb;IACF;IAEA,YAAY;IACZ,MAAM,aAAa,CAAC;QAClB,OAAO,gBAAgB,OAAO,aAAa,CAAC,MAAM,EAAE,mBAAmB,MAAM;IAC/E;IAEA,OAAO;IACP,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;mBAHP;;;;;;;;;;IAQlB;IAEA,OAAO;IACP,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,8OAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,MAAM;IACN,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;sBAAqD;;;;;;IAIxE;IAEA,SAAS;IACT,qBACE,8OAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC,4JAAA,CAAA,UAAI;gBAEH,MAAM,CAAC,MAAM,EAAE,mBAAmB,IAAI,IAAI,GAAG;gBAC7C,SAAS,IAAM,eAAe,IAAI,IAAI;gBACtC,WAAW,CAAC,uFAAuF,EACjG,WAAW,IAAI,IAAI,IACf,oCACA,uCACJ;;kCAEF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;0CAKN,8OAAC;gCAAK,WAAU;0CAAY,IAAI,IAAI;;;;;;;;;;;;oBAIrC,IAAI,KAAK,GAAG,mBACX,8OAAC;wBAAK,WAAW,CAAC,+CAA+C,EAC/D,WAAW,IAAI,IAAI,IACf,+CACA,4DACJ;kCACC,IAAI,KAAK;;;;;;;eArCT,IAAI,IAAI;;;;;;;;;;AA4CvB", "debugId": null}}, {"offset": {"line": 5715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/navigation/RecentPrompts.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { api } from '~/trpc/react'\r\n\r\ninterface RecentPromptsProps {\r\n  limit?: number\r\n  onPromptClick?: (promptId: string) => void\r\n}\r\n\r\nexport const RecentPrompts = ({ limit = 10, onPromptClick }: RecentPromptsProps) => {\r\n  const pathname = usePathname()\r\n  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null)\r\n\r\n  // 获取最近使用的提示词\r\n  const { data: prompts, isLoading, error } = api.prompts.getRecent.useQuery({\r\n    limit,\r\n  })\r\n\r\n  // 处理提示词点击\r\n  const handlePromptClick = (promptId: string) => {\r\n    setSelectedPrompt(promptId)\r\n    if (onPromptClick) {\r\n      onPromptClick(promptId)\r\n    }\r\n  }\r\n\r\n  // 检查提示词是否被选中\r\n  const isSelected = (promptId: string) => {\r\n    return selectedPrompt === promptId || pathname === `/prompts/${promptId}`\r\n  }\r\n\r\n  // 格式化时间\r\n  const formatTime = (date: Date) => {\r\n    const now = new Date()\r\n    const diffMs = now.getTime() - date.getTime()\r\n    const diffMins = Math.floor(diffMs / (1000 * 60))\r\n    const diffHours = Math.floor(diffMins / 60)\r\n    const diffDays = Math.floor(diffHours / 24)\r\n\r\n    if (diffMins < 1) return '刚刚'\r\n    if (diffMins < 60) return `${diffMins}分钟前`\r\n    if (diffHours < 24) return `${diffHours}小时前`\r\n    if (diffDays < 7) return `${diffDays}天前`\r\n    return date.toLocaleDateString('zh-CN', {\r\n      month: 'short',\r\n      day: 'numeric',\r\n    })\r\n  }\r\n\r\n  // 加载状态\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-2\">\r\n        {Array.from({ length: 5 }, (_, i) => (\r\n          <div key={i} className=\"flex items-center space-x-2 px-3 py-2\">\r\n            <div className=\"w-2 h-2 bg-base-300 rounded-full animate-pulse\"></div>\r\n            <div className=\"flex-1 space-y-1\">\r\n              <div className=\"h-3 bg-base-300 rounded animate-pulse\"></div>\r\n              <div className=\"h-2 bg-base-300 rounded animate-pulse w-2/3\"></div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 错误状态\r\n  if (error) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-error\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-4 h-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n            />\r\n          </svg>\r\n          <span>加载失败</span>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 空状态\r\n  if (!prompts || prompts.length === 0) {\r\n    return (\r\n      <div className=\"px-3 py-2 text-sm text-base-content/70 text-center\">\r\n        暂无最近使用\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 渲染提示词列表\r\n  return (\r\n    <div className=\"space-y-1\">\r\n      {prompts.map((prompt) => (\r\n        <Link\r\n          key={prompt.id}\r\n          href={`/prompts/${prompt.id}`}\r\n          onClick={() => handlePromptClick(prompt.id)}\r\n          className={`block px-3 py-2 text-sm rounded-lg transition-colors group ${\r\n            isSelected(prompt.id)\r\n              ? 'bg-primary text-primary-content'\r\n              : 'text-base-content hover:bg-base-200'\r\n          }`}\r\n        >\r\n          <div className=\"flex items-start space-x-2\">\r\n            {/* 提示词图标 */}\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-3 h-3 flex-shrink-0 mt-0.5\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n              />\r\n            </svg>\r\n\r\n            <div className=\"flex-1 min-w-0\">\r\n              {/* 提示词标题 */}\r\n              <div className=\"font-medium truncate\">{prompt.title}</div>\r\n              \r\n              {/* 最后使用时间 */}\r\n              <div className={`text-xs mt-0.5 ${\r\n                isSelected(prompt.id)\r\n                  ? 'text-primary-content/70'\r\n                  : 'text-base-content/70'\r\n              }`}>\r\n                {formatTime(prompt.lastUsedAt!)}\r\n              </div>\r\n            </div>\r\n\r\n            {/* 使用次数 */}\r\n            {prompt.usageCount > 0 && (\r\n              <span className={`text-xs px-1.5 py-0.5 rounded-full flex-shrink-0 ${\r\n                isSelected(prompt.id)\r\n                  ? 'bg-primary-content/20 text-primary-content'\r\n                  : 'bg-base-200 text-base-content/70 group-hover:bg-base-300'\r\n              }`}>\r\n                {prompt.usageCount}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,EAAE,aAAa,EAAsB;IAC7E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,aAAa;IACb,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;QACzE;IACF;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,aAAa;IACb,MAAM,aAAa,CAAC;QAClB,OAAO,mBAAmB,YAAY,aAAa,CAAC,SAAS,EAAE,UAAU;IAC3E;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QAC/C,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;QACxC,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QAExC,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,IAAI,OAAO,GAAG,SAAS,GAAG,CAAC;QAC1C,IAAI,YAAY,IAAI,OAAO,GAAG,UAAU,GAAG,CAAC;QAC5C,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,EAAE,CAAC;QACxC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,OAAO;YACP,KAAK;QACP;IACF;IAEA,OAAO;IACP,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;mBAJT;;;;;;;;;;IAUlB;IAEA,OAAO;IACP,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,8OAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,MAAM;IACN,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,qBACE,8OAAC;YAAI,WAAU;sBAAqD;;;;;;IAIxE;IAEA,UAAU;IACV,qBACE,8OAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,4JAAA,CAAA,UAAI;gBAEH,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;gBAC7B,SAAS,IAAM,kBAAkB,OAAO,EAAE;gBAC1C,WAAW,CAAC,2DAA2D,EACrE,WAAW,OAAO,EAAE,IAChB,oCACA,uCACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;sCAIN,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CAAwB,OAAO,KAAK;;;;;;8CAGnD,8OAAC;oCAAI,WAAW,CAAC,eAAe,EAC9B,WAAW,OAAO,EAAE,IAChB,4BACA,wBACJ;8CACC,WAAW,OAAO,UAAU;;;;;;;;;;;;wBAKhC,OAAO,UAAU,GAAG,mBACnB,8OAAC;4BAAK,WAAW,CAAC,iDAAiD,EACjE,WAAW,OAAO,EAAE,IAChB,+CACA,4DACJ;sCACC,OAAO,UAAU;;;;;;;;;;;;eA/CnB,OAAO,EAAE;;;;;;;;;;AAuDxB", "debugId": null}}, {"offset": {"line": 5957, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport Link from 'next/link'\r\nimport { usePathname } from 'next/navigation'\r\nimport { useMainStore } from '~/stores'\r\nimport { useCategoriesStore } from '~/stores/categories'\r\nimport { CategoryTree } from '~/components/navigation/CategoryTree'\r\nimport { PopularTags } from '~/components/navigation/PopularTags'\r\nimport { RecentPrompts } from '~/components/navigation/RecentPrompts'\r\n\r\nexport const Sidebar = () => {\r\n  const pathname = usePathname()\r\n  const { ui } = useMainStore()\r\n  const { categories } = useCategoriesStore()\r\n  const [expandedSections, setExpandedSections] = useState({\r\n    categories: true,\r\n    tags: true,\r\n    recent: true,\r\n  })\r\n\r\n  const toggleSection = (section: keyof typeof expandedSections) => {\r\n    setExpandedSections(prev => ({\r\n      ...prev,\r\n      [section]: !prev[section],\r\n    }))\r\n  }\r\n\r\n  const navigationItems = [\r\n    {\r\n      href: '/',\r\n      label: '首页',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/prompts',\r\n      label: '所有提示词',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/favorites',\r\n      label: '收藏夹',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/tags',\r\n      label: '标签管理',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n          />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      href: '/stats',\r\n      label: '统计分析',\r\n      icon: (\r\n        <svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          strokeWidth={1.5}\r\n          stroke=\"currentColor\"\r\n          className=\"w-5 h-5\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            d=\"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z\"\r\n          />\r\n        </svg>\r\n      ),\r\n    },\r\n  ]\r\n\r\n  const isActive = (href: string) => {\r\n    if (href === '/') {\r\n      return pathname === '/'\r\n    }\r\n    return pathname.startsWith(href)\r\n  }\r\n\r\n  return (\r\n    <aside\r\n      className={`fixed inset-y-0 left-0 z-40 w-64 bg-base-100 border-r border-base-200 transform transition-transform duration-300 ease-in-out ${\r\n        ui.sidebarOpen ? 'translate-x-0' : '-translate-x-full'\r\n      } lg:translate-x-0 lg:static lg:inset-0`}\r\n    >\r\n      <div className=\"flex flex-col h-full\">\r\n        {/* 侧边栏头部 */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-base-200\">\r\n          <h2 className=\"text-lg font-semibold text-base-content\">导航</h2>\r\n          <button\r\n            className=\"btn btn-ghost btn-sm lg:hidden\"\r\n            onClick={() => useMainStore.getState().toggleSidebar()}\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M6 18L18 6M6 6l12 12\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* 滚动内容区域 */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"p-4 space-y-4\">\r\n            {/* 主导航 */}\r\n            <nav className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\r\n                    isActive(item.href)\r\n                      ? 'bg-primary text-primary-content'\r\n                      : 'text-base-content hover:bg-base-200'\r\n                  }`}\r\n                >\r\n                  {item.icon}\r\n                  <span>{item.label}</span>\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* 分类部分 */}\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => toggleSection('categories')}\r\n                className=\"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-base-content hover:bg-base-200 rounded-lg\"\r\n              >\r\n                <span className=\"flex items-center space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n                    />\r\n                  </svg>\r\n                  <span>分类</span>\r\n                </span>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-4 h-4 transform transition-transform ${\r\n                    expandedSections.categories ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              {expandedSections.categories && (\r\n                <div className=\"ml-4 space-y-1\">\r\n                  {/* 分类导航树 */}\r\n                  <CategoryTree categories={categories} />\r\n                  \r\n                  {/* 添加分类按钮 */}\r\n                  <div className=\"pt-2\">\r\n                    <Link\r\n                      href=\"/categories\"\r\n                      className=\"flex items-center space-x-2 px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded-lg\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        strokeWidth={1.5}\r\n                        stroke=\"currentColor\"\r\n                        className=\"w-4 h-4\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          d=\"M12 4.5v15m7.5-7.5h-15\"\r\n                        />\r\n                      </svg>\r\n                      <span>新增分类</span>\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 标签部分 */}\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => toggleSection('tags')}\r\n                className=\"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-base-content hover:bg-base-200 rounded-lg\"\r\n              >\r\n                <span className=\"flex items-center space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\r\n                    />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6z\" />\r\n                  </svg>\r\n                  <span>常用标签</span>\r\n                </span>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-4 h-4 transform transition-transform ${\r\n                    expandedSections.tags ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              {expandedSections.tags && (\r\n                <div className=\"ml-4 space-y-1\">\r\n                  {/* 热门标签 */}\r\n                  <PopularTags limit={8} />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 最近使用部分 */}\r\n            <div className=\"space-y-2\">\r\n              <button\r\n                onClick={() => toggleSection('recent')}\r\n                className=\"flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-base-content hover:bg-base-200 rounded-lg\"\r\n              >\r\n                <span className=\"flex items-center space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                  <span>最近使用</span>\r\n                </span>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-4 h-4 transform transition-transform ${\r\n                    expandedSections.recent ? 'rotate-90' : ''\r\n                  }`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M8.25 4.5l7.5 7.5-7.5 7.5\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              {expandedSections.recent && (\r\n                <div className=\"ml-4 space-y-1\">\r\n                  {/* 最近使用的提示词 */}\r\n                  <RecentPrompts limit={6} />\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 侧边栏底部 */}\r\n        <div className=\"p-4 border-t border-base-200\">\r\n          <Link\r\n            href=\"/prompts/new\"\r\n            className=\"btn btn-primary btn-sm w-full\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M12 4.5v15m7.5-7.5h-15\"\r\n              />\r\n            </svg>\r\n            新建提示词\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 移动端遮罩层 */}\r\n      {ui.sidebarOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black/50 z-30 lg:hidden\"\r\n          onClick={() => useMainStore.getState().toggleSidebar()}\r\n        />\r\n      )}\r\n    </aside>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,MAAM,UAAU;IACrB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAC1B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD;IACxC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,YAAY;QACZ,MAAM;QACN,QAAQ;IACV;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,8OAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,8OAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,8OAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;;kCAEV,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,GAAE;;;;;;kCAEJ,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,GAAE;;;;;;;;;;;;QAG3D;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBACC,OAAM;gBACN,MAAK;gBACL,SAAQ;gBACR,aAAa;gBACb,QAAO;gBACP,WAAU;0BAEV,cAAA,8OAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,GAAE;;;;;;;;;;;QAIV;KACD;IAED,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,8HAA8H,EACxI,GAAG,WAAW,GAAG,kBAAkB,oBACpC,sCAAsC,CAAC;;0BAExC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,sHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;0CAEpD,cAAA,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAOV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,uFAAuF,EACjG,SAAS,KAAK,IAAI,IACd,oCACA,uCACJ;;gDAED,KAAK,IAAI;8DACV,8OAAC;8DAAM,KAAK,KAAK;;;;;;;2CATZ,KAAK,IAAI;;;;;;;;;;8CAepB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;sEAGN,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAW,CAAC,uCAAuC,EACjD,iBAAiB,UAAU,GAAG,cAAc,IAC5C;8DAEF,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;wCAKP,iBAAiB,UAAU,kBAC1B,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC,gJAAA,CAAA,eAAY;oDAAC,YAAY;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEACC,OAAM;gEACN,MAAK;gEACL,SAAQ;gEACR,aAAa;gEACb,QAAO;gEACP,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;;;;;;0EAGN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;;8EAEV,8OAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,GAAE;;;;;;8EAEJ,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,GAAE;;;;;;;;;;;;sEAEvD,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAW,CAAC,uCAAuC,EACjD,iBAAiB,IAAI,GAAG,cAAc,IACtC;8DAEF,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;wCAKP,iBAAiB,IAAI,kBACpB,8OAAC;4CAAI,WAAU;sDAEb,cAAA,8OAAC,+IAAA,CAAA,cAAW;gDAAC,OAAO;;;;;;;;;;;;;;;;;8CAM1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DACC,OAAM;4DACN,MAAK;4DACL,SAAQ;4DACR,aAAa;4DACb,QAAO;4DACP,WAAU;sEAEV,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,GAAE;;;;;;;;;;;sEAGN,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAW,CAAC,uCAAuC,EACjD,iBAAiB,MAAM,GAAG,cAAc,IACxC;8DAEF,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;wCAKP,iBAAiB,MAAM,kBACtB,8OAAC;4CAAI,WAAU;sDAEb,cAAA,8OAAC,iJAAA,CAAA,gBAAa;gDAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;gCAEA;;;;;;;;;;;;;;;;;;YAOX,GAAG,WAAW,kBACb,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,sHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;;;;;;;;;;;;AAK9D", "debugId": null}}, {"offset": {"line": 6626, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/transitions/PageTransition.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { usePathname } from 'next/navigation'\r\nimport { useEffect, useState } from 'react'\r\n\r\ninterface PageTransitionProps {\r\n  children: React.ReactNode\r\n  className?: string\r\n}\r\n\r\n// 页面过渡动画配置\r\nconst pageVariants = {\r\n  initial: {\r\n    opacity: 0,\r\n    y: 20,\r\n    scale: 0.98,\r\n  },\r\n  in: {\r\n    opacity: 1,\r\n    y: 0,\r\n    scale: 1,\r\n  },\r\n  out: {\r\n    opacity: 0,\r\n    y: -20,\r\n    scale: 1.02,\r\n  },\r\n}\r\n\r\nconst pageTransition = {\r\n  type: 'tween',\r\n  ease: 'anticipate',\r\n  duration: 0.4,\r\n}\r\n\r\nexport const PageTransition = ({ children, className = '' }: PageTransitionProps) => {\r\n  const pathname = usePathname()\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  useEffect(() => {\r\n    // 页面切换时显示加载状态\r\n    setIsLoading(true)\r\n    const timer = setTimeout(() => setIsLoading(false), 100)\r\n    return () => clearTimeout(timer)\r\n  }, [pathname])\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      {!isLoading && (\r\n        <motion.div\r\n          key={pathname}\r\n          initial=\"initial\"\r\n          animate=\"in\"\r\n          exit=\"out\"\r\n          variants={pageVariants}\r\n          transition={pageTransition}\r\n          className={className}\r\n        >\r\n          {children}\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 路由切换加载指示器\r\nexport const RouteProgress = () => {\r\n  const pathname = usePathname()\r\n  const [isLoading, setIsLoading] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setIsLoading(true)\r\n    const timer = setTimeout(() => setIsLoading(false), 400)\r\n    return () => clearTimeout(timer)\r\n  }, [pathname])\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isLoading && (\r\n        <motion.div\r\n          initial={{ scaleX: 0 }}\r\n          animate={{ scaleX: 1 }}\r\n          exit={{ scaleX: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n          className=\"fixed top-0 left-0 right-0 h-1 bg-primary z-50 origin-left\"\r\n        />\r\n      )}\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 页面滑入动画\r\nexport const SlideInPage = ({ children, direction = 'right' }: {\r\n  children: React.ReactNode\r\n  direction?: 'left' | 'right' | 'up' | 'down'\r\n}) => {\r\n  const pathname = usePathname()\r\n\r\n  const getInitialPosition = () => {\r\n    switch (direction) {\r\n      case 'left': return { x: -100, opacity: 0 }\r\n      case 'right': return { x: 100, opacity: 0 }\r\n      case 'up': return { y: -100, opacity: 0 }\r\n      case 'down': return { y: 100, opacity: 0 }\r\n      default: return { x: 100, opacity: 0 }\r\n    }\r\n  }\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={getInitialPosition()}\r\n        animate={{ x: 0, y: 0, opacity: 1 }}\r\n        exit={{ x: direction === 'left' ? 100 : -100, opacity: 0 }}\r\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 淡入淡出页面过渡\r\nexport const FadeTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 缩放页面过渡\r\nexport const ScaleTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ scale: 0.95, opacity: 0 }}\r\n        animate={{ scale: 1, opacity: 1 }}\r\n        exit={{ scale: 1.05, opacity: 0 }}\r\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 旋转页面过渡\r\nexport const RotateTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ rotate: -5, opacity: 0 }}\r\n        animate={{ rotate: 0, opacity: 1 }}\r\n        exit={{ rotate: 5, opacity: 0 }}\r\n        transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 弹跳页面过渡\r\nexport const BounceTransition = ({ children }: { children: React.ReactNode }) => {\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      <motion.div\r\n        key={pathname}\r\n        initial={{ y: -100, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        exit={{ y: 100, opacity: 0 }}\r\n        transition={{ type: 'spring', bounce: 0.4, duration: 0.6 }}\r\n      >\r\n        {children}\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}\r\n\r\n// 页面过渡容器\r\nexport const PageTransitionContainer = ({ \r\n  children, \r\n  variant = 'default',\r\n  className = '' \r\n}: {\r\n  children: React.ReactNode\r\n  variant?: 'default' | 'slide' | 'fade' | 'scale' | 'rotate' | 'bounce'\r\n  className?: string\r\n}) => {\r\n  const TransitionComponent = {\r\n    default: PageTransition,\r\n    slide: SlideInPage,\r\n    fade: FadeTransition,\r\n    scale: ScaleTransition,\r\n    rotate: RotateTransition,\r\n    bounce: BounceTransition,\r\n  }[variant]\r\n\r\n  return (\r\n    <div className={`min-h-screen ${className}`}>\r\n      <RouteProgress />\r\n      <TransitionComponent>\r\n        {children}\r\n      </TransitionComponent>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAWA,WAAW;AACX,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,IAAI;QACF,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,KAAK;QACH,SAAS;QACT,GAAG,CAAC;QACJ,OAAO;IACT;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEO,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAuB;IAC9E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;QACd,aAAa;QACb,MAAM,QAAQ,WAAW,IAAM,aAAa,QAAQ;QACpD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACnB,CAAC,2BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;YACZ,WAAW;sBAEV;WARI;;;;;;;;;;AAaf;AAGO,MAAM,gBAAgB;IAC3B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,MAAM,QAAQ,WAAW,IAAM,aAAa,QAAQ;QACpD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,QAAQ;YAAE;YACrB,SAAS;gBAAE,QAAQ;YAAE;YACrB,MAAM;gBAAE,QAAQ;YAAE;YAClB,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;;;;;;;;;;AAKpB;AAGO,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,YAAY,OAAO,EAG1D;IACC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBAAQ,OAAO;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;YAC1C,KAAK;gBAAS,OAAO;oBAAE,GAAG;oBAAK,SAAS;gBAAE;YAC1C,KAAK;gBAAM,OAAO;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;YACxC,KAAK;gBAAQ,OAAO;oBAAE,GAAG;oBAAK,SAAS;gBAAE;YACzC;gBAAS,OAAO;oBAAE,GAAG;oBAAK,SAAS;gBAAE;QACvC;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;YACT,SAAS;gBAAE,GAAG;gBAAG,GAAG;gBAAG,SAAS;YAAE;YAClC,MAAM;gBAAE,GAAG,cAAc,SAAS,MAAM,CAAC;gBAAK,SAAS;YAAE;YACzD,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;sBAE7D;WANI;;;;;;;;;;AAUb;AAGO,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAiC;IACxE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;sBAE3B;WANI;;;;;;;;;;AAUb;AAGO,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAiC;IACzE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,OAAO;gBAAM,SAAS;YAAE;YACnC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,MAAM;gBAAE,OAAO;gBAAM,SAAS;YAAE;YAChC,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;sBAE7D;WANI;;;;;;;;;;AAUb;AAGO,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAiC;IAC1E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,QAAQ,CAAC;gBAAG,SAAS;YAAE;YAClC,SAAS;gBAAE,QAAQ;gBAAG,SAAS;YAAE;YACjC,MAAM;gBAAE,QAAQ;gBAAG,SAAS;YAAE;YAC9B,YAAY;gBAAE,MAAM;gBAAS,MAAM;gBAAa,UAAU;YAAI;sBAE7D;WANI;;;;;;;;;;AAUb;AAGO,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAiC;IAC1E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,GAAG,CAAC;gBAAK,SAAS;YAAE;YAC/B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,MAAM;gBAAE,GAAG;gBAAK,SAAS;YAAE;YAC3B,YAAY;gBAAE,MAAM;gBAAU,QAAQ;gBAAK,UAAU;YAAI;sBAExD;WANI;;;;;;;;;;AAUb;AAGO,MAAM,0BAA0B,CAAC,EACtC,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EAKf;IACC,MAAM,sBAAsB;QAC1B,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV,CAAC,CAAC,QAAQ;IAEV,qBACE,8OAAC;QAAI,WAAW,CAAC,aAAa,EAAE,WAAW;;0BACzC,8OAAC;;;;;0BACD,8OAAC;0BACE;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 6964, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/transitions/index.ts"], "sourcesContent": ["export {\r\n  PageTransition,\r\n  RouteProgress,\r\n  SlideInPage,\r\n  FadeTransition,\r\n  ScaleTransition,\r\n  RotateTransition,\r\n  BounceTransition,\r\n  PageTransitionContainer,\r\n} from './PageTransition'"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 6978, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect } from 'react'\r\nimport { useAuth } from '~/lib/auth/context'\r\nimport { useMainStore } from '~/stores'\r\nimport { Header } from './Header'\r\nimport { Sidebar } from './Sidebar'\r\nimport { PageTransition } from '~/components/transitions'\r\n\r\ninterface MainLayoutProps {\r\n  children: React.ReactNode\r\n}\r\n\r\nexport const MainLayout = ({ children }: MainLayoutProps) => {\r\n  const { user, loading } = useAuth()\r\n  const { ui } = useMainStore()\r\n\r\n  // 初始化主题\r\n  useEffect(() => {\r\n    document.documentElement.setAttribute('data-theme', ui.theme)\r\n  }, [ui.theme])\r\n\r\n  // 如果正在加载认证状态，显示加载页面\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n        <div className=\"text-center\">\r\n          <div className=\"loading loading-spinner loading-lg\"></div>\r\n          <p className=\"mt-4 text-base-content/70\">加载中...</p>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // 如果未登录，显示登录提示\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center bg-base-200\">\r\n        <div className=\"card w-96 bg-base-100 shadow-xl\">\r\n          <div className=\"card-body text-center\">\r\n            <h2 className=\"card-title justify-center\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-8 h-8 text-primary\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z\"\r\n                />\r\n              </svg>\r\n              需要登录\r\n            </h2>\r\n            <p className=\"text-base-content/70\">\r\n              请先登录以访问提示词管理工具\r\n            </p>\r\n            <div className=\"card-actions justify-center mt-4\">\r\n              <a href=\"/auth/login\" className=\"btn btn-primary\">\r\n                前往登录\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-base-200\">\r\n      {/* 头部导航 */}\r\n      <Header />\r\n\r\n      <div className=\"flex\">\r\n        {/* 侧边栏 */}\r\n        <Sidebar />\r\n\r\n        {/* 主内容区域 */}\r\n        <main\r\n          className={`flex-1 min-h-screen transition-all duration-300 ease-in-out ${\r\n            ui.sidebarOpen ? 'lg:ml-0' : 'lg:ml-0'\r\n          }`}\r\n        >\r\n          <div className=\"p-6\">\r\n            <PageTransition>\r\n              {children}\r\n            </PageTransition>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAPA;;;;;;;;AAaO,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAE1B,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,eAAe,CAAC,YAAY,CAAC,cAAc,GAAG,KAAK;IAC9D,GAAG;QAAC,GAAG,KAAK;KAAC;IAEb,oBAAoB;IACpB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;;IAIjD;IAEA,eAAe;IACf,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;gCAEA;;;;;;;sCAGR,8OAAC;4BAAE,WAAU;sCAAuB;;;;;;sCAGpC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,MAAK;gCAAc,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ9D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,uIAAA,CAAA,UAAO;;;;;kCAGR,8OAAC;wBACC,WAAW,CAAC,4DAA4D,EACtE,GAAG,WAAW,GAAG,YAAY,WAC7B;kCAEF,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mJAAA,CAAA,iBAAc;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}