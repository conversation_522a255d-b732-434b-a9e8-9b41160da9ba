import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { PromptForm } from '~/components/forms/PromptForm'

// Mock 依赖
vi.mock('~/hooks/useAutoSave', () => ({
  useAutoSave: () => ({
    isSaving: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    saveNow: vi.fn(),
    clearSaved: vi.fn(),
  }),
}))

vi.mock('~/trpc/react', () => ({
  api: {
    prompts: {
      create: {
        useMutation: () => ({
          mutate: vi.fn(),
          isLoading: false,
        }),
      },
      update: {
        useMutation: () => ({
          mutate: vi.fn(),
          isLoading: false,
        }),
      },
    },
    categories: {
      getAll: {
        useQuery: () => ({
          data: [
            { id: '1', name: '工作', color: '#3B82F6' },
            { id: '2', name: '学习', color: '#10B981' },
          ],
          isLoading: false,
        }),
      },
    },
    tags: {
      getAll: {
        useQuery: () => ({
          data: [
            { id: '1', name: '测试' },
            { id: '2', name: '开发' },
          ],
          isLoading: false,
        }),
      },
    },
  },
}))

const mockPrompt = {
  id: '1',
  title: '测试提示词',
  content: '这是一个测试提示词的内容',
  description: '测试描述',
  categoryId: '1',
  tags: [
    { id: '1', name: '测试' },
    { id: '2', name: '开发' },
  ],
  isFavorite: false,
}

describe('PromptForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该渲染表单字段', () => {
    render(<PromptForm />)
    
    expect(screen.getByLabelText('标题')).toBeInTheDocument()
    expect(screen.getByLabelText('内容')).toBeInTheDocument()
    expect(screen.getByLabelText('描述')).toBeInTheDocument()
    expect(screen.getByLabelText('分类')).toBeInTheDocument()
    expect(screen.getByLabelText('标签')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '保存' })).toBeInTheDocument()
  })

  it('应该在编辑模式下填充现有数据', () => {
    render(<PromptForm prompt={mockPrompt} />)
    
    expect(screen.getByDisplayValue('测试提示词')).toBeInTheDocument()
    expect(screen.getByDisplayValue('这是一个测试提示词的内容')).toBeInTheDocument()
    expect(screen.getByDisplayValue('测试描述')).toBeInTheDocument()
  })

  it('应该验证必填字段', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    expect(screen.getByText('标题不能为空')).toBeInTheDocument()
    expect(screen.getByText('内容不能为空')).toBeInTheDocument()
  })

  it('应该验证标题长度', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const titleInput = screen.getByLabelText('标题')
    await user.type(titleInput, 'a'.repeat(101))
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    expect(screen.getByText('标题长度不能超过100个字符')).toBeInTheDocument()
  })

  it('应该验证内容长度', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const contentInput = screen.getByLabelText('内容')
    await user.type(contentInput, 'a'.repeat(10001))
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    expect(screen.getByText('内容长度不能超过10000个字符')).toBeInTheDocument()
  })

  it('应该处理表单提交', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<PromptForm onSubmit={onSubmit} />)
    
    await user.type(screen.getByLabelText('标题'), '新提示词')
    await user.type(screen.getByLabelText('内容'), '新内容')
    await user.type(screen.getByLabelText('描述'), '新描述')
    
    const submitButton = screen.getByRole('button', { name: '保存' })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        title: '新提示词',
        content: '新内容',
        description: '新描述',
        categoryId: undefined,
        tags: [],
        isFavorite: false,
      })
    })
  })

  it('应该处理分类选择', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const categorySelect = screen.getByLabelText('分类')
    await user.selectOptions(categorySelect, '1')
    
    expect(screen.getByDisplayValue('工作')).toBeInTheDocument()
  })

  it('应该处理标签选择', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const tagInput = screen.getByLabelText('标签')
    await user.type(tagInput, '测试')
    
    // 模拟选择标签
    const tagOption = screen.getByText('测试')
    await user.click(tagOption)
    
    expect(screen.getByText('测试')).toBeInTheDocument()
  })

  it('应该支持创建新标签', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const tagInput = screen.getByLabelText('标签')
    await user.type(tagInput, '新标签')
    
    const createButton = screen.getByRole('button', { name: '创建新标签' })
    await user.click(createButton)
    
    expect(screen.getByText('新标签')).toBeInTheDocument()
  })

  it('应该处理收藏切换', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const favoriteCheckbox = screen.getByRole('checkbox', { name: '收藏' })
    await user.click(favoriteCheckbox)
    
    expect(favoriteCheckbox).toBeChecked()
  })

  it('应该显示保存状态', () => {
    // Mock 保存状态
    vi.doMock('~/hooks/useAutoSave', () => ({
      useAutoSave: () => ({
        isSaving: true,
        lastSaved: new Date(),
        hasUnsavedChanges: true,
        saveNow: vi.fn(),
        clearSaved: vi.fn(),
      }),
    }))
    
    render(<PromptForm />)
    
    expect(screen.getByText('保存中...')).toBeInTheDocument()
  })

  it('应该显示最后保存时间', () => {
    const lastSaved = new Date('2024-01-01T12:00:00')
    
    vi.doMock('~/hooks/useAutoSave', () => ({
      useAutoSave: () => ({
        isSaving: false,
        lastSaved,
        hasUnsavedChanges: false,
        saveNow: vi.fn(),
        clearSaved: vi.fn(),
      }),
    }))
    
    render(<PromptForm />)
    
    expect(screen.getByText(/最后保存于/)).toBeInTheDocument()
  })

  it('应该处理取消操作', async () => {
    const user = userEvent.setup()
    const onCancel = vi.fn()
    
    render(<PromptForm onCancel={onCancel} />)
    
    const cancelButton = screen.getByRole('button', { name: '取消' })
    await user.click(cancelButton)
    
    expect(onCancel).toHaveBeenCalled()
  })

  it('应该处理重置操作', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    // 输入一些数据
    await user.type(screen.getByLabelText('标题'), '测试标题')
    await user.type(screen.getByLabelText('内容'), '测试内容')
    
    const resetButton = screen.getByRole('button', { name: '重置' })
    await user.click(resetButton)
    
    expect(screen.getByLabelText('标题')).toHaveValue('')
    expect(screen.getByLabelText('内容')).toHaveValue('')
  })

  it('应该支持代码编辑器', () => {
    render(<PromptForm />)
    
    // 检查是否有代码编辑器工具栏
    expect(screen.getByRole('toolbar')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '格式化' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '插入模板' })).toBeInTheDocument()
  })

  it('应该处理模板插入', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    const templateButton = screen.getByRole('button', { name: '插入模板' })
    await user.click(templateButton)
    
    // 检查是否有模板选择器
    expect(screen.getByText('选择模板')).toBeInTheDocument()
  })

  it('应该支持快捷键', async () => {
    const user = userEvent.setup()
    const onSubmit = vi.fn()
    
    render(<PromptForm onSubmit={onSubmit} />)
    
    await user.type(screen.getByLabelText('标题'), '测试')
    await user.type(screen.getByLabelText('内容'), '测试内容')
    
    // 模拟 Ctrl+S 快捷键
    await user.keyboard('{Control>}s{/Control}')
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalled()
    })
  })

  it('应该处理预览模式', async () => {
    const user = userEvent.setup()
    render(<PromptForm />)
    
    await user.type(screen.getByLabelText('内容'), '# 标题\n\n这是**粗体**内容')
    
    const previewButton = screen.getByRole('button', { name: '预览' })
    await user.click(previewButton)
    
    expect(screen.getByText('标题')).toBeInTheDocument()
    expect(screen.getByText('这是粗体内容')).toBeInTheDocument()
  })

  describe('表单验证', () => {
    it('应该验证特殊字符', async () => {
      const user = userEvent.setup()
      render(<PromptForm />)
      
      const titleInput = screen.getByLabelText('标题')
      await user.type(titleInput, '标题\n换行')
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('标题不能包含换行符')).toBeInTheDocument()
    })

    it('应该验证描述长度', async () => {
      const user = userEvent.setup()
      render(<PromptForm />)
      
      const descriptionInput = screen.getByLabelText('描述')
      await user.type(descriptionInput, 'a'.repeat(501))
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('描述长度不能超过500个字符')).toBeInTheDocument()
    })

    it('应该验证标签数量', async () => {
      const user = userEvent.setup()
      render(<PromptForm />)
      
      // 模拟添加过多标签
      const tagInput = screen.getByLabelText('标签')
      
      for (let i = 0; i < 21; i++) {
        await user.type(tagInput, `标签${i}`)
        await user.keyboard('{Enter}')
      }
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      expect(screen.getByText('标签数量不能超过20个')).toBeInTheDocument()
    })
  })

  describe('无障碍性', () => {
    it('应该有正确的标签关联', () => {
      render(<PromptForm />)
      
      const titleInput = screen.getByLabelText('标题')
      expect(titleInput).toHaveAttribute('aria-required', 'true')
      
      const contentInput = screen.getByLabelText('内容')
      expect(contentInput).toHaveAttribute('aria-required', 'true')
    })

    it('应该显示验证错误的 ARIA 描述', async () => {
      const user = userEvent.setup()
      render(<PromptForm />)
      
      const submitButton = screen.getByRole('button', { name: '保存' })
      await user.click(submitButton)
      
      const titleInput = screen.getByLabelText('标题')
      expect(titleInput).toHaveAttribute('aria-describedby')
      expect(titleInput).toHaveAttribute('aria-invalid', 'true')
    })

    it('应该支持键盘导航', async () => {
      const user = userEvent.setup()
      render(<PromptForm />)
      
      await user.tab()
      expect(screen.getByLabelText('标题')).toHaveFocus()
      
      await user.tab()
      expect(screen.getByLabelText('内容')).toHaveFocus()
    })
  })

  describe('响应式设计', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<PromptForm />)
      
      const form = screen.getByRole('form')
      expect(form).toHaveClass('w-full')
    })

    it('应该在桌面设备上正确显示', () => {
      // 模拟桌面设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      })
      
      render(<PromptForm />)
      
      const form = screen.getByRole('form')
      expect(form).toHaveClass('max-w-4xl')
    })
  })

  describe('性能优化', () => {
    it('应该防抖表单验证', async () => {
      const user = userEvent.setup()
      render(<PromptForm />)
      
      const titleInput = screen.getByLabelText('标题')
      
      // 快速输入多个字符
      await user.type(titleInput, 'abc', { delay: 10 })
      
      // 验证应该被防抖
      await waitFor(() => {
        expect(screen.queryByText('标题不能为空')).not.toBeInTheDocument()
      })
    })

    it('应该缓存分类和标签数据', () => {
      const { rerender } = render(<PromptForm />)
      
      // 重新渲染不应该重新加载数据
      rerender(<PromptForm />)
      
      expect(screen.getByText('工作')).toBeInTheDocument()
      expect(screen.getByText('学习')).toBeInTheDocument()
    })
  })
})