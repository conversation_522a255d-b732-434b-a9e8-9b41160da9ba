const CHUNK_PUBLIC_PATH = "server/app/api/trpc/[trpc]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_node-fetch_lib_index_36fb22dd.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__2062fe5b._.js");
runtime.loadChunk("server/chunks/node_modules_next_823fefc4._.js");
runtime.loadChunk("server/chunks/node_modules_@trpc_server_dist_db6083d7._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v3_cc34a201._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_ws_daabdc74._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_4e4e8dc6._.js");
runtime.loadChunk("server/chunks/node_modules_699b62aa._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/trpc/[trpc]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/trpc/[trpc]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
