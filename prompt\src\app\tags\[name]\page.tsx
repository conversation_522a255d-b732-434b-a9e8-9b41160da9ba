'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { api } from '~/trpc/react'
import { PromptGrid } from '~/components/prompts/PromptGrid'

export default function TagDetailPage() {
  const params = useParams()
  const router = useRouter()
  const tagName = decodeURIComponent(params.name as string)
  
  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'usage' | 'title'>('updated')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 获取该标签的提示词
  const { data: promptsData, isLoading, refetch } = api.prompts.getFiltered.useQuery({
    tagIds: [], // 这里需要根据标签名称获取标签ID，暂时留空
    sortBy,
    sortOrder,
  })

  const prompts = promptsData?.prompts || []

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 返回按钮 */}
      <div className="mb-6">
        <button
          onClick={() => router.back()}
          className="btn btn-ghost btn-sm"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-4 h-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
            />
          </svg>
          返回
        </button>
      </div>

      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-8 h-8 text-primary"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z"
            />
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 6h.008v.008H6V6z" />
          </svg>
          <h1 className="text-3xl font-bold text-base-content">标签：{tagName}</h1>
        </div>
        <p className="text-base-content/70">包含此标签的所有提示词</p>
      </div>

      {/* 排序和筛选 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        {/* 排序选择 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-base-content/70">排序：</span>
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder]
              setSortBy(field)
              setSortOrder(order)
            }}
            className="select select-bordered select-sm"
          >
            <option value="updated-desc">最近更新</option>
            <option value="created-desc">最新创建</option>
            <option value="usage-desc">使用最多</option>
            <option value="title-asc">标题 A-Z</option>
            <option value="title-desc">标题 Z-A</option>
          </select>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2 ml-auto">
          <button
            onClick={() => refetch()}
            className="btn btn-outline btn-sm"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading loading-spinner loading-xs"></span>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-4 h-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
                />
              </svg>
            )}
            刷新
          </button>
        </div>
      </div>

      {/* 提示词列表 */}
      <PromptGrid
        prompts={prompts}
        loading={isLoading}
        columns={3}
        showCategory={true}
        showActions={true}
        emptyMessage={`暂无包含"${tagName}"标签的提示词`}
        emptyAction={
          <Link href="/prompts/new" className="btn btn-primary">
            创建新提示词
          </Link>
        }
        onPromptUpdate={refetch}
      />

      {/* 统计信息 */}
      {!isLoading && prompts.length > 0 && (
        <div className="mt-8 text-center text-sm text-base-content/50">
          共找到 {prompts.length} 个包含"{tagName}"标签的提示词
        </div>
      )}
    </div>
  )
}
