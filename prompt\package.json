{"name": "prompt", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "build:analyze": "ANALYZE=true next build", "build:check": "next lint && tsc --noEmit", "build:size": "npx @next/bundle-analyzer", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "deploy": "./scripts/deploy.sh", "deploy:preview": "./scripts/deploy.sh preview", "deploy:prod": "./scripts/deploy.sh production", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "test": "vitest", "test:ui": "vitest --ui", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --reporter=verbose", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "typecheck": "tsc --noEmit", "type-check": "tsc --noEmit", "vercel:build": "next build", "vercel:dev": "next dev"}, "dependencies": {"@prisma/client": "^6.5.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.39.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@uiw/react-textarea-code-editor": "^3.0.2", "daisyui": "^4.12.12", "framer-motion": "^11.5.4", "immer": "^10.1.1", "next": "^15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.4.1", "server-only": "^0.0.1", "superjson": "^2.2.1", "zod": "^3.24.2", "zustand": "^4.5.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@playwright/test": "^1.44.1", "@tailwindcss/postcss": "^4.0.15", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitest/ui": "^1.6.1", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "jsdom": "^24.1.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0", "vitest": "^1.6.1"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}