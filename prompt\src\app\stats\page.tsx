'use client'

import { Suspense } from 'react'
import { StatsOverview } from '~/components/stats/StatsOverview'

// 加载组件
const StatsLoading = () => (
  <div className="container mx-auto px-4 py-8 space-y-6">
    {/* 标题骨架 */}
    <div className="flex items-center justify-between">
      <div className="h-8 w-32 bg-base-300 rounded animate-pulse"></div>
      <div className="h-10 w-64 bg-base-300 rounded animate-pulse"></div>
    </div>
    
    {/* 统计卡片骨架 */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="bg-base-100 rounded-lg p-6 space-y-3">
          <div className="flex items-center justify-between">
            <div className="h-6 w-20 bg-base-300 rounded animate-pulse"></div>
            <div className="h-8 w-8 bg-base-300 rounded animate-pulse"></div>
          </div>
          <div className="h-10 w-24 bg-base-300 rounded animate-pulse"></div>
          <div className="h-4 w-16 bg-base-300 rounded animate-pulse"></div>
        </div>
      ))}
    </div>
    
    {/* 图表骨架 */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="bg-base-100 rounded-lg p-6">
        <div className="h-6 w-24 bg-base-300 rounded animate-pulse mb-4"></div>
        <div className="h-64 bg-base-300 rounded animate-pulse"></div>
      </div>
      <div className="bg-base-100 rounded-lg p-6">
        <div className="h-6 w-24 bg-base-300 rounded animate-pulse mb-4"></div>
        <div className="space-y-3">
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-base-300 rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-base-300 rounded animate-pulse"></div>
                <div className="h-3 bg-base-300 rounded animate-pulse w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
)

export default function StatsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <Suspense fallback={<StatsLoading />}>
        <StatsOverview className="w-full" />
      </Suspense>
    </div>
  )
}

