{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/stats/UsageStatsCard.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { motion } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\n\r\ninterface UsageStatsCardProps {\r\n  title: string\r\n  value: number | string\r\n  change?: number\r\n  changeType?: 'increase' | 'decrease' | 'neutral'\r\n  icon: React.ReactNode\r\n  color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info'\r\n  loading?: boolean\r\n  className?: string\r\n}\r\n\r\nexport const UsageStatsCard = ({\r\n  title,\r\n  value,\r\n  change,\r\n  changeType = 'neutral',\r\n  icon,\r\n  color = 'primary',\r\n  loading = false,\r\n  className = '',\r\n}: UsageStatsCardProps) => {\r\n  const getChangeColor = () => {\r\n    switch (changeType) {\r\n      case 'increase':\r\n        return 'text-success'\r\n      case 'decrease':\r\n        return 'text-error'\r\n      default:\r\n        return 'text-base-content/70'\r\n    }\r\n  }\r\n\r\n  const getChangeIcon = () => {\r\n    switch (changeType) {\r\n      case 'increase':\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-3 h-3\"\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 15.75l7.5-7.5 7.5 7.5\" />\r\n          </svg>\r\n        )\r\n      case 'decrease':\r\n        return (\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-3 h-3\"\r\n          >\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\r\n          </svg>\r\n        )\r\n      default:\r\n        return null\r\n    }\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      whileHover={{ scale: 1.02 }}\r\n      className={`stat bg-base-100 rounded-lg shadow-md hover:shadow-lg transition-shadow ${className}`}\r\n    >\r\n      <div className={`stat-figure text-${color}`}>\r\n        {icon}\r\n      </div>\r\n      \r\n      <div className=\"stat-title text-base-content/70\">{title}</div>\r\n      \r\n      <div className={`stat-value text-${color}`}>\r\n        {loading ? (\r\n          <span className=\"loading loading-spinner loading-md\"></span>\r\n        ) : (\r\n          value\r\n        )}\r\n      </div>\r\n      \r\n      {change !== undefined && !loading && (\r\n        <div className={`stat-desc ${getChangeColor()} flex items-center space-x-1`}>\r\n          {getChangeIcon()}\r\n          <span>\r\n            {changeType === 'increase' ? '+' : changeType === 'decrease' ? '-' : ''}\r\n            {Math.abs(change)}\r\n            {typeof change === 'number' ? '%' : ''}\r\n          </span>\r\n          <span className=\"text-base-content/50\">vs 上期</span>\r\n        </div>\r\n      )}\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBO,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,KAAK,EACL,MAAM,EACN,aAAa,SAAS,EACtB,IAAI,EACJ,QAAQ,SAAS,EACjB,UAAU,KAAK,EACf,YAAY,EAAE,EACM;IACpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,aAAa;oBACb,QAAO;oBACP,WAAU;8BAEV,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,GAAE;;;;;;;;;;;YAG3D,KAAK;gBACH,qBACE,8OAAC;oBACC,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,aAAa;oBACb,QAAO;oBACP,WAAU;8BAEV,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,GAAE;;;;;;;;;;;YAG3D;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;QAAK;QAC1B,WAAW,CAAC,wEAAwE,EAAE,WAAW;;0BAEjG,8OAAC;gBAAI,WAAW,CAAC,iBAAiB,EAAE,OAAO;0BACxC;;;;;;0BAGH,8OAAC;gBAAI,WAAU;0BAAmC;;;;;;0BAElD,8OAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO;0BACvC,wBACC,8OAAC;oBAAK,WAAU;;;;;+DAEhB;;;;;;YAIH,WAAW,aAAa,CAAC,yBACxB,8OAAC;gBAAI,WAAW,CAAC,UAAU,EAAE,iBAAiB,4BAA4B,CAAC;;oBACxE;kCACD,8OAAC;;4BACE,eAAe,aAAa,MAAM,eAAe,aAAa,MAAM;4BACpE,KAAK,GAAG,CAAC;4BACT,OAAO,WAAW,WAAW,MAAM;;;;;;;kCAEtC,8OAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;;;;;;;AAKjD", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/stats/UsageChart.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useMemo } from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\ninterface UsageDataPoint {\r\n  date: string\r\n  count: number\r\n  label?: string\r\n}\r\n\r\ninterface UsageChartProps {\r\n  data: UsageDataPoint[]\r\n  title?: string\r\n  height?: number\r\n  color?: string\r\n  showGrid?: boolean\r\n  showLabels?: boolean\r\n  animate?: boolean\r\n  className?: string\r\n}\r\n\r\nexport const UsageChart = ({\r\n  data,\r\n  title = '使用趋势',\r\n  height = 200,\r\n  color = '#3b82f6',\r\n  showGrid = true,\r\n  showLabels = true,\r\n  animate = true,\r\n  className = '',\r\n}: UsageChartProps) => {\r\n  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)\r\n\r\n  // 计算数据统计\r\n  const stats = useMemo(() => {\r\n    if (!data.length) return { max: 0, min: 0, total: 0, average: 0 }\r\n    \r\n    const counts = data.map(d => d.count)\r\n    const max = Math.max(...counts)\r\n    const min = Math.min(...counts)\r\n    const total = counts.reduce((sum, count) => sum + count, 0)\r\n    const average = total / counts.length\r\n    \r\n    return { max, min, total, average }\r\n  }, [data])\r\n\r\n  // 生成路径\r\n  const generatePath = () => {\r\n    if (!data.length) return ''\r\n    \r\n    const width = 400\r\n    const chartHeight = height - 40 // 留出标签空间\r\n    const xStep = width / (data.length - 1)\r\n    \r\n    return data\r\n      .map((point, index) => {\r\n        const x = index * xStep\r\n        const y = chartHeight - (point.count / stats.max) * chartHeight\r\n        return `${index === 0 ? 'M' : 'L'} ${x} ${y}`\r\n      })\r\n      .join(' ')\r\n  }\r\n\r\n  // 生成面积路径\r\n  const generateAreaPath = () => {\r\n    if (!data.length) return ''\r\n    \r\n    const width = 400\r\n    const chartHeight = height - 40\r\n    const xStep = width / (data.length - 1)\r\n    \r\n    const path = data\r\n      .map((point, index) => {\r\n        const x = index * xStep\r\n        const y = chartHeight - (point.count / stats.max) * chartHeight\r\n        return `${index === 0 ? 'M' : 'L'} ${x} ${y}`\r\n      })\r\n      .join(' ')\r\n    \r\n    const lastX = (data.length - 1) * xStep\r\n    return `${path} L ${lastX} ${chartHeight} L 0 ${chartHeight} Z`\r\n  }\r\n\r\n  // 生成网格线\r\n  const generateGridLines = () => {\r\n    if (!showGrid) return null\r\n    \r\n    const lines = []\r\n    const width = 400\r\n    const chartHeight = height - 40\r\n    \r\n    // 水平网格线\r\n    for (let i = 0; i <= 4; i++) {\r\n      const y = (chartHeight / 4) * i\r\n      lines.push(\r\n        <line\r\n          key={`h-${i}`}\r\n          x1={0}\r\n          y1={y}\r\n          x2={width}\r\n          y2={y}\r\n          stroke=\"currentColor\"\r\n          strokeOpacity={0.1}\r\n          strokeWidth={1}\r\n        />\r\n      )\r\n    }\r\n    \r\n    // 垂直网格线\r\n    const xStep = width / (data.length - 1)\r\n    for (let i = 0; i < data.length; i++) {\r\n      const x = i * xStep\r\n      lines.push(\r\n        <line\r\n          key={`v-${i}`}\r\n          x1={x}\r\n          y1={0}\r\n          x2={x}\r\n          y2={chartHeight}\r\n          stroke=\"currentColor\"\r\n          strokeOpacity={0.1}\r\n          strokeWidth={1}\r\n        />\r\n      )\r\n    }\r\n    \r\n    return lines\r\n  }\r\n\r\n  // 生成数据点\r\n  const generateDataPoints = () => {\r\n    const width = 400\r\n    const chartHeight = height - 40\r\n    const xStep = width / (data.length - 1)\r\n    \r\n    return data.map((point, index) => {\r\n      const x = index * xStep\r\n      const y = chartHeight - (point.count / stats.max) * chartHeight\r\n      \r\n      return (\r\n        <motion.circle\r\n          key={index}\r\n          cx={x}\r\n          cy={y}\r\n          r={hoveredIndex === index ? 6 : 4}\r\n          fill={color}\r\n          className=\"cursor-pointer\"\r\n          onMouseEnter={() => setHoveredIndex(index)}\r\n          onMouseLeave={() => setHoveredIndex(null)}\r\n          initial={animate ? { scale: 0 } : {}}\r\n          animate={{ scale: 1 }}\r\n          transition={{ delay: index * 0.1 }}\r\n        />\r\n      )\r\n    })\r\n  }\r\n\r\n  if (!data.length) {\r\n    return (\r\n      <div className={`bg-base-100 rounded-lg p-6 ${className}`}>\r\n        <h3 className=\"text-lg font-semibold text-base-content mb-4\">{title}</h3>\r\n        <div className=\"flex items-center justify-center h-48 text-base-content/50\">\r\n          <div className=\"text-center\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-12 h-12 mx-auto mb-2\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z\"\r\n              />\r\n            </svg>\r\n            <p>暂无数据</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className={`bg-base-100 rounded-lg p-6 ${className}`}>\r\n      {/* 标题和统计 */}\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <h3 className=\"text-lg font-semibold text-base-content\">{title}</h3>\r\n        <div className=\"flex items-center space-x-4 text-sm text-base-content/70\">\r\n          <span>总计: {stats.total}</span>\r\n          <span>平均: {Math.round(stats.average)}</span>\r\n          <span>峰值: {stats.max}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 图表容器 */}\r\n      <div className=\"relative\">\r\n        <svg\r\n          width=\"100%\"\r\n          height={height}\r\n          viewBox={`0 0 400 ${height}`}\r\n          className=\"text-base-content\"\r\n        >\r\n          {/* 网格线 */}\r\n          {generateGridLines()}\r\n          \r\n          {/* 面积填充 */}\r\n          <motion.path\r\n            d={generateAreaPath()}\r\n            fill={color}\r\n            fillOpacity={0.1}\r\n            initial={animate ? { pathLength: 0 } : {}}\r\n            animate={{ pathLength: 1 }}\r\n            transition={{ duration: 1 }}\r\n          />\r\n          \r\n          {/* 线条 */}\r\n          <motion.path\r\n            d={generatePath()}\r\n            fill=\"none\"\r\n            stroke={color}\r\n            strokeWidth={2}\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            initial={animate ? { pathLength: 0 } : {}}\r\n            animate={{ pathLength: 1 }}\r\n            transition={{ duration: 1 }}\r\n          />\r\n          \r\n          {/* 数据点 */}\r\n          {generateDataPoints()}\r\n        </svg>\r\n\r\n        {/* 悬停工具提示 */}\r\n        {hoveredIndex !== null && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            className=\"absolute bg-base-300 text-base-content px-3 py-2 rounded-lg shadow-lg text-sm pointer-events-none\"\r\n            style={{\r\n              left: `${(hoveredIndex / (data.length - 1)) * 100}%`,\r\n              top: '10px',\r\n              transform: 'translateX(-50%)',\r\n            }}\r\n          >\r\n            <div className=\"font-medium\">{data[hoveredIndex].label || data[hoveredIndex].date}</div>\r\n            <div className=\"text-xs\">使用次数: {data[hoveredIndex].count}</div>\r\n          </motion.div>\r\n        )}\r\n      </div>\r\n\r\n      {/* X轴标签 */}\r\n      {showLabels && (\r\n        <div className=\"flex justify-between mt-2 text-xs text-base-content/50\">\r\n          {data.map((point, index) => {\r\n            // 只显示部分标签以避免拥挤\r\n            if (data.length > 10 && index % Math.ceil(data.length / 6) !== 0) {\r\n              return <span key={index} />\r\n            }\r\n            return (\r\n              <span key={index} className=\"truncate\">\r\n                {point.label || new Date(point.date).toLocaleDateString('zh-CN', {\r\n                  month: 'short',\r\n                  day: 'numeric',\r\n                })}\r\n              </span>\r\n            )\r\n          })}\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAsBO,MAAM,aAAa,CAAC,EACzB,IAAI,EACJ,QAAQ,MAAM,EACd,SAAS,GAAG,EACZ,QAAQ,SAAS,EACjB,WAAW,IAAI,EACf,aAAa,IAAI,EACjB,UAAU,IAAI,EACd,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,SAAS;IACT,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO;YAAE,KAAK;YAAG,KAAK;YAAG,OAAO;YAAG,SAAS;QAAE;QAEhE,MAAM,SAAS,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;QACpC,MAAM,MAAM,KAAK,GAAG,IAAI;QACxB,MAAM,MAAM,KAAK,GAAG,IAAI;QACxB,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO;QACzD,MAAM,UAAU,QAAQ,OAAO,MAAM;QAErC,OAAO;YAAE;YAAK;YAAK;YAAO;QAAQ;IACpC,GAAG;QAAC;KAAK;IAET,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO;QAEzB,MAAM,QAAQ;QACd,MAAM,cAAc,SAAS,GAAG,SAAS;;QACzC,MAAM,QAAQ,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC;QAEtC,OAAO,KACJ,GAAG,CAAC,CAAC,OAAO;YACX,MAAM,IAAI,QAAQ;YAClB,MAAM,IAAI,cAAc,AAAC,MAAM,KAAK,GAAG,MAAM,GAAG,GAAI;YACpD,OAAO,GAAG,UAAU,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;QAC/C,GACC,IAAI,CAAC;IACV;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO;QAEzB,MAAM,QAAQ;QACd,MAAM,cAAc,SAAS;QAC7B,MAAM,QAAQ,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC;QAEtC,MAAM,OAAO,KACV,GAAG,CAAC,CAAC,OAAO;YACX,MAAM,IAAI,QAAQ;YAClB,MAAM,IAAI,cAAc,AAAC,MAAM,KAAK,GAAG,MAAM,GAAG,GAAI;YACpD,OAAO,GAAG,UAAU,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;QAC/C,GACC,IAAI,CAAC;QAER,MAAM,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC,IAAI;QAClC,OAAO,GAAG,KAAK,GAAG,EAAE,MAAM,CAAC,EAAE,YAAY,KAAK,EAAE,YAAY,EAAE,CAAC;IACjE;IAEA,QAAQ;IACR,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,QAAQ,EAAE;QAChB,MAAM,QAAQ;QACd,MAAM,cAAc,SAAS;QAE7B,QAAQ;QACR,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,IAAI,AAAC,cAAc,IAAK;YAC9B,MAAM,IAAI,eACR,8OAAC;gBAEC,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,QAAO;gBACP,eAAe;gBACf,aAAa;eAPR,CAAC,EAAE,EAAE,GAAG;;;;;QAUnB;QAEA,QAAQ;QACR,MAAM,QAAQ,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,IAAI,IAAI;YACd,MAAM,IAAI,eACR,8OAAC;gBAEC,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,QAAO;gBACP,eAAe;gBACf,aAAa;eAPR,CAAC,EAAE,EAAE,GAAG;;;;;QAUnB;QAEA,OAAO;IACT;IAEA,QAAQ;IACR,MAAM,qBAAqB;QACzB,MAAM,QAAQ;QACd,MAAM,cAAc,SAAS;QAC7B,MAAM,QAAQ,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC;QAEtC,OAAO,KAAK,GAAG,CAAC,CAAC,OAAO;YACtB,MAAM,IAAI,QAAQ;YAClB,MAAM,IAAI,cAAc,AAAC,MAAM,KAAK,GAAG,MAAM,GAAG,GAAI;YAEpD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBAEZ,IAAI;gBACJ,IAAI;gBACJ,GAAG,iBAAiB,QAAQ,IAAI;gBAChC,MAAM;gBACN,WAAU;gBACV,cAAc,IAAM,gBAAgB;gBACpC,cAAc,IAAM,gBAAgB;gBACpC,SAAS,UAAU;oBAAE,OAAO;gBAAE,IAAI,CAAC;gBACnC,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,OAAO,QAAQ;gBAAI;eAV5B;;;;;QAaX;IACF;IAEA,IAAI,CAAC,KAAK,MAAM,EAAE;QAChB,qBACE,8OAAC;YAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;;8BACvD,8OAAC;oBAAG,WAAU;8BAAgD;;;;;;8BAC9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,GAAE;;;;;;;;;;;0CAGN,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;;0BAEvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCAAK,MAAM,KAAK;;;;;;;0CACtB,8OAAC;;oCAAK;oCAAK,KAAK,KAAK,CAAC,MAAM,OAAO;;;;;;;0CACnC,8OAAC;;oCAAK;oCAAK,MAAM,GAAG;;;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAM;wBACN,QAAQ;wBACR,SAAS,CAAC,QAAQ,EAAE,QAAQ;wBAC5B,WAAU;;4BAGT;0CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,GAAG;gCACH,MAAM;gCACN,aAAa;gCACb,SAAS,UAAU;oCAAE,YAAY;gCAAE,IAAI,CAAC;gCACxC,SAAS;oCAAE,YAAY;gCAAE;gCACzB,YAAY;oCAAE,UAAU;gCAAE;;;;;;0CAI5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,GAAG;gCACH,MAAK;gCACL,QAAQ;gCACR,aAAa;gCACb,eAAc;gCACd,gBAAe;gCACf,SAAS,UAAU;oCAAE,YAAY;gCAAE,IAAI,CAAC;gCACxC,SAAS;oCAAE,YAAY;gCAAE;gCACzB,YAAY;oCAAE,UAAU;gCAAE;;;;;;4BAI3B;;;;;;;oBAIF,iBAAiB,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,AAAC,eAAe,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK,IAAI,CAAC,CAAC;4BACpD,KAAK;4BACL,WAAW;wBACb;;0CAEA,8OAAC;gCAAI,WAAU;0CAAe,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI;;;;;;0CACjF,8OAAC;gCAAI,WAAU;;oCAAU;oCAAO,IAAI,CAAC,aAAa,CAAC,KAAK;;;;;;;;;;;;;;;;;;;YAM7D,4BACC,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,OAAO;oBAChB,eAAe;oBACf,IAAI,KAAK,MAAM,GAAG,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG,OAAO,GAAG;wBAChE,qBAAO,8OAAC,YAAU;;;;;oBACpB;oBACA,qBACE,8OAAC;wBAAiB,WAAU;kCACzB,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC,SAAS;4BAC/D,OAAO;4BACP,KAAK;wBACP;uBAJS;;;;;gBAOf;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/stats/StatsOverview.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion } from 'framer-motion'\r\nimport { UsageStatsCard } from './UsageStatsCard'\r\nimport { UsageChart } from './UsageChart'\r\nimport { api } from '~/trpc/react'\r\n\r\ninterface StatsOverviewProps {\r\n  timeRange?: '7d' | '30d' | '90d' | 'all'\r\n  className?: string\r\n}\r\n\r\nexport const StatsOverview = ({ \r\n  timeRange = '30d',\r\n  className = '' \r\n}: StatsOverviewProps) => {\r\n  const [selectedRange, setSelectedRange] = useState(timeRange)\r\n\r\n  // 获取统计数据\r\n  const { data: stats, isLoading: statsLoading } = api.prompts.getStats.useQuery({\r\n    timeRange: selectedRange,\r\n  })\r\n\r\n  // 获取使用趋势数据\r\n  const { data: usageData, isLoading: usageLoading } = api.prompts.getUsageStats.useQuery({\r\n    timeRange: selectedRange,\r\n  })\r\n\r\n  // 获取热门提示词\r\n  const { data: topPrompts, isLoading: topLoading } = api.prompts.getTopUsed.useQuery({\r\n    limit: 5,\r\n    timeRange: selectedRange,\r\n  })\r\n\r\n  // 获取分类统计\r\n  const { data: categoryStats, isLoading: categoryLoading } = api.categories.getStats.useQuery({\r\n    timeRange: selectedRange,\r\n  })\r\n\r\n  const timeRangeOptions = [\r\n    { value: '7d', label: '近7天' },\r\n    { value: '30d', label: '近30天' },\r\n    { value: '90d', label: '近90天' },\r\n    { value: 'all', label: '全部' },\r\n  ]\r\n\r\n  return (\r\n    <div className={`space-y-6 ${className}`}>\r\n      {/* 时间范围选择器 */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <h2 className=\"text-2xl font-bold text-base-content\">使用统计</h2>\r\n        <div className=\"tabs tabs-boxed\">\r\n          {timeRangeOptions.map(option => (\r\n            <button\r\n              key={option.value}\r\n              onClick={() => setSelectedRange(option.value as any)}\r\n              className={`tab ${selectedRange === option.value ? 'tab-active' : ''}`}\r\n            >\r\n              {option.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* 统计卡片 */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n        <UsageStatsCard\r\n          title=\"总使用次数\"\r\n          value={stats?.totalUsage || 0}\r\n          change={stats?.usageChange}\r\n          changeType={stats?.usageChange && stats.usageChange > 0 ? 'increase' : 'decrease'}\r\n          icon={\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n              />\r\n            </svg>\r\n          }\r\n          color=\"primary\"\r\n          loading={statsLoading}\r\n        />\r\n\r\n        <UsageStatsCard\r\n          title=\"活跃提示词\"\r\n          value={stats?.activePrompts || 0}\r\n          change={stats?.activePromptsChange}\r\n          changeType={stats?.activePromptsChange && stats.activePromptsChange > 0 ? 'increase' : 'decrease'}\r\n          icon={\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n              />\r\n            </svg>\r\n          }\r\n          color=\"secondary\"\r\n          loading={statsLoading}\r\n        />\r\n\r\n        <UsageStatsCard\r\n          title=\"平均使用频率\"\r\n          value={stats?.averageUsage ? `${stats.averageUsage.toFixed(1)}/天` : '0/天'}\r\n          change={stats?.frequencyChange}\r\n          changeType={stats?.frequencyChange && stats.frequencyChange > 0 ? 'increase' : 'decrease'}\r\n          icon={\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z\"\r\n              />\r\n            </svg>\r\n          }\r\n          color=\"accent\"\r\n          loading={statsLoading}\r\n        />\r\n\r\n        <UsageStatsCard\r\n          title=\"新增提示词\"\r\n          value={stats?.newPrompts || 0}\r\n          change={stats?.newPromptsChange}\r\n          changeType={stats?.newPromptsChange && stats.newPromptsChange > 0 ? 'increase' : 'decrease'}\r\n          icon={\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M12 4.5v15m7.5-7.5h-15\"\r\n              />\r\n            </svg>\r\n          }\r\n          color=\"success\"\r\n          loading={statsLoading}\r\n        />\r\n      </div>\r\n\r\n      {/* 使用趋势图表 */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n        >\r\n          <UsageChart\r\n            data={usageData || []}\r\n            title=\"使用趋势\"\r\n            height={300}\r\n            color=\"#3b82f6\"\r\n          />\r\n        </motion.div>\r\n\r\n        {/* 热门提示词 */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-base-100 rounded-lg p-6\"\r\n        >\r\n          <h3 className=\"text-lg font-semibold text-base-content mb-4\">热门提示词</h3>\r\n          {topLoading ? (\r\n            <div className=\"space-y-3\">\r\n              {Array.from({ length: 5 }, (_, i) => (\r\n                <div key={i} className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-base-300 rounded animate-pulse\"></div>\r\n                  <div className=\"flex-1 space-y-2\">\r\n                    <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                    <div className=\"h-3 bg-base-300 rounded animate-pulse w-1/2\"></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : topPrompts && topPrompts.length > 0 ? (\r\n            <div className=\"space-y-3\">\r\n              {topPrompts.map((prompt, index) => (\r\n                <motion.div\r\n                  key={prompt.id}\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  transition={{ delay: index * 0.1 }}\r\n                  className=\"flex items-center space-x-3 p-3 bg-base-200 rounded-lg hover:bg-base-300 transition-colors\"\r\n                >\r\n                  <div className=\"flex-shrink-0 w-8 h-8 bg-primary text-primary-content rounded-full flex items-center justify-center text-sm font-bold\">\r\n                    {index + 1}\r\n                  </div>\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <p className=\"font-medium text-base-content truncate\">{prompt.title}</p>\r\n                    <p className=\"text-sm text-base-content/70\">\r\n                      使用 {prompt.usageCount} 次\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1 text-xs text-base-content/50\">\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      fill=\"none\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      strokeWidth={1.5}\r\n                      stroke=\"currentColor\"\r\n                      className=\"w-3 h-3\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-8 text-base-content/50\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-12 h-12 mx-auto mb-2\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                />\r\n              </svg>\r\n              <p>暂无使用数据</p>\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* 分类使用统计 */}\r\n      {categoryStats && categoryStats.length > 0 && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n          className=\"bg-base-100 rounded-lg p-6\"\r\n        >\r\n          <h3 className=\"text-lg font-semibold text-base-content mb-4\">分类使用统计</h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            {categoryStats.map((category, index) => (\r\n              <motion.div\r\n                key={category.id}\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ delay: index * 0.1 }}\r\n                className=\"bg-base-200 rounded-lg p-4\"\r\n              >\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div\r\n                    className=\"w-4 h-4 rounded-full\"\r\n                    style={{ backgroundColor: category.color }}\r\n                  />\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"font-medium text-base-content\">{category.name}</p>\r\n                    <p className=\"text-sm text-base-content/70\">\r\n                      {category.promptCount} 个提示词 · 使用 {category.usageCount} 次\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mt-2\">\r\n                  <div className=\"flex justify-between text-xs text-base-content/50 mb-1\">\r\n                    <span>使用率</span>\r\n                    <span>{((category.usageCount / (stats?.totalUsage || 1)) * 100).toFixed(1)}%</span>\r\n                  </div>\r\n                  <div className=\"w-full bg-base-300 rounded-full h-2\">\r\n                    <motion.div\r\n                      className=\"h-2 rounded-full\"\r\n                      style={{ backgroundColor: category.color }}\r\n                      initial={{ width: 0 }}\r\n                      animate={{ \r\n                        width: `${(category.usageCount / (stats?.totalUsage || 1)) * 100}%` \r\n                      }}\r\n                      transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAaO,MAAM,gBAAgB,CAAC,EAC5B,YAAY,KAAK,EACjB,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,SAAS;IACT,MAAM,EAAE,MAAM,KAAK,EAAE,WAAW,YAAY,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC7E,WAAW;IACb;IAEA,WAAW;IACX,MAAM,EAAE,MAAM,SAAS,EAAE,WAAW,YAAY,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtF,WAAW;IACb;IAEA,UAAU;IACV,MAAM,EAAE,MAAM,UAAU,EAAE,WAAW,UAAU,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAClF,OAAO;QACP,WAAW;IACb;IAEA,SAAS;IACT,MAAM,EAAE,MAAM,aAAa,EAAE,WAAW,eAAe,EAAE,GAAG,qHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC3F,WAAW;IACb;IAEA,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAM,OAAO;QAAM;QAC5B;YAAE,OAAO;YAAO,OAAO;QAAO;QAC9B;YAAE,OAAO;YAAO,OAAO;QAAO;QAC9B;YAAE,OAAO;YAAO,OAAO;QAAK;KAC7B;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;gCAEC,SAAS,IAAM,iBAAiB,OAAO,KAAK;gCAC5C,WAAW,CAAC,IAAI,EAAE,kBAAkB,OAAO,KAAK,GAAG,eAAe,IAAI;0CAErE,OAAO,KAAK;+BAJR,OAAO,KAAK;;;;;;;;;;;;;;;;0BAWzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6IAAA,CAAA,iBAAc;wBACb,OAAM;wBACN,OAAO,OAAO,cAAc;wBAC5B,QAAQ,OAAO;wBACf,YAAY,OAAO,eAAe,MAAM,WAAW,GAAG,IAAI,aAAa;wBACvE,oBACE,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAIR,OAAM;wBACN,SAAS;;;;;;kCAGX,8OAAC,6IAAA,CAAA,iBAAc;wBACb,OAAM;wBACN,OAAO,OAAO,iBAAiB;wBAC/B,QAAQ,OAAO;wBACf,YAAY,OAAO,uBAAuB,MAAM,mBAAmB,GAAG,IAAI,aAAa;wBACvF,oBACE,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAIR,OAAM;wBACN,SAAS;;;;;;kCAGX,8OAAC,6IAAA,CAAA,iBAAc;wBACb,OAAM;wBACN,OAAO,OAAO,eAAe,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG;wBACpE,QAAQ,OAAO;wBACf,YAAY,OAAO,mBAAmB,MAAM,eAAe,GAAG,IAAI,aAAa;wBAC/E,oBACE,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAIR,OAAM;wBACN,SAAS;;;;;;kCAGX,8OAAC,6IAAA,CAAA,iBAAc;wBACb,OAAM;wBACN,OAAO,OAAO,cAAc;wBAC5B,QAAQ,OAAO;wBACf,YAAY,OAAO,oBAAoB,MAAM,gBAAgB,GAAG,IAAI,aAAa;wBACjF,oBACE,8OAAC;4BACC,OAAM;4BACN,MAAK;4BACL,SAAQ;4BACR,aAAa;4BACb,QAAO;4BACP,WAAU;sCAEV,cAAA,8OAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;wBAIR,OAAM;wBACN,SAAS;;;;;;;;;;;;0BAKb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,yIAAA,CAAA,aAAU;4BACT,MAAM,aAAa,EAAE;4BACrB,OAAM;4BACN,QAAQ;4BACR,OAAM;;;;;;;;;;;kCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA+C;;;;;;4BAC5D,2BACC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAJT;;;;;;;;;2EASZ,cAAc,WAAW,MAAM,GAAG,kBACpC,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ;;;;;;0DAEX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA0C,OAAO,KAAK;;;;;;kEACnE,8OAAC;wDAAE,WAAU;;4DAA+B;4DACtC,OAAO,UAAU;4DAAC;;;;;;;;;;;;;0DAG1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAQ;oDACR,aAAa;oDACb,QAAO;oDACP,WAAU;8DAEV,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,GAAE;;;;;;;;;;;;;;;;;uCA3BH,OAAO,EAAE;;;;;;;;;yFAmCpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;kDAGN,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;YAOV,iBAAiB,cAAc,MAAM,GAAG,mBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;;;;;;0DAE3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAiC,SAAS,IAAI;;;;;;kEAC3D,8OAAC;wDAAE,WAAU;;4DACV,SAAS,WAAW;4DAAC;4DAAY,SAAS,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;;4DAAM,CAAC,AAAC,SAAS,UAAU,GAAG,CAAC,OAAO,cAAc,CAAC,IAAK,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAE7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,OAAO;wDAAE,iBAAiB,SAAS,KAAK;oDAAC;oDACzC,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDACP,OAAO,GAAG,AAAC,SAAS,UAAU,GAAG,CAAC,OAAO,cAAc,CAAC,IAAK,IAAI,CAAC,CAAC;oDACrE;oDACA,YAAY;wDAAE,OAAO,QAAQ,MAAM;wDAAK,UAAU;oDAAI;;;;;;;;;;;;;;;;;;+BA/BvD,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AA0ChC", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/stats/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Suspense } from 'react'\r\nimport { StatsOverview } from '~/components/stats/StatsOverview'\r\n\r\n// 加载组件\r\nconst StatsLoading = () => (\r\n  <div className=\"container mx-auto px-4 py-8 space-y-6\">\r\n    {/* 标题骨架 */}\r\n    <div className=\"flex items-center justify-between\">\r\n      <div className=\"h-8 w-32 bg-base-300 rounded animate-pulse\"></div>\r\n      <div className=\"h-10 w-64 bg-base-300 rounded animate-pulse\"></div>\r\n    </div>\r\n    \r\n    {/* 统计卡片骨架 */}\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n      {Array.from({ length: 4 }, (_, i) => (\r\n        <div key={i} className=\"bg-base-100 rounded-lg p-6 space-y-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"h-6 w-20 bg-base-300 rounded animate-pulse\"></div>\r\n            <div className=\"h-8 w-8 bg-base-300 rounded animate-pulse\"></div>\r\n          </div>\r\n          <div className=\"h-10 w-24 bg-base-300 rounded animate-pulse\"></div>\r\n          <div className=\"h-4 w-16 bg-base-300 rounded animate-pulse\"></div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n    \r\n    {/* 图表骨架 */}\r\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n      <div className=\"bg-base-100 rounded-lg p-6\">\r\n        <div className=\"h-6 w-24 bg-base-300 rounded animate-pulse mb-4\"></div>\r\n        <div className=\"h-64 bg-base-300 rounded animate-pulse\"></div>\r\n      </div>\r\n      <div className=\"bg-base-100 rounded-lg p-6\">\r\n        <div className=\"h-6 w-24 bg-base-300 rounded animate-pulse mb-4\"></div>\r\n        <div className=\"space-y-3\">\r\n          {Array.from({ length: 5 }, (_, i) => (\r\n            <div key={i} className=\"flex items-center space-x-3\">\r\n              <div className=\"w-8 h-8 bg-base-300 rounded-full animate-pulse\"></div>\r\n              <div className=\"flex-1 space-y-2\">\r\n                <div className=\"h-4 bg-base-300 rounded animate-pulse\"></div>\r\n                <div className=\"h-3 bg-base-300 rounded animate-pulse w-1/2\"></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n)\r\n\r\nexport default function StatsPage() {\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-8\">\r\n      <Suspense fallback={<StatsLoading />}>\r\n        <StatsOverview className=\"w-full\" />\r\n      </Suspense>\r\n    </div>\r\n  )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,OAAO;AACP,MAAM,eAAe,kBACnB,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wBAAY,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;uBANP;;;;;;;;;;0BAYd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,CAAC,GAAG,kBAC7B,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAJT;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcP,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;YAAC,wBAAU,8OAAC;;;;;sBACnB,cAAA,8OAAC,4IAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIjC", "debugId": null}}]}