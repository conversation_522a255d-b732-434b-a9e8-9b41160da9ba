{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/categories/CategoryCard.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { motion } from 'framer-motion'\r\nimport Link from 'next/link'\r\nimport { Category } from '~/types'\r\n\r\ninterface CategoryCardProps {\r\n  category: Category\r\n  viewMode?: 'grid' | 'list'\r\n  onEdit?: (category: Category) => void\r\n  onDelete?: (category: Category) => void\r\n  delay?: number\r\n}\r\n\r\nexport const CategoryCard = ({\r\n  category,\r\n  viewMode = 'grid',\r\n  onEdit,\r\n  onDelete,\r\n  delay = 0,\r\n}: CategoryCardProps) => {\r\n  const handleEdit = (e: React.MouseEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    if (onEdit) {\r\n      onEdit(category)\r\n    }\r\n  }\r\n\r\n  const handleDelete = (e: React.MouseEvent) => {\r\n    e.preventDefault()\r\n    e.stopPropagation()\r\n    if (onDelete) {\r\n      onDelete(category)\r\n    }\r\n  }\r\n\r\n  if (viewMode === 'list') {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, x: -20 }}\r\n        animate={{ opacity: 1, x: 0 }}\r\n        exit={{ opacity: 0, x: -20 }}\r\n        transition={{ delay }}\r\n        className=\"bg-base-100 rounded-lg shadow-md hover:shadow-lg transition-shadow group\"\r\n      >\r\n        <Link href={`/categories/${category.id}`} className=\"block p-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* 分类图标和颜色 */}\r\n              <div\r\n                className=\"w-12 h-12 rounded-lg flex items-center justify-center text-white font-semibold\"\r\n                style={{ backgroundColor: category.color }}\r\n              >\r\n                {category.icon ? (\r\n                  <span className=\"text-xl\">{category.icon}</span>\r\n                ) : (\r\n                  <span className=\"text-lg\">{category.name.charAt(0)}</span>\r\n                )}\r\n              </div>\r\n\r\n              {/* 分类信息 */}\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h3 className=\"font-semibold text-base-content truncate\">\r\n                  {category.name}\r\n                </h3>\r\n                {category.description && (\r\n                  <p className=\"text-sm text-base-content/70 truncate\">\r\n                    {category.description}\r\n                  </p>\r\n                )}\r\n                <div className=\"flex items-center space-x-4 mt-1 text-xs text-base-content/60\">\r\n                  <span>{category.promptCount || 0} 个提示词</span>\r\n                  <span>使用 {category.usageCount || 0} 次</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 操作按钮 */}\r\n            <div className=\"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n              <button\r\n                onClick={handleEdit}\r\n                className=\"btn btn-ghost btn-sm\"\r\n                title=\"编辑分类\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n\r\n              <button\r\n                onClick={handleDelete}\r\n                className=\"btn btn-ghost btn-sm hover:btn-error\"\r\n                title=\"删除分类\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                  />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </Link>\r\n      </motion.div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      exit={{ opacity: 0, y: 20 }}\r\n      transition={{ delay }}\r\n      whileHover={{ y: -4 }}\r\n      className=\"bg-base-100 rounded-lg shadow-md hover:shadow-xl transition-all duration-200 group\"\r\n    >\r\n      <Link href={`/categories/${category.id}`} className=\"block\">\r\n        <div className=\"p-6\">\r\n          {/* 分类头部 */}\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* 分类图标 */}\r\n              <div\r\n                className=\"w-12 h-12 rounded-lg flex items-center justify-center text-white font-semibold shadow-md\"\r\n                style={{ backgroundColor: category.color }}\r\n              >\r\n                {category.icon ? (\r\n                  <span className=\"text-xl\">{category.icon}</span>\r\n                ) : (\r\n                  <span className=\"text-lg\">{category.name.charAt(0)}</span>\r\n                )}\r\n              </div>\r\n\r\n              {/* 分类名称 */}\r\n              <div>\r\n                <h3 className=\"font-semibold text-lg text-base-content\">\r\n                  {category.name}\r\n                </h3>\r\n                {category.parentId && (\r\n                  <p className=\"text-xs text-base-content/60\">子分类</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* 操作按钮 */}\r\n            <div className=\"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n              <motion.button\r\n                onClick={handleEdit}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-ghost btn-sm\"\r\n                title=\"编辑分类\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n\r\n              <motion.button\r\n                onClick={handleDelete}\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                className=\"btn btn-ghost btn-sm hover:btn-error\"\r\n                title=\"删除分类\"\r\n              >\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                  />\r\n                </svg>\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 分类描述 */}\r\n          {category.description && (\r\n            <p className=\"text-sm text-base-content/80 mb-4 line-clamp-2\">\r\n              {category.description}\r\n            </p>\r\n          )}\r\n\r\n          {/* 统计信息 */}\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <div className=\"flex items-center space-x-4 text-base-content/60\">\r\n              <div className=\"flex items-center space-x-1\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n                  />\r\n                </svg>\r\n                <span>{category.promptCount || 0} 提示词</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-1\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className=\"w-4 h-4\"\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n                  />\r\n                </svg>\r\n                <span>{category.usageCount || 0} 次使用</span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 使用率进度条 */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-20 h-2 bg-base-300 rounded-full overflow-hidden\">\r\n                <motion.div\r\n                  className=\"h-full rounded-full\"\r\n                  style={{ backgroundColor: category.color }}\r\n                  initial={{ width: 0 }}\r\n                  animate={{\r\n                    width: `${Math.min(((category.usageCount || 0) / 100) * 100, 100)}%`,\r\n                  }}\r\n                  transition={{ delay: delay + 0.5, duration: 0.8 }}\r\n                />\r\n              </div>\r\n              <span className=\"text-xs text-base-content/50\">\r\n                {Math.round(((category.usageCount || 0) / 100) * 100)}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 最近更新时间 */}\r\n          <div className=\"mt-4 pt-4 border-t border-base-200\">\r\n            <p className=\"text-xs text-base-content/50\">\r\n              更新于 {new Date(category.updatedAt).toLocaleDateString('zh-CN')}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </Link>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcO,MAAM,eAAe;QAAC,EAC3B,QAAQ,EACR,WAAW,MAAM,EACjB,MAAM,EACN,QAAQ,EACR,QAAQ,CAAC,EACS;IAClB,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,IAAI,aAAa,QAAQ;QACvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE;YAAM;YACpB,WAAU;sBAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,AAAC,eAA0B,OAAZ,SAAS,EAAE;gBAAI,WAAU;0BAClD,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,SAAS,KAAK;oCAAC;8CAExC,SAAS,IAAI,iBACZ,6LAAC;wCAAK,WAAU;kDAAW,SAAS,IAAI;;;;;iGAExC,6LAAC;wCAAK,WAAU;kDAAW,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;8CAKpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;wCAEf,SAAS,WAAW,kBACnB,6LAAC;4CAAE,WAAU;sDACV,SAAS,WAAW;;;;;;sDAGzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAM,SAAS,WAAW,IAAI;wDAAE;;;;;;;8DACjC,6LAAC;;wDAAK;wDAAI,SAAS,UAAU,IAAI;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAKR,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASpB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG;QAAG;QAC1B,YAAY;YAAE;QAAM;QACpB,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,WAAU;kBAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM,AAAC,eAA0B,OAAZ,SAAS,EAAE;YAAI,WAAU;sBAClD,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,SAAS,KAAK;wCAAC;kDAExC,SAAS,IAAI,iBACZ,6LAAC;4CAAK,WAAU;sDAAW,SAAS,IAAI;;;;;qGAExC,6LAAC;4CAAK,WAAU;sDAAW,SAAS,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kDAKpD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;4CAEf,SAAS,QAAQ,kBAChB,6LAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAMlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;;;;;;kDAKR,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQX,SAAS,WAAW,kBACnB,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;kCAKzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAM;gDACN,MAAK;gDACL,SAAQ;gDACR,aAAa;gDACb,QAAO;gDACP,WAAU;0DAEV,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;0DAGN,6LAAC;;oDAAM,SAAS,WAAW,IAAI;oDAAE;;;;;;;;;;;;;kDAGnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAM;gDACN,MAAK;gDACL,SAAQ;gDACR,aAAa;gDACb,QAAO;gDACP,WAAU;0DAEV,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,GAAE;;;;;;;;;;;0DAGN,6LAAC;;oDAAM,SAAS,UAAU,IAAI;oDAAE;;;;;;;;;;;;;;;;;;;0CAKpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,OAAO;gDAAE,iBAAiB,SAAS,KAAK;4CAAC;4CACzC,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDACP,OAAO,AAAC,GAA0D,OAAxD,KAAK,GAAG,CAAC,AAAC,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,MAAO,KAAK,MAAK;4CACpE;4CACA,YAAY;gDAAE,OAAO,QAAQ;gDAAK,UAAU;4CAAI;;;;;;;;;;;kDAGpD,6LAAC;wCAAK,WAAU;;4CACb,KAAK,KAAK,CAAC,AAAC,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,MAAO;4CAAK;;;;;;;;;;;;;;;;;;;kCAM5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAA+B;gCACrC,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnE;KArRa", "debugId": null}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/categories/CategoryForm.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\nimport { toast } from 'react-hot-toast'\r\n\r\ninterface CategoryFormProps {\r\n  category?: Category | null\r\n  onClose: () => void\r\n}\r\n\r\nconst DEFAULT_COLORS = [\r\n  '#3b82f6', // blue\r\n  '#ef4444', // red\r\n  '#10b981', // green\r\n  '#f59e0b', // yellow\r\n  '#8b5cf6', // purple\r\n  '#ec4899', // pink\r\n  '#06b6d4', // cyan\r\n  '#84cc16', // lime\r\n  '#f97316', // orange\r\n  '#6b7280', // gray\r\n]\r\n\r\nconst DEFAULT_ICONS = [\r\n  '📁', '📂', '📋', '📝', '💡', '🎯', '🚀', '⭐', '🔥', '💎',\r\n  '🎨', '🔧', '⚙️', '📊', '🎮', '💻', '📱', '🌟', '🎪', '🎭',\r\n]\r\n\r\nexport const CategoryForm = ({ category, onClose }: CategoryFormProps) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    description: '',\r\n    color: DEFAULT_COLORS[0],\r\n    icon: '',\r\n    parentId: null as string | null,\r\n  })\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({})\r\n  const [showColorPicker, setShowColorPicker] = useState(false)\r\n  const [showIconPicker, setShowIconPicker] = useState(false)\r\n  const [customColor, setCustomColor] = useState('')\r\n\r\n  // 获取所有分类（用于父分类选择）\r\n  const { data: categories } = api.categories.getAll.useQuery()\r\n\r\n  // 创建分类\r\n  const createMutation = api.categories.create.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类创建成功')\r\n      onClose()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('创建失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 更新分类\r\n  const updateMutation = api.categories.update.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类更新成功')\r\n      onClose()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('更新失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 初始化表单数据\r\n  useEffect(() => {\r\n    if (category) {\r\n      setFormData({\r\n        name: category.name,\r\n        description: category.description || '',\r\n        color: category.color,\r\n        icon: category.icon || '',\r\n        parentId: category.parentId,\r\n      })\r\n    }\r\n  }, [category])\r\n\r\n  // 表单验证\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {}\r\n\r\n    if (!formData.name.trim()) {\r\n      newErrors.name = '分类名称不能为空'\r\n    } else if (formData.name.length > 50) {\r\n      newErrors.name = '分类名称不能超过50个字符'\r\n    }\r\n\r\n    if (formData.description && formData.description.length > 200) {\r\n      newErrors.description = '描述不能超过200个字符'\r\n    }\r\n\r\n    if (!formData.color) {\r\n      newErrors.color = '请选择分类颜色'\r\n    }\r\n\r\n    setErrors(newErrors)\r\n    return Object.keys(newErrors).length === 0\r\n  }\r\n\r\n  // 处理提交\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n\r\n    if (!validateForm()) {\r\n      return\r\n    }\r\n\r\n    const submitData = {\r\n      ...formData,\r\n      description: formData.description || undefined,\r\n      icon: formData.icon || undefined,\r\n      parentId: formData.parentId || undefined,\r\n    }\r\n\r\n    if (category) {\r\n      updateMutation.mutate({\r\n        id: category.id,\r\n        ...submitData,\r\n      })\r\n    } else {\r\n      createMutation.mutate(submitData)\r\n    }\r\n  }\r\n\r\n  // 处理颜色选择\r\n  const handleColorSelect = (color: string) => {\r\n    setFormData(prev => ({ ...prev, color }))\r\n    setShowColorPicker(false)\r\n  }\r\n\r\n  // 处理自定义颜色\r\n  const handleCustomColor = () => {\r\n    if (customColor && /^#[0-9A-F]{6}$/i.test(customColor)) {\r\n      handleColorSelect(customColor)\r\n      setCustomColor('')\r\n    } else {\r\n      toast.error('请输入有效的颜色值 (如: #FF0000)')\r\n    }\r\n  }\r\n\r\n  // 处理图标选择\r\n  const handleIconSelect = (icon: string) => {\r\n    setFormData(prev => ({ ...prev, icon }))\r\n    setShowIconPicker(false)\r\n  }\r\n\r\n  // 获取可用的父分类选项\r\n  const getParentOptions = () => {\r\n    if (!categories) return []\r\n    \r\n    return categories.filter(cat => {\r\n      // 排除当前分类和其子分类\r\n      if (category) {\r\n        return cat.id !== category.id && cat.parentId !== category.id\r\n      }\r\n      return true\r\n    })\r\n  }\r\n\r\n  const isLoading = createMutation.isLoading || updateMutation.isLoading\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\r\n      onClick={(e) => e.target === e.currentTarget && onClose()}\r\n    >\r\n      <motion.div\r\n        initial={{ scale: 0.95, opacity: 0, y: 20 }}\r\n        animate={{ scale: 1, opacity: 1, y: 0 }}\r\n        exit={{ scale: 0.95, opacity: 0, y: 20 }}\r\n        transition={{ type: \"spring\", duration: 0.3 }}\r\n        className=\"bg-base-100 rounded-xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-hidden border border-base-200\"\r\n      >\r\n        {/* 表单头部 */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-base-200 bg-base-50\">\r\n          <h2 className=\"text-2xl font-bold text-base-content\">\r\n            {category ? '编辑分类' : '新建分类'}\r\n          </h2>\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            className=\"btn btn-ghost btn-sm btn-circle hover:bg-base-200\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={2}\r\n              stroke=\"currentColor\"\r\n              className=\"w-5 h-5\"\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* 表单内容 */}\r\n        <div className=\"overflow-y-auto max-h-[calc(90vh-140px)]\">\r\n          <form id=\"category-form\" onSubmit={handleSubmit} className=\"p-6 space-y-6\">\r\n\r\n            {/* 分类名称 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">分类名称 *</span>\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                value={formData.name}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\r\n                className={`input input-bordered w-full ${errors.name ? 'input-error' : 'focus:input-primary'}`}\r\n                placeholder=\"输入分类名称\"\r\n                maxLength={50}\r\n                autoFocus\r\n              />\r\n              {errors.name && (\r\n                <label className=\"label\">\r\n                  <span className=\"label-text-alt text-error\">{errors.name}</span>\r\n                </label>\r\n              )}\r\n              <label className=\"label\">\r\n                <span className=\"label-text-alt text-base-content/60\">{formData.name.length}/50</span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* 分类描述 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">描述</span>\r\n              </label>\r\n              <textarea\r\n                value={formData.description}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n                className={`textarea textarea-bordered h-24 resize-none ${errors.description ? 'textarea-error' : 'focus:textarea-primary'}`}\r\n                placeholder=\"输入分类描述（可选）\"\r\n                maxLength={200}\r\n              />\r\n              {errors.description && (\r\n                <label className=\"label\">\r\n                  <span className=\"label-text-alt text-error\">{errors.description}</span>\r\n                </label>\r\n              )}\r\n              <label className=\"label\">\r\n                <span className=\"label-text-alt text-base-content/60\">{formData.description.length}/200</span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* 父分类选择 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">父分类</span>\r\n              </label>\r\n              <select\r\n                value={formData.parentId || ''}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, parentId: e.target.value || null }))}\r\n                className=\"select select-bordered w-full focus:select-primary\"\r\n              >\r\n                <option value=\"\">无（顶级分类）</option>\r\n                {getParentOptions().map(cat => (\r\n                  <option key={cat.id} value={cat.id}>\r\n                    {cat.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* 颜色和图标选择 */}\r\n            <div className=\"grid grid-cols-2 gap-6\">\r\n              {/* 颜色选择 */}\r\n              <div className=\"form-control\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">颜色 *</span>\r\n                </label>\r\n                <div className=\"relative\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setShowColorPicker(!showColorPicker)}\r\n                    className=\"w-full h-12 rounded-lg border-2 border-base-300 flex items-center justify-center hover:border-primary transition-colors\"\r\n                    style={{ backgroundColor: formData.color }}\r\n                  >\r\n                    <span className=\"text-white font-medium text-sm\">{formData.color}</span>\r\n                  </button>\r\n                \r\n                  <AnimatePresence>\r\n                    {showColorPicker && (\r\n                      <motion.div\r\n                        initial={{ opacity: 0, scale: 0.9, y: -10 }}\r\n                        animate={{ opacity: 1, scale: 1, y: 0 }}\r\n                        exit={{ opacity: 0, scale: 0.9, y: -10 }}\r\n                        className=\"absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-4 shadow-xl z-20 min-w-[200px]\"\r\n                      >\r\n                        <div className=\"grid grid-cols-5 gap-2 mb-4\">\r\n                          {DEFAULT_COLORS.map(color => (\r\n                            <button\r\n                              key={color}\r\n                              type=\"button\"\r\n                              onClick={() => handleColorSelect(color)}\r\n                              className={`w-8 h-8 rounded-md border-2 hover:scale-110 transition-all ${\r\n                                formData.color === color ? 'border-primary ring-2 ring-primary/30' : 'border-base-300'\r\n                              }`}\r\n                              style={{ backgroundColor: color }}\r\n                            />\r\n                          ))}\r\n                        </div>\r\n                        <div className=\"flex space-x-2\">\r\n                          <input\r\n                            type=\"text\"\r\n                            value={customColor}\r\n                            onChange={(e) => setCustomColor(e.target.value)}\r\n                            className=\"input input-sm flex-1\"\r\n                            placeholder=\"#FF0000\"\r\n                          />\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={handleCustomColor}\r\n                            className=\"btn btn-sm btn-primary\"\r\n                          >\r\n                            应用\r\n                          </button>\r\n                        </div>\r\n                      </motion.div>\r\n                    )}\r\n                  </AnimatePresence>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 图标选择 */}\r\n              <div className=\"form-control\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">图标</span>\r\n                </label>\r\n                <div className=\"relative\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setShowIconPicker(!showIconPicker)}\r\n                    className=\"w-full h-12 border-2 border-base-300 rounded-lg flex items-center justify-center text-2xl hover:border-primary hover:bg-base-50 transition-colors\"\r\n                  >\r\n                    {formData.icon || '📁'}\r\n                  </button>\r\n                \r\n                  <AnimatePresence>\r\n                    {showIconPicker && (\r\n                      <motion.div\r\n                        initial={{ opacity: 0, scale: 0.9, y: -10 }}\r\n                        animate={{ opacity: 1, scale: 1, y: 0 }}\r\n                        exit={{ opacity: 0, scale: 0.9, y: -10 }}\r\n                        className=\"absolute top-full left-0 mt-2 bg-base-100 border border-base-300 rounded-lg p-4 shadow-xl z-20 min-w-[200px]\"\r\n                      >\r\n                        <div className=\"grid grid-cols-5 gap-2 mb-3\">\r\n                          {DEFAULT_ICONS.map(icon => (\r\n                            <button\r\n                              key={icon}\r\n                              type=\"button\"\r\n                              onClick={() => handleIconSelect(icon)}\r\n                              className={`w-8 h-8 flex items-center justify-center text-lg hover:bg-primary hover:text-white rounded-md transition-all ${\r\n                                formData.icon === icon ? 'bg-primary text-white' : 'hover:bg-base-200'\r\n                              }`}\r\n                            >\r\n                              {icon}\r\n                            </button>\r\n                          ))}\r\n                        </div>\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => handleIconSelect('')}\r\n                          className=\"btn btn-sm btn-ghost w-full\"\r\n                        >\r\n                          使用默认图标\r\n                        </button>\r\n                      </motion.div>\r\n                    )}\r\n                  </AnimatePresence>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* 预览 */}\r\n            <div className=\"form-control\">\r\n              <label className=\"label\">\r\n                <span className=\"label-text font-medium\">预览</span>\r\n              </label>\r\n              <div className=\"flex items-center space-x-4 p-4 bg-gradient-to-r from-base-200 to-base-100 rounded-lg border border-base-300\">\r\n                <div\r\n                  className=\"w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg shadow-md\"\r\n                  style={{ backgroundColor: formData.color }}\r\n                >\r\n                  {formData.icon || formData.name.charAt(0) || '📁'}\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"font-semibold text-base-content truncate\">\r\n                    {formData.name || '分类名称'}\r\n                  </p>\r\n                  {formData.description && (\r\n                    <p className=\"text-sm text-base-content/70 truncate mt-1\">\r\n                      {formData.description}\r\n                    </p>\r\n                  )}\r\n                  {formData.parentId && (\r\n                    <p className=\"text-xs text-primary mt-1\">\r\n                      子分类 • 父级：{categories?.find(c => c.id === formData.parentId)?.name}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n\r\n        {/* 操作按钮 */}\r\n        <div className=\"flex justify-end space-x-3 p-6 border-t border-base-200 bg-base-50\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            className=\"btn btn-ghost\"\r\n            disabled={isLoading}\r\n          >\r\n            取消\r\n          </button>\r\n          <button\r\n            type=\"submit\"\r\n            form=\"category-form\"\r\n            className=\"btn btn-primary min-w-[100px]\"\r\n            disabled={isLoading || !formData.name.trim()}\r\n          >\r\n            {isLoading && <span className=\"loading loading-spinner loading-sm mr-2\"></span>}\r\n            {category ? '更新分类' : '创建分类'}\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n    </motion.div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;;;AANA;;;;;AAaA,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAK;IAAM;IACrD;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CACvD;AAEM,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE,OAAO,EAAqB;QAwXrC;;IAvX9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,OAAO,cAAc,CAAC,EAAE;QACxB,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kBAAkB;IAClB,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE3D,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;wDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;wDAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;;QACA,OAAO;wDAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;gBACZ,YAAY;oBACV,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW,IAAI;oBACrC,OAAO,SAAS,KAAK;oBACrB,MAAM,SAAS,IAAI,IAAI;oBACvB,UAAU,SAAS,QAAQ;gBAC7B;YACF;QACF;iCAAG;QAAC;KAAS;IAEb,OAAO;IACP,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YACpC,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,aAAa;YACjB,GAAG,QAAQ;YACX,aAAa,SAAS,WAAW,IAAI;YACrC,MAAM,SAAS,IAAI,IAAI;YACvB,UAAU,SAAS,QAAQ,IAAI;QACjC;QAEA,IAAI,UAAU;YACZ,eAAe,MAAM,CAAC;gBACpB,IAAI,SAAS,EAAE;gBACf,GAAG,UAAU;YACf;QACF,OAAO;YACL,eAAe,MAAM,CAAC;QACxB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAM,CAAC;QACvC,mBAAmB;IACrB;IAEA,UAAU;IACV,MAAM,oBAAoB;QACxB,IAAI,eAAe,kBAAkB,IAAI,CAAC,cAAc;YACtD,kBAAkB;YAClB,eAAe;QACjB,OAAO;YACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;QACtC,kBAAkB;IACpB;IAEA,aAAa;IACb,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,cAAc;YACd,IAAI,UAAU;gBACZ,OAAO,IAAI,EAAE,KAAK,SAAS,EAAE,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC/D;YACA,OAAO;QACT;IACF;IAEA,MAAM,YAAY,eAAe,SAAS,IAAI,eAAe,SAAS;IAEtE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAU;QACV,SAAS,CAAC,IAAM,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI;kBAEhD,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,OAAO;gBAAM,SAAS;gBAAG,GAAG;YAAG;YAC1C,SAAS;gBAAE,OAAO;gBAAG,SAAS;gBAAG,GAAG;YAAE;YACtC,MAAM;gBAAE,OAAO;gBAAM,SAAS;gBAAG,GAAG;YAAG;YACvC,YAAY;gBAAE,MAAM;gBAAU,UAAU;YAAI;YAC5C,WAAU;;8BAGV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,WAAW,SAAS;;;;;;sCAEvB,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,aAAa;gCACb,QAAO;gCACP,WAAU;0CAEV,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAM3D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,IAAG;wBAAgB,UAAU;wBAAc,WAAU;;0CAGzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,WAAW,AAAC,+BAAkF,OAApD,OAAO,IAAI,GAAG,gBAAgB;wCACxE,aAAY;wCACZ,WAAW;wCACX,SAAS;;;;;;oCAEV,OAAO,IAAI,kBACV,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAA6B,OAAO,IAAI;;;;;;;;;;;kDAG5D,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;;gDAAuC,SAAS,IAAI,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAKhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC;wCACC,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,WAAW,AAAC,+CAA+G,OAAjE,OAAO,WAAW,GAAG,mBAAmB;wCAClG,aAAY;wCACZ,WAAW;;;;;;oCAEZ,OAAO,WAAW,kBACjB,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAA6B,OAAO,WAAW;;;;;;;;;;;kDAGnE,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;;gDAAuC,SAAS,WAAW,CAAC,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAKvF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC;wCACC,OAAO,SAAS,QAAQ,IAAI;wCAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;gDAAK,CAAC;wCACnF,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,mBAAmB,GAAG,CAAC,CAAA,oBACtB,6LAAC;oDAAoB,OAAO,IAAI,EAAE;8DAC/B,IAAI,IAAI;mDADE,IAAI,EAAE;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,mBAAmB,CAAC;wDACnC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,SAAS,KAAK;wDAAC;kEAEzC,cAAA,6LAAC;4DAAK,WAAU;sEAAkC,SAAS,KAAK;;;;;;;;;;;kEAGlE,6LAAC,4LAAA,CAAA,kBAAe;kEACb,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;gEAAG,OAAO;gEAAK,GAAG,CAAC;4DAAG;4DAC1C,SAAS;gEAAE,SAAS;gEAAG,OAAO;gEAAG,GAAG;4DAAE;4DACtC,MAAM;gEAAE,SAAS;gEAAG,OAAO;gEAAK,GAAG,CAAC;4DAAG;4DACvC,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;8EACZ,eAAe,GAAG,CAAC,CAAA,sBAClB,6LAAC;4EAEC,MAAK;4EACL,SAAS,IAAM,kBAAkB;4EACjC,WAAW,AAAC,8DAEX,OADC,SAAS,KAAK,KAAK,QAAQ,0CAA0C;4EAEvE,OAAO;gFAAE,iBAAiB;4EAAM;2EAN3B;;;;;;;;;;8EAUX,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,MAAK;4EACL,OAAO;4EACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4EAC9C,WAAU;4EACV,aAAY;;;;;;sFAEd,6LAAC;4EACC,MAAK;4EACL,SAAS;4EACT,WAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,kBAAkB,CAAC;wDAClC,WAAU;kEAET,SAAS,IAAI,IAAI;;;;;;kEAGpB,6LAAC,4LAAA,CAAA,kBAAe;kEACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;gEAAG,OAAO;gEAAK,GAAG,CAAC;4DAAG;4DAC1C,SAAS;gEAAE,SAAS;gEAAG,OAAO;gEAAG,GAAG;4DAAE;4DACtC,MAAM;gEAAE,SAAS;gEAAG,OAAO;gEAAK,GAAG,CAAC;4DAAG;4DACvC,WAAU;;8EAEV,6LAAC;oEAAI,WAAU;8EACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;4EAEC,MAAK;4EACL,SAAS,IAAM,iBAAiB;4EAChC,WAAW,AAAC,gHAEX,OADC,SAAS,IAAI,KAAK,OAAO,0BAA0B;sFAGpD;2EAPI;;;;;;;;;;8EAWX,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,WAAU;8EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;kDAE3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;0DAExC,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,SAAS,IAAI,IAAI;;;;;;oDAEnB,SAAS,WAAW,kBACnB,6LAAC;wDAAE,WAAU;kEACV,SAAS,WAAW;;;;;;oDAGxB,SAAS,QAAQ,kBAChB,6LAAC;wDAAE,WAAU;;4DAA4B;4DAC7B,uBAAA,kCAAA,mBAAA,WAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,QAAQ,eAAhD,uCAAA,iBAAmD,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU/E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;4BACV,UAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,MAAK;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU,aAAa,CAAC,SAAS,IAAI,CAAC,IAAI;;gCAEzC,2BAAa,6LAAC;oCAAK,WAAU;;;;;;gCAC7B,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;GAxZa;KAAA", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/components/modals/ConfirmDeleteModal.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\n\r\ninterface ConfirmDeleteModalProps {\r\n  isOpen: boolean\r\n  onClose: () => void\r\n  onConfirm: () => void\r\n  title?: string\r\n  message?: string\r\n  itemName?: string\r\n  itemType?: string\r\n  isDangerous?: boolean\r\n  isLoading?: boolean\r\n  requireConfirmation?: boolean\r\n}\r\n\r\nexport const ConfirmDeleteModal = ({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  itemName,\r\n  itemType = '项目',\r\n  isDangerous = false,\r\n  isLoading = false,\r\n  requireConfirmation = false,\r\n}: ConfirmDeleteModalProps) => {\r\n  const [confirmText, setConfirmText] = useState('')\r\n  const [isConfirmed, setIsConfirmed] = useState(false)\r\n\r\n  const defaultTitle = title || `删除${itemType}`\r\n  const defaultMessage = message || `确定要删除这个${itemType}吗？此操作无法撤销。`\r\n  const confirmationText = itemName || '删除'\r\n  const canConfirm = !requireConfirmation || confirmText === confirmationText\r\n\r\n  const handleConfirm = () => {\r\n    if (canConfirm) {\r\n      onConfirm()\r\n    }\r\n  }\r\n\r\n  const handleClose = () => {\r\n    setConfirmText('')\r\n    setIsConfirmed(false)\r\n    onClose()\r\n  }\r\n\r\n  if (!isOpen) return null\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        exit={{ opacity: 0 }}\r\n        className=\"fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\r\n        onClick={handleClose}\r\n      >\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          exit={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n          className=\"bg-base-100 rounded-lg shadow-xl w-full max-w-md overflow-hidden\"\r\n          onClick={(e) => e.stopPropagation()}\r\n        >\r\n          {/* 头部 */}\r\n          <div className=\"bg-base-200 border-b border-base-300 p-4\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              {/* 警告图标 */}\r\n              <div className={`p-2 rounded-full ${isDangerous ? 'bg-error/20' : 'bg-warning/20'}`}>\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  strokeWidth={1.5}\r\n                  stroke=\"currentColor\"\r\n                  className={`w-6 h-6 ${isDangerous ? 'text-error' : 'text-warning'}`}\r\n                >\r\n                  <path\r\n                    strokeLinecap=\"round\"\r\n                    strokeLinejoin=\"round\"\r\n                    d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                  />\r\n                </svg>\r\n              </div>\r\n              \r\n              <div>\r\n                <h3 className=\"text-lg font-semibold text-base-content\">{defaultTitle}</h3>\r\n                <p className=\"text-sm text-base-content/70\">请确认您的操作</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* 内容 */}\r\n          <div className=\"p-4 space-y-4\">\r\n            {/* 主要消息 */}\r\n            <div className=\"space-y-2\">\r\n              <p className=\"text-base-content\">{defaultMessage}</p>\r\n              \r\n              {itemName && (\r\n                <div className=\"bg-base-200 rounded-lg p-3\">\r\n                  <p className=\"text-sm text-base-content/70 mb-1\">要删除的{itemType}:</p>\r\n                  <p className=\"font-medium text-base-content break-words\">{itemName}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 危险警告 */}\r\n            {isDangerous && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"bg-error/10 border border-error/20 rounded-lg p-3\"\r\n              >\r\n                <div className=\"flex items-start space-x-2\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-5 h-5 text-error flex-shrink-0 mt-0.5\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"\r\n                    />\r\n                  </svg>\r\n                  <div className=\"text-sm\">\r\n                    <p className=\"font-medium text-error\">危险操作</p>\r\n                    <p className=\"text-error/80 mt-1\">\r\n                      此操作将永久删除数据，无法恢复。如果您确定要继续，请在下方输入确认信息。\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            {/* 确认输入 */}\r\n            {requireConfirmation && (\r\n              <div className=\"space-y-2\">\r\n                <label className=\"label\">\r\n                  <span className=\"label-text font-medium\">\r\n                    请输入 <code className=\"bg-base-200 px-2 py-1 rounded text-sm\">{confirmationText}</code> 以确认删除:\r\n                  </span>\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={confirmText}\r\n                  onChange={(e) => setConfirmText(e.target.value)}\r\n                  className=\"input input-bordered w-full\"\r\n                  placeholder={`输入 \"${confirmationText}\"`}\r\n                  autoComplete=\"off\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* 附加确认选项 */}\r\n            {isDangerous && (\r\n              <div className=\"form-control\">\r\n                <label className=\"label cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={isConfirmed}\r\n                    onChange={(e) => setIsConfirmed(e.target.checked)}\r\n                    className=\"checkbox checkbox-error\"\r\n                  />\r\n                  <span className=\"label-text ml-2\">我理解此操作的后果并确认删除</span>\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 底部操作 */}\r\n          <div className=\"bg-base-200 border-t border-base-300 p-4 flex items-center justify-end space-x-3\">\r\n            <motion.button\r\n              onClick={handleClose}\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"btn btn-ghost\"\r\n              disabled={isLoading}\r\n            >\r\n              取消\r\n            </motion.button>\r\n            \r\n            <motion.button\r\n              onClick={handleConfirm}\r\n              whileHover={{ scale: canConfirm ? 1.05 : 1 }}\r\n              whileTap={{ scale: canConfirm ? 0.95 : 1 }}\r\n              disabled={!canConfirm || isLoading || (isDangerous && !isConfirmed)}\r\n              className={`btn ${isDangerous ? 'btn-error' : 'btn-warning'}`}\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <span className=\"loading loading-spinner loading-sm\"></span>\r\n                  删除中...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    strokeWidth={1.5}\r\n                    stroke=\"currentColor\"\r\n                    className=\"w-4 h-4\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      d=\"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0\"\r\n                    />\r\n                  </svg>\r\n                  确认删除\r\n                </>\r\n              )}\r\n            </motion.button>\r\n          </div>\r\n        </motion.div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAkBO,MAAM,qBAAqB;QAAC,EACjC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACR,WAAW,IAAI,EACf,cAAc,KAAK,EACnB,YAAY,KAAK,EACjB,sBAAsB,KAAK,EACH;;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,SAAS,AAAC,KAAa,OAAT;IACnC,MAAM,iBAAiB,WAAW,AAAC,UAAkB,OAAT,UAAS;IACrD,MAAM,mBAAmB,YAAY;IACrC,MAAM,aAAa,CAAC,uBAAuB,gBAAgB;IAE3D,MAAM,gBAAgB;QACpB,IAAI,YAAY;YACd;QACF;IACF;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,eAAe;QACf;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAK,GAAG;gBAAG;gBACzC,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAG,GAAG;gBAAE;gBACtC,MAAM;oBAAE,SAAS;oBAAG,OAAO;oBAAK,GAAG;gBAAG;gBACtC,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;gBAC1D,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,AAAC,oBAAiE,OAA9C,cAAc,gBAAgB;8CAChE,cAAA,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAW,AAAC,WAAsD,OAA5C,cAAc,eAAe;kDAEnD,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,GAAE;;;;;;;;;;;;;;;;8CAKR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;oCAEjC,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAoC;oDAAK;oDAAS;;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;4BAM/D,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;4BASzC,qCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDACf,cAAA,6LAAC;4CAAK,WAAU;;gDAAyB;8DACnC,6LAAC;oDAAK,WAAU;8DAAyC;;;;;;gDAAwB;;;;;;;;;;;;kDAGzF,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,aAAa,AAAC,OAAuB,OAAjB,kBAAiB;wCACrC,cAAa;;;;;;;;;;;;4BAMlB,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;4CAChD,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,UAAU;0CACX;;;;;;0CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,YAAY;oCAAE,OAAO,aAAa,OAAO;gCAAE;gCAC3C,UAAU;oCAAE,OAAO,aAAa,OAAO;gCAAE;gCACzC,UAAU,CAAC,cAAc,aAAc,eAAe,CAAC;gCACvD,WAAW,AAAC,OAAgD,OAA1C,cAAc,cAAc;0CAE7C,0BACC;;sDACE,6LAAC;4CAAK,WAAU;;;;;;wCAA4C;;iEAI9D;;sDACE,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;wCAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GAjNa;KAAA", "debugId": null}}, {"offset": {"line": 1925, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/app/categories/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState } from 'react'\r\nimport { motion, AnimatePresence } from 'framer-motion'\r\nimport { api } from '~/trpc/react'\r\nimport { Category } from '~/types'\r\nimport { CategoryCard } from '~/components/categories/CategoryCard'\r\nimport { CategoryForm } from '~/components/categories/CategoryForm'\r\nimport { ConfirmDeleteModal } from '~/components/modals/ConfirmDeleteModal'\r\nimport { toast } from 'react-hot-toast'\r\n\r\nexport default function CategoriesPage() {\r\n  const [isCreating, setIsCreating] = useState(false)\r\n  const [editingCategory, setEditingCategory] = useState<Category | null>(null)\r\n  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null)\r\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\r\n\r\n  // 获取分类列表\r\n  const { data: categories, isLoading, refetch } = api.categories.getAll.useQuery()\r\n\r\n  // 删除分类\r\n  const deleteMutation = api.categories.delete.useMutation({\r\n    onSuccess: () => {\r\n      toast.success('分类删除成功')\r\n      setDeletingCategory(null)\r\n      refetch()\r\n    },\r\n    onError: (error) => {\r\n      toast.error('删除失败: ' + error.message)\r\n    },\r\n  })\r\n\r\n  // 处理删除\r\n  const handleDelete = (category: Category) => {\r\n    setDeletingCategory(category)\r\n  }\r\n\r\n  // 确认删除\r\n  const confirmDelete = () => {\r\n    if (deletingCategory) {\r\n      deleteMutation.mutate({ id: deletingCategory.id })\r\n    }\r\n  }\r\n\r\n  // 处理编辑\r\n  const handleEdit = (category: Category) => {\r\n    setEditingCategory(category)\r\n  }\r\n\r\n  // 处理表单关闭\r\n  const handleFormClose = () => {\r\n    setIsCreating(false)\r\n    setEditingCategory(null)\r\n    refetch()\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"animate-pulse space-y-6\">\r\n          {/* 标题骨架 */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"h-8 w-32 bg-base-300 rounded\"></div>\r\n            <div className=\"h-10 w-24 bg-base-300 rounded\"></div>\r\n          </div>\r\n          \r\n          {/* 统计骨架 */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n            {Array.from({ length: 3 }, (_, i) => (\r\n              <div key={i} className=\"bg-base-100 rounded-lg p-4 space-y-3\">\r\n                <div className=\"h-4 w-20 bg-base-300 rounded\"></div>\r\n                <div className=\"h-8 w-16 bg-base-300 rounded\"></div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          {/* 分类卡片骨架 */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            {Array.from({ length: 6 }, (_, i) => (\r\n              <div key={i} className=\"bg-base-100 rounded-lg p-6 space-y-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-12 h-12 bg-base-300 rounded-lg\"></div>\r\n                  <div className=\"space-y-2 flex-1\">\r\n                    <div className=\"h-5 w-24 bg-base-300 rounded\"></div>\r\n                    <div className=\"h-4 w-32 bg-base-300 rounded\"></div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"h-12 bg-base-300 rounded\"></div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  const totalCategories = categories?.length || 0\r\n  const totalPrompts = categories?.reduce((sum, cat) => sum + (cat.promptCount || 0), 0) || 0\r\n  const totalUsage = categories?.reduce((sum, cat) => sum + (cat.usageCount || 0), 0) || 0\r\n\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-8 space-y-6\">\r\n      {/* 页面头部 */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <h1 className=\"text-3xl font-bold text-base-content\">分类管理</h1>\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* 视图切换 */}\r\n          <div className=\"btn-group\">\r\n            <button\r\n              onClick={() => setViewMode('grid')}\r\n              className={`btn btn-sm ${viewMode === 'grid' ? 'btn-active' : ''}`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            <button\r\n              onClick={() => setViewMode('list')}\r\n              className={`btn btn-sm ${viewMode === 'list' ? 'btn-active' : ''}`}\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                strokeWidth={1.5}\r\n                stroke=\"currentColor\"\r\n                className=\"w-4 h-4\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  d=\"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* 新建分类按钮 */}\r\n          <button\r\n            onClick={() => setIsCreating(true)}\r\n            className=\"btn btn-primary btn-sm\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-4 h-4\"\r\n            >\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\r\n            </svg>\r\n            新建分类\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 统计信息 */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-figure text-primary\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"stat-title\">总分类数</div>\r\n          <div className=\"stat-value text-primary\">{totalCategories}</div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-figure text-secondary\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"stat-title\">提示词总数</div>\r\n          <div className=\"stat-value text-secondary\">{totalPrompts}</div>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"stat bg-base-100 rounded-lg shadow-md\"\r\n        >\r\n          <div className=\"stat-figure text-accent\">\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              fill=\"none\"\r\n              viewBox=\"0 0 24 24\"\r\n              strokeWidth={1.5}\r\n              stroke=\"currentColor\"\r\n              className=\"w-8 h-8\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"stat-title\">使用总次数</div>\r\n          <div className=\"stat-value text-accent\">{totalUsage}</div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* 分类列表 */}\r\n      {categories && categories.length > 0 ? (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 0.3 }}\r\n          className={\r\n            viewMode === 'grid'\r\n              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'\r\n              : 'space-y-4'\r\n          }\r\n        >\r\n          <AnimatePresence>\r\n            {categories.map((category, index) => (\r\n              <CategoryCard\r\n                key={category.id}\r\n                category={category}\r\n                viewMode={viewMode}\r\n                onEdit={handleEdit}\r\n                onDelete={handleDelete}\r\n                delay={index * 0.1}\r\n              />\r\n            ))}\r\n          </AnimatePresence>\r\n        </motion.div>\r\n      ) : (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"text-center py-12\"\r\n        >\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n            strokeWidth={1.5}\r\n            stroke=\"currentColor\"\r\n            className=\"w-16 h-16 mx-auto text-base-content/50 mb-4\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              d=\"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z\"\r\n            />\r\n          </svg>\r\n          <h3 className=\"text-lg font-semibold text-base-content mb-2\">暂无分类</h3>\r\n          <p className=\"text-base-content/70 mb-4\">创建第一个分类来组织你的提示词</p>\r\n          <button\r\n            onClick={() => setIsCreating(true)}\r\n            className=\"btn btn-primary\"\r\n          >\r\n            创建分类\r\n          </button>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* 新建/编辑分类表单 */}\r\n      <AnimatePresence>\r\n        {(isCreating || editingCategory) && (\r\n          <CategoryForm\r\n            category={editingCategory}\r\n            onClose={handleFormClose}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 删除确认对话框 */}\r\n      <AnimatePresence>\r\n        {deletingCategory && (\r\n          <ConfirmDeleteModal\r\n            isOpen={!!deletingCategory}\r\n            onClose={() => setDeletingCategory(null)}\r\n            onConfirm={confirmDelete}\r\n            title=\"删除分类\"\r\n            message={`确定要删除分类 \"${deletingCategory.name}\" 吗？删除后该分类下的所有提示词将移至\"未分类\"。`}\r\n            type=\"warning\"\r\n            confirmText=\"删除\"\r\n            isLoading={deleteMutation.isLoading}\r\n          />\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,SAAS;IACT,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ;IAE/E,OAAO;IACP,MAAM,iBAAiB,wHAAA,CAAA,MAAG,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC;QACvD,SAAS;0DAAE;gBACT,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,oBAAoB;gBACpB;YACF;;QACA,OAAO;0DAAE,CAAC;gBACR,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,MAAM,OAAO;YACtC;;IACF;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,oBAAoB;IACtB;IAEA,OAAO;IACP,MAAM,gBAAgB;QACpB,IAAI,kBAAkB;YACpB,eAAe,MAAM,CAAC;gBAAE,IAAI,iBAAiB,EAAE;YAAC;QAClD;IACF;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,mBAAmB;IACrB;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,cAAc;QACd,mBAAmB;QACnB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAFP;;;;;;;;;;kCAQd,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;;;;;;;+BARP;;;;;;;;;;;;;;;;;;;;;IAetB;IAEA,MAAM,kBAAkB,CAAA,uBAAA,iCAAA,WAAY,MAAM,KAAI;IAC9C,MAAM,eAAe,CAAA,uBAAA,iCAAA,WAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,WAAW,IAAI,CAAC,GAAG,OAAM;IAC1F,MAAM,aAAa,CAAA,uBAAA,iCAAA,WAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG,OAAM;IAEvF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,AAAC,cAAqD,OAAxC,aAAa,SAAS,eAAe;kDAE9D,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,AAAC,cAAqD,OAAxC,aAAa,SAAS,eAAe;kDAE9D,cAAA,6LAAC;4CACC,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,aAAa;4CACb,QAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAOV,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;;kDAEV,6LAAC;wCACC,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,aAAa;wCACb,QAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,GAAE;;;;;;;;;;;oCACjD;;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA2B;;;;;;;;;;;;kCAG5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;;;;;;;kCAG9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAM;oCACN,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;0CAAa;;;;;;0CAC5B,6LAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;YAK5C,cAAc,WAAW,MAAM,GAAG,kBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WACE,aAAa,SACT,yDACA;0BAGN,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,mJAAA,CAAA,eAAY;4BAEX,UAAU;4BACV,UAAU;4BACV,QAAQ;4BACR,UAAU;4BACV,OAAO,QAAQ;2BALV,SAAS,EAAE;;;;;;;;;;;;;;qCAWxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBACC,OAAM;wBACN,MAAK;wBACL,SAAQ;wBACR,aAAa;wBACb,QAAO;wBACP,WAAU;kCAEV,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,GAAE;;;;;;;;;;;kCAGN,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAC7D,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,6LAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC,4LAAA,CAAA,kBAAe;0BACb,CAAC,cAAc,eAAe,mBAC7B,6LAAC,mJAAA,CAAA,eAAY;oBACX,UAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,qJAAA,CAAA,qBAAkB;oBACjB,QAAQ,CAAC,CAAC;oBACV,SAAS,IAAM,oBAAoB;oBACnC,WAAW;oBACX,OAAM;oBACN,SAAS,AAAC,YAAiC,OAAtB,iBAAiB,IAAI,EAAC;oBAC3C,MAAK;oBACL,aAAY;oBACZ,WAAW,eAAe,SAAS;;;;;;;;;;;;;;;;;AAM/C;GAjUwB;KAAA", "debugId": null}}]}