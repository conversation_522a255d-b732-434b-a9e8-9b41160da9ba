{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/T3%20App/prompt/src/styles/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\",\n      \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\",\n      \"Courier New\", monospace;\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-2xl: 42rem;\n    --container-4xl: 56rem;\n    --container-6xl: 72rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --leading-relaxed: 1.625;\n    --radius-sm: 0.25rem;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-sans);\n    --default-mono-font-family: var(--font-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .diff {\n    position: relative;\n    display: grid;\n    width: 100%;\n    overflow: hidden;\n    webkit-user-select: none;\n    user-select: none;\n    direction: ltr;\n    container-type: inline-size;\n    grid-template-columns: auto 1fr;\n    &:focus-visible, &:has(.diff-item-1:focus-visible) {\n      outline-style: var(--tw-outline-style);\n      outline-width: 2px;\n      outline-offset: 1px;\n      outline-color: var(--color-base-content);\n    }\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 2px;\n      outline-offset: 1px;\n      outline-color: var(--color-base-content);\n      .diff-resizer {\n        min-width: 90cqi;\n        max-width: 90cqi;\n      }\n    }\n    &:has(.diff-item-2:focus-visible) {\n      outline-style: var(--tw-outline-style);\n      outline-width: 2px;\n      outline-offset: 1px;\n      .diff-resizer {\n        min-width: 10cqi;\n        max-width: 10cqi;\n      }\n    }\n    @supports (-webkit-overflow-scrolling: touch) and (overflow: -webkit-paged-x) {\n      &:focus {\n        .diff-resizer {\n          min-width: 10cqi;\n          max-width: 10cqi;\n        }\n      }\n      &:has(.diff-item-1:focus) {\n        .diff-resizer {\n          min-width: 90cqi;\n          max-width: 90cqi;\n        }\n      }\n    }\n  }\n  .modal {\n    pointer-events: none;\n    visibility: hidden;\n    position: fixed;\n    inset: calc(0.25rem * 0);\n    margin: calc(0.25rem * 0);\n    display: grid;\n    height: 100%;\n    max-height: none;\n    width: 100%;\n    max-width: none;\n    align-items: center;\n    justify-items: center;\n    background-color: transparent;\n    padding: calc(0.25rem * 0);\n    color: inherit;\n    overflow-x: hidden;\n    transition: translate 0.3s ease-out, visibility 0.3s allow-discrete, background-color 0.3s ease-out, opacity 0.1s ease-out;\n    overflow-y: hidden;\n    overscroll-behavior: contain;\n    z-index: 999;\n    &::backdrop {\n      display: none;\n    }\n    &.modal-open, &[open], &:target {\n      pointer-events: auto;\n      visibility: visible;\n      opacity: 100%;\n      background-color: oklch(0% 0 0/ 0.4);\n      .modal-box {\n        translate: 0 0;\n        scale: 1;\n        opacity: 1;\n      }\n    }\n    @starting-style {\n      &.modal-open, &[open], &:target {\n        visibility: hidden;\n        opacity: 0%;\n      }\n    }\n  }\n  .tooltip {\n    position: relative;\n    display: inline-block;\n    --tt-bg: var(--color-neutral);\n    --tt-off: calc(100% + 0.5rem);\n    --tt-tail: calc(100% + 1px + 0.25rem);\n    > :where(.tooltip-content), &:where([data-tip]):before {\n      position: absolute;\n      max-width: 20rem;\n      border-radius: var(--radius-field);\n      padding-inline: calc(0.25rem * 2);\n      padding-block: calc(0.25rem * 1);\n      text-align: center;\n      white-space: normal;\n      color: var(--color-neutral-content);\n      opacity: 0%;\n      font-size: 0.875rem;\n      line-height: 1.25;\n      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;\n      background-color: var(--tt-bg);\n      width: max-content;\n      pointer-events: none;\n      z-index: 2;\n      --tw-content: attr(data-tip);\n      content: var(--tw-content);\n    }\n    &:after {\n      position: absolute;\n      position: absolute;\n      opacity: 0%;\n      background-color: var(--tt-bg);\n      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;\n      content: \"\";\n      pointer-events: none;\n      width: 0.625rem;\n      height: 0.25rem;\n      display: block;\n      mask-repeat: no-repeat;\n      mask-position: -1px 0;\n      --mask-tooltip: url(\"data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A\");\n      mask-image: var(--mask-tooltip);\n    }\n    &.tooltip-open, &[data-tip]:not([data-tip=\"\"]):hover, &:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover, &:has(:focus-visible) {\n      > .tooltip-content, &[data-tip]:before, &:after {\n        opacity: 100%;\n        --tt-pos: 0rem;\n        transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n      }\n    }\n    > .tooltip-content, &[data-tip]:before {\n      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));\n      inset: auto auto var(--tt-off) 50%;\n    }\n    &:after {\n      transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));\n      inset: auto auto var(--tt-tail) 50%;\n    }\n  }\n  .tab {\n    position: relative;\n    display: inline-flex;\n    cursor: pointer;\n    appearance: none;\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: center;\n    text-align: center;\n    webkit-user-select: none;\n    user-select: none;\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-base-content);\n      }\n    }\n    --tab-p: 1rem;\n    --tab-bg: var(--color-base-100);\n    --tab-border-color: var(--color-base-300);\n    --tab-radius-ss: 0;\n    --tab-radius-se: 0;\n    --tab-radius-es: 0;\n    --tab-radius-ee: 0;\n    --tab-order: 0;\n    --tab-radius-min: calc(0.75rem - var(--border));\n    border-color: #0000;\n    order: var(--tab-order);\n    height: var(--tab-height);\n    font-size: 0.875rem;\n    padding-inline-start: var(--tab-p);\n    padding-inline-end: var(--tab-p);\n    &:is(input[type=\"radio\"]) {\n      min-width: fit-content;\n      &:after {\n        content: attr(aria-label);\n      }\n    }\n    &:is(label) {\n      position: relative;\n      input {\n        position: absolute;\n        inset: calc(0.25rem * 0);\n        cursor: pointer;\n        appearance: none;\n        opacity: 0%;\n      }\n    }\n    &:checked, &:is(label:has(:checked)), &:is(.tab-active, [aria-selected=\"true\"]) {\n      & + .tab-content {\n        display: block;\n        height: calc(100% - var(--tab-height) + var(--border));\n      }\n    }\n    &:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected=\"true\"]) {\n      color: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-base-content) 50%, transparent);\n      }\n    }\n    &:not(input):empty {\n      flex-grow: 1;\n      cursor: default;\n    }\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n    &:focus-visible, &:is(label:has(:checked:focus-visible)) {\n      outline: 2px solid currentColor;\n      outline-offset: -5px;\n    }\n    &[disabled] {\n      pointer-events: none;\n      opacity: 40%;\n    }\n  }\n  .menu {\n    display: flex;\n    width: fit-content;\n    flex-direction: column;\n    flex-wrap: wrap;\n    padding: calc(0.25rem * 2);\n    --menu-active-fg: var(--color-neutral-content);\n    --menu-active-bg: var(--color-neutral);\n    font-size: 0.875rem;\n    :where(li ul) {\n      position: relative;\n      margin-inline-start: calc(0.25rem * 4);\n      padding-inline-start: calc(0.25rem * 2);\n      white-space: nowrap;\n      &:before {\n        position: absolute;\n        inset-inline-start: calc(0.25rem * 0);\n        top: calc(0.25rem * 3);\n        bottom: calc(0.25rem * 3);\n        background-color: var(--color-base-content);\n        opacity: 10%;\n        width: var(--border);\n        content: \"\";\n      }\n    }\n    :where(li > .menu-dropdown:not(.menu-dropdown-show)) {\n      display: none;\n    }\n    :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {\n      display: grid;\n      grid-auto-flow: column;\n      align-content: flex-start;\n      align-items: center;\n      gap: calc(0.25rem * 2);\n      border-radius: var(--radius-field);\n      padding-inline: calc(0.25rem * 3);\n      padding-block: calc(0.25rem * 1.5);\n      text-align: start;\n      transition-property: color, background-color, box-shadow;\n      transition-duration: 0.2s;\n      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n      grid-auto-columns: minmax(auto, max-content) auto max-content;\n      text-wrap: balance;\n      user-select: none;\n    }\n    :where(li > details > summary) {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n      &::-webkit-details-marker {\n        display: none;\n      }\n    }\n    :where(li > details > summary), :where(li > .menu-dropdown-toggle) {\n      &:after {\n        justify-self: flex-end;\n        display: block;\n        height: 0.375rem;\n        width: 0.375rem;\n        rotate: -135deg;\n        translate: 0 -1px;\n        transition-property: rotate, translate;\n        transition-duration: 0.2s;\n        content: \"\";\n        transform-origin: 50% 50%;\n        box-shadow: 2px 2px inset;\n        pointer-events: none;\n      }\n    }\n    :where(li > details[open] > summary):after, :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {\n      rotate: 45deg;\n      translate: 0 1px;\n    }\n    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title), li:not(.menu-title, .disabled) > details > summary:not(.menu-title) ):not(.menu-active, :active, .btn) {\n      &.menu-focus, &:focus-visible {\n        cursor: pointer;\n        background-color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);\n        }\n        color: var(--color-base-content);\n        --tw-outline-style: none;\n        outline-style: none;\n        @media (forced-colors: active) {\n          outline: 2px solid transparent;\n          outline-offset: 2px;\n        }\n      }\n    }\n    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title):not(.menu-active, :active, .btn):hover, li:not(.menu-title, .disabled) > details > summary:not(.menu-title):not(.menu-active, :active, .btn):hover ) {\n      cursor: pointer;\n      background-color: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);\n      }\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n      box-shadow: 0 1px oklch(0% 0 0 / 0.01) inset, 0 -1px oklch(100% 0 0 / 0.01) inset;\n    }\n    :where(li:empty) {\n      background-color: var(--color-base-content);\n      opacity: 10%;\n      margin: 0.5rem 1rem;\n      height: 1px;\n    }\n    :where(li) {\n      position: relative;\n      display: flex;\n      flex-shrink: 0;\n      flex-direction: column;\n      flex-wrap: wrap;\n      align-items: stretch;\n      .badge {\n        justify-self: flex-end;\n      }\n      & > *:not(ul, .menu-title, details, .btn):active, & > *:not(ul, .menu-title, details, .btn).menu-active, & > details > summary:active {\n        --tw-outline-style: none;\n        outline-style: none;\n        @media (forced-colors: active) {\n          outline: 2px solid transparent;\n          outline-offset: 2px;\n        }\n        color: var(--menu-active-fg);\n        background-color: var(--menu-active-bg);\n        background-size: auto, calc(var(--noise) * 100%);\n        background-image: none, var(--fx-noise);\n        &:not(&:active) {\n          box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);\n        }\n      }\n      &.menu-disabled {\n        pointer-events: none;\n        color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n        }\n      }\n    }\n    .dropdown:focus-within {\n      .menu-dropdown-toggle:after {\n        rotate: 45deg;\n        translate: 0 1px;\n      }\n    }\n    .dropdown-content {\n      margin-top: calc(0.25rem * 2);\n      padding: calc(0.25rem * 2);\n      &:before {\n        display: none;\n      }\n    }\n  }\n  .dropdown {\n    position: relative;\n    display: inline-block;\n    position-area: var(--anchor-v, bottom) var(--anchor-h, span-right);\n    & > *:not(summary):focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n    .dropdown-content {\n      position: absolute;\n    }\n    &:not(details, .dropdown-open, .dropdown-hover:hover, :focus-within) {\n      .dropdown-content {\n        display: none;\n        transform-origin: top;\n        opacity: 0%;\n        scale: 95%;\n      }\n    }\n    &[popover], .dropdown-content {\n      z-index: 999;\n      animation: dropdown 0.2s;\n      transition-property: opacity, scale, display;\n      transition-behavior: allow-discrete;\n      transition-duration: 0.2s;\n      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    }\n    @starting-style {\n      &[popover], .dropdown-content {\n        scale: 95%;\n        opacity: 0;\n      }\n    }\n    &.dropdown-open, &:not(.dropdown-hover):focus, &:focus-within {\n      > [tabindex]:first-child {\n        pointer-events: none;\n      }\n      .dropdown-content {\n        opacity: 100%;\n      }\n    }\n    &.dropdown-hover:hover {\n      .dropdown-content {\n        opacity: 100%;\n        scale: 100%;\n      }\n    }\n    &:is(details) {\n      summary {\n        &::-webkit-details-marker {\n          display: none;\n        }\n      }\n    }\n    &.dropdown-open, &:focus, &:focus-within {\n      .dropdown-content {\n        scale: 100%;\n      }\n    }\n    &:where([popover]) {\n      background: #0000;\n    }\n    &[popover] {\n      position: fixed;\n      color: inherit;\n      @supports not (position-area: bottom) {\n        margin: auto;\n        &.dropdown-open:not(:popover-open) {\n          display: none;\n          transform-origin: top;\n          opacity: 0%;\n          scale: 95%;\n        }\n        &::backdrop {\n          background-color: color-mix(in oklab, #000 30%, #0000);\n        }\n      }\n      &:not(.dropdown-open, :popover-open) {\n        display: none;\n        transform-origin: top;\n        opacity: 0%;\n        scale: 95%;\n      }\n    }\n  }\n  .btn {\n    :where(&) {\n      width: unset;\n    }\n    display: inline-flex;\n    flex-shrink: 0;\n    cursor: pointer;\n    flex-wrap: nowrap;\n    align-items: center;\n    justify-content: center;\n    gap: calc(0.25rem * 1.5);\n    text-align: center;\n    vertical-align: middle;\n    outline-offset: 2px;\n    webkit-user-select: none;\n    user-select: none;\n    padding-inline: var(--btn-p);\n    color: var(--btn-fg);\n    --tw-prose-links: var(--btn-fg);\n    height: var(--size);\n    font-size: var(--fontsize, 0.875rem);\n    font-weight: 600;\n    outline-color: var(--btn-color, var(--color-base-content));\n    transition-property: color, background-color, border-color, box-shadow;\n    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n    transition-duration: 0.2s;\n    border-start-start-radius: var(--join-ss, var(--radius-field));\n    border-start-end-radius: var(--join-se, var(--radius-field));\n    border-end-start-radius: var(--join-es, var(--radius-field));\n    border-end-end-radius: var(--join-ee, var(--radius-field));\n    background-color: var(--btn-bg);\n    background-size: auto, calc(var(--noise) * 100%);\n    background-image: none, var(--btn-noise);\n    border-width: var(--border);\n    border-style: solid;\n    border-color: var(--btn-border);\n    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));\n    touch-action: manipulation;\n    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);\n    --size: calc(var(--size-field, 0.25rem) * 10);\n    --btn-bg: var(--btn-color, var(--color-base-200));\n    --btn-fg: var(--color-base-content);\n    --btn-p: 1rem;\n    --btn-border: var(--btn-bg);\n    @supports (color: color-mix(in lab, red, red)) {\n      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));\n    }\n    --btn-shadow: 0 3px 2px -2px var(--btn-bg),\n    0 4px 3px -2px var(--btn-bg);\n    @supports (color: color-mix(in lab, red, red)) {\n      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),\n    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);\n    }\n    --btn-noise: var(--fx-noise);\n    .prose & {\n      text-decoration-line: none;\n    }\n    @media (hover: hover) {\n      &:hover {\n        --btn-bg: var(--btn-color, var(--color-base-200));\n        @supports (color: color-mix(in lab, red, red)) {\n          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);\n        }\n      }\n    }\n    &:focus-visible {\n      outline-width: 2px;\n      outline-style: solid;\n      isolation: isolate;\n    }\n    &:active:not(.btn-active) {\n      translate: 0 0.5px;\n      --btn-bg: var(--btn-color, var(--color-base-200));\n      @supports (color: color-mix(in lab, red, red)) {\n        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);\n      }\n      --btn-border: var(--btn-color, var(--color-base-200));\n      @supports (color: color-mix(in lab, red, red)) {\n        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);\n      }\n      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);\n    }\n    &:is(:disabled, [disabled], .btn-disabled) {\n      &:not(.btn-link, .btn-ghost) {\n        background-color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);\n        }\n        box-shadow: none;\n      }\n      pointer-events: none;\n      --btn-border: #0000;\n      --btn-noise: none;\n      --btn-fg: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);\n      }\n      @media (hover: hover) {\n        &:hover {\n          pointer-events: none;\n          background-color: var(--color-neutral);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);\n          }\n          --btn-border: #0000;\n          --btn-fg: var(--color-base-content);\n          @supports (color: color-mix(in lab, red, red)) {\n            --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);\n          }\n        }\n      }\n    }\n    &:is(input[type=\"checkbox\"], input[type=\"radio\"]) {\n      appearance: none;\n      &::after {\n        content: attr(aria-label);\n      }\n    }\n    &:where(input:checked:not(.filter .btn)) {\n      --btn-color: var(--color-primary);\n      --btn-fg: var(--color-primary-content);\n      isolation: isolate;\n    }\n  }\n  .\\!loading {\n    pointer-events: none !important;\n    display: inline-block !important;\n    aspect-ratio: 1 / 1 !important;\n    background-color: currentColor !important;\n    vertical-align: middle !important;\n    width: calc(var(--size-selector, 0.25rem) * 6) !important;\n    mask-size: 100% !important;\n    mask-repeat: no-repeat !important;\n    mask-position: center !important;\n    mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\") !important;\n  }\n  .loading {\n    pointer-events: none;\n    display: inline-block;\n    aspect-ratio: 1 / 1;\n    background-color: currentColor;\n    vertical-align: middle;\n    width: calc(var(--size-selector, 0.25rem) * 6);\n    mask-size: 100%;\n    mask-repeat: no-repeat;\n    mask-position: center;\n    mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .list {\n    display: flex;\n    flex-direction: column;\n    font-size: 0.875rem;\n    :where(.list-row) {\n      --list-grid-cols: minmax(0, auto) 1fr;\n      position: relative;\n      display: grid;\n      grid-auto-flow: column;\n      gap: calc(0.25rem * 4);\n      border-radius: var(--radius-box);\n      padding: calc(0.25rem * 4);\n      word-break: break-word;\n      grid-template-columns: var(--list-grid-cols);\n      &:has(.list-col-grow:nth-child(1)) {\n        --list-grid-cols: 1fr;\n      }\n      &:has(.list-col-grow:nth-child(2)) {\n        --list-grid-cols: minmax(0, auto) 1fr;\n      }\n      &:has(.list-col-grow:nth-child(3)) {\n        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;\n      }\n      &:has(.list-col-grow:nth-child(4)) {\n        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;\n      }\n      &:has(.list-col-grow:nth-child(5)) {\n        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;\n      }\n      &:has(.list-col-grow:nth-child(6)) {\n        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)\n        minmax(0, auto) 1fr;\n      }\n      :not(.list-col-wrap) {\n        grid-row-start: 1;\n      }\n    }\n    & > :not(:last-child) {\n      &.list-row, .list-row {\n        &:after {\n          content: \"\";\n          border-bottom: var(--border) solid;\n          inset-inline: var(--radius-box);\n          position: absolute;\n          bottom: calc(0.25rem * 0);\n          border-color: var(--color-base-content);\n          @supports (color: color-mix(in lab, red, red)) {\n            border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .toast {\n    position: fixed;\n    inset-inline-start: auto;\n    inset-inline-end: calc(0.25rem * 4);\n    top: auto;\n    bottom: calc(0.25rem * 4);\n    display: flex;\n    flex-direction: column;\n    gap: calc(0.25rem * 2);\n    background-color: transparent;\n    translate: var(--toast-x, 0) var(--toast-y, 0);\n    width: max-content;\n    max-width: calc(100vw - 2rem);\n    & > * {\n      animation: toast 0.25s ease-out;\n    }\n    &:where(.toast-start) {\n      inset-inline-start: calc(0.25rem * 4);\n      inset-inline-end: auto;\n      --toast-x: 0;\n    }\n    &:where(.toast-center) {\n      inset-inline-start: calc(1/2 * 100%);\n      inset-inline-end: calc(1/2 * 100%);\n      --toast-x: -50%;\n    }\n    &:where(.toast-end) {\n      inset-inline-start: auto;\n      inset-inline-end: calc(0.25rem * 4);\n      --toast-x: 0;\n    }\n    &:where(.toast-bottom) {\n      top: auto;\n      bottom: calc(0.25rem * 4);\n      --toast-y: 0;\n    }\n    &:where(.toast-middle) {\n      top: calc(1/2 * 100%);\n      bottom: auto;\n      --toast-y: -50%;\n    }\n    &:where(.toast-top) {\n      top: calc(0.25rem * 4);\n      bottom: auto;\n      --toast-y: 0;\n    }\n  }\n  .toggle {\n    border: var(--border) solid currentColor;\n    color: var(--input-color);\n    position: relative;\n    display: inline-grid;\n    flex-shrink: 0;\n    cursor: pointer;\n    appearance: none;\n    place-content: center;\n    vertical-align: middle;\n    webkit-user-select: none;\n    user-select: none;\n    grid-template-columns: 0fr 1fr 1fr;\n    --radius-selector-max: calc(\n    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)\n  );\n    border-radius: calc( var(--radius-selector) + min(var(--toggle-p), var(--radius-selector-max)) + min(var(--border), var(--radius-selector-max)) );\n    padding: var(--toggle-p);\n    box-shadow: 0 1px currentColor inset;\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000) inset;\n    }\n    transition: color 0.3s, grid-template-columns 0.2s;\n    --input-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      --input-color: color-mix(in oklab, var(--color-base-content) 50%, #0000);\n    }\n    --toggle-p: calc(var(--size) * 0.125);\n    --size: calc(var(--size-selector, 0.25rem) * 6);\n    width: calc((var(--size) * 2) - (var(--border) + var(--toggle-p)) * 2);\n    height: var(--size);\n    > * {\n      z-index: 1;\n      grid-column: span 1 / span 1;\n      grid-column-start: 2;\n      grid-row-start: 1;\n      height: 100%;\n      cursor: pointer;\n      appearance: none;\n      background-color: transparent;\n      padding: calc(0.25rem * 0.5);\n      transition: opacity 0.2s, rotate 0.4s;\n      border: none;\n      &:focus {\n        --tw-outline-style: none;\n        outline-style: none;\n        @media (forced-colors: active) {\n          outline: 2px solid transparent;\n          outline-offset: 2px;\n        }\n      }\n      &:nth-child(2) {\n        color: var(--color-base-100);\n        rotate: 0deg;\n      }\n      &:nth-child(3) {\n        color: var(--color-base-100);\n        opacity: 0%;\n        rotate: -15deg;\n      }\n    }\n    &:has(:checked) {\n      > :nth-child(2) {\n        opacity: 0%;\n        rotate: 15deg;\n      }\n      > :nth-child(3) {\n        opacity: 100%;\n        rotate: 0deg;\n      }\n    }\n    &:before {\n      position: relative;\n      inset-inline-start: calc(0.25rem * 0);\n      grid-column-start: 2;\n      grid-row-start: 1;\n      aspect-ratio: 1 / 1;\n      height: 100%;\n      border-radius: var(--radius-selector);\n      background-color: currentColor;\n      translate: 0;\n      --tw-content: \"\";\n      content: var(--tw-content);\n      transition: background-color 0.1s, translate 0.2s, inset-inline-start 0.2s;\n      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor;\n      @supports (color: color-mix(in lab, red, red)) {\n        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000);\n      }\n      background-size: auto, calc(var(--noise) * 100%);\n      background-image: none, var(--fx-noise);\n    }\n    @media (forced-colors: active) {\n      &:before {\n        outline-style: var(--tw-outline-style);\n        outline-width: 1px;\n        outline-offset: calc(1px * -1);\n      }\n    }\n    @media print {\n      &:before {\n        outline: 0.25rem solid;\n        outline-offset: -1rem;\n      }\n    }\n    &:focus-visible, &:has(:focus-visible) {\n      outline: 2px solid currentColor;\n      outline-offset: 2px;\n    }\n    &:checked, &[aria-checked=\"true\"], &:has(> input:checked) {\n      grid-template-columns: 1fr 1fr 0fr;\n      background-color: var(--color-base-100);\n      --input-color: var(--color-base-content);\n      &:before {\n        background-color: currentColor;\n      }\n      @starting-style {\n        &:before {\n          opacity: 0;\n        }\n      }\n    }\n    &:indeterminate {\n      grid-template-columns: 0.5fr 1fr 0.5fr;\n    }\n    &:disabled {\n      cursor: not-allowed;\n      opacity: 30%;\n      &:before {\n        background-color: transparent;\n        border: var(--border) solid currentColor;\n      }\n    }\n  }\n  .input {\n    cursor: text;\n    border: var(--border) solid #0000;\n    position: relative;\n    display: inline-flex;\n    flex-shrink: 1;\n    appearance: none;\n    align-items: center;\n    gap: calc(0.25rem * 2);\n    background-color: var(--color-base-100);\n    padding-inline: calc(0.25rem * 3);\n    vertical-align: middle;\n    white-space: nowrap;\n    width: clamp(3rem, 20rem, 100%);\n    height: var(--size);\n    font-size: 0.875rem;\n    touch-action: manipulation;\n    border-start-start-radius: var(--join-ss, var(--radius-field));\n    border-start-end-radius: var(--join-se, var(--radius-field));\n    border-end-start-radius: var(--join-es, var(--radius-field));\n    border-end-end-radius: var(--join-ee, var(--radius-field));\n    border-color: var(--input-color);\n    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n    }\n    --size: calc(var(--size-field, 0.25rem) * 10);\n    --input-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n    }\n    &:where(input) {\n      display: inline-flex;\n    }\n    :where(input) {\n      display: inline-flex;\n      height: 100%;\n      width: 100%;\n      appearance: none;\n      background-color: transparent;\n      border: none;\n      &:focus, &:focus-within {\n        --tw-outline-style: none;\n        outline-style: none;\n        @media (forced-colors: active) {\n          outline: 2px solid transparent;\n          outline-offset: 2px;\n        }\n      }\n    }\n    :where(input[type=\"url\"]), :where(input[type=\"email\"]) {\n      direction: ltr;\n    }\n    :where(input[type=\"date\"]) {\n      display: inline-block;\n    }\n    &:focus, &:focus-within {\n      --input-color: var(--color-base-content);\n      box-shadow: 0 1px var(--input-color);\n      @supports (color: color-mix(in lab, red, red)) {\n        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);\n      }\n      outline: 2px solid var(--input-color);\n      outline-offset: 2px;\n      isolation: isolate;\n      z-index: 1;\n    }\n    &:has(> input[disabled]), &:is(:disabled, [disabled]) {\n      cursor: not-allowed;\n      border-color: var(--color-base-200);\n      background-color: var(--color-base-200);\n      color: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);\n      }\n      &::placeholder {\n        color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n        }\n      }\n      box-shadow: none;\n    }\n    &:has(> input[disabled]) > input[disabled] {\n      cursor: not-allowed;\n    }\n    &::-webkit-date-and-time-value {\n      text-align: inherit;\n    }\n    &[type=\"number\"] {\n      &::-webkit-inner-spin-button {\n        margin-block: calc(0.25rem * -3);\n        margin-inline-end: calc(0.25rem * -3);\n      }\n    }\n    &::-webkit-calendar-picker-indicator {\n      position: absolute;\n      inset-inline-end: 0.75em;\n    }\n  }\n  .indicator {\n    position: relative;\n    display: inline-flex;\n    width: max-content;\n    :where(.indicator-item) {\n      z-index: 1;\n      position: absolute;\n      white-space: nowrap;\n      top: var(--indicator-t, 0);\n      bottom: var(--indicator-b, auto);\n      left: var(--indicator-s, auto);\n      right: var(--indicator-e, 0);\n      translate: var(--indicator-x, 50%) var(--indicator-y, -50%);\n    }\n  }\n  .table {\n    font-size: 0.875rem;\n    position: relative;\n    width: 100%;\n    border-radius: var(--radius-box);\n    text-align: left;\n    &:where(:dir(rtl), [dir=\"rtl\"], [dir=\"rtl\"] *) {\n      text-align: right;\n    }\n    tr.row-hover {\n      &, &:nth-child(even) {\n        &:hover {\n          @media (hover: hover) {\n            background-color: var(--color-base-200);\n          }\n        }\n      }\n    }\n    :where(th, td) {\n      padding-inline: calc(0.25rem * 4);\n      padding-block: calc(0.25rem * 3);\n      vertical-align: middle;\n    }\n    :where(thead, tfoot) {\n      white-space: nowrap;\n      color: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-base-content) 60%, transparent);\n      }\n      font-size: 0.875rem;\n      font-weight: 600;\n    }\n    :where(tfoot) {\n      border-top: var(--border) solid var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);\n      }\n    }\n    :where(.table-pin-rows thead tr) {\n      position: sticky;\n      top: calc(0.25rem * 0);\n      z-index: 1;\n      background-color: var(--color-base-100);\n    }\n    :where(.table-pin-rows tfoot tr) {\n      position: sticky;\n      bottom: calc(0.25rem * 0);\n      z-index: 1;\n      background-color: var(--color-base-100);\n    }\n    :where(.table-pin-cols tr th) {\n      position: sticky;\n      right: calc(0.25rem * 0);\n      left: calc(0.25rem * 0);\n      background-color: var(--color-base-100);\n    }\n    :where(thead tr, tbody tr:not(:last-child)) {\n      border-bottom: var(--border) solid var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);\n      }\n    }\n  }\n  .steps {\n    display: inline-grid;\n    grid-auto-flow: column;\n    overflow: hidden;\n    overflow-x: auto;\n    counter-reset: step;\n    grid-auto-columns: 1fr;\n    .step {\n      display: grid;\n      grid-template-columns: repeat(1, minmax(0, 1fr));\n      grid-template-columns: auto;\n      grid-template-rows: repeat(2, minmax(0, 1fr));\n      grid-template-rows: 40px 1fr;\n      place-items: center;\n      text-align: center;\n      min-width: 4rem;\n      --step-bg: var(--color-base-300);\n      --step-fg: var(--color-base-content);\n      &:before {\n        top: calc(0.25rem * 0);\n        grid-column-start: 1;\n        grid-row-start: 1;\n        height: calc(0.25rem * 2);\n        width: 100%;\n        border: 1px solid;\n        color: var(--step-bg);\n        background-color: var(--step-bg);\n        --tw-content: \"\";\n        content: var(--tw-content);\n        margin-inline-start: -100%;\n      }\n      > .step-icon, &:not(:has(.step-icon)):after {\n        content: counter(step);\n        counter-increment: step;\n        z-index: 1;\n        color: var(--step-fg);\n        background-color: var(--step-bg);\n        border: 1px solid var(--step-bg);\n        position: relative;\n        grid-column-start: 1;\n        grid-row-start: 1;\n        display: grid;\n        height: calc(0.25rem * 8);\n        width: calc(0.25rem * 8);\n        place-items: center;\n        place-self: center;\n        border-radius: calc(infinity * 1px);\n      }\n      &:first-child:before {\n        content: none;\n      }\n      &[data-content]:after {\n        content: attr(data-content);\n      }\n    }\n    .step-neutral {\n      + .step-neutral:before, &:after, > .step-icon {\n        --step-bg: var(--color-neutral);\n        --step-fg: var(--color-neutral-content);\n      }\n    }\n    .step-primary {\n      + .step-primary:before, &:after, > .step-icon {\n        --step-bg: var(--color-primary);\n        --step-fg: var(--color-primary-content);\n      }\n    }\n    .step-secondary {\n      + .step-secondary:before, &:after, > .step-icon {\n        --step-bg: var(--color-secondary);\n        --step-fg: var(--color-secondary-content);\n      }\n    }\n    .step-accent {\n      + .step-accent:before, &:after, > .step-icon {\n        --step-bg: var(--color-accent);\n        --step-fg: var(--color-accent-content);\n      }\n    }\n    .step-info {\n      + .step-info:before, &:after, > .step-icon {\n        --step-bg: var(--color-info);\n        --step-fg: var(--color-info-content);\n      }\n    }\n    .step-success {\n      + .step-success:before, &:after, > .step-icon {\n        --step-bg: var(--color-success);\n        --step-fg: var(--color-success-content);\n      }\n    }\n    .step-warning {\n      + .step-warning:before, &:after, > .step-icon {\n        --step-bg: var(--color-warning);\n        --step-fg: var(--color-warning-content);\n      }\n    }\n    .step-error {\n      + .step-error:before, &:after, > .step-icon {\n        --step-bg: var(--color-error);\n        --step-fg: var(--color-error-content);\n      }\n    }\n  }\n  .select {\n    border: var(--border) solid #0000;\n    position: relative;\n    display: inline-flex;\n    flex-shrink: 1;\n    appearance: none;\n    align-items: center;\n    gap: calc(0.25rem * 1.5);\n    background-color: var(--color-base-100);\n    padding-inline-start: calc(0.25rem * 4);\n    padding-inline-end: calc(0.25rem * 7);\n    vertical-align: middle;\n    width: clamp(3rem, 20rem, 100%);\n    height: var(--size);\n    font-size: 0.875rem;\n    touch-action: manipulation;\n    border-start-start-radius: var(--join-ss, var(--radius-field));\n    border-start-end-radius: var(--join-se, var(--radius-field));\n    border-end-start-radius: var(--join-es, var(--radius-field));\n    border-end-end-radius: var(--join-ee, var(--radius-field));\n    background-image: linear-gradient(45deg, #0000 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, #0000 50%);\n    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);\n    background-size: 4px 4px, 4px 4px;\n    background-repeat: no-repeat;\n    text-overflow: ellipsis;\n    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n    }\n    border-color: var(--input-color);\n    --input-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n    }\n    --size: calc(var(--size-field, 0.25rem) * 10);\n    [dir=\"rtl\"] & {\n      background-position: calc(0% + 12px) calc(1px + 50%), calc(0% + 16px) calc(1px + 50%);\n    }\n    select {\n      margin-inline-start: calc(0.25rem * -4);\n      margin-inline-end: calc(0.25rem * -7);\n      width: calc(100% + 2.75rem);\n      appearance: none;\n      padding-inline-start: calc(0.25rem * 4);\n      padding-inline-end: calc(0.25rem * 7);\n      height: calc(100% - 2px);\n      background: inherit;\n      border-radius: inherit;\n      border-style: none;\n      &:focus, &:focus-within {\n        --tw-outline-style: none;\n        outline-style: none;\n        @media (forced-colors: active) {\n          outline: 2px solid transparent;\n          outline-offset: 2px;\n        }\n      }\n      &:not(:last-child) {\n        margin-inline-end: calc(0.25rem * -5.5);\n        background-image: none;\n      }\n    }\n    &:focus, &:focus-within {\n      --input-color: var(--color-base-content);\n      box-shadow: 0 1px var(--input-color);\n      @supports (color: color-mix(in lab, red, red)) {\n        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);\n      }\n      outline: 2px solid var(--input-color);\n      outline-offset: 2px;\n      isolation: isolate;\n      z-index: 1;\n    }\n    &:has(> select[disabled]), &:is(:disabled, [disabled]) {\n      cursor: not-allowed;\n      border-color: var(--color-base-200);\n      background-color: var(--color-base-200);\n      color: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);\n      }\n      &::placeholder {\n        color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n        }\n      }\n    }\n    &:has(> select[disabled]) > select[disabled] {\n      cursor: not-allowed;\n    }\n  }\n  .card {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    border-radius: var(--radius-box);\n    outline-width: 2px;\n    transition: outline 0.2s ease-in-out;\n    outline: 0 solid #0000;\n    outline-offset: 2px;\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n    &:focus-visible {\n      outline-color: currentColor;\n    }\n    :where(figure:first-child) {\n      overflow: hidden;\n      border-start-start-radius: inherit;\n      border-start-end-radius: inherit;\n      border-end-start-radius: unset;\n      border-end-end-radius: unset;\n    }\n    :where(figure:last-child) {\n      overflow: hidden;\n      border-start-start-radius: unset;\n      border-start-end-radius: unset;\n      border-end-start-radius: inherit;\n      border-end-end-radius: inherit;\n    }\n    &:where(.card-border) {\n      border: var(--border) solid var(--color-base-200);\n    }\n    &:where(.card-dash) {\n      border: var(--border) dashed var(--color-base-200);\n    }\n    &.image-full {\n      display: grid;\n      > * {\n        grid-column-start: 1;\n        grid-row-start: 1;\n      }\n      > .card-body {\n        position: relative;\n        color: var(--color-neutral-content);\n      }\n      :where(figure) {\n        overflow: hidden;\n        border-radius: inherit;\n      }\n      > figure img {\n        height: 100%;\n        object-fit: cover;\n        filter: brightness(28%);\n      }\n    }\n    figure {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n    &:has(> input:is(input[type=\"checkbox\"], input[type=\"radio\"])) {\n      cursor: pointer;\n      user-select: none;\n    }\n    &:has(> :checked) {\n      outline: 2px solid currentColor;\n    }\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .avatar {\n    position: relative;\n    display: inline-flex;\n    vertical-align: middle;\n    & > div {\n      display: block;\n      aspect-ratio: 1 / 1;\n      overflow: hidden;\n    }\n    img {\n      height: 100%;\n      width: 100%;\n      object-fit: cover;\n    }\n  }\n  .checkbox {\n    border: var(--border) solid var(--input-color, var(--color-base-content));\n    @supports (color: color-mix(in lab, red, red)) {\n      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));\n    }\n    position: relative;\n    display: inline-block;\n    flex-shrink: 0;\n    cursor: pointer;\n    appearance: none;\n    border-radius: var(--radius-selector);\n    padding: calc(0.25rem * 1);\n    vertical-align: middle;\n    color: var(--color-base-content);\n    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;\n    transition: background-color 0.2s, box-shadow 0.2s;\n    --size: calc(var(--size-selector, 0.25rem) * 6);\n    width: var(--size);\n    height: var(--size);\n    background-size: auto, calc(var(--noise) * 100%);\n    background-image: none, var(--fx-noise);\n    &:before {\n      --tw-content: \"\";\n      content: var(--tw-content);\n      display: block;\n      width: 100%;\n      height: 100%;\n      rotate: 45deg;\n      background-color: currentColor;\n      opacity: 0%;\n      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;\n      transition-delay: 0.1s;\n      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);\n      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n      font-size: 1rem;\n      line-height: 0.75;\n    }\n    &:focus-visible {\n      outline: 2px solid var(--input-color, currentColor);\n      outline-offset: 2px;\n    }\n    &:checked, &[aria-checked=\"true\"] {\n      background-color: var(--input-color, #0000);\n      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));\n      &:before {\n        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);\n        opacity: 100%;\n      }\n      @media (forced-colors: active) {\n        &:before {\n          rotate: 0deg;\n          background-color: transparent;\n          --tw-content: \"✔︎\";\n          clip-path: none;\n        }\n      }\n      @media print {\n        &:before {\n          rotate: 0deg;\n          background-color: transparent;\n          --tw-content: \"✔︎\";\n          clip-path: none;\n        }\n      }\n    }\n    &:indeterminate {\n      &:before {\n        rotate: 0deg;\n        opacity: 100%;\n        translate: 0 -35%;\n        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);\n      }\n    }\n    &:disabled {\n      cursor: not-allowed;\n      opacity: 20%;\n    }\n  }\n  .radio {\n    position: relative;\n    display: inline-block;\n    flex-shrink: 0;\n    cursor: pointer;\n    appearance: none;\n    border-radius: calc(infinity * 1px);\n    padding: calc(0.25rem * 1);\n    vertical-align: middle;\n    border: var(--border) solid var(--input-color, currentColor);\n    @supports (color: color-mix(in lab, red, red)) {\n      border: var(--border) solid var(--input-color, color-mix(in srgb, currentColor 20%, #0000));\n    }\n    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset;\n    --size: calc(var(--size-selector, 0.25rem) * 6);\n    width: var(--size);\n    height: var(--size);\n    color: var(--input-color, currentColor);\n    &:before {\n      display: block;\n      width: 100%;\n      height: 100%;\n      border-radius: calc(infinity * 1px);\n      --tw-content: \"\";\n      content: var(--tw-content);\n      background-size: auto, calc(var(--noise) * 100%);\n      background-image: none, var(--fx-noise);\n    }\n    &:focus-visible {\n      outline: 2px solid currentColor;\n    }\n    &:checked, &[aria-checked=\"true\"] {\n      animation: radio 0.2s ease-out;\n      border-color: currentColor;\n      background-color: var(--color-base-100);\n      &:before {\n        background-color: currentColor;\n        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));\n      }\n      @media (forced-colors: active) {\n        &:before {\n          outline-style: var(--tw-outline-style);\n          outline-width: 1px;\n          outline-offset: calc(1px * -1);\n        }\n      }\n      @media print {\n        &:before {\n          outline: 0.25rem solid;\n          outline-offset: -1rem;\n        }\n      }\n    }\n    &:disabled {\n      cursor: not-allowed;\n      opacity: 20%;\n    }\n  }\n  .stats {\n    position: relative;\n    display: inline-grid;\n    grid-auto-flow: column;\n    overflow-x: auto;\n    border-radius: var(--radius-box);\n  }\n  .progress {\n    position: relative;\n    height: calc(0.25rem * 2);\n    width: 100%;\n    appearance: none;\n    overflow: hidden;\n    border-radius: var(--radius-box);\n    background-color: currentColor;\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, currentColor 20%, transparent);\n    }\n    color: var(--color-base-content);\n    &:indeterminate {\n      background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );\n      background-size: 200%;\n      background-position-x: 15%;\n      animation: progress 5s ease-in-out infinite;\n      @supports (-moz-appearance: none) {\n        &::-moz-progress-bar {\n          background-color: transparent;\n          background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );\n          background-size: 200%;\n          background-position-x: 15%;\n          animation: progress 5s ease-in-out infinite;\n        }\n      }\n    }\n    @supports (-moz-appearance: none) {\n      &::-moz-progress-bar {\n        border-radius: var(--radius-box);\n        background-color: currentColor;\n      }\n    }\n    @supports (-webkit-appearance: none) {\n      &::-webkit-progress-bar {\n        border-radius: var(--radius-box);\n        background-color: transparent;\n      }\n      &::-webkit-progress-value {\n        border-radius: var(--radius-box);\n        background-color: currentColor;\n      }\n    }\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .tooltip-left {\n    > .tooltip-content, &[data-tip]:before {\n      transform: translateX(calc(var(--tt-pos, 0.25rem) - 0.25rem)) translateY(-50%);\n      inset: 50% var(--tt-off) auto auto;\n    }\n    &:after {\n      transform: translateX(var(--tt-pos, 0.25rem)) translateY(-50%) rotate(-90deg);\n      inset: 50% calc(var(--tt-tail) + 1px) auto auto;\n    }\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-4 {\n    inset: calc(var(--spacing) * 4);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .dropdown-end {\n    --anchor-h: span-left;\n    :where(.dropdown-content) {\n      inset-inline-end: calc(0.25rem * 0);\n      translate: 0 0;\n      [dir=\"rtl\"] & {\n        translate: 0 0;\n      }\n    }\n    &.dropdown-left {\n      --anchor-h: left;\n      --anchor-v: span-top;\n      .dropdown-content {\n        top: auto;\n        bottom: calc(0.25rem * 0);\n      }\n    }\n    &.dropdown-right {\n      --anchor-h: right;\n      --anchor-v: span-top;\n      .dropdown-content {\n        top: auto;\n        bottom: calc(0.25rem * 0);\n      }\n    }\n  }\n  .dropdown-top {\n    --anchor-v: top;\n    .dropdown-content {\n      top: auto;\n      bottom: 100%;\n      transform-origin: bottom;\n    }\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-2\\.5 {\n    top: calc(var(--spacing) * 2.5);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-full {\n    top: 100%;\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .bottom-full {\n    bottom: 100%;\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-2\\.5 {\n    left: calc(var(--spacing) * 2.5);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-4 {\n    left: calc(var(--spacing) * 4);\n  }\n  .textarea {\n    border: var(--border) solid #0000;\n    min-height: calc(0.25rem * 20);\n    flex-shrink: 1;\n    appearance: none;\n    border-radius: var(--radius-field);\n    background-color: var(--color-base-100);\n    padding-block: calc(0.25rem * 2);\n    vertical-align: middle;\n    width: clamp(3rem, 20rem, 100%);\n    padding-inline-start: 0.75rem;\n    padding-inline-end: 0.75rem;\n    font-size: 0.875rem;\n    touch-action: manipulation;\n    border-color: var(--input-color);\n    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n    }\n    --input-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n    }\n    textarea {\n      appearance: none;\n      background-color: transparent;\n      border: none;\n      &:focus, &:focus-within {\n        --tw-outline-style: none;\n        outline-style: none;\n        @media (forced-colors: active) {\n          outline: 2px solid transparent;\n          outline-offset: 2px;\n        }\n      }\n    }\n    &:focus, &:focus-within {\n      --input-color: var(--color-base-content);\n      box-shadow: 0 1px var(--input-color);\n      @supports (color: color-mix(in lab, red, red)) {\n        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);\n      }\n      outline: 2px solid var(--input-color);\n      outline-offset: 2px;\n      isolation: isolate;\n    }\n    &:has(> textarea[disabled]), &:is(:disabled, [disabled]) {\n      cursor: not-allowed;\n      border-color: var(--color-base-200);\n      background-color: var(--color-base-200);\n      color: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);\n      }\n      &::placeholder {\n        color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n        }\n      }\n      box-shadow: none;\n    }\n    &:has(> textarea[disabled]) > textarea[disabled] {\n      cursor: not-allowed;\n    }\n  }\n  .btn-active {\n    --btn-bg: var(--btn-color, var(--color-base-200));\n    @supports (color: color-mix(in lab, red, red)) {\n      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);\n    }\n    --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);\n    isolation: isolate;\n  }\n  .stack {\n    display: inline-grid;\n    grid-template-columns: 3px 4px 1fr 4px 3px;\n    grid-template-rows: 3px 4px 1fr 4px 3px;\n    & > * {\n      height: 100%;\n      width: 100%;\n      &:nth-child(n + 2) {\n        width: 100%;\n        opacity: 70%;\n      }\n      &:nth-child(2) {\n        z-index: 2;\n        opacity: 90%;\n      }\n      &:nth-child(1) {\n        z-index: 3;\n        width: 100%;\n      }\n    }\n    &, &.stack-bottom {\n      > * {\n        grid-column: 3 / 4;\n        grid-row: 3 / 6;\n        &:nth-child(2) {\n          grid-column: 2 / 5;\n          grid-row: 2 / 5;\n        }\n        &:nth-child(1) {\n          grid-column: 1 / 6;\n          grid-row: 1 / 4;\n        }\n      }\n    }\n    &.stack-top {\n      > * {\n        grid-column: 3 / 4;\n        grid-row: 1 / 4;\n        &:nth-child(2) {\n          grid-column: 2 / 5;\n          grid-row: 2 / 5;\n        }\n        &:nth-child(1) {\n          grid-column: 1 / 6;\n          grid-row: 3 / 6;\n        }\n      }\n    }\n    &.stack-start {\n      > * {\n        grid-column: 1 / 4;\n        grid-row: 3 / 4;\n        &:nth-child(2) {\n          grid-column: 2 / 5;\n          grid-row: 2 / 5;\n        }\n        &:nth-child(1) {\n          grid-column: 3 / 6;\n          grid-row: 1 / 6;\n        }\n      }\n    }\n    &.stack-end {\n      > * {\n        grid-column: 3 / 6;\n        grid-row: 3 / 4;\n        &:nth-child(2) {\n          grid-column: 2 / 5;\n          grid-row: 2 / 5;\n        }\n        &:nth-child(1) {\n          grid-column: 1 / 4;\n          grid-row: 1 / 6;\n        }\n      }\n    }\n  }\n  .z-5 {\n    z-index: 5;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-30 {\n    z-index: 30;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-\\[1\\] {\n    z-index: 1;\n  }\n  .tab-content {\n    order: var(--tabcontent-order);\n    display: none;\n    border-color: transparent;\n    --tabcontent-radius-ss: 0;\n    --tabcontent-radius-se: 0;\n    --tabcontent-radius-es: 0;\n    --tabcontent-radius-ee: 0;\n    --tabcontent-order: 1;\n    width: 100%;\n    margin: var(--tabcontent-margin);\n    border-width: var(--border);\n    border-start-start-radius: var(--tabcontent-radius-ss);\n    border-start-end-radius: var(--tabcontent-radius-se);\n    border-end-start-radius: var(--tabcontent-radius-es);\n    border-end-end-radius: var(--tabcontent-radius-ee);\n  }\n  .stat-figure {\n    grid-column-start: 2;\n    grid-row: span 3 / span 3;\n    grid-row-start: 1;\n    place-self: center;\n    justify-self: flex-end;\n  }\n  .modal-box {\n    grid-column-start: 1;\n    grid-row-start: 1;\n    max-height: 100vh;\n    width: calc(11/12 * 100%);\n    max-width: 32rem;\n    background-color: var(--color-base-100);\n    padding: calc(0.25rem * 6);\n    transition: translate 0.3s ease-out, scale 0.3s ease-out, opacity 0.2s ease-out 0.05s, box-shadow 0.3s ease-out;\n    border-top-left-radius: var(--modal-tl, var(--radius-box));\n    border-top-right-radius: var(--modal-tr, var(--radius-box));\n    border-bottom-left-radius: var(--modal-bl, var(--radius-box));\n    border-bottom-right-radius: var(--modal-br, var(--radius-box));\n    scale: 95%;\n    opacity: 0;\n    box-shadow: oklch(0% 0 0/ 0.25) 0px 25px 50px -12px;\n    overflow-y: auto;\n    overscroll-behavior: contain;\n  }\n  .stat-value {\n    grid-column-start: 1;\n    white-space: nowrap;\n    font-size: 2rem;\n    font-weight: 800;\n  }\n  .stat-desc {\n    grid-column-start: 1;\n    white-space: nowrap;\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);\n    }\n    font-size: 0.75rem;\n  }\n  .stat-title {\n    grid-column-start: 1;\n    white-space: nowrap;\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);\n    }\n    font-size: 0.75rem;\n  }\n  .float-left {\n    float: left;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .divider {\n    display: flex;\n    height: calc(0.25rem * 4);\n    flex-direction: row;\n    align-items: center;\n    align-self: stretch;\n    white-space: nowrap;\n    margin: var(--divider-m, 1rem 0);\n    --divider-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      --divider-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);\n    }\n    &:before, &:after {\n      content: \"\";\n      height: calc(0.25rem * 0.5);\n      width: 100%;\n      flex-grow: 1;\n      background-color: var(--divider-color);\n    }\n    @media print {\n      &:before, &:after {\n        border: 0.5px solid;\n      }\n    }\n    &:not(:empty) {\n      gap: calc(0.25rem * 4);\n    }\n  }\n  .m-1 {\n    margin: calc(var(--spacing) * 1);\n  }\n  .m-2 {\n    margin: calc(var(--spacing) * 2);\n  }\n  .m-3 {\n    margin: calc(var(--spacing) * 3);\n  }\n  .m-4 {\n    margin: calc(var(--spacing) * 4);\n  }\n  .m-5 {\n    margin: calc(var(--spacing) * 5);\n  }\n  .filter {\n    display: flex;\n    flex-wrap: wrap;\n    input[type=\"radio\"] {\n      width: auto;\n    }\n    input {\n      overflow: hidden;\n      opacity: 100%;\n      scale: 1;\n      transition: margin 0.1s, opacity 0.3s, padding 0.3s, border-width 0.1s;\n      &:not(:last-child) {\n        margin-inline-end: calc(0.25rem * 1);\n      }\n      &.filter-reset {\n        aspect-ratio: 1 / 1;\n        &::after {\n          content: \"×\";\n        }\n      }\n    }\n    &:not(:has(input:checked:not(.filter-reset))) {\n      .filter-reset, input[type=\"reset\"] {\n        scale: 0;\n        border-width: 0;\n        margin-inline: calc(0.25rem * 0);\n        width: calc(0.25rem * 0);\n        padding-inline: calc(0.25rem * 0);\n        opacity: 0%;\n      }\n    }\n    &:has(input:checked:not(.filter-reset)) {\n      input:not(:checked, .filter-reset, input[type=\"reset\"]) {\n        scale: 0;\n        border-width: 0;\n        margin-inline: calc(0.25rem * 0);\n        width: calc(0.25rem * 0);\n        padding-inline: calc(0.25rem * 0);\n        opacity: 0%;\n      }\n    }\n  }\n  .mx-1 {\n    margin-inline: calc(var(--spacing) * 1);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-3 {\n    margin-inline: calc(var(--spacing) * 3);\n  }\n  .mx-4 {\n    margin-inline: calc(var(--spacing) * 4);\n  }\n  .mx-5 {\n    margin-inline: calc(var(--spacing) * 5);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .input-lg {\n    --size: calc(var(--size-field, 0.25rem) * 12);\n    font-size: 1.125rem;\n    &[type=\"number\"] {\n      &::-webkit-inner-spin-button {\n        margin-block: calc(0.25rem * -3);\n        margin-inline-end: calc(0.25rem * -3);\n      }\n    }\n  }\n  .input-md {\n    --size: calc(var(--size-field, 0.25rem) * 10);\n    font-size: 0.875rem;\n    &[type=\"number\"] {\n      &::-webkit-inner-spin-button {\n        margin-block: calc(0.25rem * -3);\n        margin-inline-end: calc(0.25rem * -3);\n      }\n    }\n  }\n  .input-sm {\n    --size: calc(var(--size-field, 0.25rem) * 8);\n    font-size: 0.75rem;\n    &[type=\"number\"] {\n      &::-webkit-inner-spin-button {\n        margin-block: calc(0.25rem * -2);\n        margin-inline-end: calc(0.25rem * -3);\n      }\n    }\n  }\n  .input-xs {\n    --size: calc(var(--size-field, 0.25rem) * 6);\n    font-size: 0.6875rem;\n    &[type=\"number\"] {\n      &::-webkit-inner-spin-button {\n        margin-block: calc(0.25rem * -1);\n        margin-inline-end: calc(0.25rem * -3);\n      }\n    }\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-2 {\n    margin-block: calc(var(--spacing) * 2);\n  }\n  .my-3 {\n    margin-block: calc(var(--spacing) * 3);\n  }\n  .my-4 {\n    margin-block: calc(var(--spacing) * 4);\n  }\n  .my-5 {\n    margin-block: calc(var(--spacing) * 5);\n  }\n  .label {\n    display: inline-flex;\n    align-items: center;\n    gap: calc(0.25rem * 1.5);\n    white-space: nowrap;\n    color: currentColor;\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, currentColor 60%, transparent);\n    }\n    &:has(input) {\n      cursor: pointer;\n    }\n    &:is(.input > *, .select > *) {\n      display: flex;\n      height: calc(100% - 0.5rem);\n      align-items: center;\n      padding-inline: calc(0.25rem * 3);\n      white-space: nowrap;\n      font-size: inherit;\n      &:first-child {\n        margin-inline-start: calc(0.25rem * -3);\n        margin-inline-end: calc(0.25rem * 3);\n        border-inline-end: var(--border) solid currentColor;\n        @supports (color: color-mix(in lab, red, red)) {\n          border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);\n        }\n      }\n      &:last-child {\n        margin-inline-start: calc(0.25rem * 3);\n        margin-inline-end: calc(0.25rem * -3);\n        border-inline-start: var(--border) solid currentColor;\n        @supports (color: color-mix(in lab, red, red)) {\n          border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);\n        }\n      }\n    }\n  }\n  .steps-horizontal {\n    grid-auto-columns: 1fr;\n    display: inline-grid;\n    grid-auto-flow: column;\n    overflow: hidden;\n    overflow-x: auto;\n    .step {\n      display: grid;\n      grid-template-columns: repeat(1, minmax(0, 1fr));\n      grid-template-columns: auto;\n      grid-template-rows: repeat(2, minmax(0, 1fr));\n      grid-template-rows: 40px 1fr;\n      place-items: center;\n      text-align: center;\n      min-width: 4rem;\n      &:before {\n        height: calc(0.25rem * 2);\n        width: 100%;\n        translate: 0;\n        content: \"\";\n        margin-inline-start: -100%;\n      }\n      [dir=\"rtl\"] &:before {\n        translate: 0;\n      }\n    }\n  }\n  .modal-action {\n    margin-top: calc(0.25rem * 6);\n    display: flex;\n    justify-content: flex-end;\n    gap: calc(0.25rem * 2);\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-5 {\n    margin-top: calc(var(--spacing) * 5);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mr-4 {\n    margin-right: calc(var(--spacing) * 4);\n  }\n  .mr-5 {\n    margin-right: calc(var(--spacing) * 5);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-5 {\n    margin-bottom: calc(var(--spacing) * 5);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-3 {\n    margin-left: calc(var(--spacing) * 3);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .ml-5 {\n    margin-left: calc(var(--spacing) * 5);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .status {\n    display: inline-block;\n    aspect-ratio: 1 / 1;\n    width: calc(0.25rem * 2);\n    height: calc(0.25rem * 2);\n    border-radius: var(--radius-selector);\n    background-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n    }\n    background-position: center;\n    background-repeat: no-repeat;\n    vertical-align: middle;\n    color: color-mix(in srgb, #000 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in srgb, #000 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-black) 30%, transparent);\n      }\n    }\n    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );\n    box-shadow: 0 2px 3px -1px currentColor;\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);\n    }\n  }\n  .status\\! {\n    display: inline-block !important;\n    aspect-ratio: 1 / 1 !important;\n    width: calc(0.25rem * 2) !important;\n    height: calc(0.25rem * 2) !important;\n    border-radius: var(--radius-selector) !important;\n    background-color: var(--color-base-content) !important;\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;\n    }\n    background-position: center !important;\n    background-repeat: no-repeat !important;\n    vertical-align: middle !important;\n    color: color-mix(in srgb, #000 30%, transparent) !important;\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in srgb, #000 30%, transparent) !important;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--color-black) 30%, transparent) !important;\n      }\n    }\n    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 ) !important;\n    box-shadow: 0 2px 3px -1px currentColor !important;\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000) !important;\n    }\n  }\n  .badge {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    gap: calc(0.25rem * 2);\n    border-radius: var(--radius-selector);\n    vertical-align: middle;\n    color: var(--badge-fg);\n    border: var(--border) solid var(--badge-color, var(--color-base-200));\n    font-size: 0.875rem;\n    width: fit-content;\n    padding-inline: calc(0.25rem * 3 - var(--border));\n    background-size: auto, calc(var(--noise) * 100%);\n    background-image: none, var(--fx-noise);\n    background-color: var(--badge-bg);\n    --badge-bg: var(--badge-color, var(--color-base-100));\n    --badge-fg: var(--color-base-content);\n    --size: calc(var(--size-selector, 0.25rem) * 6);\n    height: var(--size);\n  }\n  .kbd {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: var(--radius-field);\n    background-color: var(--color-base-200);\n    vertical-align: middle;\n    padding-left: 0.5em;\n    padding-right: 0.5em;\n    border: var(--border) solid var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      border: var(--border) solid color-mix(in srgb, var(--color-base-content) 20%, #0000);\n    }\n    border-bottom: calc(var(--border) + 1px) solid var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-bottom: calc(var(--border) + 1px) solid color-mix(in srgb, var(--color-base-content) 20%, #0000);\n    }\n    --size: calc(var(--size-selector, 0.25rem) * 6);\n    font-size: 0.875rem;\n    height: var(--size);\n    min-width: var(--size);\n  }\n  .tabs {\n    display: flex;\n    flex-wrap: wrap;\n    --tabs-height: auto;\n    --tabs-direction: row;\n    --tab-height: calc(var(--size-field, 0.25rem) * 10);\n    height: var(--tabs-height);\n    flex-direction: var(--tabs-direction);\n  }\n  .navbar {\n    display: flex;\n    width: 100%;\n    align-items: center;\n    padding: 0.5rem;\n    min-height: 4rem;\n  }\n  .stat {\n    display: inline-grid;\n    width: 100%;\n    column-gap: calc(0.25rem * 4);\n    padding-inline: calc(0.25rem * 6);\n    padding-block: calc(0.25rem * 4);\n    grid-template-columns: repeat(1, 1fr);\n    &:not(:last-child) {\n      border-inline-end: var(--border) dashed currentColor;\n      @supports (color: color-mix(in lab, red, red)) {\n        border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);\n      }\n      border-block-end: none;\n    }\n  }\n  .navbar-end {\n    display: inline-flex;\n    align-items: center;\n    width: 50%;\n    justify-content: flex-end;\n  }\n  .navbar-start {\n    display: inline-flex;\n    align-items: center;\n    width: 50%;\n    justify-content: flex-start;\n  }\n  .card-body {\n    display: flex;\n    flex: auto;\n    flex-direction: column;\n    gap: calc(0.25rem * 2);\n    padding: var(--card-p, 1.5rem);\n    font-size: var(--card-fs, 0.875rem);\n    :where(p) {\n      flex-grow: 1;\n    }\n  }\n  .navbar-center {\n    display: inline-flex;\n    align-items: center;\n    flex-shrink: 0;\n  }\n  .alert {\n    display: grid;\n    align-items: center;\n    gap: calc(0.25rem * 4);\n    border-radius: var(--radius-box);\n    padding-inline: calc(0.25rem * 4);\n    padding-block: calc(0.25rem * 3);\n    color: var(--color-base-content);\n    background-color: var(--alert-color, var(--color-base-200));\n    justify-content: start;\n    justify-items: start;\n    grid-auto-flow: column;\n    grid-template-columns: auto;\n    text-align: start;\n    border: var(--border) solid var(--color-base-200);\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n    background-size: auto, calc(var(--noise) * 100%);\n    background-image: none, var(--fx-noise);\n    box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px #000, 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px color-mix( in oklab, color-mix(in oklab, #000 20%, var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%), #0000 ), 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));\n    }\n    &:has(:nth-child(2)) {\n      grid-template-columns: auto minmax(auto, 1fr);\n    }\n    &.alert-outline {\n      background-color: transparent;\n      color: var(--alert-color);\n      box-shadow: none;\n      background-image: none;\n    }\n    &.alert-dash {\n      background-color: transparent;\n      color: var(--alert-color);\n      border-style: dashed;\n      box-shadow: none;\n      background-image: none;\n    }\n    &.alert-soft {\n      color: var(--alert-color, var(--color-base-content));\n      background: var(--alert-color, var(--color-base-content));\n      @supports (color: color-mix(in lab, red, red)) {\n        background: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 8%, var(--color-base-100) );\n      }\n      border-color: var(--alert-color, var(--color-base-content));\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 10%, var(--color-base-100) );\n      }\n      box-shadow: none;\n      background-image: none;\n    }\n  }\n  .card-actions {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: flex-start;\n    gap: calc(0.25rem * 2);\n  }\n  .card-title {\n    display: flex;\n    align-items: center;\n    gap: calc(0.25rem * 2);\n    font-size: var(--cardtitle-fs, 1.125rem);\n    font-weight: 600;\n  }\n  .join {\n    display: inline-flex;\n    align-items: stretch;\n    --join-ss: 0;\n    --join-se: 0;\n    --join-es: 0;\n    --join-ee: 0;\n    :where(.join-item) {\n      border-start-start-radius: var(--join-ss, 0);\n      border-start-end-radius: var(--join-se, 0);\n      border-end-start-radius: var(--join-es, 0);\n      border-end-end-radius: var(--join-ee, 0);\n      * {\n        --join-ss: var(--radius-field);\n        --join-se: var(--radius-field);\n        --join-es: var(--radius-field);\n        --join-ee: var(--radius-field);\n      }\n    }\n    > .join-item:where(:first-child) {\n      --join-ss: var(--radius-field);\n      --join-se: 0;\n      --join-es: var(--radius-field);\n      --join-ee: 0;\n    }\n    :first-child:not(:last-child) {\n      :where(.join-item) {\n        --join-ss: var(--radius-field);\n        --join-se: 0;\n        --join-es: var(--radius-field);\n        --join-ee: 0;\n      }\n    }\n    > .join-item:where(:last-child) {\n      --join-ss: 0;\n      --join-se: var(--radius-field);\n      --join-es: 0;\n      --join-ee: var(--radius-field);\n    }\n    :last-child:not(:first-child) {\n      :where(.join-item) {\n        --join-ss: 0;\n        --join-se: var(--radius-field);\n        --join-es: 0;\n        --join-ee: var(--radius-field);\n      }\n    }\n    > .join-item:where(:only-child) {\n      --join-ss: var(--radius-field);\n      --join-se: var(--radius-field);\n      --join-es: var(--radius-field);\n      --join-ee: var(--radius-field);\n    }\n    :only-child {\n      :where(.join-item) {\n        --join-ss: var(--radius-field);\n        --join-se: var(--radius-field);\n        --join-es: var(--radius-field);\n        --join-ee: var(--radius-field);\n      }\n    }\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .prose {\n    :root & {\n      --tw-prose-body: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-body: color-mix(in oklab, var(--color-base-content) 80%, #0000);\n      }\n      --tw-prose-headings: var(--color-base-content);\n      --tw-prose-lead: var(--color-base-content);\n      --tw-prose-links: var(--color-base-content);\n      --tw-prose-bold: var(--color-base-content);\n      --tw-prose-counters: var(--color-base-content);\n      --tw-prose-bullets: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-bullets: color-mix(in oklab, var(--color-base-content) 50%, #0000);\n      }\n      --tw-prose-hr: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-hr: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n      }\n      --tw-prose-quotes: var(--color-base-content);\n      --tw-prose-quote-borders: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-quote-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n      }\n      --tw-prose-captions: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-captions: color-mix(in oklab, var(--color-base-content) 50%, #0000);\n      }\n      --tw-prose-code: var(--color-base-content);\n      --tw-prose-pre-code: var(--color-neutral-content);\n      --tw-prose-pre-bg: var(--color-neutral);\n      --tw-prose-th-borders: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-th-borders: color-mix(in oklab, var(--color-base-content) 50%, #0000);\n      }\n      --tw-prose-td-borders: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-td-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n      }\n      --tw-prose-kbd: var(--color-base-content);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-prose-kbd: color-mix(in oklab, var(--color-base-content) 80%, #0000);\n      }\n      :where(code):not(pre > code) {\n        background-color: var(--color-base-200);\n        border-radius: var(--radius-selector);\n        border: var(--border) solid var(--color-base-300);\n        padding-inline: 0.5em;\n        font-weight: inherit;\n        &:before, &:after {\n          display: none;\n        }\n      }\n    }\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .table {\n    display: table;\n  }\n  .btn-circle {\n    border-radius: calc(infinity * 1px);\n    padding-inline: calc(0.25rem * 0);\n    width: var(--size);\n    height: var(--size);\n  }\n  .btn-square {\n    padding-inline: calc(0.25rem * 0);\n    width: var(--size);\n    height: var(--size);\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-48 {\n    height: calc(var(--spacing) * 48);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-full {\n    height: 100%;\n  }\n  .max-h-32 {\n    max-height: calc(var(--spacing) * 32);\n  }\n  .max-h-64 {\n    max-height: calc(var(--spacing) * 64);\n  }\n  .max-h-80 {\n    max-height: calc(var(--spacing) * 80);\n  }\n  .max-h-96 {\n    max-height: calc(var(--spacing) * 96);\n  }\n  .max-h-\\[90vh\\] {\n    max-height: 90vh;\n  }\n  .max-h-\\[calc\\(90vh-140px\\)\\] {\n    max-height: calc(90vh - 140px);\n  }\n  .min-h-\\[400px\\] {\n    min-height: 400px;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .loading-lg {\n    width: calc(var(--size-selector, 0.25rem) * 7);\n  }\n  .loading-md {\n    width: calc(var(--size-selector, 0.25rem) * 6);\n  }\n  .loading-sm {\n    width: calc(var(--size-selector, 0.25rem) * 5);\n  }\n  .loading-xs {\n    width: calc(var(--size-selector, 0.25rem) * 4);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\/2 {\n    width: calc(1/2 * 100%);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\/3 {\n    width: calc(2/3 * 100%);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-14 {\n    width: calc(var(--spacing) * 14);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-32 {\n    width: calc(var(--spacing) * 32);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-52 {\n    width: calc(var(--spacing) * 52);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-96 {\n    width: calc(var(--spacing) * 96);\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-px {\n    width: 1px;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-none {\n    max-width: none;\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-48 {\n    min-width: calc(var(--spacing) * 48);\n  }\n  .min-w-\\[100px\\] {\n    min-width: 100px;\n  }\n  .min-w-\\[200px\\] {\n    min-width: 200px;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .origin-left {\n    transform-origin: left;\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-full {\n    --tw-translate-x: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-0 {\n    --tw-translate-x: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .rotate-90 {\n    rotate: 90deg;\n  }\n  .rotate-180 {\n    rotate: 180deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .link {\n    cursor: pointer;\n    text-decoration-line: underline;\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n    &:focus-visible {\n      outline: 2px solid currentColor;\n      outline-offset: 2px;\n    }\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .menu-sm {\n    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {\n      border-radius: var(--radius-field);\n      padding-inline: calc(0.25rem * 2.5);\n      padding-block: calc(0.25rem * 1);\n      font-size: 0.75rem;\n    }\n    .menu-title {\n      padding-inline: calc(0.25rem * 3);\n      padding-block: calc(0.25rem * 2);\n    }\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-box {\n    border-radius: var(--radius-box);\n  }\n  .rounded-box {\n    border-radius: var(--radius-box);\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-sm {\n    border-radius: var(--radius-sm);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-4 {\n    border-style: var(--tw-border-style);\n    border-width: 4px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .badge-ghost {\n    border-color: var(--color-base-200);\n    background-color: var(--color-base-200);\n    color: var(--color-base-content);\n    background-image: none;\n  }\n  .badge-outline {\n    color: var(--badge-color);\n    --badge-bg: #0000;\n    background-image: none;\n    border-color: currentColor;\n  }\n  .alert-error {\n    border-color: var(--color-error);\n    color: var(--color-error-content);\n    --alert-color: var(--color-error);\n  }\n  .alert-info {\n    border-color: var(--color-info);\n    color: var(--color-info-content);\n    --alert-color: var(--color-info);\n  }\n  .alert-success {\n    border-color: var(--color-success);\n    color: var(--color-success-content);\n    --alert-color: var(--color-success);\n  }\n  .border-base-200 {\n    border-color: var(--color-base-200);\n  }\n  .border-base-300 {\n    border-color: var(--color-base-300);\n  }\n  .border-error\\/20 {\n    border-color: var(--color-error);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-error) 20%, transparent);\n    }\n  }\n  .border-info\\/20 {\n    border-color: var(--color-info);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-info) 20%, transparent);\n    }\n  }\n  .border-primary {\n    border-color: var(--color-primary);\n  }\n  .border-primary\\/20 {\n    border-color: var(--color-primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-primary) 20%, transparent);\n    }\n  }\n  .border-warning {\n    border-color: var(--color-warning);\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .bg-base-100 {\n    background-color: var(--color-base-100);\n  }\n  .bg-base-200 {\n    background-color: var(--color-base-200);\n  }\n  .bg-base-300 {\n    background-color: var(--color-base-300);\n  }\n  .bg-base-content\\/20 {\n    background-color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n    }\n  }\n  .bg-black\\/30 {\n    background-color: color-mix(in srgb, #000 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-black\\/60 {\n    background-color: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n  }\n  .bg-current {\n    background-color: currentcolor;\n  }\n  .bg-error {\n    background-color: var(--color-error);\n  }\n  .bg-error\\/10 {\n    background-color: var(--color-error);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-error) 10%, transparent);\n    }\n  }\n  .bg-error\\/20 {\n    background-color: var(--color-error);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-error) 20%, transparent);\n    }\n  }\n  .bg-info\\/10 {\n    background-color: var(--color-info);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-info) 10%, transparent);\n    }\n  }\n  .bg-primary {\n    background-color: var(--color-primary);\n  }\n  .bg-primary-content\\/20 {\n    background-color: var(--color-primary-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary-content) 20%, transparent);\n    }\n  }\n  .bg-primary\\/5 {\n    background-color: var(--color-primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 5%, transparent);\n    }\n  }\n  .bg-primary\\/10 {\n    background-color: var(--color-primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);\n    }\n  }\n  .bg-success\\/10 {\n    background-color: var(--color-success);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-success) 10%, transparent);\n    }\n  }\n  .bg-warning {\n    background-color: var(--color-warning);\n  }\n  .bg-warning\\/20 {\n    background-color: var(--color-warning);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);\n    }\n  }\n  .bg-yellow-200 {\n    background-color: var(--color-yellow-200);\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-base-200 {\n    --tw-gradient-from: var(--color-base-200);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary {\n    --tw-gradient-from: var(--color-primary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-base-100 {\n    --tw-gradient-to: var(--color-base-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary {\n    --tw-gradient-to: var(--color-secondary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .loading-spinner {\n    mask-image: url(\"data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E\");\n  }\n  .stroke-current {\n    stroke: currentcolor;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .checkbox-sm {\n    padding: 0.1875rem;\n    --size: calc(var(--size-selector, 0.25rem) * 5);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-12 {\n    padding: calc(var(--spacing) * 12);\n  }\n  .badge-lg {\n    --size: calc(var(--size-selector, 0.25rem) * 7);\n    font-size: 1rem;\n    padding-inline: calc(0.25rem * 3.5 - var(--border));\n  }\n  .badge-sm {\n    --size: calc(var(--size-selector, 0.25rem) * 5);\n    font-size: 0.75rem;\n    padding-inline: calc(0.25rem * 2.5 - var(--border));\n  }\n  .badge-xs {\n    --size: calc(var(--size-selector, 0.25rem) * 4);\n    font-size: 0.625rem;\n    padding-inline: calc(0.25rem * 2 - var(--border));\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-5 {\n    padding-block: calc(var(--spacing) * 5);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .pt-1 {\n    padding-top: calc(var(--spacing) * 1);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-5 {\n    padding-top: calc(var(--spacing) * 5);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pr-1 {\n    padding-right: calc(var(--spacing) * 1);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-3 {\n    padding-right: calc(var(--spacing) * 3);\n  }\n  .pr-4 {\n    padding-right: calc(var(--spacing) * 4);\n  }\n  .pr-5 {\n    padding-right: calc(var(--spacing) * 5);\n  }\n  .pr-10 {\n    padding-right: calc(var(--spacing) * 10);\n  }\n  .pr-20 {\n    padding-right: calc(var(--spacing) * 20);\n  }\n  .pb-1 {\n    padding-bottom: calc(var(--spacing) * 1);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pb-5 {\n    padding-bottom: calc(var(--spacing) * 5);\n  }\n  .pl-1 {\n    padding-left: calc(var(--spacing) * 1);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-3 {\n    padding-left: calc(var(--spacing) * 3);\n  }\n  .pl-4 {\n    padding-left: calc(var(--spacing) * 4);\n  }\n  .pl-5 {\n    padding-left: calc(var(--spacing) * 5);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .pl-10 {\n    padding-left: calc(var(--spacing) * 10);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .font-mono {\n    font-family: var(--font-mono);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .kbd-xs {\n    --size: calc(var(--size-selector, 0.25rem) * 4);\n    font-size: 0.625rem;\n  }\n  .select-sm {\n    --size: calc(var(--size-field, 0.25rem) * 8);\n    font-size: 0.75rem;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-pre-wrap {\n    white-space: pre-wrap;\n  }\n  .checkbox-error {\n    color: var(--color-error-content);\n    --input-color: var(--color-error);\n  }\n  .checkbox-primary {\n    color: var(--color-primary-content);\n    --input-color: var(--color-primary);\n  }\n  .link-primary {\n    color: var(--color-primary);\n    @media (hover: hover) {\n      &:hover {\n        color: var(--color-primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-primary) 80%, #000);\n        }\n      }\n    }\n  }\n  .text-accent {\n    color: var(--color-accent);\n  }\n  .text-base-content {\n    color: var(--color-base-content);\n  }\n  .text-base-content\\/30 {\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 30%, transparent);\n    }\n  }\n  .text-base-content\\/40 {\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);\n    }\n  }\n  .text-base-content\\/50 {\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);\n    }\n  }\n  .text-base-content\\/60 {\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);\n    }\n  }\n  .text-base-content\\/70 {\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 70%, transparent);\n    }\n  }\n  .text-base-content\\/80 {\n    color: var(--color-base-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-base-content) 80%, transparent);\n    }\n  }\n  .text-error {\n    color: var(--color-error);\n  }\n  .text-error-content {\n    color: var(--color-error-content);\n  }\n  .text-error\\/80 {\n    color: var(--color-error);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-error) 80%, transparent);\n    }\n  }\n  .text-info {\n    color: var(--color-info);\n  }\n  .text-info-content {\n    color: var(--color-info-content);\n  }\n  .text-primary {\n    color: var(--color-primary);\n  }\n  .text-primary-content {\n    color: var(--color-primary-content);\n  }\n  .text-primary-content\\/70 {\n    color: var(--color-primary-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-primary-content) 70%, transparent);\n    }\n  }\n  .text-primary-content\\/90 {\n    color: var(--color-primary-content);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-primary-content) 90%, transparent);\n    }\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-secondary {\n    color: var(--color-secondary);\n  }\n  .text-success {\n    color: var(--color-success);\n  }\n  .text-success-content {\n    color: var(--color-success-content);\n  }\n  .text-warning {\n    color: var(--color-warning);\n  }\n  .text-warning-content {\n    color: var(--color-warning-content);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .text-yellow-900 {\n    color: var(--color-yellow-900);\n  }\n  .italic {\n    font-style: italic;\n  }\n  .line-through {\n    text-decoration-line: line-through;\n  }\n  .overline {\n    text-decoration-line: overline;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-2 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-primary {\n    --tw-ring-color: var(--color-primary);\n  }\n  .ring-primary\\/30 {\n    --tw-ring-color: var(--color-primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--color-primary) 30%, transparent);\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .btn-ghost {\n    &:not(.btn-active, :hover, :active:focus, :focus-visible) {\n      --btn-shadow: \"\";\n      --btn-bg: #0000;\n      --btn-border: #0000;\n      --btn-noise: none;\n      &:not(:disabled, [disabled], .btn-disabled) {\n        outline-color: currentColor;\n        --btn-fg: currentColor;\n      }\n    }\n    @media (hover: none) {\n      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {\n        --btn-shadow: \"\";\n        --btn-bg: #0000;\n        --btn-border: #0000;\n        --btn-noise: none;\n        --btn-fg: currentColor;\n      }\n    }\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition\\! {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events !important;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;\n    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .btn-outline {\n    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {\n      --btn-shadow: \"\";\n      --btn-bg: #0000;\n      --btn-fg: var(--btn-color);\n      --btn-border: var(--btn-color);\n      --btn-noise: none;\n    }\n    @media (hover: none) {\n      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {\n        --btn-shadow: \"\";\n        --btn-bg: #0000;\n        --btn-fg: var(--btn-color);\n        --btn-border: var(--btn-color);\n        --btn-noise: none;\n      }\n    }\n  }\n  .btn-sm {\n    --fontsize: 0.75rem;\n    --btn-p: 0.75rem;\n    --size: calc(var(--size-field, 0.25rem) * 8);\n  }\n  .btn-xs {\n    --fontsize: 0.6875rem;\n    --btn-p: 0.5rem;\n    --size: calc(var(--size-field, 0.25rem) * 6);\n  }\n  .badge-primary {\n    --badge-color: var(--color-primary);\n    --badge-fg: var(--color-primary-content);\n  }\n  .badge-secondary {\n    --badge-color: var(--color-secondary);\n    --badge-fg: var(--color-secondary-content);\n  }\n  .badge-warning {\n    --badge-color: var(--color-warning);\n    --badge-fg: var(--color-warning-content);\n  }\n  .btn-accent {\n    --btn-color: var(--color-accent);\n    --btn-fg: var(--color-accent-content);\n  }\n  .btn-error {\n    --btn-color: var(--color-error);\n    --btn-fg: var(--color-error-content);\n  }\n  .btn-primary {\n    --btn-color: var(--color-primary);\n    --btn-fg: var(--color-primary-content);\n  }\n  .btn-secondary {\n    --btn-color: var(--color-secondary);\n    --btn-fg: var(--color-secondary-content);\n  }\n  .btn-success {\n    --btn-color: var(--color-success);\n    --btn-fg: var(--color-success-content);\n  }\n  .btn-warning {\n    --btn-color: var(--color-warning);\n    --btn-fg: var(--color-warning-content);\n  }\n  .input-error {\n    &, &:focus, &:focus-within {\n      --input-color: var(--color-error);\n    }\n  }\n  .radio-error {\n    --input-color: var(--color-error);\n  }\n  .select-error {\n    &, &:focus, &:focus-within {\n      --input-color: var(--color-error);\n    }\n  }\n  .textarea-error {\n    &, &:focus, &:focus-within {\n      --input-color: var(--color-error);\n    }\n  }\n  .group-hover\\:bg-base-300 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: var(--color-base-300);\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:scale-105 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:scale-110 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:border-primary {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-primary);\n      }\n    }\n  }\n  .hover\\:bg-base-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-base-200);\n      }\n    }\n  }\n  .hover\\:bg-base-300 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-base-300);\n      }\n    }\n  }\n  .hover\\:bg-base-content\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-base-content) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-primary);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-base-content {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-base-content);\n      }\n    }\n  }\n  .hover\\:text-base-content\\/70 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-base-content);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--color-base-content) 70%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-md {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:badge-primary {\n    &:hover {\n      @media (hover: hover) {\n        --badge-color: var(--color-primary);\n        --badge-fg: var(--color-primary-content);\n      }\n    }\n  }\n  .hover\\:btn-error {\n    &:hover {\n      @media (hover: hover) {\n        --btn-color: var(--color-error);\n        --btn-fg: var(--color-error-content);\n      }\n    }\n  }\n  .hover\\:btn-info {\n    &:hover {\n      @media (hover: hover) {\n        --btn-color: var(--color-info);\n        --btn-fg: var(--color-info-content);\n      }\n    }\n  }\n  .hover\\:btn-primary {\n    &:hover {\n      @media (hover: hover) {\n        --btn-color: var(--color-primary);\n        --btn-fg: var(--color-primary-content);\n      }\n    }\n  }\n  .hover\\:btn-secondary {\n    &:hover {\n      @media (hover: hover) {\n        --btn-color: var(--color-secondary);\n        --btn-fg: var(--color-secondary-content);\n      }\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-primary {\n    &:focus {\n      --tw-ring-color: var(--color-primary);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus\\:input-primary {\n    &:focus {\n      &, &:focus, &:focus-within {\n        --input-color: var(--color-primary);\n      }\n    }\n  }\n  .focus\\:select-primary {\n    &:focus {\n      &, &:focus, &:focus-within {\n        --input-color: var(--color-primary);\n      }\n    }\n  }\n  .focus\\:textarea-primary {\n    &:focus {\n      &, &:focus, &:focus-within {\n        --input-color: var(--color-primary);\n      }\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:static {\n    @media (width >= 64rem) {\n      position: static;\n    }\n  }\n  .lg\\:inset-0 {\n    @media (width >= 64rem) {\n      inset: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:col-span-2 {\n    @media (width >= 64rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .lg\\:ml-0 {\n    @media (width >= 64rem) {\n      margin-left: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:translate-x-0 {\n    @media (width >= 64rem) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .xl\\:grid-cols-4 {\n    @media (width >= 80rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .\\32 xl\\:grid-cols-6 {\n    @media (width >= 96rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n}\n.card-hover {\n  transition: all 0.3s ease;\n}\n.card-hover:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 24px -4px rgba(0, 0, 0, 0.1);\n}\n.search-highlight {\n  background-color: #fef3c7;\n  color: #92400e;\n  padding: 1px 3px;\n  border-radius: 2px;\n}\n.prose-code {\n  background-color: #f1f5f9;\n  color: #334155;\n  padding: 2px 4px;\n  border-radius: 3px;\n  font-size: 0.875em;\n}\n.animate-fade-in {\n  animation: fadeIn 0.3s ease-in-out;\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.animate-slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n@keyframes slideIn {\n  from {\n    transform: translateX(-100%);\n  }\n  to {\n    transform: translateX(0);\n  }\n}\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n@layer base {\n  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {\n    color-scheme: light;\n    --color-base-100: oklch(100% 0 0);\n    --color-base-200: oklch(98% 0 0);\n    --color-base-300: oklch(95% 0 0);\n    --color-base-content: oklch(21% 0.006 285.885);\n    --color-primary: oklch(45% 0.24 277.023);\n    --color-primary-content: oklch(93% 0.034 272.788);\n    --color-secondary: oklch(65% 0.241 354.308);\n    --color-secondary-content: oklch(94% 0.028 342.258);\n    --color-accent: oklch(77% 0.152 181.912);\n    --color-accent-content: oklch(38% 0.063 188.416);\n    --color-neutral: oklch(14% 0.005 285.823);\n    --color-neutral-content: oklch(92% 0.004 286.32);\n    --color-info: oklch(74% 0.16 232.661);\n    --color-info-content: oklch(29% 0.066 243.157);\n    --color-success: oklch(76% 0.177 163.223);\n    --color-success-content: oklch(37% 0.077 168.94);\n    --color-warning: oklch(82% 0.189 84.429);\n    --color-warning-content: oklch(41% 0.112 45.904);\n    --color-error: oklch(71% 0.194 13.428);\n    --color-error-content: oklch(27% 0.105 12.094);\n    --radius-selector: 0.5rem;\n    --radius-field: 0.25rem;\n    --radius-box: 0.5rem;\n    --size-selector: 0.25rem;\n    --size-field: 0.25rem;\n    --border: 1px;\n    --depth: 1;\n    --noise: 0;\n  }\n}\n@layer base {\n  @media (prefers-color-scheme: dark) {\n    :root {\n      color-scheme: dark;\n      --color-base-100: oklch(25.33% 0.016 252.42);\n      --color-base-200: oklch(23.26% 0.014 253.1);\n      --color-base-300: oklch(21.15% 0.012 254.09);\n      --color-base-content: oklch(97.807% 0.029 256.847);\n      --color-primary: oklch(58% 0.233 277.117);\n      --color-primary-content: oklch(96% 0.018 272.314);\n      --color-secondary: oklch(65% 0.241 354.308);\n      --color-secondary-content: oklch(94% 0.028 342.258);\n      --color-accent: oklch(77% 0.152 181.912);\n      --color-accent-content: oklch(38% 0.063 188.416);\n      --color-neutral: oklch(14% 0.005 285.823);\n      --color-neutral-content: oklch(92% 0.004 286.32);\n      --color-info: oklch(74% 0.16 232.661);\n      --color-info-content: oklch(29% 0.066 243.157);\n      --color-success: oklch(76% 0.177 163.223);\n      --color-success-content: oklch(37% 0.077 168.94);\n      --color-warning: oklch(82% 0.189 84.429);\n      --color-warning-content: oklch(41% 0.112 45.904);\n      --color-error: oklch(71% 0.194 13.428);\n      --color-error-content: oklch(27% 0.105 12.094);\n      --radius-selector: 0.5rem;\n      --radius-field: 0.25rem;\n      --radius-box: 0.5rem;\n      --size-selector: 0.25rem;\n      --size-field: 0.25rem;\n      --border: 1px;\n      --depth: 1;\n      --noise: 0;\n    }\n  }\n}\n@layer base {\n  :root:has(input.theme-controller[value=light]:checked),[data-theme=light] {\n    color-scheme: light;\n    --color-base-100: oklch(100% 0 0);\n    --color-base-200: oklch(98% 0 0);\n    --color-base-300: oklch(95% 0 0);\n    --color-base-content: oklch(21% 0.006 285.885);\n    --color-primary: oklch(45% 0.24 277.023);\n    --color-primary-content: oklch(93% 0.034 272.788);\n    --color-secondary: oklch(65% 0.241 354.308);\n    --color-secondary-content: oklch(94% 0.028 342.258);\n    --color-accent: oklch(77% 0.152 181.912);\n    --color-accent-content: oklch(38% 0.063 188.416);\n    --color-neutral: oklch(14% 0.005 285.823);\n    --color-neutral-content: oklch(92% 0.004 286.32);\n    --color-info: oklch(74% 0.16 232.661);\n    --color-info-content: oklch(29% 0.066 243.157);\n    --color-success: oklch(76% 0.177 163.223);\n    --color-success-content: oklch(37% 0.077 168.94);\n    --color-warning: oklch(82% 0.189 84.429);\n    --color-warning-content: oklch(41% 0.112 45.904);\n    --color-error: oklch(71% 0.194 13.428);\n    --color-error-content: oklch(27% 0.105 12.094);\n    --radius-selector: 0.5rem;\n    --radius-field: 0.25rem;\n    --radius-box: 0.5rem;\n    --size-selector: 0.25rem;\n    --size-field: 0.25rem;\n    --border: 1px;\n    --depth: 1;\n    --noise: 0;\n  }\n}\n@layer base {\n  :root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {\n    color-scheme: dark;\n    --color-base-100: oklch(25.33% 0.016 252.42);\n    --color-base-200: oklch(23.26% 0.014 253.1);\n    --color-base-300: oklch(21.15% 0.012 254.09);\n    --color-base-content: oklch(97.807% 0.029 256.847);\n    --color-primary: oklch(58% 0.233 277.117);\n    --color-primary-content: oklch(96% 0.018 272.314);\n    --color-secondary: oklch(65% 0.241 354.308);\n    --color-secondary-content: oklch(94% 0.028 342.258);\n    --color-accent: oklch(77% 0.152 181.912);\n    --color-accent-content: oklch(38% 0.063 188.416);\n    --color-neutral: oklch(14% 0.005 285.823);\n    --color-neutral-content: oklch(92% 0.004 286.32);\n    --color-info: oklch(74% 0.16 232.661);\n    --color-info-content: oklch(29% 0.066 243.157);\n    --color-success: oklch(76% 0.177 163.223);\n    --color-success-content: oklch(37% 0.077 168.94);\n    --color-warning: oklch(82% 0.189 84.429);\n    --color-warning-content: oklch(41% 0.112 45.904);\n    --color-error: oklch(71% 0.194 13.428);\n    --color-error-content: oklch(27% 0.105 12.094);\n    --radius-selector: 0.5rem;\n    --radius-field: 0.25rem;\n    --radius-box: 0.5rem;\n    --size-selector: 0.25rem;\n    --size-field: 0.25rem;\n    --border: 1px;\n    --depth: 1;\n    --noise: 0;\n  }\n}\n@layer base {\n  :root {\n    --fx-noise: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E\");\n  }\n}\n@layer base {\n  :root, [data-theme] {\n    background-color: var(--root-bg, var(--color-base-100));\n    color: var(--color-base-content);\n  }\n}\n@layer base {\n  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*=\"drawer-open\"]) > .drawer-toggle:checked ) {\n    overflow: hidden;\n  }\n}\n@layer base {\n  @property --radialprogress {\n    syntax: \"<percentage>\";\n    inherits: true;\n    initial-value: 0%;\n  }\n}\n@layer base {\n  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {\n    scrollbar-gutter: stable;\n    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));\n    --root-bg: var(--color-base-100);\n    @supports (color: color-mix(in lab, red, red)) {\n      --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);\n    }\n  }\n  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {\n    scrollbar-gutter: stable;\n  }\n}\n@layer base {\n  :root {\n    scrollbar-color: currentColor #0000;\n    @supports (color: color-mix(in lab, red, red)) {\n      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;\n    }\n  }\n}\n@keyframes progress {\n  50% {\n    background-position-x: -115%;\n  }\n}\n@keyframes rating {\n  0%, 40% {\n    scale: 1.1;\n    filter: brightness(1.05) contrast(1.05);\n  }\n}\n@keyframes skeleton {\n  0% {\n    background-position: 150%;\n  }\n  100% {\n    background-position: -50%;\n  }\n}\n@keyframes dropdown {\n  0% {\n    opacity: 0;\n  }\n}\n@keyframes radio {\n  0% {\n    padding: 5px;\n  }\n  50% {\n    padding: 3px;\n  }\n}\n@keyframes toast {\n  0% {\n    scale: 0.9;\n    opacity: 0;\n  }\n  100% {\n    scale: 1;\n    opacity: 1;\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAqwJE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArwJJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;EAAA;IAAA;;;;;;;;;AAFF;EAuDE;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAMA;;;;;;;;;;;EASA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAAA;;;;;;;;;;;EAAA;;;;;;;;;;;EAUA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAGA;;;;EAGA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAGA;;;;EAAA;;;;EAGA;;;;EAskIA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAiCA;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA;MAAA;;;;;;;;;;;;;;;;;;;;;;;;IAAA;MAAA;;;;;;;;;;;;;;;;;;;;;;;;;EAkCF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAiCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA;IAAA;;;;;;;;;;;;;;;;;;;;;;;;EAiCA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAOA;;;;;;EAIE;IAAgD;;;;;EAIlD;;;;EAAA;;;;EAAA;;;;EAKA;;;;EAEE;IAAgD;;;;;;AAr7IpD;;AAAA;EAwME;;;;;;;;;;;;;;EAUE;;;;;;;EAAA;;;;;;;EAMA;;;;;;;EAKE;;;;;EAKF;;;;;;EAIE;;;;;EAKF;IAEI;;;;;IAMA;;;;;;EAON;;;;;;;;;;;;;;;;;;;;;;;;EAqBE;;;;EAGA;;;;;;;;;EAKE;;;;;;EALF;;;;;;;;;EAKE;;;;;;EALF;;;;;;;;;EAKE;;;;;;EAMF;IACE;;;;;IAAA;;;;;IAAA;;;;;;EAMJ;;;;;;;;EAME;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;;EAoBA;;;;;;;;;;;;;;;;;;;EAiBE;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAMF;;;;;EAAA;;;;;EAIA;;;;;EAKF;;;;;;;;;;;;;;;;;EAYI;IAAuB;;;;;EAIjB;;;;;;;;;;;;;;;;EAeR;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAEE;;;;EAAA;;;;EAAA;;;;EAIF;;;;EAEE;;;;;;;;;;;;;EAFF;;;;EAEE;;;;;;;;;;;;;EAFF;;;;EAEE;;;;;;;;;;;;;EASA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAKF;;;;EAEE;IAAgD;;;;;EAFlD;;;;EAEE;IAAgD;;;;;EAFlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAIA;;;;;EAKF;;;;;;;;;;;EASE;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAKE;;;;;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAWF;;;;EAGA;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;;;;;EAiBA;;;;;EAGE;IAAgC;;;;;;EAIhC;;;;EAKA;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;EAeF;;;;;EAAA;;;;;EAKE;;;;;EAGE;IAAgD;;;;;EAG1C;;;;;;EAGN;IAAgC;;;;;;EATlC;;;;;EAGE;IAAgD;;;;;EAG1C;;;;;;EAGN;IAAgC;;;;;;EATlC;;;;;EAGE;IAAgD;;;;;EAG1C;;;;;;EAGN;IAAgC;;;;;;EATlC;;;;;EAGE;IAAgD;;;;;EAG1C;;;;;;EAGN;IAAgC;;;;;;EATlC;;;;;EAGE;IAAgD;;;;;EAG1C;;;;;;EAGN;IAAgC;;;;;;EATlC;;;;;EAGE;IAAgD;;;;;EAG1C;;;;;;EAGN;IAAgC;;;;;;EAMpC;;;;;EAGE;IAAgD;;;;;EAG7B;;;;;EAEnB;IAAgC;;;;;;EAIrB;;;;;;EAZb;;;;;EAGE;IAAgD;;;;;EAG7B;;;;;EAEnB;IAAgC;;;;;;EAIrB;;;;;;EAZb;;;;;EAGE;IAAgD;;;;;EAG7B;;;;;EAEnB;IAAgC;;;;;;EAIrB;;;;;;EAEb;;;;;;;EAMA;;;;;;;;EAOE;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAXF;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAXF;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAXF;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAXF;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAXF;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAXF;;;;;EAGE;IAAgC;;;;;;EAI1B;;;;;;;EAIN;;;;EAIF;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAKF;;;;;EAGE;;;;EAKJ;;;;;;EAIE;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;EAIE;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAOF;;;;;;;;;EAAA;;;;;;;;;EAQA;IACE;;;;;IAAA;;;;;;EAMA;;;;EAGA;;;;EAHA;;;;EAGA;;;;EAHA;;;;EAGA;;;;EAKA;;;;;EAOE;;;;EAAA;;;;EAAA;;;;EAMF;;;;EAAA;;;;EAAA;;;;EAIF;;;;EAGA;;;;;EAGE;IAAuC;;;;IAErC;;;;;;;IAMA;;;;;;;EAIF;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EASF;;;;EAGQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwCR;IAAgD;;;;;EAGnC;;;;EAEb;IAAgD;;;;;EAIpC;;;;EACZ;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;;;EAKA;;;;;EAGE;IAAgD;;;;;EAGnC;;;;EACb;IAAgD;;;;;EAGnC;;;;EAGb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EALb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EALb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EAEE;;;;;;;EAIf;IAAgD;;;;;EAGhD;IACE;;;;;IAGE;MAAgD;;;;;IAGnC;;;;;IAEb;MAAgD;;;;;;EAvBpD;;;;EAEE;IAAgD;;;;;EAGrC;;;;EALb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EALb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EAEE;;;;;;;EAIf;IAAgD;;;;;EAGhD;IACE;;;;;IAGE;MAAgD;;;;;IAGnC;;;;;IAEb;MAAgD;;;;;;EAvBpD;;;;EAEE;IAAgD;;;;;EAGrC;;;;EALb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EALb;;;;EAEE;IAAgD;;;;;EAGrC;;;;EAEE;;;;;;;EAIf;IAAgD;;;;;EAGhD;IACE;;;;;IAGE;MAAgD;;;;;IAGnC;;;;;IAEb;MAAgD;;;;;;EAMtD;;;;;;EAEE;;;;EAFF;;;;;;EAEE;;;;EAFF;;;;;;EAEE;;;;EAIF;;;;;;EAMF;;;;;;;;;;;;;;;;;EAYA;;;;;;;;;;;;;;;;;EAYA;;;;EAGA;;;;EAGA;;;;;;EAIE;;;;;;;;;;;;EAUE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIA;;;;EAME;;;;;;;;;EAOE;IAAgD;;;;;EAPlD;;;;;;;;;EAOE;IAAgD;;;;;EAOxD;;;;;;;;;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAaE;;;;EAGA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAKA;;;;;;EAKA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAMF;;;;;;;;;;;;;;;;;;;;;;;EAmBE;IAAgD;;;;;EAGrC;;;;;EAEX;IAAgD;;;;;EAGrC;;;;;;;EAIX;;;;;;;;;;;;;;;EAYE;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;;EAOA;;;;;EAIA;;;;;EAKF;;;;;;;;;;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAcE;IAAgD;;;;;EAGhC;;;;;EAGlB;IACE;;;;;;;EAMF;IACE;;;;;;EAKF;;;;;EAAA;;;;;EAIA;;;;;;EAIE;;;;EAGA;IACE;;;;;EARJ;;;;;;EAIE;;;;EAGA;IACE;;;;;EARJ;;;;;;EAIE;;;;EAGA;IACE;;;;;EAKJ;;;;EAGA;;;;;EAGE;;;;;EAMJ;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAuBE;IAAgD;;;;;EAGzC;;;;;EAEP;IAAgD;;;;;EAGhD;;;;EAGA;;;;;;;;;;;EAOE;;;;;EAGE;IAAgC;;;;;;EAHlC;;;;;EAGE;IAAgC;;;;;;EAMpC;;;;EAAA;;;;EAGA;;;;EAGA;;;;;EAGE;IAAgD;;;;;EAGxC;;;;;;;EANV;;;;;EAGE;IAAgD;;;;;EAGxC;;;;;;;EAKV;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAdb;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAdb;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAdb;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAEb;;;;EAGA;;;;EAIE;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAKF;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAKF;;;;;;EAIE;;;;;;;;;;;EAWF;;;;;;;;EAME;;;;EAAA;;;;EAAA;;;;EAMM;IAAuB;;;;;EAAvB;IAAuB;;;;;EAM7B;;;;;;;;EAKA;;;;;EAGE;IAAgD;;;;;EAGtC;;;;;EAGZ;;;;EAEE;IAAgD;;;;;EAIlD;;;;;;;;EAMA;;;;;;;;EAMA;;;;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAKpD;;;;;;;;;EAOE;;;;;;;;;;;EAWE;;;;;;;;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAaA;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;;;;;;;;;;EAiBA;;;;EAGA;;;;EAKA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMA;;;;;EAAA;;;;;EAAA;;;;;EAMJ;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EA0BE;IAAgD;;;;;EAGnC;;;;;EAEb;IAAgD;;;;;EAGzC;;;;EACP;;;;EAGA;;;;;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAWE;;;;;EAGE;IAAgC;;;;;;EAHlC;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAKF;;;;;EAGE;IAAgD;;;;;EAGxC;;;;;;;EANV;;;;;EAGE;IAAgD;;;;;EAGxC;;;;;;;EAKV;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAVpD;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAVpD;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAVpD;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAKpD;;;;EAIF;;;;;;;;;;EASE;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;EAGA;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAOA;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;EAEE;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAMF;;;;;;EAKA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAIA;;;;EAIF;;;;;;;;;;;;EAWA;;;;;;EAIE;;;;;;EAKA;;;;;;EAMF;;;;EAEE;IAAgD;;;;;EAGvC;;;;;;;;;;;;;;;;;;;;;EAgBT;;;;;;;;;;;;;;;;EAgBA;;;;;EAIA;;;;;EAGE;;;;;EAIA;IACE;;;;;;;;EAOF;IACE;;;;;;;;EAhBJ;;;;;EAGE;;;;;EAIA;IACE;;;;;;;;EAOF;IACE;;;;;;;;EASF;;;;;;;EAOF;;;;;EAKF;;;;;;;;;;;;;;EAUE;IAAgD;;;;;EAGrC;;;;;;;;EAKX;;;;;;;;;;;EAUA;;;;EAGA;;;;;;EAIE;;;;;EAIA;IACE;;;;;;;EAMF;IACE;;;;;;EAhBJ;;;;;;EAIE;;;;;EAIA;IACE;;;;;;;EAMF;IACE;;;;;;EAMJ;;;;;EAKF;;;;;;;;EAOA;;;;;;;;;;;;EAQE;IAAgD;;;;;EAG1C;;;;EACN;;;;;;;EAKE;IACE;;;;;;;;;EASJ;IACE;;;;;;EAKF;IACE;;;;;IAIA;;;;;;EAMJ;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIE;;;;;EAAA;;;;;EAIA;;;;;EAKF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGE;;;;EAIF;;;;;EAGE;;;;;EAKF;;;;;EAGE;;;;;EAMJ;;;;EAEE;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;;;;;;;EAgBE;IAAgD;;;;;EAGlC;;;;EACd;IAAgD;;;;;EAGhD;;;;;;;;EAIE;;;;;EAGE;IAAgC;;;;;;EAHlC;;;;;EAGE;IAAgC;;;;;;EAMpC;;;;;EAGE;IAAgD;;;;;EAGxC;;;;;;EANV;;;;;EAGE;IAAgD;;;;;EAGxC;;;;;;EAIV;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAdb;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAdb;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAdb;;;;;;;EAKE;IAAgD;;;;;EAGhD;;;;EAEE;IAAgD;;;;;EAIvC;;;;EAEb;;;;EAIF;;;;EAEE;IAAgD;;;;;EAGnC;;;;;EAGf;;;;;;EAIE;;;;;EAGE;;;;;EAIA;;;;;EAIA;;;;;EAMA;;;;EAGE;;;;EAIA;;;;EAPF;;;;EAGE;;;;EAIA;;;;EAOF;;;;EAGE;;;;EAIA;;;;EAOF;;;;EAGE;;;;EAIA;;;;EAOF;;;;EAGE;;;;EAIA;;;;EAON;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAiBA;;;;;;EAOA;;;;;;;;;;;;;;;;;;;;;;EAmBA;;;;;;;EAMA;;;;;;EAIE;IAAgD;;;;;EAGtC;;;;EAEZ;;;;;;EAIE;IAAgD;;;;;EAGtC;;;;EAEZ;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;;;;;;;;EASE;IAAgD;;;;;EAGhD;;;;;;;;EAAA;;;;;;;;EAOA;IACE;;;;IAAA;;;;;EAIF;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGE;;;;EAGA;;;;;;;EAKE;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAGA;;;;EAEE;;;;EAMF;;;;;;;;;;;EAAA;;;;;;;;;;;EAUA;;;;;;;;;;;EAAA;;;;;;;;;;;EAAA;;;;;;;;;;;EAUJ;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;;EAII;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMJ;;;;;EAII;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMJ;;;;;EAII;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMJ;;;;;EAII;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAMJ;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;EAME;IAAgD;;;;;EAGhD;;;;EAGA;;;;;;;;;;EAOE;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAIE;IAAgD;;;EAIlD;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAAA;;;;;;EAIE;IAAgD;;;EAMtD;;;;;;;;EAME;;;;;;;;;EASE;;;;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAOA;;;;EAKJ;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;EAOE;IAAgD;;;;;EAG5B;;;;;;;EAIpB;IAAgD;;;;IAE9C;MAAgD;;;;;;EAIjC;;;;;EAEjB;IAAgD;;;;;EAIlD;;;;;;;;;EAOE;IAAgD;;;;;EAG5B;;;;;;;EAIpB;IAAgD;;;;IAE9C;MAAgD;;;;;;EAIjC;;;;;EAEjB;IAAgD;;;;;EAIlD;;;;;;;;;;;;;;;;;;;;;;EAoBA;;;;;;;;;;;;EAUE;IAAgD;;;;;EAGlC;;;;EACd;IAAgD;;;;;EAGzC;;;;;;;EAKT;;;;;;;;;;EASA;;;;;;;;EAOA;;;;;;;;;;;EAOE;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAAA;;;;EAEE;IAAgD;;;EAG/B;;;;EAGrB;;;;;;;EAMA;;;;;;;EAMA;;;;;;;;;EAOE;;;;EAIF;;;;;;EAKA;;;;;;;;;;;;;;;;;;;;;;;EAoBE;IAAgD;;;;;EAGhD;;;;EAGA;;;;;;;EAMA;;;;;;;;EAOA;;;;;EAGE;IAAgD;;;;;EAGnC;;;;EACb;IAAgD;;;;;EAGrC;;;;;EAIf;;;;;;;EAMA;;;;;;;;EAOA;;;;;;;;;EAOE;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAAA;;;;;;;EAKE;;;;;;;EAOF;;;;;;;EAOE;;;;;;;EAOF;;;;;;;EAOE;;;;;;;EAOF;;;;;;;EAOE;;;;;;;EAQJ;;;;;;;EAOE;;;;EAEE;IAAgD;;;;;EAG5B;;;;;;;;;EAMpB;IAAgD;;;;;EAGlC;;;;EACd;IAAgD;;;;;EAG9B;;;;;EAElB;IAAgD;;;;;EAG5B;;;;EACpB;IAAgD;;;;;EAGhC;;;;;;;EAIhB;IAAgD;;;;;EAG1B;;;;EACtB;IAAgD;;;;;EAGjC;;;;EACf;IAAgD;;;;;EAGhD;;;;;;;;;EAME;;;;EAAA;;;;EAMN;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;EAMA;;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAGE;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAKF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAOA;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAAA;;;;;EAMF;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EAAA;;;;;;;;;EAMA;;;;;;;EAKF;;;;EAGA;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAEE;IACE;;;;IAEE;MAAgD;;;;;;EAMtD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAGA;;;;;EAGA;;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAKE;;;;;;;EAKE;;;;;EAAA;;;;;EAAA;;;;;EALF;;;;;;;EAKE;;;;;EAAA;;;;;EAAA;;;;;EALF;;;;;;;EAKE;;;;;EAAA;;;;;EAAA;;;;;EAKF;IACE;;;;;;;;IAAA;;;;;;;;IAAA;;;;;;;;;EASJ;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAKE;;;;;;;;EAAA;;;;;;;;EAAA;;;;;;;;EAOA;IACE;;;;;;;;IAAA;;;;;;;;IAAA;;;;;;;;;EASJ;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAKE;;;;EAAA;;;;EAAA;;;;EAIF;;;;EAIE;;;;EAAA;;;;EAAA;;;;EAKA;;;;EAAA;;;;EAAA;;;;EAME;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAOzB;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAOE;;;;EAAA;;;;EAAA;;;;EAOA;;;;EAAA;;;;EAAA;;;;EAOA;;;;EAAA;;;;EAAA;;;;EAMF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;AAK7B;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;;;;;AAOA;;;;AAGA;;;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAQA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAqLA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;AAQA;;;;;;AAKA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA", "debugId": null}}, {"offset": {"line": 9634, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_31c89cc1.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwYGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwSGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwcGFWNOITd.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Fallback';\n    src: local(\"Arial\");\n    ascent-override: 95.94%;\ndescent-override: 28.16%;\nline-gap-override: 0.00%;\nsize-adjust: 104.76%;\n\n}\n.className {\n    font-family: 'Geist', 'Geist Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-sans: 'Geist', 'Geist Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}]}