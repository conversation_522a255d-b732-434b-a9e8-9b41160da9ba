import { NextResponse } from 'next/server'
import { db } from '@/server/db'

export async function GET() {
  try {
    const start = Date.now()
    
    // 数据库连接检查
    await db.$queryRaw`SELECT 1`
    
    const dbLatency = Date.now() - start
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
      },
      database: {
        status: 'connected',
        latency: dbLatency
      },
      services: {
        api: 'healthy',
        database: 'healthy'
      }
    }
    
    return NextResponse.json(health)
  } catch (error) {
    console.error('健康检查失败:', error)
    
    const health = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
      },
      database: {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      services: {
        api: 'healthy',
        database: 'unhealthy'
      }
    }
    
    return NextResponse.json(health, { status: 500 })
  }
}